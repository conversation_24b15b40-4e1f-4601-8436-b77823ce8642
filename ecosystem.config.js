module.exports = {
  apps: [
    {
      name: 'mtbrmg-backend',
      cwd: './apps/backend',
      script: './venv/bin/python',
      args: 'manage.py runserver 8000',
      env: {
        DJANGO_SETTINGS_MODULE: 'mtbrmg_erp.settings',
        PYTHONUNBUFFERED: '1',
        DEBUG: 'True'
      },
      watch: false,
      autorestart: true,
      max_restarts: 10,
      min_uptime: '10s',
      max_memory_restart: '500M',
      error_file: './logs/backend-error.log',
      out_file: './logs/backend-out.log',
      log_file: './logs/backend-combined.log',
      time: true
    },
    {
      name: 'mtbrmg-frontend',
      cwd: './apps/frontend',
      script: 'pnpm',
      args: 'dev',
      env: {
        NODE_ENV: 'development',
        PORT: '3001',
        NEXT_TELEMETRY_DISABLED: '1'
      },
      watch: false,
      autorestart: true,
      max_restarts: 10,
      min_uptime: '10s',
      max_memory_restart: '1G',
      error_file: './logs/frontend-error.log',
      out_file: './logs/frontend-out.log',
      log_file: './logs/frontend-combined.log',
      time: true
    }
  ]
};
