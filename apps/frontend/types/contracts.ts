/**
 * Contracts types and interfaces following established patterns from invoices and clients modules.
 */

// Contract Template Types
export interface ContractTemplate {
  id: string;
  name: string;
  description?: string;
  template_type: 'website' | 'mobile_app' | 'web_app' | 'ecommerce' | 'maintenance' | 'marketing' | 'branding' | 'seo' | 'social_media' | 'custom';
  template_type_display: string;
  content: string;
  is_active: boolean;
  created_by?: string;
  created_by_name?: string;
  created_at: string;
  updated_at: string;
  usage_count?: number;
  last_used?: string;
}

// Contract Status and Priority Types
export type ContractStatus = 
  | 'draft' 
  | 'pending_review' 
  | 'pending_signature' 
  | 'active' 
  | 'completed' 
  | 'cancelled' 
  | 'expired';

export type ContractPriority = 
  | 'low' 
  | 'medium' 
  | 'high' 
  | 'urgent';

// Contract Clause Types
export interface ContractClause {
  id: string;
  title: string;
  content: string;
  clause_type: 'payment' | 'delivery' | 'warranty' | 'cancellation' | 'ip' | 'confidentiality' | 'limitation' | 'general';
  clause_type_display: string;
  is_mandatory: boolean;
  is_active: boolean;
  created_by?: string;
  created_by_name?: string;
  created_at: string;
  updated_at: string;
}

// Contract Signature Types
export interface ContractSignature {
  id: string;
  contract: string;
  contract_number: string;
  signer_name: string;
  signer_email: string;
  signature_type: 'client' | 'company' | 'witness';
  signature_type_display: string;
  signed_at?: string;
  ip_address?: string;
  signature_data?: string;
  is_signed: boolean;
  created_at: string;
}

// Contract Renewal Types
export interface ContractRenewal {
  id: string;
  original_contract: string;
  original_contract_number: string;
  new_contract?: string;
  new_contract_number?: string;
  renewal_date: string;
  new_end_date: string;
  new_value?: number;
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  status_display: string;
  notes?: string;
  created_at: string;
  created_by?: string;
  created_by_name?: string;
}

// Main Contract Interface
export interface Contract {
  id: string;
  contract_number: string;
  title: string;
  description?: string;
  
  // Status & Priority
  status: ContractStatus;
  status_display: string;
  priority: ContractPriority;
  priority_display: string;
  
  // Relationships
  client?: {
    id: string;
    name: string;
    email: string;
    company?: string;
    phone: string;
  };
  client_details?: {
    id: string;
    name: string;
    email: string;
    company?: string;
    phone: string;
    governorate?: string;
  };
  project?: {
    id: string;
    name: string;
    type: string;
    status: string;
  };
  project_details?: {
    id: string;
    name: string;
    type: string;
    status: string;
  };
  template?: {
    id: string;
    name: string;
    template_type: string;
  };
  template_details?: {
    id: string;
    name: string;
    template_type: string;
    template_type_display: string;
  };
  assigned_to?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  assigned_to_details?: {
    id: string;
    username: string;
    first_name: string;
    last_name: string;
    full_name: string;
    role: string;
  };
  
  // Contract Content
  content: string;
  terms_conditions?: string;
  
  // Financial Information
  contract_value?: number;
  
  // Timeline
  start_date: string;
  end_date: string;
  signed_date?: string;
  
  // File Management
  pdf_file?: string;
  signed_pdf_file?: string;
  
  // Renewal Information
  auto_renewal: boolean;
  renewal_period_months?: number;
  renewal_notice_days: number;
  
  // Additional Information
  notes?: string;
  
  // Computed Properties
  is_active: boolean;
  days_until_expiry: number;
  needs_renewal_notice: boolean;
  
  // Related Data
  signatures?: ContractSignature[];
  renewals?: ContractRenewal[];
  
  // Timestamps
  created_at: string;
  updated_at: string;
  created_by?: string;
  created_by_details?: {
    id: string;
    username: string;
    first_name: string;
    last_name: string;
    full_name: string;
  };
}

// Form Data Types
export interface ContractFormData {
  title: string;
  description?: string;
  priority: ContractPriority;
  client_id: string;
  project_id?: string;
  template_id?: string;
  assigned_to_id?: string;
  content: string;
  terms_conditions?: string;
  contract_value?: number;
  start_date: string;
  end_date: string;
  auto_renewal: boolean;
  renewal_period_months?: number;
  renewal_notice_days: number;
  notes?: string;
}

export interface ContractTemplateFormData {
  name: string;
  description?: string;
  template_type: ContractTemplate['template_type'];
  content: string;
  is_active: boolean;
}

export interface ContractClauseFormData {
  title: string;
  content: string;
  clause_type: ContractClause['clause_type'];
  is_mandatory: boolean;
  is_active: boolean;
}

// Filter Types
export interface ContractFilters {
  status?: ContractStatus[];
  priority?: ContractPriority[];
  client?: string;
  project?: string;
  assigned_to?: string;
  template?: string;
  auto_renewal?: boolean;
  date_from?: string;
  date_to?: string;
  value_min?: number;
  value_max?: number;
  expiring_soon?: boolean;
  search?: string;
  page?: number;
  page_size?: number;
  ordering?: string;
}

// API Response Types
export interface ContractListResponse {
  count: number;
  next?: string;
  previous?: string;
  results: Contract[];
}

export interface ContractTemplateListResponse {
  count: number;
  next?: string;
  previous?: string;
  results: ContractTemplate[];
}

export interface ContractClauseListResponse {
  count: number;
  next?: string;
  previous?: string;
  results: ContractClause[];
}

export interface ContractStatsResponse {
  total_contracts: number;
  active_contracts: number;
  pending_signature: number;
  expiring_soon: number;
  total_value: number;
  by_status: Record<ContractStatus, { count: number; label: string }>;
  by_priority: Record<ContractPriority, { count: number; label: string }>;
}

// Constants
export const CONTRACT_STATUS_OPTIONS: { value: ContractStatus; label: string; color: string }[] = [
  { value: 'draft', label: 'مسودة', color: 'gray' },
  { value: 'pending_review', label: 'في انتظار المراجعة', color: 'orange' },
  { value: 'pending_signature', label: 'في انتظار التوقيع', color: 'blue' },
  { value: 'active', label: 'نشط', color: 'green' },
  { value: 'completed', label: 'مكتمل', color: 'purple' },
  { value: 'cancelled', label: 'ملغي', color: 'red' },
  { value: 'expired', label: 'منتهي الصلاحية', color: 'red' },
];

export const CONTRACT_PRIORITY_OPTIONS: { value: ContractPriority; label: string; color: string }[] = [
  { value: 'low', label: 'منخفض', color: 'green' },
  { value: 'medium', label: 'متوسط', color: 'yellow' },
  { value: 'high', label: 'عالي', color: 'orange' },
  { value: 'urgent', label: 'عاجل', color: 'red' },
];

export const CONTRACT_TEMPLATE_TYPE_OPTIONS: { value: ContractTemplate['template_type']; label: string }[] = [
  { value: 'website', label: 'تطوير موقع إلكتروني' },
  { value: 'mobile_app', label: 'تطبيق جوال' },
  { value: 'web_app', label: 'تطبيق ويب' },
  { value: 'ecommerce', label: 'متجر إلكتروني' },
  { value: 'maintenance', label: 'صيانة' },
  { value: 'marketing', label: 'تسويق رقمي' },
  { value: 'custom', label: 'مخصص' },
];

export const CONTRACT_CLAUSE_TYPE_OPTIONS: { value: ContractClause['clause_type']; label: string }[] = [
  { value: 'payment', label: 'الدفع' },
  { value: 'delivery', label: 'التسليم' },
  { value: 'warranty', label: 'الضمان' },
  { value: 'cancellation', label: 'الإلغاء' },
  { value: 'ip', label: 'الملكية الفكرية' },
  { value: 'confidentiality', label: 'السرية' },
  { value: 'limitation', label: 'تحديد المسؤولية' },
  { value: 'general', label: 'عام' },
];
