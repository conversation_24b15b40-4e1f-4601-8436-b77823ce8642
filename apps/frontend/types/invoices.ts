/**
 * Invoice types and interfaces following established patterns from clients and projects modules.
 */

// Invoice Template Types
export interface InvoiceTemplate {
  id: string;
  name: string;
  description?: string;
  template_type: 'standard' | 'detailed' | 'minimal' | 'custom';
  template_type_display: string;
  header_text?: string;
  footer_text?: string;
  terms_conditions?: string;
  payment_instructions?: string;
  primary_color: string;
  secondary_color: string;
  font_family: string;
  is_active: boolean;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

// Invoice Status and Priority Types
export type InvoiceStatus = 
  | 'draft' 
  | 'pending' 
  | 'sent' 
  | 'viewed' 
  | 'paid' 
  | 'partially_paid' 
  | 'overdue' 
  | 'cancelled' 
  | 'refunded';

export type InvoicePriority = 'low' | 'medium' | 'high' | 'urgent';

export type PaymentTerms = 
  | 'net_15' 
  | 'net_30' 
  | 'net_45' 
  | 'net_60' 
  | 'due_on_receipt' 
  | 'custom';

// Invoice Item Types
export interface InvoiceItem {
  id: string;
  item_type: 'service' | 'product' | 'discount' | 'shipping' | 'other';
  item_type_display: string;
  name: string;
  description: string;
  quantity: number;
  unit: string;
  unit_price: number;
  total_price: number;
  is_taxable: boolean;
  tax_percentage: number;
  quotation_item?: string;
  created_at: string;
}

// Invoice Payment Types
export type PaymentMethod = 
  | 'cash' 
  | 'bank_transfer' 
  | 'credit_card' 
  | 'debit_card' 
  | 'check' 
  | 'paypal' 
  | 'stripe' 
  | 'fawry' 
  | 'vodafone_cash' 
  | 'orange_money' 
  | 'etisalat_cash' 
  | 'other';

export type PaymentStatus = 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded';

export interface InvoicePayment {
  id: string;
  payment_method: PaymentMethod;
  payment_method_display: string;
  amount: number;
  status: PaymentStatus;
  status_display: string;
  transaction_id?: string;
  reference_number?: string;
  payment_date: string;
  processed_date?: string;
  processed_by?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  notes?: string;
  receipt_file?: string;
  created_at: string;
  updated_at: string;
}

// Main Invoice Interface
export interface Invoice {
  id: string;
  invoice_number: string;
  title: string;
  description?: string;
  
  // Relationships
  client: {
    id: string;
    name: string;
    email: string;
    company?: string;
    phone: string;
  };
  project?: {
    id: string;
    name: string;
    type: string;
    status: string;
  };
  quotation?: {
    id: string;
    quotation_number: string;
    title: string;
  };
  sales_rep?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  template?: InvoiceTemplate;
  
  // Status and Priority
  status: InvoiceStatus;
  status_display: string;
  priority: InvoicePriority;
  priority_display: string;
  
  // Financial Details
  subtotal: number;
  discount_percentage: number;
  discount_amount: number;
  tax_percentage: number;
  tax_amount: number;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  
  // Payment Terms
  payment_terms: PaymentTerms;
  payment_terms_display: string;
  custom_payment_days?: number;
  
  // Dates
  invoice_date: string;
  due_date?: string;
  sent_date?: string;
  viewed_date?: string;
  paid_date?: string;
  
  // Additional Information
  notes?: string;
  terms_conditions?: string;
  payment_instructions?: string;
  pdf_file?: string;
  
  // Computed Fields
  is_overdue: boolean;
  days_overdue: number;
  payment_percentage: number;
  is_fully_paid: boolean;
  is_partially_paid: boolean;
  
  // Related Data
  items: InvoiceItem[];
  payments: InvoicePayment[];
  items_count: number;
  payments_count: number;
  
  // Timestamps
  created_at: string;
  updated_at: string;
}

// Form Data Types
export interface InvoiceFormData {
  title: string;
  description?: string;
  client_id: string;
  project_id?: string;
  quotation_id?: string;
  template_id?: string;
  priority: InvoicePriority;
  discount_percentage: number;
  tax_percentage: number;
  payment_terms: PaymentTerms;
  custom_payment_days?: number;
  invoice_date: string;
  due_date?: string;
  notes?: string;
  terms_conditions?: string;
  payment_instructions?: string;
  items_data: InvoiceItemFormData[];
}

export interface InvoiceItemFormData {
  item_type: 'service' | 'product' | 'discount' | 'shipping' | 'other';
  name: string;
  description: string;
  quantity: number;
  unit: string;
  unit_price: number;
  is_taxable: boolean;
  tax_percentage: number;
  quotation_item?: string;
}

export interface InvoicePaymentFormData {
  payment_method: PaymentMethod;
  amount: number;
  transaction_id?: string;
  reference_number?: string;
  payment_date: string;
  notes?: string;
  receipt_file?: File;
}

// Statistics Types
export interface InvoiceStats {
  total_invoices: number;
  total_amount: number;
  paid_amount: number;
  pending_amount: number;
  overdue_amount: number;
  draft_count: number;
  pending_count: number;
  sent_count: number;
  paid_count: number;
  overdue_count: number;
  avg_payment_time: number;
  payment_rate: number;
}

// Filter Types
export interface InvoiceFilters {
  status?: InvoiceStatus[];
  priority?: InvoicePriority[];
  client?: string;
  project?: string;
  sales_rep?: string;
  payment_terms?: PaymentTerms[];
  date_from?: string;
  date_to?: string;
  amount_min?: number;
  amount_max?: number;
  is_overdue?: boolean;
  search?: string;
  page?: number;
  page_size?: number;
  ordering?: string;
}

// API Response Types
export interface InvoiceListResponse {
  count: number;
  next?: string;
  previous?: string;
  results: Invoice[];
}

export interface InvoiceTemplateListResponse {
  count: number;
  next?: string;
  previous?: string;
  results: InvoiceTemplate[];
}

// Constants
export const INVOICE_STATUS_OPTIONS = [
  { value: 'draft', label: 'مسودة', color: 'gray' },
  { value: 'pending', label: 'في الانتظار', color: 'yellow' },
  { value: 'sent', label: 'مرسلة', color: 'blue' },
  { value: 'viewed', label: 'تم عرضها', color: 'purple' },
  { value: 'paid', label: 'مدفوعة', color: 'green' },
  { value: 'partially_paid', label: 'مدفوعة جزئياً', color: 'orange' },
  { value: 'overdue', label: 'متأخرة', color: 'red' },
  { value: 'cancelled', label: 'ملغية', color: 'gray' },
  { value: 'refunded', label: 'مستردة', color: 'red' },
] as const;

export const INVOICE_PRIORITY_OPTIONS = [
  { value: 'low', label: 'منخفض', color: 'gray' },
  { value: 'medium', label: 'متوسط', color: 'blue' },
  { value: 'high', label: 'عالي', color: 'orange' },
  { value: 'urgent', label: 'عاجل', color: 'red' },
] as const;

export const PAYMENT_TERMS_OPTIONS = [
  { value: 'due_on_receipt', label: 'مستحق عند الاستلام' },
  { value: 'net_15', label: '15 يوم' },
  { value: 'net_30', label: '30 يوم' },
  { value: 'net_45', label: '45 يوم' },
  { value: 'net_60', label: '60 يوم' },
  { value: 'custom', label: 'مخصص' },
] as const;

export const PAYMENT_METHOD_OPTIONS = [
  { value: 'cash', label: 'نقدي' },
  { value: 'bank_transfer', label: 'تحويل بنكي' },
  { value: 'credit_card', label: 'بطاقة ائتمان' },
  { value: 'debit_card', label: 'بطاقة خصم' },
  { value: 'check', label: 'شيك' },
  { value: 'paypal', label: 'باي بال' },
  { value: 'stripe', label: 'سترايب' },
  { value: 'fawry', label: 'فوري' },
  { value: 'vodafone_cash', label: 'فودافون كاش' },
  { value: 'orange_money', label: 'أورانج موني' },
  { value: 'etisalat_cash', label: 'اتصالات كاش' },
  { value: 'other', label: 'أخرى' },
] as const;
