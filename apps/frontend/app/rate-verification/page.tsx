'use client';

import { useState, useEffect } from 'react';
import { useCurrency } from '@/lib/stores/currency-store';

export default function RateVerificationPage() {
  const { updateExchangeRate, getManualExchangeRate, format, currentCurrency, setCurrency } = useCurrency();
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const runVerificationTests = () => {
    setTestResults([]);
    addResult('Starting manual rate verification tests...');

    // Test 1: Set USD rate to 50 EGP
    addResult('Test 1: Setting 1 USD = 50 EGP');
    updateExchangeRate('USD', 50);
    const usdRate = getManualExchangeRate('USD');
    addResult(`✓ USD rate set to: 1 USD = ${usdRate} EGP`);

    // Test 2: Set SAR rate to 13 EGP
    addResult('Test 2: Setting 1 SAR = 13 EGP');
    updateExchangeRate('SAR', 13);
    const sarRate = getManualExchangeRate('SAR');
    addResult(`✓ SAR rate set to: 1 SAR = ${sarRate} EGP`);

    // Test 3: Set AED rate to 13.5 EGP
    addResult('Test 3: Setting 1 AED = 13.5 EGP');
    updateExchangeRate('AED', 13.5);
    const aedRate = getManualExchangeRate('AED');
    addResult(`✓ AED rate set to: 1 AED = ${aedRate} EGP`);

    // Test 4: Format currency with different amounts
    addResult('Test 4: Testing currency formatting');
    setCurrency('USD');
    const formatted1000USD = format(1000);
    addResult(`✓ 1000 EGP formatted as USD: ${formatted1000USD}`);

    setCurrency('SAR');
    const formatted1000SAR = format(1000);
    addResult(`✓ 1000 EGP formatted as SAR: ${formatted1000SAR}`);

    setCurrency('AED');
    const formatted1000AED = format(1000);
    addResult(`✓ 1000 EGP formatted as AED: ${formatted1000AED}`);

    setCurrency('EGP');
    const formatted1000EGP = format(1000);
    addResult(`✓ 1000 EGP formatted as EGP: ${formatted1000EGP}`);

    addResult('✅ All manual rate verification tests completed successfully!');
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen p-8 bg-gray-100">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Manual Exchange Rate Verification</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Control Panel */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-bold mb-4">Test Controls</h2>
            <div className="space-y-4">
              <button 
                onClick={runVerificationTests}
                className="w-full bg-green-500 text-white p-3 rounded hover:bg-green-600"
              >
                Run Verification Tests
              </button>
              
              <button 
                onClick={clearResults}
                className="w-full bg-gray-500 text-white p-3 rounded hover:bg-gray-600"
              >
                Clear Results
              </button>

              <div className="mt-6 p-4 bg-blue-50 rounded">
                <h3 className="font-bold mb-2">Current Rates:</h3>
                <p>USD: 1 USD = {getManualExchangeRate('USD')} EGP</p>
                <p>SAR: 1 SAR = {getManualExchangeRate('SAR')} EGP</p>
                <p>AED: 1 AED = {getManualExchangeRate('AED')} EGP</p>
                <p className="mt-2 text-sm text-gray-600">Current Currency: {currentCurrency}</p>
              </div>

              <div className="mt-4 p-4 bg-yellow-50 rounded">
                <h3 className="font-bold mb-2">Quick Format Tests:</h3>
                <p>1000 EGP as USD: {format(1000)}</p>
                <p>5000 EGP as current: {format(5000)}</p>
                <p>100 EGP as current: {format(100)}</p>
              </div>
            </div>
          </div>

          {/* Results Panel */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-bold mb-4">Test Results</h2>
            <div className="bg-gray-50 p-4 rounded max-h-96 overflow-y-auto">
              {testResults.length === 0 ? (
                <p className="text-gray-500 italic">No tests run yet. Click "Run Verification Tests" to start.</p>
              ) : (
                <div className="space-y-1">
                  {testResults.map((result, index) => (
                    <div key={index} className="text-sm font-mono">
                      {result}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="mt-8 text-center">
          <a href="/founder-dashboard/settings" className="text-blue-500 underline mr-4">
            ← Go to Settings (Manual Rate Management)
          </a>
          <a href="/currency-test" className="text-blue-500 underline mr-4">
            Currency Test Page
          </a>
          <a href="/" className="text-blue-500 underline">
            Back to Home
          </a>
        </div>
      </div>
    </div>
  );
}
