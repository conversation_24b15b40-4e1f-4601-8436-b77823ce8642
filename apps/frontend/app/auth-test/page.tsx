'use client';

import { useAuthStore } from '@/lib/stores/auth-store';
import { UserRole } from '@mtbrmg/shared';

export default function AuthTestPage() {
  const { user, isAuthenticated, isLoading } = useAuthStore();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
        <h1 className="text-2xl font-bold mb-4">Authentication Test</h1>
        
        <div className="space-y-4">
          <div>
            <strong>Is Loading:</strong> {isLoading ? 'Yes' : 'No'}
          </div>
          
          <div>
            <strong>Is Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}
          </div>
          
          <div>
            <strong>User:</strong> {user ? JSON.stringify(user, null, 2) : 'No user'}
          </div>
          
          <div>
            <strong>User Role:</strong> {user?.role || 'No role'}
          </div>
          
          <div>
            <strong>Is Admin:</strong> {user?.role === UserRole.ADMIN ? 'Yes' : 'No'}
          </div>
          
          <div>
            <strong>Local Storage Access Token:</strong> 
            {typeof window !== 'undefined' ? (localStorage.getItem('access_token') ? 'Present' : 'Missing') : 'Server Side'}
          </div>
          
          <div>
            <strong>Local Storage Refresh Token:</strong> 
            {typeof window !== 'undefined' ? (localStorage.getItem('refresh_token') ? 'Present' : 'Missing') : 'Server Side'}
          </div>
        </div>
        
        <div className="mt-6 space-y-2">
          <a href="/login" className="block text-blue-500 underline">Go to Login</a>
          <a href="/founder-dashboard" className="block text-blue-500 underline">Go to Founder Dashboard</a>
        </div>
      </div>
    </div>
  );
}
