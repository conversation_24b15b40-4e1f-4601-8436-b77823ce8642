"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  DollarSign, 
  FileText, 
  Settings,
  Download,
  RefreshCw,
  Plus,
  Eye
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

import ExecutiveDashboard from '@/components/business-intelligence/ExecutiveDashboard';
import KPIMetrics from '@/components/business-intelligence/KPIMetrics';
import AnalyticsReports from '@/components/business-intelligence/AnalyticsReports';
import DashboardWidgets from '@/components/business-intelligence/DashboardWidgets';
import { businessIntelligenceApi } from '@/lib/api/business-intelligence';

interface DashboardStats {
  totalReports: number;
  activeKPIs: number;
  totalWidgets: number;
  lastUpdated: string;
}

export default function BusinessIntelligencePage() {
  const [activeTab, setActiveTab] = useState('executive');
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadDashboardStats();
  }, []);

  const loadDashboardStats = async () => {
    try {
      setLoading(true);
      // Load dashboard statistics
      const [reportsResponse, kpisResponse, widgetsResponse] = await Promise.all([
        businessIntelligenceApi.getReports(),
        businessIntelligenceApi.getKPIs(),
        businessIntelligenceApi.getWidgets()
      ]);

      setStats({
        totalReports: reportsResponse.data.count || 0,
        activeKPIs: kpisResponse.data.filter((kpi: any) => kpi.is_visible).length || 0,
        totalWidgets: widgetsResponse.data.count || 0,
        lastUpdated: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
      toast({
        title: "خطأ",
        description: "فشل في تحميل إحصائيات لوحة التحكم",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshData = async () => {
    try {
      await businessIntelligenceApi.calculateKPIs();
      await loadDashboardStats();
      toast({
        title: "تم التحديث",
        description: "تم تحديث البيانات بنجاح",
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث البيانات",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">ذكاء الأعمال والتحليلات</h1>
          <p className="text-gray-600 mt-2">
            لوحة تحكم شاملة لتحليل الأداء واتخاذ القرارات الذكية
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleRefreshData} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 ml-2" />
            تحديث البيانات
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 ml-2" />
            تصدير التقرير
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي التقارير</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalReports}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">مؤشرات الأداء النشطة</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.activeKPIs}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">ودجتات لوحة التحكم</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalWidgets}</p>
                </div>
                <BarChart3 className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">آخر تحديث</p>
                  <p className="text-sm text-gray-900">
                    {new Date(stats.lastUpdated).toLocaleString('ar-EG')}
                  </p>
                </div>
                <RefreshCw className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="executive" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            لوحة المدير التنفيذي
          </TabsTrigger>
          <TabsTrigger value="kpis" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            مؤشرات الأداء
          </TabsTrigger>
          <TabsTrigger value="reports" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            التقارير التحليلية
          </TabsTrigger>
          <TabsTrigger value="widgets" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            ودجتات لوحة التحكم
          </TabsTrigger>
        </TabsList>

        <TabsContent value="executive" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                لوحة المدير التنفيذي
              </CardTitle>
              <CardDescription>
                نظرة شاملة على أداء الشركة والمؤشرات الرئيسية
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ExecutiveDashboard />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="kpis" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    مؤشرات الأداء الرئيسية
                  </CardTitle>
                  <CardDescription>
                    إدارة ومتابعة مؤشرات الأداء الرئيسية للشركة
                  </CardDescription>
                </div>
                <Button size="sm">
                  <Plus className="h-4 w-4 ml-2" />
                  إضافة مؤشر جديد
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <KPIMetrics />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    التقارير التحليلية
                  </CardTitle>
                  <CardDescription>
                    إنشاء وإدارة التقارير التحليلية المخصصة
                  </CardDescription>
                </div>
                <Button size="sm">
                  <Plus className="h-4 w-4 ml-2" />
                  إنشاء تقرير جديد
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <AnalyticsReports />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="widgets" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    ودجتات لوحة التحكم
                  </CardTitle>
                  <CardDescription>
                    تخصيص وإدارة ودجتات لوحة التحكم
                  </CardDescription>
                </div>
                <Button size="sm">
                  <Plus className="h-4 w-4 ml-2" />
                  إضافة ودجت جديد
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <DashboardWidgets />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
