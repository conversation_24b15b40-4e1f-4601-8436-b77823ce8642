'use client';

import { <PERSON>R<PERSON>, FileText, Plus, Save, Trash2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';

import { UnifiedLayout } from '@/components/layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { clientsAPI, invoicesAPI, quotationsAPI } from '@/lib/api';
import { formatCurrency } from '@/lib/utils';
import {
    INVOICE_PRIORITY_OPTIONS,
    InvoiceFormData,
    InvoiceItemFormData,
    PAYMENT_TERMS_OPTIONS
} from '@/types/invoices';

interface Client {
  id: string;
  name: string;
  email: string;
  company?: string;
  phone: string;
}

interface Quotation {
  id: string;
  quotation_number: string;
  title: string;
  client: Client;
  total_amount: number;
}

const NewInvoicePage = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);
  const [quotations, setQuotations] = useState<Quotation[]>([]);
  const [selectedQuotation, setSelectedQuotation] = useState<Quotation | null>(null);

  // Form state
  const [formData, setFormData] = useState<InvoiceFormData>({
    title: '',
    description: '',
    client_id: '',
    quotation_id: 'none',
    priority: 'medium',
    discount_percentage: 0,
    tax_percentage: 14,
    payment_terms: 'net_30',
    custom_payment_days: undefined,
    invoice_date: new Date().toISOString().split('T')[0],
    due_date: '',
    notes: '',
    terms_conditions: '',
    payment_instructions: '',
    items_data: []
  });

  const [items, setItems] = useState<InvoiceItemFormData[]>([]);

  useEffect(() => {
    fetchClients();
    fetchQuotations();
    
    // Set default due date based on payment terms
    updateDueDate('net_30');
  }, []);

  const fetchClients = async () => {
    try {
      const data = await clientsAPI.getClients();
      setClients(data.results || data);
    } catch (error) {
      console.error('Error fetching clients:', error);
      toast.error('حدث خطأ في تحميل العملاء');
    }
  };

  const fetchQuotations = async () => {
    try {
      const data = await quotationsAPI.getQuotations({ 
        status: 'approved',
        page_size: 100 
      });
      setQuotations(data.results || data);
    } catch (error) {
      console.error('Error fetching quotations:', error);
      toast.error('حدث خطأ في تحميل العروض');
    }
  };

  const updateDueDate = (paymentTerms: string, customDays?: number) => {
    const invoiceDate = new Date(formData.invoice_date);
    let daysToAdd = 30; // default

    switch (paymentTerms) {
      case 'due_on_receipt':
        daysToAdd = 0;
        break;
      case 'net_15':
        daysToAdd = 15;
        break;
      case 'net_30':
        daysToAdd = 30;
        break;
      case 'net_45':
        daysToAdd = 45;
        break;
      case 'net_60':
        daysToAdd = 60;
        break;
      case 'custom':
        daysToAdd = customDays || 30;
        break;
    }

    const dueDate = new Date(invoiceDate);
    dueDate.setDate(dueDate.getDate() + daysToAdd);
    
    setFormData(prev => ({
      ...prev,
      due_date: dueDate.toISOString().split('T')[0]
    }));
  };

  const addItem = () => {
    const newItem: InvoiceItemFormData = {
      item_type: 'service',
      name: '',
      description: '',
      quantity: 1,
      unit: 'خدمة',
      unit_price: 0,
      is_taxable: true,
      tax_percentage: formData.tax_percentage
    };
    setItems([...items, newItem]);
  };

  const removeItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const updateItem = (index: number, field: keyof InvoiceItemFormData, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    setItems(updatedItems);
  };

  const handleQuotationSelect = async (quotationId: string) => {
    if (!quotationId || quotationId === 'none') {
      setSelectedQuotation(null);
      setFormData(prev => ({ ...prev, quotation_id: 'none' }));
      return;
    }

    try {
      const quotation = quotations.find(q => q.id === quotationId);
      if (quotation) {
        setSelectedQuotation(quotation);
        setFormData(prev => ({
          ...prev,
          quotation_id: quotationId,
          client_id: quotation.client.id,
          title: `فاتورة للعرض ${quotation.quotation_number}`,
          description: `فاتورة مبنية على العرض: ${quotation.title}`
        }));

        // Fetch quotation details to get items
        const quotationDetails = await quotationsAPI.getQuotation(parseInt(quotationId));
        if (quotationDetails.items) {
          const invoiceItems: InvoiceItemFormData[] = quotationDetails.items.map((item: any) => ({
            item_type: 'service' as const,
            name: item.service?.name_ar || item.description,
            description: item.description,
            quantity: item.quantity,
            unit: item.service?.unit || 'خدمة',
            unit_price: parseFloat(item.unit_price),
            is_taxable: true,
            tax_percentage: formData.tax_percentage,
            quotation_item: item.id.toString()
          }));
          setItems(invoiceItems);
        }
      }
    } catch (error) {
      console.error('Error fetching quotation details:', error);
      toast.error('حدث خطأ في تحميل تفاصيل العرض');
    }
  };

  const calculateTotals = () => {
    const subtotal = items.reduce((sum, item) => {
      return sum + (item.quantity * item.unit_price);
    }, 0);
    
    const discountAmount = subtotal * (formData.discount_percentage / 100);
    const afterDiscount = subtotal - discountAmount;
    
    const taxAmount = items.reduce((sum, item) => {
      if (item.is_taxable) {
        const itemTotal = item.quantity * item.unit_price;
        const itemAfterDiscount = itemTotal * (1 - formData.discount_percentage / 100);
        return sum + (itemAfterDiscount * (item.tax_percentage / 100));
      }
      return sum;
    }, 0);
    
    const total = afterDiscount + taxAmount;

    return {
      subtotal,
      discountAmount,
      taxAmount,
      total
    };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title || !formData.client_id || items.length === 0) {
      toast.error('يرجى ملء جميع الحقول المطلوبة وإضافة عنصر واحد على الأقل');
      return;
    }

    setLoading(true);

    try {
      const invoiceData = {
        ...formData,
        items_data: items
      };

      const result = await invoicesAPI.createInvoice(invoiceData);
      toast.success('تم إنشاء الفاتورة بنجاح');
      router.push(`/founder-dashboard/invoices/${result.id}`);
    } catch (error: any) {
      console.error('Error creating invoice:', error);
      toast.error(error.response?.data?.message || 'حدث خطأ في إنشاء الفاتورة');
    } finally {
      setLoading(false);
    }
  };

  const totals = calculateTotals();

  return (
    <UnifiedLayout>
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-6">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
          className="flex items-center gap-2 w-fit touch-target"
        >
          <ArrowRight className="w-4 h-4" />
          رجوع
        </Button>
        <div className="flex-1">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mobile-truncate">إنشاء فاتورة جديدة</h1>
          <p className="text-gray-600 mt-1 text-sm sm:text-base">إنشاء فاتورة احترافية للعملاء</p>
        </div>
      </div>

      {/* Form Section */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information Card */}
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg sm:text-xl flex items-center gap-2">
              <FileText className="h-5 w-5" />
              المعلومات الأساسية
            </CardTitle>
            <CardDescription>معلومات الفاتورة الأساسية</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Create from Quotation Option */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <Label className="text-sm font-medium text-blue-800 mb-2 block">
                إنشاء من عرض سعر موجود (اختياري)
              </Label>
              <Select value={formData.quotation_id} onValueChange={handleQuotationSelect}>
                <SelectTrigger className="touch-target bg-white">
                  <SelectValue placeholder="اختر عرض سعر لإنشاء فاتورة منه" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">بدون عرض سعر</SelectItem>
                  {quotations.map((quotation) => (
                    <SelectItem key={quotation.id} value={quotation.id}>
                      {quotation.quotation_number} - {quotation.title} ({quotation.client.name})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Responsive Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title" className="text-sm font-medium">عنوان الفاتورة *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="مثال: فاتورة تطوير موقع إلكتروني"
                  className="touch-target"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="client" className="text-sm font-medium">العميل *</Label>
                <Select 
                  value={formData.client_id} 
                  onValueChange={(value) => setFormData(prev => ({ ...prev, client_id: value }))}
                  disabled={!!selectedQuotation}
                >
                  <SelectTrigger className="touch-target">
                    <SelectValue placeholder="اختر العميل" />
                  </SelectTrigger>
                  <SelectContent>
                    {clients.map((client) => (
                      <SelectItem key={client.id} value={client.id}>
                        {client.name} - {client.company || 'فردي'}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Description Field */}
            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium">وصف الفاتورة</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="وصف تفصيلي للفاتورة..."
                rows={3}
                className="touch-target resize-none"
              />
            </div>

            {/* Priority and Settings Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="priority" className="text-sm font-medium">الأولوية</Label>
                <Select value={formData.priority} onValueChange={(value: any) => setFormData(prev => ({ ...prev, priority: value }))}>
                  <SelectTrigger className="touch-target">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {INVOICE_PRIORITY_OPTIONS.map((priority) => (
                      <SelectItem key={priority.value} value={priority.value}>
                        {priority.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="payment_terms" className="text-sm font-medium">شروط الدفع</Label>
                <Select
                  value={formData.payment_terms}
                  onValueChange={(value: any) => {
                    setFormData(prev => ({ ...prev, payment_terms: value }));
                    updateDueDate(value, formData.custom_payment_days);
                  }}
                >
                  <SelectTrigger className="touch-target">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {PAYMENT_TERMS_OPTIONS.map((term) => (
                      <SelectItem key={term.value} value={term.value}>
                        {term.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {formData.payment_terms === 'custom' && (
                <div className="space-y-2">
                  <Label htmlFor="custom_payment_days" className="text-sm font-medium">أيام الدفع المخصصة</Label>
                  <Input
                    id="custom_payment_days"
                    type="number"
                    min="0"
                    value={formData.custom_payment_days || ''}
                    onChange={(e) => {
                      const days = parseInt(e.target.value) || 0;
                      setFormData(prev => ({ ...prev, custom_payment_days: days }));
                      updateDueDate('custom', days);
                    }}
                    className="touch-target"
                    placeholder="عدد الأيام"
                  />
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="tax_percentage" className="text-sm font-medium">نسبة الضريبة (%)</Label>
                <Input
                  id="tax_percentage"
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={formData.tax_percentage}
                  onChange={(e) => setFormData(prev => ({ ...prev, tax_percentage: parseFloat(e.target.value) || 0 }))}
                  className="touch-target"
                />
              </div>
            </div>

            {/* Dates Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="invoice_date" className="text-sm font-medium">تاريخ الفاتورة</Label>
                <Input
                  id="invoice_date"
                  type="date"
                  value={formData.invoice_date}
                  onChange={(e) => {
                    setFormData(prev => ({ ...prev, invoice_date: e.target.value }));
                    updateDueDate(formData.payment_terms, formData.custom_payment_days);
                  }}
                  className="touch-target"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="due_date" className="text-sm font-medium">تاريخ الاستحقاق</Label>
                <Input
                  id="due_date"
                  type="date"
                  value={formData.due_date}
                  onChange={(e) => setFormData(prev => ({ ...prev, due_date: e.target.value }))}
                  className="touch-target"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Items Section */}
        <Card className="shadow-sm">
          <CardHeader>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
              <div>
                <CardTitle className="text-lg sm:text-xl">عناصر الفاتورة</CardTitle>
                <CardDescription>الخدمات والمنتجات المتضمنة في الفاتورة</CardDescription>
              </div>
              <Button
                type="button"
                onClick={addItem}
                className="flex items-center gap-2 touch-target w-fit"
                size="sm"
              >
                <Plus className="w-4 h-4" />
                إضافة عنصر
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {items.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <div className="mb-4">
                  <Plus className="w-12 h-12 mx-auto text-gray-300" />
                </div>
                <p className="text-sm sm:text-base">لا توجد عناصر. اضغط "إضافة عنصر" لبدء إنشاء الفاتورة.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {items.map((item, index) => (
                  <div key={index} className="border rounded-lg p-4 space-y-4 bg-gray-50/50">
                    <div className="flex justify-between items-center">
                      <h4 className="font-medium text-sm sm:text-base">عنصر {index + 1}</h4>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeItem(index)}
                        className="text-red-600 hover:text-red-700 touch-target"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>

                    {/* Item Fields Grid */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">نوع العنصر</Label>
                        <Select
                          value={item.item_type}
                          onValueChange={(value: any) => updateItem(index, 'item_type', value)}
                        >
                          <SelectTrigger className="touch-target">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="service">خدمة</SelectItem>
                            <SelectItem value="product">منتج</SelectItem>
                            <SelectItem value="discount">خصم</SelectItem>
                            <SelectItem value="shipping">شحن</SelectItem>
                            <SelectItem value="other">أخرى</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium">اسم العنصر</Label>
                        <Input
                          value={item.name}
                          onChange={(e) => updateItem(index, 'name', e.target.value)}
                          placeholder="اسم الخدمة أو المنتج"
                          className="touch-target"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium">الكمية</Label>
                        <Input
                          type="number"
                          min="1"
                          value={item.quantity}
                          onChange={(e) => updateItem(index, 'quantity', parseInt(e.target.value) || 1)}
                          className="touch-target"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium">سعر الوحدة (ج.م)</Label>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          value={item.unit_price}
                          onChange={(e) => updateItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                          className="touch-target"
                        />
                      </div>
                    </div>

                    {/* Description and Unit */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">الوصف</Label>
                        <Textarea
                          value={item.description}
                          onChange={(e) => updateItem(index, 'description', e.target.value)}
                          placeholder="وصف العنصر..."
                          rows={2}
                          className="touch-target resize-none"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium">الوحدة</Label>
                        <Input
                          value={item.unit}
                          onChange={(e) => updateItem(index, 'unit', e.target.value)}
                          placeholder="مثال: خدمة، قطعة، ساعة"
                          className="touch-target"
                        />
                      </div>
                    </div>

                    {/* Tax Settings */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <input
                          type="checkbox"
                          id={`taxable-${index}`}
                          checked={item.is_taxable}
                          onChange={(e) => updateItem(index, 'is_taxable', e.target.checked)}
                          className="rounded border-gray-300"
                        />
                        <Label htmlFor={`taxable-${index}`} className="text-sm font-medium">
                          خاضع للضريبة
                        </Label>
                      </div>

                      {item.is_taxable && (
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">نسبة الضريبة (%)</Label>
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            step="0.01"
                            value={item.tax_percentage}
                            onChange={(e) => updateItem(index, 'tax_percentage', parseFloat(e.target.value) || 0)}
                            className="touch-target"
                          />
                        </div>
                      )}
                    </div>

                    {/* Item Total */}
                    <div className="flex justify-between items-center pt-2 border-t border-gray-200">
                      <span className="text-sm text-gray-600">إجمالي العنصر:</span>
                      <span className="text-lg font-bold text-blue-600">
                        {formatCurrency(item.quantity * item.unit_price)} ج.م
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Totals Section */}
        {items.length > 0 && (
          <Card className="shadow-sm border-blue-200">
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl text-blue-700">ملخص الفاتورة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center text-sm sm:text-base">
                  <span className="text-gray-600">المجموع الفرعي:</span>
                  <span className="font-medium">{formatCurrency(totals.subtotal)} ج.م</span>
                </div>

                {formData.discount_percentage > 0 && (
                  <div className="flex justify-between items-center text-green-600 text-sm sm:text-base">
                    <span>الخصم ({formData.discount_percentage}%):</span>
                    <span className="font-medium">-{formatCurrency(totals.discountAmount)} ج.م</span>
                  </div>
                )}

                <div className="flex justify-between items-center text-sm sm:text-base">
                  <span className="text-gray-600">الضريبة:</span>
                  <span className="font-medium">{formatCurrency(totals.taxAmount)} ج.م</span>
                </div>

                <hr className="border-gray-300" />

                <div className="flex justify-between items-center text-lg sm:text-xl font-bold text-blue-700">
                  <span>الإجمالي النهائي:</span>
                  <span>{formatCurrency(totals.total)} ج.م</span>
                </div>
              </div>

              {/* Discount Input */}
              <div className="mt-6 space-y-2">
                <Label htmlFor="discount_percentage" className="text-sm font-medium">نسبة الخصم (%)</Label>
                <Input
                  id="discount_percentage"
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={formData.discount_percentage}
                  onChange={(e) => setFormData(prev => ({ ...prev, discount_percentage: parseFloat(e.target.value) || 0 }))}
                  className="touch-target max-w-xs"
                />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Additional Information Section */}
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg sm:text-xl">معلومات إضافية</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="notes" className="text-sm font-medium">ملاحظات</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="ملاحظات إضافية للفاتورة..."
                rows={3}
                className="touch-target resize-none"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="terms_conditions" className="text-sm font-medium">الشروط والأحكام</Label>
              <Textarea
                id="terms_conditions"
                value={formData.terms_conditions}
                onChange={(e) => setFormData(prev => ({ ...prev, terms_conditions: e.target.value }))}
                placeholder="الشروط والأحكام الخاصة بالفاتورة..."
                rows={4}
                className="touch-target resize-none"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="payment_instructions" className="text-sm font-medium">تعليمات الدفع</Label>
              <Textarea
                id="payment_instructions"
                value={formData.payment_instructions}
                onChange={(e) => setFormData(prev => ({ ...prev, payment_instructions: e.target.value }))}
                placeholder="تعليمات الدفع وطرق التحويل..."
                rows={3}
                className="touch-target resize-none"
              />
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            className="touch-target"
          >
            إلغاء
          </Button>
          <Button
            type="submit"
            disabled={loading || items.length === 0}
            className="bg-purple-600 hover:bg-purple-700 touch-target"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                جاري الإنشاء...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                إنشاء الفاتورة
              </>
            )}
          </Button>
        </div>
      </form>
    </UnifiedLayout>
  );
};

export default NewInvoicePage;
