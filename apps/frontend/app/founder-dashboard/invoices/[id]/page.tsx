'use client';

import {
    <PERSON><PERSON><PERSON>riangle,
    ArrowRight,
    Calendar,
    CheckCircle,
    Clock,
    Copy,
    CreditCard,
    DollarSign,
    Download,
    Edit,
    FileText,
    Send,
    User,
    XCircle
} from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { UnifiedLayout } from '@/components/layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { invoicesAPI } from '@/lib/api';
import { showToast } from '@/lib/toast';
import { formatCurrency, formatDate, formatDateTime } from '@/lib/utils';
import { Invoice, INVOICE_PRIORITY_OPTIONS, INVOICE_STATUS_OPTIONS } from '@/types/invoices';

const InvoiceDetailPage = () => {
  const params = useParams();
  const router = useRouter();
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (params.id) {
      fetchInvoice();
    }
  }, [params.id]);

  const fetchInvoice = async () => {
    try {
      setLoading(true);
      const data = await invoicesAPI.getInvoice(params.id as string);
      setInvoice(data);
    } catch (error) {
      console.error('Error fetching invoice:', error);
      showToast.error('حدث خطأ في تحميل الفاتورة');
      router.push('/founder-dashboard/invoices');
    } finally {
      setLoading(false);
    }
  };

  const handleSendInvoice = async () => {
    if (!invoice) return;

    try {
      await invoicesAPI.sendInvoice(invoice.id);
      showToast.success('تم إرسال الفاتورة بنجاح');
      fetchInvoice();
    } catch (error) {
      console.error('Error sending invoice:', error);
      showToast.error('حدث خطأ في إرسال الفاتورة');
    }
  };

  const handleMarkViewed = async () => {
    if (!invoice) return;

    try {
      await invoicesAPI.markViewed(invoice.id);
      showToast.success('تم تسجيل عرض الفاتورة');
      fetchInvoice();
    } catch (error) {
      console.error('Error marking as viewed:', error);
      showToast.error('حدث خطأ في تسجيل العرض');
    }
  };

  const handleDownloadPDF = async () => {
    if (!invoice) return;

    try {
      const pdfBlob = await invoicesAPI.generatePDF(invoice.id);
      const url = window.URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `invoice-${invoice.invoice_number}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      showToast.success('تم تحميل الفاتورة بصيغة PDF');
    } catch (error) {
      console.error('Error downloading PDF:', error);
      showToast.error('فشل في تحميل الفاتورة');
    }
  };

  const handleDuplicateInvoice = async () => {
    if (!invoice) return;

    try {
      const duplicatedInvoice = await invoicesAPI.duplicateInvoice(invoice.id);
      showToast.success('تم نسخ الفاتورة بنجاح');
      router.push(`/founder-dashboard/invoices/${duplicatedInvoice.id}`);
    } catch (error) {
      console.error('Error duplicating invoice:', error);
      showToast.error('فشل في نسخ الفاتورة');
    }
  };

  const handleCancelInvoice = async () => {
    if (!invoice) return;

    if (confirm('هل أنت متأكد من إلغاء هذه الفاتورة؟')) {
      try {
        await invoicesAPI.cancelInvoice(invoice.id);
        showToast.success('تم إلغاء الفاتورة بنجاح');
        fetchInvoice();
      } catch (error) {
        console.error('Error cancelling invoice:', error);
        showToast.error('فشل في إلغاء الفاتورة');
      }
    }
  };

  const getStatusBadge = (status: string) => {
    const statusOption = INVOICE_STATUS_OPTIONS.find(option => option.value === status);
    if (!statusOption) return <Badge variant="outline">{status}</Badge>;

    const colorClasses = {
      gray: 'bg-gray-100 text-gray-800',
      yellow: 'bg-yellow-100 text-yellow-800',
      blue: 'bg-blue-100 text-blue-800',
      purple: 'bg-purple-100 text-purple-800',
      green: 'bg-green-100 text-green-800',
      orange: 'bg-orange-100 text-orange-800',
      red: 'bg-red-100 text-red-800',
    };

    return (
      <Badge className={colorClasses[statusOption.color as keyof typeof colorClasses]}>
        {statusOption.label}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityOption = INVOICE_PRIORITY_OPTIONS.find(option => option.value === priority);
    if (!priorityOption) return <Badge variant="outline">{priority}</Badge>;

    const colorClasses = {
      gray: 'bg-gray-100 text-gray-800',
      blue: 'bg-blue-100 text-blue-800',
      orange: 'bg-orange-100 text-orange-800',
      red: 'bg-red-100 text-red-800',
    };

    return (
      <Badge variant="outline" className={colorClasses[priorityOption.color as keyof typeof colorClasses]}>
        {priorityOption.label}
      </Badge>
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'overdue':
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      case 'cancelled':
        return <XCircle className="h-5 w-5 text-gray-600" />;
      case 'sent':
      case 'viewed':
        return <Clock className="h-5 w-5 text-blue-600" />;
      default:
        return <FileText className="h-5 w-5 text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <UnifiedLayout>
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">جاري التحميل...</p>
        </div>
      </UnifiedLayout>
    );
  }

  if (!invoice) {
    return (
      <UnifiedLayout>
        <div className="text-center py-8">
          <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">الفاتورة غير موجودة</h3>
          <p className="text-gray-600 mb-4">لم يتم العثور على الفاتورة المطلوبة</p>
          <Button onClick={() => router.push('/founder-dashboard/invoices')} className="touch-target">
            العودة إلى الفواتير
          </Button>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      {/* Page Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center gap-2 w-fit touch-target"
          >
            <ArrowRight className="w-4 h-4" />
            رجوع
          </Button>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3">
              {getStatusIcon(invoice.status)}
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mobile-truncate">{invoice.title}</h1>
            </div>
            <p className="text-gray-600 mt-1 text-sm sm:text-base">رقم الفاتورة: {invoice.invoice_number}</p>
          </div>
        </div>

        <div className="flex items-center gap-2 flex-wrap">
          {getStatusBadge(invoice.status)}
          {getPriorityBadge(invoice.priority)}
          {invoice.is_overdue && (
            <Badge className="bg-red-100 text-red-800">
              متأخر {invoice.days_overdue} يوم
            </Badge>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2 flex-wrap mb-6">
        {invoice.status === 'draft' && (
          <>
            <Button onClick={handleSendInvoice} className="flex items-center gap-2 touch-target">
              <Send className="w-4 h-4" />
              <span className="hidden sm:inline">إرسال الفاتورة</span>
              <span className="sm:hidden">إرسال</span>
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push(`/founder-dashboard/invoices/${invoice.id}/edit`)}
              className="flex items-center gap-2 touch-target"
            >
              <Edit className="w-4 h-4" />
              <span className="hidden sm:inline">تعديل</span>
              <span className="sm:hidden">تعديل</span>
            </Button>
          </>
        )}

        {invoice.status === 'sent' && (
          <Button onClick={handleMarkViewed} className="flex items-center gap-2 touch-target">
            <CheckCircle className="w-4 h-4" />
            <span className="hidden sm:inline">تسجيل كمعروضة</span>
            <span className="sm:hidden">معروضة</span>
          </Button>
        )}

        <Button variant="outline" onClick={handleDownloadPDF} className="flex items-center gap-2 touch-target">
          <Download className="w-4 h-4" />
          <span className="hidden sm:inline">تحميل PDF</span>
          <span className="sm:hidden">PDF</span>
        </Button>

        <Button variant="outline" onClick={handleDuplicateInvoice} className="flex items-center gap-2 touch-target">
          <Copy className="w-4 h-4" />
          <span className="hidden sm:inline">نسخ الفاتورة</span>
          <span className="sm:hidden">نسخ</span>
        </Button>

        {!['paid', 'cancelled'].includes(invoice.status) && (
          <Button
            variant="outline"
            onClick={handleCancelInvoice}
            className="flex items-center gap-2 touch-target text-red-600 hover:text-red-700"
          >
            <XCircle className="w-4 h-4" />
            <span className="hidden sm:inline">إلغاء الفاتورة</span>
            <span className="sm:hidden">إلغاء</span>
          </Button>
        )}

        <Button
          variant="outline"
          onClick={() => router.push(`/founder-dashboard/invoices/${invoice.id}/payments`)}
          className="flex items-center gap-2 touch-target"
        >
          <CreditCard className="w-4 h-4" />
          <span className="hidden sm:inline">إدارة المدفوعات</span>
          <span className="sm:hidden">مدفوعات</span>
        </Button>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Client Information */}
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl flex items-center gap-2">
                <User className="h-5 w-5" />
                معلومات العميل
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                  <span className="font-medium text-gray-600">الاسم:</span>
                  <span className="font-medium mobile-truncate">{invoice.client.name}</span>
                </div>
                <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                  <span className="font-medium text-gray-600">البريد الإلكتروني:</span>
                  <span className="mobile-truncate">{invoice.client.email}</span>
                </div>
                <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                  <span className="font-medium text-gray-600">الهاتف:</span>
                  <span className="mobile-truncate">{invoice.client.phone}</span>
                </div>
                {invoice.client.company && (
                  <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                    <span className="font-medium text-gray-600">الشركة:</span>
                    <span className="mobile-truncate">{invoice.client.company}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Project Information */}
          {invoice.project && (
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">معلومات المشروع</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                    <span className="font-medium text-gray-600">اسم المشروع:</span>
                    <span className="font-medium mobile-truncate">{invoice.project.name}</span>
                  </div>
                  <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                    <span className="font-medium text-gray-600">نوع المشروع:</span>
                    <span className="mobile-truncate">{invoice.project.type}</span>
                  </div>
                  <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                    <span className="font-medium text-gray-600">حالة المشروع:</span>
                    <Badge variant="outline">{invoice.project.status}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Quotation Information */}
          {invoice.quotation && (
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">معلومات العرض المرجعي</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                    <span className="font-medium text-gray-600">رقم العرض:</span>
                    <span className="font-medium mobile-truncate">{invoice.quotation.quotation_number}</span>
                  </div>
                  <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                    <span className="font-medium text-gray-600">عنوان العرض:</span>
                    <span className="mobile-truncate">{invoice.quotation.title}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Items Section */}
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">عناصر الفاتورة</CardTitle>
              <CardDescription>{invoice.items.length} عنصر</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {invoice.items.map((item, index) => (
                  <div key={item.id} className="border rounded-lg p-4 bg-gray-50/50">
                    <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-3 mb-3">
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm sm:text-base mobile-truncate">{item.name}</h4>
                        <p className="text-xs sm:text-sm text-gray-600 mt-1">{item.description}</p>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant="outline" className="text-xs">
                            {item.item_type_display}
                          </Badge>
                          {item.is_taxable && (
                            <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
                              خاضع للضريبة {item.tax_percentage}%
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="text-left lg:text-right">
                        <div className="font-bold text-purple-600 text-sm sm:text-base">
                          {formatCurrency(item.total_price)} ج.م
                        </div>
                        <div className="text-xs sm:text-sm text-gray-600">
                          {item.quantity} {item.unit} × {formatCurrency(item.unit_price)} ج.م
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Payment History */}
          {invoice.payments && invoice.payments.length > 0 && (
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  سجل المدفوعات
                </CardTitle>
                <CardDescription>{invoice.payments.length} دفعة</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {invoice.payments.map((payment) => (
                    <div key={payment.id} className="border rounded-lg p-4 bg-gray-50/50">
                      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge className={
                              payment.status === 'completed' ? 'bg-green-100 text-green-800' :
                              payment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              payment.status === 'failed' ? 'bg-red-100 text-red-800' :
                              'bg-gray-100 text-gray-800'
                            }>
                              {payment.status_display}
                            </Badge>
                            <Badge variant="outline">
                              {payment.payment_method_display}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600">
                            تاريخ الدفع: {formatDate(payment.payment_date)}
                          </p>
                          {payment.transaction_id && (
                            <p className="text-xs text-gray-500">
                              رقم المعاملة: {payment.transaction_id}
                            </p>
                          )}
                          {payment.notes && (
                            <p className="text-xs text-gray-600 mt-1">
                              {payment.notes}
                            </p>
                          )}
                        </div>
                        <div className="text-left sm:text-right">
                          <div className="font-bold text-green-600 text-lg">
                            {formatCurrency(payment.amount)} ج.م
                          </div>
                          {payment.processed_by && (
                            <p className="text-xs text-gray-500">
                              بواسطة: {payment.processed_by.first_name} {payment.processed_by.last_name}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Description and Notes */}
          {(invoice.description || invoice.notes) && (
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">تفاصيل إضافية</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {invoice.description && (
                  <div>
                    <h4 className="font-medium mb-2 text-sm sm:text-base">وصف الفاتورة</h4>
                    <p className="text-gray-700 text-sm sm:text-base leading-relaxed">{invoice.description}</p>
                  </div>
                )}

                {invoice.notes && (
                  <div>
                    <h4 className="font-medium mb-2 text-sm sm:text-base">ملاحظات</h4>
                    <p className="text-gray-700 text-sm sm:text-base leading-relaxed">{invoice.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Terms and Conditions */}
          {(invoice.terms_conditions || invoice.payment_instructions) && (
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">الشروط والتعليمات</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {invoice.terms_conditions && (
                  <div>
                    <h4 className="font-medium mb-2 text-sm sm:text-base">الشروط والأحكام</h4>
                    <p className="text-gray-700 whitespace-pre-line text-sm sm:text-base leading-relaxed">
                      {invoice.terms_conditions}
                    </p>
                  </div>
                )}

                {invoice.payment_instructions && (
                  <div>
                    <h4 className="font-medium mb-2 text-sm sm:text-base">تعليمات الدفع</h4>
                    <p className="text-gray-700 whitespace-pre-line text-sm sm:text-base leading-relaxed">
                      {invoice.payment_instructions}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Financial Summary */}
          <Card className="shadow-sm border-purple-200">
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl text-purple-700 flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                ملخص الفاتورة
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center text-sm sm:text-base">
                <span className="text-gray-600">المجموع الفرعي:</span>
                <span className="font-medium">{formatCurrency(invoice.subtotal)} ج.م</span>
              </div>

              {invoice.discount_percentage > 0 && (
                <div className="flex justify-between items-center text-green-600 text-sm sm:text-base">
                  <span>الخصم ({invoice.discount_percentage}%):</span>
                  <span className="font-medium">-{formatCurrency(invoice.discount_amount)} ج.م</span>
                </div>
              )}

              <div className="flex justify-between items-center text-sm sm:text-base">
                <span className="text-gray-600">الضريبة ({invoice.tax_percentage}%):</span>
                <span className="font-medium">{formatCurrency(invoice.tax_amount)} ج.م</span>
              </div>

              <Separator className="border-gray-300" />

              <div className="flex justify-between items-center text-lg sm:text-xl font-bold text-purple-700">
                <span>الإجمالي النهائي:</span>
                <span>{formatCurrency(invoice.total_amount)} ج.م</span>
              </div>

              {/* Payment Progress */}
              {invoice.paid_amount > 0 && (
                <>
                  <Separator className="border-gray-300" />
                  <div className="space-y-2">
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-600">المبلغ المدفوع:</span>
                      <span className="font-medium text-green-600">{formatCurrency(invoice.paid_amount)} ج.م</span>
                    </div>
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-600">المبلغ المتبقي:</span>
                      <span className="font-medium text-orange-600">{formatCurrency(invoice.remaining_amount)} ج.م</span>
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs text-gray-600">
                        <span>نسبة الدفع</span>
                        <span>{invoice.payment_percentage.toFixed(1)}%</span>
                      </div>
                      <Progress value={invoice.payment_percentage} className="h-2" />
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Paymob Payment Integration */}
          {!['paid', 'cancelled'].includes(invoice.status) && invoice.remaining_amount > 0 && (
            <Card className="shadow-sm border-green-200">
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl text-green-700 flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  الدفع الإلكتروني
                </CardTitle>
                <CardDescription>
                  ادفع فاتورتك بأمان عبر بوابة Paymob المصرية
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <DollarSign className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium text-green-800">المبلغ المطلوب دفعه</span>
                    </div>
                    <div className="text-2xl font-bold text-green-700">
                      {formatCurrency(invoice.remaining_amount)} ج.م
                    </div>
                  </div>

                  <PaymobPayment
                    invoiceId={invoice.id}
                    amount={invoice.remaining_amount}
                    currency="EGP"
                    invoiceNumber={invoice.invoice_number}
                    clientName={invoice.client.name}
                    onPaymentSuccess={(data) => {
                      showToast.success('تم الدفع بنجاح! سيتم تحديث حالة الفاتورة قريباً');
                      // Refresh invoice data after successful payment
                      setTimeout(() => {
                        fetchInvoice();
                      }, 2000);
                    }}
                    onPaymentError={(error) => {
                      console.error('Payment error:', error);
                      showToast.error('فشل في عملية الدفع. يرجى المحاولة مرة أخرى');
                    }}
                    onPaymentCancel={() => {
                      showToast.info('تم إلغاء عملية الدفع');
                    }}
                    className="w-full"
                  />

                  <div className="text-xs text-gray-500 text-center">
                    <div className="flex items-center justify-center gap-1 mb-1">
                      <span>🔒</span>
                      <span>دفع آمن ومشفر</span>
                    </div>
                    <div>يدعم البطاقات الائتمانية، فودافون كاش، وفوري</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Invoice Details */}
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                تفاصيل الفاتورة
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                <span className="text-gray-600 text-sm sm:text-base">الحالة:</span>
                <div>{getStatusBadge(invoice.status)}</div>
              </div>

              <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                <span className="text-gray-600 text-sm sm:text-base">الأولوية:</span>
                <div>{getPriorityBadge(invoice.priority)}</div>
              </div>

              <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                <span className="text-gray-600 text-sm sm:text-base">شروط الدفع:</span>
                <span className="font-medium text-sm sm:text-base">{invoice.payment_terms_display}</span>
              </div>

              <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                <span className="text-gray-600 text-sm sm:text-base">تاريخ الإنشاء:</span>
                <span className="font-medium text-sm sm:text-base">{formatDate(invoice.invoice_date)}</span>
              </div>

              {invoice.due_date && (
                <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                  <span className="text-gray-600 text-sm sm:text-base">تاريخ الاستحقاق:</span>
                  <span className={`font-medium text-sm sm:text-base ${
                    invoice.is_overdue ? 'text-red-600' : 'text-gray-900'
                  }`}>
                    {formatDate(invoice.due_date)}
                    {invoice.is_overdue && ` (متأخر ${invoice.days_overdue} يوم)`}
                  </span>
                </div>
              )}

              {invoice.sent_date && (
                <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                  <span className="text-gray-600 text-sm sm:text-base">تاريخ الإرسال:</span>
                  <span className="font-medium text-sm sm:text-base">{formatDateTime(invoice.sent_date)}</span>
                </div>
              )}

              {invoice.viewed_date && (
                <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                  <span className="text-gray-600 text-sm sm:text-base">تاريخ العرض:</span>
                  <span className="font-medium text-sm sm:text-base">{formatDateTime(invoice.viewed_date)}</span>
                </div>
              )}

              {invoice.paid_date && (
                <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                  <span className="text-gray-600 text-sm sm:text-base">تاريخ الدفع:</span>
                  <span className="font-medium text-sm sm:text-base">{formatDateTime(invoice.paid_date)}</span>
                </div>
              )}

              {invoice.sales_rep && (
                <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                  <span className="text-gray-600 text-sm sm:text-base">مندوب المبيعات:</span>
                  <span className="font-medium text-sm sm:text-base mobile-truncate">
                    {invoice.sales_rep.first_name} {invoice.sales_rep.last_name}
                  </span>
                </div>
              )}

              <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                <span className="text-gray-600 text-sm sm:text-base">عدد العناصر:</span>
                <span className="font-medium text-sm sm:text-base">{invoice.items_count}</span>
              </div>

              <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                <span className="text-gray-600 text-sm sm:text-base">عدد المدفوعات:</span>
                <span className="font-medium text-sm sm:text-base">{invoice.payments_count}</span>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">إجراءات سريعة</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => router.push(`/founder-dashboard/invoices/new?from_invoice=${invoice.id}`)}
              >
                <Copy className="w-4 h-4 mr-2" />
                إنشاء فاتورة مشابهة
              </Button>

              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => router.push(`/founder-dashboard/clients/${invoice.client.id}`)}
              >
                <User className="w-4 h-4 mr-2" />
                عرض ملف العميل
              </Button>

              {invoice.project && (
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => router.push(`/founder-dashboard/projects/${invoice.project.id}`)}
                >
                  <FileText className="w-4 h-4 mr-2" />
                  عرض المشروع
                </Button>
              )}

              {invoice.quotation && (
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => router.push(`/founder-dashboard/quotations/${invoice.quotation.id}`)}
                >
                  <FileText className="w-4 h-4 mr-2" />
                  عرض العرض المرجعي
                </Button>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </UnifiedLayout>
  );
};

export default InvoiceDetailPage;
