'use client';

import { UnifiedLayout } from '@/components/layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { invoicesAPI } from '@/lib/api';
import { useAuthStore } from '@/lib/stores/auth-store';
import { showToast } from '@/lib/toast';
import { formatCurrency, formatDate } from '@/lib/utils';
import { Invoice, INVOICE_PRIORITY_OPTIONS, INVOICE_STATUS_OPTIONS, InvoicePriority, InvoiceStatus } from '@/types/invoices';
import {
    AlertTriangle,
    Calendar,
    CheckCircle,
    Clock,
    Copy,
    DollarSign,
    Download,
    Edit,
    Eye,
    FileText,
    MoreHorizontal,
    Plus,
    Search,
    Send,
    User,
    XCircle
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function InvoicesPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [loading, setLoading] = useState(true);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);

  // Load invoices from API
  const loadInvoices = async () => {
    if (!isAuthenticated) return;

    try {
      setLoading(true);
      const response = await invoicesAPI.getInvoices({
        page_size: 50,
        ordering: '-created_at'
      });
      setInvoices(response.results || []);
    } catch (error) {
      console.error('Error loading invoices:', error);
      showToast.error('فشل في تحميل الفواتير');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  useEffect(() => {
    if (mounted && isAuthenticated) {
      loadInvoices();
    }
  }, [mounted, isAuthenticated]);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل إدارة الفواتير...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى إدارة الفواتير</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  // Filter invoices based on search and filters
  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = 
      invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.client.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || invoice.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  // Handle invoice actions
  const handleCreateInvoice = () => {
    router.push('/founder-dashboard/invoices/new');
  };

  const handleViewInvoice = (invoice: Invoice) => {
    router.push(`/founder-dashboard/invoices/${invoice.id}`);
  };

  const handleEditInvoice = (invoice: Invoice) => {
    router.push(`/founder-dashboard/invoices/${invoice.id}/edit`);
  };

  const handleSendInvoice = async (invoice: Invoice) => {
    try {
      await invoicesAPI.sendInvoice(invoice.id);
      showToast.success('تم إرسال الفاتورة بنجاح');
      loadInvoices(); // Refresh the list
    } catch (error) {
      console.error('Error sending invoice:', error);
      showToast.error('فشل في إرسال الفاتورة');
    }
  };

  const handleDownloadPDF = async (invoice: Invoice) => {
    try {
      const pdfBlob = await invoicesAPI.generatePDF(invoice.id);
      const url = window.URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `invoice-${invoice.invoice_number}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      showToast.success('تم تحميل الفاتورة بصيغة PDF');
    } catch (error) {
      console.error('Error downloading PDF:', error);
      showToast.error('فشل في تحميل الفاتورة');
    }
  };

  const handleDuplicateInvoice = async (invoice: Invoice) => {
    try {
      const duplicatedInvoice = await invoicesAPI.duplicateInvoice(invoice.id);
      showToast.success('تم نسخ الفاتورة بنجاح');
      router.push(`/founder-dashboard/invoices/${duplicatedInvoice.id}`);
    } catch (error) {
      console.error('Error duplicating invoice:', error);
      showToast.error('فشل في نسخ الفاتورة');
    }
  };

  const handleCancelInvoice = async (invoice: Invoice) => {
    if (confirm('هل أنت متأكد من إلغاء هذه الفاتورة؟')) {
      try {
        await invoicesAPI.cancelInvoice(invoice.id);
        showToast.success('تم إلغاء الفاتورة بنجاح');
        loadInvoices(); // Refresh the list
      } catch (error) {
        console.error('Error cancelling invoice:', error);
        showToast.error('فشل في إلغاء الفاتورة');
      }
    }
  };

  // Get status badge component
  const getStatusBadge = (status: InvoiceStatus) => {
    const statusOption = INVOICE_STATUS_OPTIONS.find(option => option.value === status);
    if (!statusOption) return <Badge variant="outline">{status}</Badge>;

    const colorClasses = {
      gray: 'bg-gray-100 text-gray-800',
      yellow: 'bg-yellow-100 text-yellow-800',
      blue: 'bg-blue-100 text-blue-800',
      purple: 'bg-purple-100 text-purple-800',
      green: 'bg-green-100 text-green-800',
      orange: 'bg-orange-100 text-orange-800',
      red: 'bg-red-100 text-red-800',
    };

    return (
      <Badge className={colorClasses[statusOption.color as keyof typeof colorClasses]}>
        {statusOption.label}
      </Badge>
    );
  };

  // Get priority badge component
  const getPriorityBadge = (priority: InvoicePriority) => {
    const priorityOption = INVOICE_PRIORITY_OPTIONS.find(option => option.value === priority);
    if (!priorityOption) return <Badge variant="outline">{priority}</Badge>;

    const colorClasses = {
      gray: 'bg-gray-100 text-gray-800',
      blue: 'bg-blue-100 text-blue-800',
      orange: 'bg-orange-100 text-orange-800',
      red: 'bg-red-100 text-red-800',
    };

    return (
      <Badge variant="outline" className={colorClasses[priorityOption.color as keyof typeof colorClasses]}>
        {priorityOption.label}
      </Badge>
    );
  };

  // Get status icon
  const getStatusIcon = (status: InvoiceStatus) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'overdue':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-gray-600" />;
      case 'sent':
      case 'viewed':
        return <Clock className="h-4 w-4 text-blue-600" />;
      default:
        return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <FileText className="h-8 w-8 text-purple-600" />
              إدارة الفواتير
            </h1>
            <p className="text-gray-600 mt-1">
              إدارة شاملة لجميع فواتير العملاء والمدفوعات
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Button onClick={handleCreateInvoice} className="bg-purple-600 hover:bg-purple-700">
              <Plus className="h-4 w-4 ml-2" />
              فاتورة جديدة
            </Button>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في الفواتير (رقم الفاتورة، العنوان، العميل)..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="حالة الفاتورة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الحالات</SelectItem>
                    {INVOICE_STATUS_OPTIONS.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="الأولوية" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الأولويات</SelectItem>
                    {INVOICE_PRIORITY_OPTIONS.map((priority) => (
                      <SelectItem key={priority.value} value={priority.value}>
                        {priority.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Loading State */}
        {loading && (
          <Card className="text-center py-12">
            <CardContent>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
              <p className="text-gray-600">جاري تحميل الفواتير...</p>
            </CardContent>
          </Card>
        )}

        {/* Invoices Grid */}
        {!loading && (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredInvoices.map((invoice) => (
              <Card
                key={invoice.id}
                className="hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => handleViewInvoice(invoice)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(invoice.status)}
                      <CardTitle className="text-lg">{invoice.invoice_number}</CardTitle>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          handleViewInvoice(invoice);
                        }}>
                          <Eye className="h-4 w-4 ml-2" />
                          عرض التفاصيل
                        </DropdownMenuItem>
                        {invoice.status === 'draft' && (
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            handleEditInvoice(invoice);
                          }}>
                            <Edit className="h-4 w-4 ml-2" />
                            تعديل
                          </DropdownMenuItem>
                        )}
                        {invoice.status === 'draft' && (
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            handleSendInvoice(invoice);
                          }}>
                            <Send className="h-4 w-4 ml-2" />
                            إرسال
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          handleDownloadPDF(invoice);
                        }}>
                          <Download className="h-4 w-4 ml-2" />
                          تحميل PDF
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          handleDuplicateInvoice(invoice);
                        }}>
                          <Copy className="h-4 w-4 ml-2" />
                          نسخ
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {!['paid', 'cancelled'].includes(invoice.status) && (
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleCancelInvoice(invoice);
                            }}
                          >
                            <XCircle className="h-4 w-4 ml-2" />
                            إلغاء
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <CardDescription className="text-sm font-medium">
                    {invoice.title}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {/* Status and Priority */}
                    <div className="flex items-center justify-between">
                      {getStatusBadge(invoice.status)}
                      {getPriorityBadge(invoice.priority)}
                    </div>

                    {/* Client Info */}
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <User className="h-4 w-4" />
                      <span>{invoice.client.name}</span>
                    </div>

                    {/* Amount */}
                    <div className="flex items-center gap-2 text-sm">
                      <DollarSign className="h-4 w-4 text-green-600" />
                      <span className="font-medium text-green-600">
                        {formatCurrency(invoice.total_amount)} ج.م
                      </span>
                    </div>

                    {/* Payment Progress */}
                    {invoice.paid_amount > 0 && (
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs text-gray-600">
                          <span>مدفوع: {formatCurrency(invoice.paid_amount)} ج.م</span>
                          <span>{invoice.payment_percentage.toFixed(1)}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-green-600 h-2 rounded-full"
                            style={{ width: `${Math.min(invoice.payment_percentage, 100)}%` }}
                          ></div>
                        </div>
                      </div>
                    )}

                    {/* Dates */}
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Calendar className="h-4 w-4" />
                      <span>تاريخ الإنشاء: {formatDate(invoice.invoice_date)}</span>
                    </div>

                    {invoice.due_date && (
                      <div className="flex items-center gap-2 text-sm">
                        <Calendar className="h-4 w-4" />
                        <span className={invoice.is_overdue ? 'text-red-600 font-medium' : 'text-gray-600'}>
                          تاريخ الاستحقاق: {formatDate(invoice.due_date)}
                          {invoice.is_overdue && ` (متأخر ${invoice.days_overdue} يوم)`}
                        </span>
                      </div>
                    )}

                    {/* Items Count */}
                    <div className="pt-2 border-t">
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-gray-600">عدد العناصر:</span>
                        <span className="font-medium">{invoice.items_count}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!loading && filteredInvoices.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد فواتير</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || statusFilter !== 'all' || priorityFilter !== 'all'
                  ? 'لا توجد فواتير تطابق معايير البحث المحددة'
                  : 'لم يتم إنشاء أي فواتير بعد'
                }
              </p>
              {(!searchTerm && statusFilter === 'all' && priorityFilter === 'all') && (
                <Button onClick={handleCreateInvoice} className="bg-purple-600 hover:bg-purple-700">
                  <Plus className="h-4 w-4 ml-2" />
                  إنشاء أول فاتورة
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </UnifiedLayout>
  );
}
