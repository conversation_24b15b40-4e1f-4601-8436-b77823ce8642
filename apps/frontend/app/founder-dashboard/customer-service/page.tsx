'use client';

import { UnifiedLayout } from '@/components/layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { customerServiceAPI } from '@/lib/api';
import { formatRelativeTime } from '@mtbrmg/shared';
import {
    AlertTriangle,
    BookOpen,
    CheckCircle,
    Clock,
    FileText,
    Filter,
    MessageSquare,
    Plus,
    RefreshCw,
    Search,
    Settings,
    TrendingUp,
    XCircle
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

// Types
interface Ticket {
  id: number;
  ticket_number: string;
  title: string;
  description: string;
  client: {
    id: number;
    name: string;
    email: string;
    company?: string;
    phone: string;
  };
  project?: {
    id: number;
    name: string;
    type: string;
    status: string;
  };
  assigned_to?: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
    full_name: string;
  };
  status: 'open' | 'in_progress' | 'pending_client' | 'resolved' | 'closed' | 'escalated';
  priority: 'critical' | 'high' | 'medium' | 'low';
  category: 'technical' | 'billing' | 'general' | 'project_related' | 'account' | 'feature_request' | 'bug_report';
  source: 'email' | 'phone' | 'whatsapp' | 'web_portal' | 'internal';
  created_at: string;
  is_overdue: boolean;
  responses_count: number;
  category_display: string;
  source_display: string;
}

interface CustomerServiceDashboard {
  stats: {
    total_tickets: number;
    open_tickets: number;
    in_progress_tickets: number;
    resolved_tickets: number;
    closed_tickets: number;
    overdue_tickets: number;
    resolution_rate: number;
  };
}

// Helper function for relative time formatting
const formatRelativeTime = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return 'منذ لحظات';
  if (diffInSeconds < 3600) return `منذ ${Math.floor(diffInSeconds / 60)} دقيقة`;
  if (diffInSeconds < 86400) return `منذ ${Math.floor(diffInSeconds / 3600)} ساعة`;
  if (diffInSeconds < 2592000) return `منذ ${Math.floor(diffInSeconds / 86400)} يوم`;
  return date.toLocaleDateString('ar-EG');
};

const CustomerServicePage = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<CustomerServiceDashboard | null>(null);
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [dashboardResponse, ticketsResponse] = await Promise.all([
        customerServiceAPI.getCustomerServiceDashboard(),
        customerServiceAPI.getTickets()
      ]);

      setDashboardData(dashboardResponse.data);
      // Handle different response structures (paginated vs direct)
      setTickets(ticketsResponse.results || ticketsResponse.data || ticketsResponse || []);
    } catch (error) {
      console.error('Error loading customer service data:', error);
      toast.error('فشل في تحميل بيانات خدمة العملاء');
      // Set empty array as fallback to prevent filter errors
      setTickets([]);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      open: { color: 'bg-blue-100 text-blue-800', icon: Clock, label: 'مفتوح' },
      in_progress: { color: 'bg-yellow-100 text-yellow-800', icon: RefreshCw, label: 'قيد المعالجة' },
      pending_client: { color: 'bg-orange-100 text-orange-800', icon: MessageSquare, label: 'في انتظار العميل' },
      resolved: { color: 'bg-green-100 text-green-800', icon: CheckCircle, label: 'تم الحل' },
      closed: { color: 'bg-gray-100 text-gray-800', icon: XCircle, label: 'مغلق' },
      escalated: { color: 'bg-red-100 text-red-800', icon: AlertTriangle, label: 'تم التصعيد' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.open;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      critical: { color: 'bg-red-100 text-red-800 border-red-200', label: 'حرج' },
      high: { color: 'bg-orange-100 text-orange-800 border-orange-200', label: 'عالي' },
      medium: { color: 'bg-yellow-100 text-yellow-800 border-yellow-200', label: 'متوسط' },
      low: { color: 'bg-green-100 text-green-800 border-green-200', label: 'منخفض' },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;

    return (
      <Badge variant="outline" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const filteredTickets = Array.isArray(tickets) ? tickets.filter(ticket => {
    const matchesSearch = ticket.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         ticket.ticket_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         ticket.client.name.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === 'all' || ticket.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || ticket.priority === priorityFilter;

    return matchesSearch && matchesStatus && matchesPriority;
  }) : [];

  if (loading) {
    return (
      <UnifiedLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-gray-600">جاري تحميل بيانات خدمة العملاء...</p>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header Section - Following VST and GRS patterns */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">خدمة العملاء</h1>
            <p className="text-gray-600 mt-1">إدارة تذاكر الدعم وخدمة العملاء</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            <Button 
              onClick={() => router.push('/founder-dashboard/customer-service/tickets/new')}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              تذكرة جديدة
            </Button>
            <Button 
              variant="outline"
              onClick={() => router.push('/founder-dashboard/customer-service/knowledge-base')}
            >
              <BookOpen className="h-4 w-4 mr-2" />
              قاعدة المعرفة
            </Button>
          </div>
        </div>

        {/* Stats Cards - CPR Implementation */}
        {dashboardData && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">إجمالي التذاكر</p>
                    <p className="text-2xl font-bold text-gray-900">{dashboardData.stats.total_tickets}</p>
                  </div>
                  <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <FileText className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">التذاكر المفتوحة</p>
                    <p className="text-2xl font-bold text-blue-600">{dashboardData.stats.open_tickets}</p>
                  </div>
                  <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Clock className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">التذاكر المتأخرة</p>
                    <p className="text-2xl font-bold text-red-600">{dashboardData.stats.overdue_tickets}</p>
                  </div>
                  <div className="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <AlertTriangle className="h-6 w-6 text-red-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">معدل الحل</p>
                    <p className="text-2xl font-bold text-green-600">{dashboardData.stats.resolution_rate.toFixed(1)}%</p>
                  </div>
                  <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Main Content Tabs - ULM Implementation */}
        <Tabs defaultValue="tickets" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="tickets">التذاكر</TabsTrigger>
            <TabsTrigger value="analytics">التحليلات</TabsTrigger>
            <TabsTrigger value="settings">الإعدادات</TabsTrigger>
          </TabsList>

          <TabsContent value="tickets" className="space-y-6">
            {/* Filters Section - GRS Implementation */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  البحث والتصفية
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="البحث في التذاكر..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="الحالة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع الحالات</SelectItem>
                      <SelectItem value="open">مفتوح</SelectItem>
                      <SelectItem value="in_progress">قيد المعالجة</SelectItem>
                      <SelectItem value="pending_client">في انتظار العميل</SelectItem>
                      <SelectItem value="resolved">تم الحل</SelectItem>
                      <SelectItem value="closed">مغلق</SelectItem>
                      <SelectItem value="escalated">تم التصعيد</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="الأولوية" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع الأولويات</SelectItem>
                      <SelectItem value="critical">حرج</SelectItem>
                      <SelectItem value="high">عالي</SelectItem>
                      <SelectItem value="medium">متوسط</SelectItem>
                      <SelectItem value="low">منخفض</SelectItem>
                    </SelectContent>
                  </Select>

                  <Button variant="outline" onClick={loadDashboardData}>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    تحديث
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Tickets List - SMA Implementation */}
            <div className="space-y-4">
              {filteredTickets.length === 0 ? (
                <Card>
                  <CardContent className="p-8 text-center">
                    <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد تذاكر</h3>
                    <p className="text-gray-600">لم يتم العثور على تذاكر تطابق معايير البحث</p>
                  </CardContent>
                </Card>
              ) : (
                filteredTickets.map((ticket) => (
                  <Card key={ticket.id} className="hover:shadow-md transition-shadow cursor-pointer">
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start mb-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900">
                              {ticket.title}
                            </h3>
                            {getStatusBadge(ticket.status)}
                            {getPriorityBadge(ticket.priority)}
                            {ticket.is_overdue && (
                              <Badge className="bg-red-100 text-red-800">
                                <AlertTriangle className="h-3 w-3 mr-1" />
                                متأخر
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mb-2">
                            رقم التذكرة: {ticket.ticket_number}
                          </p>
                          <p className="text-sm text-gray-600">
                            العميل: {ticket.client.name} - {ticket.client.company || 'فردي'}
                          </p>
                        </div>
                        <div className="text-left">
                          <p className="text-sm text-gray-500">
                            {formatRelativeTime(ticket.created_at)}
                          </p>
                          {ticket.assigned_to && (
                            <p className="text-sm text-gray-600 mt-1">
                              مكلف إلى: {ticket.assigned_to.full_name}
                            </p>
                          )}
                          <p className="text-sm text-gray-500 mt-1">
                            الردود: {ticket.responses_count}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <span>الفئة: {ticket.category_display}</span>
                          <span>•</span>
                          <span>المصدر: {ticket.source_display}</span>
                        </div>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => router.push(`/founder-dashboard/customer-service/tickets/${ticket.id}`)}
                        >
                          عرض التفاصيل
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="analytics">
            <Card>
              <CardHeader>
                <CardTitle>تحليلات خدمة العملاء</CardTitle>
                <CardDescription>إحصائيات وتحليلات شاملة لأداء خدمة العملاء</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">سيتم إضافة التحليلات والرسوم البيانية قريباً...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات خدمة العملاء</CardTitle>
                <CardDescription>إدارة قواعد SLA والقوالب والإعدادات العامة</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Button 
                    variant="outline"
                    onClick={() => router.push('/founder-dashboard/customer-service/sla-rules')}
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    إدارة قواعد SLA
                  </Button>
                  <Button 
                    variant="outline"
                    onClick={() => router.push('/founder-dashboard/customer-service/templates')}
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    إدارة القوالب
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </UnifiedLayout>
  );
};

export default CustomerServicePage;
