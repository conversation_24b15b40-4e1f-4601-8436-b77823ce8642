'use client';

import { UnifiedLayout } from '@/components/layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { clientsAPI, customerServiceAPI, projectsAPI } from '@/lib/api';
import { ArrowLeft, FileText, RefreshCw, Save, User } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

// Types
interface Client {
  id: number;
  name: string;
  email: string;
  company?: string;
  phone: string;
}

interface Project {
  id: number;
  name: string;
  type: string;
  status: string;
  client: {
    id: number;
    name: string;
  };
}

interface NewTicketForm {
  title: string;
  description: string;
  client_id: string;
  project_id: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  category: 'technical' | 'billing' | 'general' | 'project_related' | 'account' | 'feature_request' | 'bug_report';
  source: 'email' | 'phone' | 'whatsapp' | 'web_portal' | 'internal';
}

const NewTicketPage = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [clientProjects, setClientProjects] = useState<Project[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  
  const [form, setForm] = useState<NewTicketForm>({
    title: '',
    description: '',
    client_id: '',
    project_id: '',
    priority: 'medium',
    category: 'general',
    source: 'web_portal'
  });

  const [errors, setErrors] = useState<Partial<NewTicketForm>>({});

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    if (form.client_id && Array.isArray(projects)) {
      const selectedClientProjects = projects.filter(
        project => project.client && project.client.id === parseInt(form.client_id)
      );
      setClientProjects(selectedClientProjects);

      // Reset project selection if current project doesn't belong to selected client
      if (form.project_id && !selectedClientProjects.find(p => p.id === parseInt(form.project_id))) {
        setForm(prev => ({ ...prev, project_id: '' }));
      }
    } else {
      setClientProjects([]);
      setForm(prev => ({ ...prev, project_id: '' }));
    }
  }, [form.client_id, projects]);

  const loadInitialData = async () => {
    try {
      setLoadingData(true);
      const [clientsResponse, projectsResponse] = await Promise.all([
        clientsAPI.getClients(),
        projectsAPI.getProjects()
      ]);

      // Handle different response structures (paginated vs direct)
      setClients(clientsResponse.results || clientsResponse.data || clientsResponse || []);
      setProjects(projectsResponse.results || projectsResponse.data || projectsResponse || []);
    } catch (error) {
      console.error('Error loading initial data:', error);
      toast.error('فشل في تحميل البيانات الأولية');
      // Set empty arrays as fallback to prevent map errors
      setClients([]);
      setProjects([]);
    } finally {
      setLoadingData(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<NewTicketForm> = {};

    if (!form.title.trim()) {
      newErrors.title = 'عنوان التذكرة مطلوب';
    }

    if (!form.description.trim()) {
      newErrors.description = 'وصف التذكرة مطلوب';
    }

    if (!form.client_id) {
      newErrors.client_id = 'العميل مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('يرجى تصحيح الأخطاء في النموذج');
      return;
    }

    try {
      setLoading(true);
      
      const ticketData = {
        title: form.title,
        description: form.description,
        client_id: parseInt(form.client_id),
        project_id: form.project_id ? parseInt(form.project_id) : undefined,
        priority: form.priority,
        category: form.category,
        source: form.source
      };

      const response = await customerServiceAPI.createTicket(ticketData);
      
      toast.success('تم إنشاء التذكرة بنجاح');
      router.push(`/founder-dashboard/customer-service/tickets/${response.data.id}`);
    } catch (error) {
      console.error('Error creating ticket:', error);
      toast.error('فشل في إنشاء التذكرة');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof NewTicketForm, value: string) => {
    setForm(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  if (loadingData) {
    return (
      <UnifiedLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header Section */}
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/founder-dashboard/customer-service')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إنشاء تذكرة جديدة</h1>
            <p className="text-gray-600">إنشاء تذكرة دعم جديدة للعملاء</p>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                معلومات التذكرة الأساسية
              </CardTitle>
              <CardDescription>
                أدخل المعلومات الأساسية للتذكرة
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Title */}
              <div className="space-y-2">
                <Label htmlFor="title">عنوان التذكرة *</Label>
                <Input
                  id="title"
                  placeholder="أدخل عنوان التذكرة"
                  value={form.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  className={errors.title ? 'border-red-500' : ''}
                />
                {errors.title && (
                  <p className="text-sm text-red-600">{errors.title}</p>
                )}
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description">وصف التذكرة *</Label>
                <Textarea
                  id="description"
                  placeholder="أدخل وصف مفصل للمشكلة أو الطلب"
                  value={form.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={4}
                  className={errors.description ? 'border-red-500' : ''}
                />
                {errors.description && (
                  <p className="text-sm text-red-600">{errors.description}</p>
                )}
              </div>

              {/* Client and Project */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="client">العميل *</Label>
                  <Select 
                    value={form.client_id} 
                    onValueChange={(value) => handleInputChange('client_id', value)}
                  >
                    <SelectTrigger className={errors.client_id ? 'border-red-500' : ''}>
                      <SelectValue placeholder="اختر العميل" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.isArray(clients) && clients.map((client) => (
                        <SelectItem key={client.id} value={client.id.toString()}>
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4" />
                            <span>{client.name}</span>
                            {client.company && (
                              <span className="text-gray-500">- {client.company}</span>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.client_id && (
                    <p className="text-sm text-red-600">{errors.client_id}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="project">المشروع (اختياري)</Label>
                  <Select 
                    value={form.project_id} 
                    onValueChange={(value) => handleInputChange('project_id', value)}
                    disabled={!form.client_id}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر المشروع" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.isArray(clientProjects) && clientProjects.map((project) => (
                        <SelectItem key={project.id} value={project.id.toString()}>
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            <span>{project.name}</span>
                            <span className="text-gray-500">- {project.type}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>تصنيف التذكرة</CardTitle>
              <CardDescription>
                حدد أولوية وفئة ومصدر التذكرة
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Priority */}
                <div className="space-y-2">
                  <Label htmlFor="priority">الأولوية</Label>
                  <Select 
                    value={form.priority} 
                    onValueChange={(value: any) => handleInputChange('priority', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">منخفض</SelectItem>
                      <SelectItem value="medium">متوسط</SelectItem>
                      <SelectItem value="high">عالي</SelectItem>
                      <SelectItem value="critical">حرج</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Category */}
                <div className="space-y-2">
                  <Label htmlFor="category">الفئة</Label>
                  <Select 
                    value={form.category} 
                    onValueChange={(value: any) => handleInputChange('category', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="general">عام</SelectItem>
                      <SelectItem value="technical">تقني</SelectItem>
                      <SelectItem value="billing">الفواتير</SelectItem>
                      <SelectItem value="project_related">متعلق بالمشروع</SelectItem>
                      <SelectItem value="account">الحساب</SelectItem>
                      <SelectItem value="feature_request">طلب ميزة</SelectItem>
                      <SelectItem value="bug_report">تقرير خطأ</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Source */}
                <div className="space-y-2">
                  <Label htmlFor="source">المصدر</Label>
                  <Select 
                    value={form.source} 
                    onValueChange={(value: any) => handleInputChange('source', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="web_portal">البوابة الإلكترونية</SelectItem>
                      <SelectItem value="email">بريد إلكتروني</SelectItem>
                      <SelectItem value="phone">هاتف</SelectItem>
                      <SelectItem value="whatsapp">واتساب</SelectItem>
                      <SelectItem value="internal">داخلي</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/founder-dashboard/customer-service')}
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              إنشاء التذكرة
            </Button>
          </div>
        </form>
      </div>
    </UnifiedLayout>
  );
};

export default NewTicketPage;
