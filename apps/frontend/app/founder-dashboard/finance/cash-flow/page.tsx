'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Receipt,
  TrendingUp,
  TrendingDown,
  BarChart3,
  Calendar,

  AlertTriangle,
  CheckCircle,
  Loader2,
  RefreshCw,
  Download,
  Plus
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';
import { financeAPI } from '@/lib/api';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { showToast } from '@/lib/toast';
import { CurrencyIcon } from '@/components/ui/currency-icon';
import { useCurrency } from '@/lib/stores/currency-store';

export default function CashFlowAnalysisPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const { format: formatCurrencyWithStore } = useCurrency();
  const [mounted, setMounted] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('current_year');
  const [selectedType, setSelectedType] = useState('monthly');
  const [cashFlowData, setCashFlowData] = useState([]);
  const [cashFlowSummary, setCashFlowSummary] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && isAuthenticated) {
      fetchCashFlowData();
    }
  }, [mounted, isAuthenticated, selectedPeriod, selectedType]);

  const fetchCashFlowData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch cash flow projections
      const cashFlowResponse = await financeAPI.getCashFlowProjections({
        period: selectedPeriod,
        type: selectedType
      });
      setCashFlowData(cashFlowResponse.results || cashFlowResponse);

      // Fetch cash flow summary
      const summaryResponse = await financeAPI.getCashFlowSummary();
      setCashFlowSummary(summaryResponse);

    } catch (err) {
      console.error('Error fetching cash flow data:', err);
      setError('حدث خطأ في تحميل بيانات التدفق النقدي');
      showToast.error('فشل في تحميل بيانات التدفق النقدي');
    } finally {
      setLoading(false);
    }
  };

  const handleAutoUpdate = async () => {
    try {
      await financeAPI.autoUpdateCashFlow();
      showToast.success('تم تحديث التدفق النقدي تلقائياً');
      fetchCashFlowData();
    } catch (err) {
      console.error('Error auto-updating cash flow:', err);
      showToast.error('فشل في التحديث التلقائي');
    }
  };

  const handleAddProjection = () => {
    router.push('/founder-dashboard/finance/cash-flow/add');
  };

  const handleExportData = () => {
    // TODO: Implement export functionality
    showToast.info('سيتم إضافة وظيفة التصدير قريباً');
  };

  const formatCurrency = (amount) => {
    return formatCurrencyWithStore(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-EG');
  };

  const formatPeriod = (period) => {
    const date = new Date(period);
    const options = { year: 'numeric', month: 'long' };
    return date.toLocaleDateString('ar-EG', options);
  };

  const getVarianceColor = (variance) => {
    if (variance > 0) return 'text-green-600';
    if (variance < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getVarianceIcon = (variance) => {
    if (variance > 0) return <TrendingUp className="h-4 w-4 text-green-600" />;
    if (variance < 0) return <TrendingDown className="h-4 w-4 text-red-600" />;
    return <BarChart3 className="h-4 w-4 text-gray-600" />;
  };

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل تحليل التدفق النقدي...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى تحليل التدفق النقدي</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <UnifiedLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
            <p className="text-gray-600">جاري تحميل بيانات التدفق النقدي...</p>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  if (error) {
    return (
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-red-500 mb-4">
                <Receipt className="h-12 w-12 mx-auto mb-4" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل البيانات</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={fetchCashFlowData} className="bg-purple-600 hover:bg-purple-700">
                إعادة المحاولة
              </Button>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header - Responsive Design */}
        <div className="mb-4 sm:mb-6">
          {/* Title Section */}
          <div className="mb-4 sm:mb-6">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 flex items-center gap-2 sm:gap-3 mb-2">
              <Receipt className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600" />
              <span className="mobile-truncate">تحليل التدفق النقدي</span>
            </h1>
            <p className="text-sm sm:text-base text-gray-600">
              مراقبة وتحليل التدفقات النقدية الداخلة والخارجة
            </p>
          </div>

          {/* Actions Section - Responsive Layout */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-end gap-3 sm:gap-4">
            <Button
              onClick={handleAutoUpdate}
              variant="outline"
              className="w-full sm:w-auto min-h-[44px] touch-target"
            >
              <RefreshCw className="h-4 w-4 ml-2" />
              <span className="text-sm sm:text-base">تحديث تلقائي</span>
            </Button>
            <Button
              onClick={handleExportData}
              variant="outline"
              className="w-full sm:w-auto min-h-[44px] touch-target"
            >
              <Download className="h-4 w-4 ml-2" />
              <span className="text-sm sm:text-base">تصدير البيانات</span>
            </Button>
            <Button
              onClick={handleAddProjection}
              className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto min-h-[44px] touch-target"
            >
              <Plus className="h-4 w-4 ml-2" />
              <span className="text-sm sm:text-base">إضافة توقع</span>
            </Button>
          </div>
        </div>

        {/* Cash Flow Summary Cards - Enhanced Responsive */}
        {cashFlowSummary && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">التدفق النقدي الحالي</CardTitle>
                <CurrencyIcon className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getVarianceColor(cashFlowSummary.current_cash_flow)}`}>
                  {formatCurrency(cashFlowSummary.current_cash_flow)}
                </div>
                <p className="text-xs text-gray-600">صافي التدفق</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">التدفق المتوقع</CardTitle>
                <TrendingUp className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(cashFlowSummary.projected_inflow)}
                </div>
                <p className="text-xs text-gray-600">الشهر القادم</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">المصروفات المتوقعة</CardTitle>
                <TrendingDown className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {formatCurrency(cashFlowSummary.projected_outflow)}
                </div>
                <p className="text-xs text-gray-600">الشهر القادم</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">دقة التوقعات</CardTitle>
                <BarChart3 className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">
                  {(cashFlowSummary.forecast_accuracy || 0).toFixed(1)}%
                </div>
                <p className="text-xs text-gray-600">متوسط الدقة</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Period and Type Filters - Enhanced Responsive */}
        <Card className="mb-4 sm:mb-6">
          <CardContent className="p-4 sm:p-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الفترة الزمنية
                </label>
                <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                  <SelectTrigger className="min-h-[44px] touch-target">
                    <SelectValue placeholder="اختر الفترة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="current_year">السنة الحالية</SelectItem>
                    <SelectItem value="last_year">السنة الماضية</SelectItem>
                    <SelectItem value="last_6_months">آخر 6 أشهر</SelectItem>
                    <SelectItem value="last_3_months">آخر 3 أشهر</SelectItem>
                    <SelectItem value="current_month">الشهر الحالي</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  نوع التحليل
                </label>
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger className="min-h-[44px] touch-target">
                    <SelectValue placeholder="اختر النوع" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="monthly">شهري</SelectItem>
                    <SelectItem value="quarterly">ربع سنوي</SelectItem>
                    <SelectItem value="yearly">سنوي</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Cash Flow Chart Placeholder */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              مخطط التدفق النقدي
            </CardTitle>
            <CardDescription>
              تطور التدفقات النقدية الداخلة والخارجة خلال الفترة المحددة
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg">
              <div className="text-center">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">مخطط التدفق النقدي التفاعلي</p>
                <p className="text-sm text-gray-400">سيتم إضافة مخطط تفاعلي هنا</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Cash Flow Projections Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              توقعات التدفق النقدي
            </CardTitle>
            <CardDescription>
              مقارنة التوقعات مع الأرقام الفعلية والانحرافات
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Desktop Table View */}
            <div className="hidden lg:block overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-right py-3 px-4 font-medium">الفترة</th>
                    <th className="text-right py-3 px-4 font-medium">الإيرادات المتوقعة</th>
                    <th className="text-right py-3 px-4 font-medium">الإيرادات الفعلية</th>
                    <th className="text-right py-3 px-4 font-medium">المصروفات المتوقعة</th>
                    <th className="text-right py-3 px-4 font-medium">المصروفات الفعلية</th>
                    <th className="text-right py-3 px-4 font-medium">صافي التدفق</th>
                    <th className="text-right py-3 px-4 font-medium">الانحراف</th>
                  </tr>
                </thead>
                <tbody>
                  {cashFlowData.map((item, index) => (
                    <tr key={index} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        {formatPeriod(item.period_start)}
                      </td>
                      <td className="py-3 px-4 text-green-600">
                        {formatCurrency(item.projected_revenue)}
                      </td>
                      <td className="py-3 px-4 text-green-700 font-medium">
                        {formatCurrency(item.actual_revenue || 0)}
                      </td>
                      <td className="py-3 px-4 text-red-600">
                        {formatCurrency(item.projected_expenses)}
                      </td>
                      <td className="py-3 px-4 text-red-700 font-medium">
                        {formatCurrency(item.actual_expenses || 0)}
                      </td>
                      <td className={`py-3 px-4 font-bold ${getVarianceColor(item.net_cash_flow)}`}>
                        {formatCurrency(item.net_cash_flow)}
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-2">
                          {getVarianceIcon(item.variance)}
                          <span className={`font-medium ${getVarianceColor(item.variance)}`}>
                            {item.variance > 0 ? '+' : ''}{item.variance?.toFixed(1)}%
                          </span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile/Tablet Card View */}
            <div className="lg:hidden space-y-4">
              {cashFlowData.map((item, index) => (
                <Card key={index} className="border border-gray-200">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <h4 className="font-medium text-gray-900">
                        {formatPeriod(item.period_start)}
                      </h4>
                      <div className="flex items-center gap-2">
                        {getVarianceIcon(item.variance)}
                        <span className={`text-sm font-medium ${getVarianceColor(item.variance)}`}>
                          {item.variance > 0 ? '+' : ''}{item.variance?.toFixed(1)}%
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                      <div className="bg-green-50 p-3 rounded-lg">
                        <div className="text-xs text-green-700 mb-1">الإيرادات</div>
                        <div className="text-green-600 font-medium">
                          متوقع: {formatCurrency(item.projected_revenue)}
                        </div>
                        <div className="text-green-700 font-bold">
                          فعلي: {formatCurrency(item.actual_revenue || 0)}
                        </div>
                      </div>

                      <div className="bg-red-50 p-3 rounded-lg">
                        <div className="text-xs text-red-700 mb-1">المصروفات</div>
                        <div className="text-red-600 font-medium">
                          متوقع: {formatCurrency(item.projected_expenses)}
                        </div>
                        <div className="text-red-700 font-bold">
                          فعلي: {formatCurrency(item.actual_expenses || 0)}
                        </div>
                      </div>
                    </div>

                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-600">صافي التدفق:</span>
                        <span className={`font-bold ${getVarianceColor(item.net_cash_flow)}`}>
                          {formatCurrency(item.net_cash_flow)}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {cashFlowData.length === 0 && (
              <div className="text-center py-12">
                <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد بيانات تدفق نقدي</h3>
                <p className="text-gray-600 mb-4">لم يتم إنشاء أي توقعات للتدفق النقدي للفترة المحددة</p>
                <Button onClick={handleAddProjection} className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 ml-2" />
                  إضافة توقع جديد
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </UnifiedLayout>
  );
}
