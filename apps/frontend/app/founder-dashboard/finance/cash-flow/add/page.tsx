'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Receipt,
  ArrowLeft,
  Save,
  Loader2,
  DollarSign,
  Calendar,
  FileText,
  TrendingUp,
  TrendingDown,
  BarChart3
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';
import { financeAPI } from '@/lib/api';
import { showToast } from '@/lib/toast';

// Period types
const periodTypes = [
  { value: 'monthly', label: 'شهري' },
  { value: 'quarterly', label: 'ربع سنوي' },
  { value: 'yearly', label: 'سنوي' }
];

export default function AddCashFlowProjectionPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [loading, setLoading] = useState(false);
  
  // Form data
  const [formData, setFormData] = useState({
    title: '',
    period_type: 'monthly',
    period_start: '',
    period_end: '',
    projected_revenue: '',
    projected_expenses: '',
    notes: ''
  });

  // Calculated values
  const [projectedProfit, setProjectedProfit] = useState(0);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    // Calculate projected profit when revenue or expenses change
    const revenue = parseFloat(formData.projected_revenue) || 0;
    const expenses = parseFloat(formData.projected_expenses) || 0;
    setProjectedProfit(revenue - expenses);
  }, [formData.projected_revenue, formData.projected_expenses]);

  useEffect(() => {
    // Auto-calculate period end based on period type and start date
    if (formData.period_start && formData.period_type) {
      const startDate = new Date(formData.period_start);
      let endDate = new Date(startDate);

      switch (formData.period_type) {
        case 'monthly':
          endDate.setMonth(endDate.getMonth() + 1);
          endDate.setDate(endDate.getDate() - 1);
          break;
        case 'quarterly':
          endDate.setMonth(endDate.getMonth() + 3);
          endDate.setDate(endDate.getDate() - 1);
          break;
        case 'yearly':
          endDate.setFullYear(endDate.getFullYear() + 1);
          endDate.setDate(endDate.getDate() - 1);
          break;
      }

      setFormData(prev => ({
        ...prev,
        period_end: endDate.toISOString().split('T')[0]
      }));
    }
  }, [formData.period_start, formData.period_type]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      showToast.error('يرجى إدخال عنوان التوقع');
      return;
    }

    if (!formData.period_start) {
      showToast.error('يرجى اختيار تاريخ بداية الفترة');
      return;
    }

    if (!formData.period_end) {
      showToast.error('يرجى اختيار تاريخ نهاية الفترة');
      return;
    }

    if (!formData.projected_revenue || parseFloat(formData.projected_revenue) < 0) {
      showToast.error('يرجى إدخال مبلغ صحيح للإيرادات المتوقعة');
      return;
    }

    if (!formData.projected_expenses || parseFloat(formData.projected_expenses) < 0) {
      showToast.error('يرجى إدخال مبلغ صحيح للمصروفات المتوقعة');
      return;
    }

    // Validate dates
    const startDate = new Date(formData.period_start);
    const endDate = new Date(formData.period_end);
    
    if (endDate <= startDate) {
      showToast.error('تاريخ نهاية الفترة يجب أن يكون بعد تاريخ البداية');
      return;
    }

    try {
      setLoading(true);

      // Prepare data for API
      const submitData = {
        title: formData.title.trim(),
        period_type: formData.period_type,
        period_start: formData.period_start,
        period_end: formData.period_end,
        projected_revenue: parseFloat(formData.projected_revenue),
        projected_expenses: parseFloat(formData.projected_expenses),
        notes: formData.notes.trim() || null
      };

      await financeAPI.createCashFlowProjection(submitData);
      
      showToast.success('تم إضافة توقع التدفق النقدي بنجاح');
      router.push('/founder-dashboard/finance/cash-flow');
      
    } catch (error) {
      console.error('Error creating cash flow projection:', error);
      if (error.response?.data?.detail) {
        showToast.error(error.response.data.detail);
      } else if (error.response?.data?.non_field_errors) {
        showToast.error(error.response.data.non_field_errors[0]);
      } else {
        showToast.error('فشل في إضافة توقع التدفق النقدي');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/founder-dashboard/finance/cash-flow');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getProfitColor = (profit: number) => {
    if (profit > 0) return 'text-green-600';
    if (profit < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  if (!mounted || !isAuthenticated) {
    return null;
  }

  return (
    <UnifiedLayout>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            العودة
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Receipt className="h-8 w-8 text-blue-600" />
              إضافة توقع تدفق نقدي
            </h1>
            <p className="text-gray-600 mt-1">
              إضافة توقع جديد للتدفق النقدي للتخطيط المالي
            </p>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                المعلومات الأساسية
              </CardTitle>
              <CardDescription>
                أدخل المعلومات الأساسية لتوقع التدفق النقدي
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Title */}
                <div className="space-y-2">
                  <Label htmlFor="title">عنوان التوقع *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="مثال: توقع التدفق النقدي - يناير 2024"
                    required
                  />
                </div>

                {/* Period Type */}
                <div className="space-y-2">
                  <Label htmlFor="period_type">نوع الفترة *</Label>
                  <Select
                    value={formData.period_type}
                    onValueChange={(value) => handleInputChange('period_type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر نوع الفترة" />
                    </SelectTrigger>
                    <SelectContent>
                      {periodTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Period Start */}
                <div className="space-y-2">
                  <Label htmlFor="period_start">تاريخ بداية الفترة *</Label>
                  <Input
                    id="period_start"
                    type="date"
                    value={formData.period_start}
                    onChange={(e) => handleInputChange('period_start', e.target.value)}
                    required
                  />
                </div>

                {/* Period End */}
                <div className="space-y-2">
                  <Label htmlFor="period_end">تاريخ نهاية الفترة *</Label>
                  <Input
                    id="period_end"
                    type="date"
                    value={formData.period_end}
                    onChange={(e) => handleInputChange('period_end', e.target.value)}
                    required
                  />
                  <p className="text-sm text-gray-500">
                    يتم حساب تاريخ النهاية تلقائياً بناءً على نوع الفترة
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                التوقعات المالية
              </CardTitle>
              <CardDescription>
                أدخل التوقعات المالية للفترة المحددة
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Projected Revenue */}
                <div className="space-y-2">
                  <Label htmlFor="projected_revenue" className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    الإيرادات المتوقعة (ج.م) *
                  </Label>
                  <Input
                    id="projected_revenue"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.projected_revenue}
                    onChange={(e) => handleInputChange('projected_revenue', e.target.value)}
                    placeholder="0.00"
                    required
                  />
                </div>

                {/* Projected Expenses */}
                <div className="space-y-2">
                  <Label htmlFor="projected_expenses" className="flex items-center gap-2">
                    <TrendingDown className="h-4 w-4 text-red-600" />
                    المصروفات المتوقعة (ج.م) *
                  </Label>
                  <Input
                    id="projected_expenses"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.projected_expenses}
                    onChange={(e) => handleInputChange('projected_expenses', e.target.value)}
                    placeholder="0.00"
                    required
                  />
                </div>
              </div>

              {/* Projected Profit Display */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-blue-600" />
                    <span className="font-medium">الربح المتوقع:</span>
                  </div>
                  <div className={`text-xl font-bold ${getProfitColor(projectedProfit)}`}>
                    {formatCurrency(projectedProfit)}
                  </div>
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  يتم حساب الربح المتوقع تلقائياً (الإيرادات - المصروفات)
                </p>
              </div>

              {/* Notes */}
              <div className="space-y-2">
                <Label htmlFor="notes">ملاحظات</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="أدخل أي ملاحظات أو تفاصيل إضافية حول التوقع"
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex items-center justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={loading}
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin ml-2" />
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 ml-2" />
                  حفظ التوقع
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </UnifiedLayout>
  );
}
