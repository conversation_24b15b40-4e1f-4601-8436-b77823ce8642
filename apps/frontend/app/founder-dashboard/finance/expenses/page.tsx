'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  TrendingDown,
  Search,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Loader2,
  DollarSign,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  Filter,
  Users,
  Building
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';
import { financeAPI } from '@/lib/api';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { showToast } from '@/lib/toast';

export default function ExpenseTrackingPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [expenses, setExpenses] = useState([]);
  const [expenseSummary, setExpenseSummary] = useState(null);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && isAuthenticated) {
      fetchExpenseData();
    }
  }, [mounted, isAuthenticated, statusFilter, categoryFilter, departmentFilter]);

  const fetchExpenseData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Build filter parameters
      const params = {};
      if (statusFilter !== 'all') params.status = statusFilter;
      if (categoryFilter !== 'all') params.category = categoryFilter;
      if (departmentFilter !== 'all') params.department = departmentFilter;
      if (searchTerm) params.search = searchTerm;

      // Fetch expenses
      const expenseResponse = await financeAPI.getExpenses(params);
      setExpenses(expenseResponse.results || expenseResponse);

      // Fetch expense summary
      const summaryResponse = await financeAPI.getExpenseSummary();
      setExpenseSummary(summaryResponse);

      // Fetch categories
      const categoriesResponse = await financeAPI.getExpenseCategories();
      setCategories(categoriesResponse.results || categoriesResponse);

    } catch (err) {
      console.error('Error fetching expense data:', err);
      setError('حدث خطأ في تحميل بيانات المصروفات');
      showToast.error('فشل في تحميل بيانات المصروفات');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    fetchExpenseData();
  };

  const handleAddExpense = () => {
    router.push('/founder-dashboard/finance/expenses/add');
  };

  const handleEditExpense = (expense) => {
    router.push(`/founder-dashboard/finance/expenses/edit/${expense.id}`);
  };

  const handleDeleteExpense = async (expense) => {
    if (confirm('هل أنت متأكد من حذف هذا المصروف؟')) {
      try {
        await financeAPI.deleteExpense(expense.id);
        showToast.success('تم حذف المصروف بنجاح');
        fetchExpenseData();
      } catch (err) {
        console.error('Error deleting expense:', err);
        showToast.error('فشل في حذف المصروف');
      }
    }
  };

  const handleApproveExpense = async (expense) => {
    try {
      await financeAPI.approveExpense(expense.id);
      showToast.success('تم اعتماد المصروف بنجاح');
      fetchExpenseData();
    } catch (err) {
      console.error('Error approving expense:', err);
      showToast.error('فشل في اعتماد المصروف');
    }
  };

  const handleRejectExpense = async (expense) => {
    if (confirm('هل أنت متأكد من رفض هذا المصروف؟')) {
      try {
        await financeAPI.rejectExpense(expense.id);
        showToast.success('تم رفض المصروف');
        fetchExpenseData();
      } catch (err) {
        console.error('Error rejecting expense:', err);
        showToast.error('فشل في رفض المصروف');
      }
    }
  };

  const handleViewExpense = (expense) => {
    router.push(`/founder-dashboard/finance/expenses/view/${expense.id}`);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-EG');
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-orange-600 border-orange-600">معلق</Badge>;
      case 'approved':
        return <Badge variant="outline" className="text-blue-600 border-blue-600">معتمد</Badge>;
      case 'paid':
        return <Badge variant="outline" className="text-green-600 border-green-600">مدفوع</Badge>;
      case 'rejected':
        return <Badge variant="destructive">مرفوض</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getDepartmentDisplay = (department) => {
    const departments = {
      'sales': 'فريق المبيعات',
      'development': 'فريق المطورين',
      'design': 'فريق المصممين',
      'media_buying': 'فريق مشتري الإعلانات',
      'management': 'الإدارة',
      'general': 'عام'
    };
    return departments[department] || department;
  };

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل تتبع المصروفات...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى تتبع المصروفات</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <UnifiedLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
            <p className="text-gray-600">جاري تحميل بيانات المصروفات...</p>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  if (error) {
    return (
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-red-500 mb-4">
                <TrendingDown className="h-12 w-12 mx-auto mb-4" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل البيانات</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={fetchExpenseData} className="bg-purple-600 hover:bg-purple-700">
                إعادة المحاولة
              </Button>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header - Responsive Design */}
        <div className="mb-4 sm:mb-6">
          {/* Title Section */}
          <div className="mb-4 sm:mb-6">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 flex items-center gap-2 sm:gap-3 mb-2">
              <TrendingDown className="h-6 w-6 sm:h-8 sm:w-8 text-red-600" />
              <span className="mobile-truncate">تتبع المصروفات</span>
            </h1>
            <p className="text-sm sm:text-base text-gray-600">
              إدارة ومراقبة جميع مصروفات الشركة والأقسام
            </p>
          </div>

          {/* Actions Section - Responsive Layout */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-end gap-3 sm:gap-4">
            <Button
              onClick={handleAddExpense}
              className="bg-red-600 hover:bg-red-700 w-full sm:w-auto min-h-[44px] touch-target"
            >
              <Plus className="h-4 w-4 ml-2" />
              <span className="text-sm sm:text-base">إضافة مصروف جديد</span>
            </Button>
          </div>
        </div>

        {/* Expense Summary Cards - Enhanced Responsive */}
        {expenseSummary && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">إجمالي المصروفات</CardTitle>
                <DollarSign className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {formatCurrency(expenseSummary.total_expenses)}
                </div>
                <p className="text-xs text-gray-600">مدفوع</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">مصروفات معلقة</CardTitle>
                <Clock className="h-4 w-4 text-orange-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {formatCurrency(expenseSummary.pending_expenses)}
                </div>
                <p className="text-xs text-gray-600">في انتظار الاعتماد</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">مصروفات معتمدة</CardTitle>
                <CheckCircle className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {expenseSummary.approved_count}
                </div>
                <p className="text-xs text-gray-600">جاهزة للدفع</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">متوسط المصروف</CardTitle>
                <TrendingDown className="h-4 w-4 text-gray-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-600">
                  {formatCurrency(expenseSummary.average_expense || 0)}
                </div>
                <p className="text-xs text-gray-600">لكل مصروف</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Search and Filters - Enhanced Responsive */}
        <Card className="mb-4 sm:mb-6">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col gap-4">
              {/* Search Bar */}
              <div className="w-full">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في المصروفات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="pr-10 min-h-[44px] touch-target"
                  />
                </div>
              </div>

              {/* Filters - Responsive Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full min-h-[44px] touch-target">
                    <SelectValue placeholder="الحالة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الحالات</SelectItem>
                    <SelectItem value="pending">معلق</SelectItem>
                    <SelectItem value="approved">معتمد</SelectItem>
                    <SelectItem value="paid">مدفوع</SelectItem>
                    <SelectItem value="rejected">مرفوض</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-full min-h-[44px] touch-target">
                    <SelectValue placeholder="الفئة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الفئات</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                  <SelectTrigger className="w-full min-h-[44px] touch-target">
                    <SelectValue placeholder="القسم" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الأقسام</SelectItem>
                    <SelectItem value="sales">فريق المبيعات</SelectItem>
                    <SelectItem value="development">فريق المطورين</SelectItem>
                    <SelectItem value="design">فريق المصممين</SelectItem>
                    <SelectItem value="media_buying">فريق مشتري الإعلانات</SelectItem>
                    <SelectItem value="management">الإدارة</SelectItem>
                    <SelectItem value="general">عام</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  onClick={handleSearch}
                  variant="outline"
                  className="w-full min-h-[44px] touch-target"
                >
                  <Filter className="h-4 w-4 ml-2" />
                  <span className="sm:hidden">تطبيق الفلاتر</span>
                  <span className="hidden sm:inline">فلترة</span>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Expense List */}
        <div className="grid grid-cols-1 gap-4">
          {expenses.map((expense) => (
            <Card key={expense.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    {/* Title and Badges - Responsive */}
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 mb-3">
                      <h3 className="text-base sm:text-lg font-semibold mobile-truncate flex-1">
                        {expense.title}
                      </h3>
                      <div className="flex flex-wrap items-center gap-2">
                        {getStatusBadge(expense.status)}
                        <Badge variant="secondary" className="flex items-center gap-1 text-xs">
                          <Building className="h-3 w-3" />
                          <span className="mobile-truncate">{getDepartmentDisplay(expense.department)}</span>
                        </Badge>
                        {expense.team_member && (
                          <Badge variant="outline" className="flex items-center gap-1 text-xs">
                            <Users className="h-3 w-3" />
                            <span className="mobile-truncate">{expense.team_member_name}</span>
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Details Grid - Enhanced Responsive */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 text-sm text-gray-600">
                      <div className="bg-red-50 p-3 rounded-lg">
                        <span className="font-medium text-xs text-red-700">المبلغ:</span>
                        <div className="text-base sm:text-lg font-bold text-red-600">
                          {formatCurrency(expense.amount)}
                        </div>
                      </div>

                      <div>
                        <span className="font-medium">الفئة:</span>
                        <div className="mobile-truncate">{expense.category_name || 'غير محدد'}</div>
                      </div>

                      <div>
                        <span className="font-medium">تاريخ المصروف:</span>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <span className="mobile-truncate">{formatDate(expense.expense_date)}</span>
                        </div>
                      </div>

                      <div>
                        <span className="font-medium">المشروع:</span>
                        <div className="mobile-truncate">{expense.project_name || 'غير مرتبط'}</div>
                      </div>
                    </div>

                    {expense.description && (
                      <div className="mt-3 text-sm text-gray-600">
                        <span className="font-medium">الوصف:</span>
                        <p className="mt-1">{expense.description}</p>
                      </div>
                    )}
                  </div>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="min-h-[44px] min-w-[44px] touch-target">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewExpense(expense)}>
                        <Eye className="h-4 w-4 ml-2" />
                        عرض التفاصيل
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditExpense(expense)}>
                        <Edit className="h-4 w-4 ml-2" />
                        تعديل
                      </DropdownMenuItem>
                      {expense.status === 'pending' && (
                        <>
                          <DropdownMenuItem 
                            className="text-green-600"
                            onClick={() => handleApproveExpense(expense)}
                          >
                            <CheckCircle className="h-4 w-4 ml-2" />
                            اعتماد
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            className="text-red-600"
                            onClick={() => handleRejectExpense(expense)}
                          >
                            <AlertTriangle className="h-4 w-4 ml-2" />
                            رفض
                          </DropdownMenuItem>
                        </>
                      )}
                      <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => handleDeleteExpense(expense)}
                      >
                        <Trash2 className="h-4 w-4 ml-2" />
                        حذف
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {expenses.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <TrendingDown className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مصروفات</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || statusFilter !== 'all' || categoryFilter !== 'all' || departmentFilter !== 'all'
                  ? 'لا توجد مصروفات تطابق معايير البحث المحددة'
                  : 'لم يتم إضافة أي مصروفات بعد'
                }
              </p>
              {!searchTerm && statusFilter === 'all' && categoryFilter === 'all' && departmentFilter === 'all' && (
                <Button onClick={handleAddExpense} className="bg-red-600 hover:bg-red-700">
                  <Plus className="h-4 w-4 ml-2" />
                  إضافة أول مصروف
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </UnifiedLayout>
  );
}
