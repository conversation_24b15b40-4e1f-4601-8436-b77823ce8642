'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  TrendingUp,
  ArrowLeft,
  Save,
  Loader2,
  Calendar,
  User,
  Building,
  FolderOpen,
  AlertCircle
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';
import { financeAPI, clientsAPI, projectsAPI, usersAPI } from '@/lib/api';
import { showToast } from '@/lib/toast';
import { CurrencyIcon } from '@/components/ui/currency-icon';
import { useCurrency } from '@/lib/stores/currency-store';

interface RevenueFormData {
  title: string;
  description: string;
  type: string;
  status: string;
  amount: number;
  tax_amount: number;
  client_id: number | null;
  project_id: number | null;
  sales_rep_id: number | null;
  invoice_date: string;
  due_date: string;
  payment_date: string;
}

const initialFormData: RevenueFormData = {
  title: '',
  description: '',
  type: 'project_payment',
  status: 'pending',
  amount: 0,
  tax_amount: 0,
  client_id: null,
  project_id: null,
  sales_rep_id: null,
  invoice_date: '',
  due_date: '',
  payment_date: ''
};

const revenueTypes = [
  { value: 'project_payment', label: 'دفعة مشروع' },
  { value: 'subscription', label: 'اشتراك' },
  { value: 'maintenance', label: 'صيانة' },
  { value: 'consultation', label: 'استشارة' },
  { value: 'hosting', label: 'استضافة' },
  { value: 'domain', label: 'نطاق' },
  { value: 'other', label: 'أخرى' }
];

const revenueStatuses = [
  { value: 'pending', label: 'معلق' },
  { value: 'received', label: 'مستلم' },
  { value: 'overdue', label: 'متأخر' },
  { value: 'cancelled', label: 'ملغي' }
];

export default function AddRevenuePage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const { format: formatCurrencyWithStore } = useCurrency();
  const [mounted, setMounted] = useState(false);
  const [formData, setFormData] = useState<RevenueFormData>(initialFormData);
  const [clients, setClients] = useState([]);
  const [projects, setProjects] = useState([]);
  const [salesReps, setSalesReps] = useState([]);
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && isAuthenticated) {
      fetchInitialData();
    }
  }, [mounted, isAuthenticated]);

  const fetchInitialData = async () => {
    try {
      setDataLoading(true);
      
      // Fetch clients, projects, and sales reps in parallel
      const [clientsResponse, projectsResponse, usersResponse] = await Promise.all([
        clientsAPI.getClients({ page_size: 100 }),
        projectsAPI.getProjects({ page_size: 100 }),
        usersAPI.getUsers({ page_size: 100 })
      ]);

      setClients(clientsResponse.results || clientsResponse);
      setProjects(projectsResponse.results || projectsResponse);
      setSalesReps(usersResponse.results || usersResponse);

      // Set default dates
      const today = new Date().toISOString().split('T')[0];
      const nextMonth = new Date();
      nextMonth.setMonth(nextMonth.getMonth() + 1);
      const dueDate = nextMonth.toISOString().split('T')[0];

      setFormData(prev => ({
        ...prev,
        invoice_date: today,
        due_date: dueDate
      }));

    } catch (err) {
      console.error('Error fetching initial data:', err);
      showToast.error('فشل في تحميل البيانات الأولية');
    } finally {
      setDataLoading(false);
    }
  };

  const handleInputChange = (field: keyof RevenueFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'عنوان الإيراد مطلوب';
    }

    if (!formData.type) {
      newErrors.type = 'نوع الإيراد مطلوب';
    }

    if (!formData.amount || formData.amount <= 0) {
      newErrors.amount = 'مبلغ الإيراد يجب أن يكون أكبر من صفر';
    }

    if (!formData.invoice_date) {
      newErrors.invoice_date = 'تاريخ الفاتورة مطلوب';
    }

    if (!formData.due_date) {
      newErrors.due_date = 'تاريخ الاستحقاق مطلوب';
    }

    if (formData.invoice_date && formData.due_date && formData.due_date < formData.invoice_date) {
      newErrors.due_date = 'تاريخ الاستحقاق يجب أن يكون بعد تاريخ الفاتورة';
    }

    if (formData.tax_amount < 0) {
      newErrors.tax_amount = 'مبلغ الضريبة لا يمكن أن يكون سالباً';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      showToast.error('يرجى تصحيح الأخطاء في النموذج');
      return;
    }

    try {
      setLoading(true);
      
      // Prepare data for API
      const submitData = {
        ...formData,
        client_id: formData.client_id && formData.client_id !== 'none' ? formData.client_id : null,
        project_id: formData.project_id && formData.project_id !== 'none' ? formData.project_id : null,
        sales_rep_id: formData.sales_rep_id && formData.sales_rep_id !== 'none' ? formData.sales_rep_id : null,
        payment_date: formData.payment_date || null
      };

      await financeAPI.createRevenue(submitData);
      showToast.success('تم إضافة الإيراد بنجاح');
      router.push('/founder-dashboard/finance/revenue');
      
    } catch (err) {
      console.error('Error creating revenue:', err);
      showToast.error('فشل في إضافة الإيراد');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/founder-dashboard/finance/revenue');
  };

  if (!mounted || !isAuthenticated) {
    return null;
  }

  return (
    <UnifiedLayout>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCancel}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            العودة
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <TrendingUp className="h-8 w-8 text-green-600" />
              إضافة إيراد جديد
            </h1>
            <p className="text-gray-600 mt-1">
              إضافة مصدر إيراد جديد إلى النظام المالي
            </p>
          </div>
        </div>

        {dataLoading ? (
          <Card>
            <CardContent className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
              <span className="mr-2 text-gray-600">جاري تحميل البيانات...</span>
            </CardContent>
          </Card>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5" />
                  المعلومات الأساسية
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">عنوان الإيراد *</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      placeholder="أدخل عنوان الإيراد"
                      className={errors.title ? 'border-red-500' : ''}
                    />
                    {errors.title && (
                      <p className="text-sm text-red-500 mt-1">{errors.title}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="type">نوع الإيراد *</Label>
                    <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
                      <SelectTrigger className={errors.type ? 'border-red-500' : ''}>
                        <SelectValue placeholder="اختر نوع الإيراد" />
                      </SelectTrigger>
                      <SelectContent>
                        {revenueTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.type && (
                      <p className="text-sm text-red-500 mt-1">{errors.type}</p>
                    )}
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">الوصف</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="أدخل وصف الإيراد (اختياري)"
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Financial Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CurrencyIcon className="h-5 w-5" />
                  التفاصيل المالية
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="amount">مبلغ الإيراد (ج.م) *</Label>
                    <div className="relative">
                      <CurrencyIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        id="amount"
                        type="number"
                        min="0"
                        step="0.01"
                        value={formData.amount || ''}
                        onChange={(e) => handleInputChange('amount', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                        className={`pr-10 ${errors.amount ? 'border-red-500' : ''}`}
                      />
                    </div>
                    {errors.amount && (
                      <p className="text-sm text-red-500 mt-1">{errors.amount}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="tax_amount">مبلغ الضريبة (جنيه مصري)</Label>
                    <div className="relative">
                      <CurrencyIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        id="tax_amount"
                        type="number"
                        min="0"
                        step="0.01"
                        value={formData.tax_amount || ''}
                        onChange={(e) => handleInputChange('tax_amount', parseFloat(e.target.value) || 0)}
                        placeholder="0.00"
                        className={`pr-10 ${errors.tax_amount ? 'border-red-500' : ''}`}
                      />
                    </div>
                    {errors.tax_amount && (
                      <p className="text-sm text-red-500 mt-1">{errors.tax_amount}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="status">حالة الإيراد</Label>
                    <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر حالة الإيراد" />
                      </SelectTrigger>
                      <SelectContent>
                        {revenueStatuses.map((status) => (
                          <SelectItem key={status.value} value={status.value}>
                            {status.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Net Amount Display */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-700">المبلغ الصافي:</span>
                    <span className="text-lg font-bold text-green-600">
                      {formatCurrencyWithStore((formData.amount || 0) - (formData.tax_amount || 0))}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Relationships */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  العلاقات
                </CardTitle>
                <CardDescription>
                  ربط الإيراد بالعميل والمشروع ومندوب المبيعات (اختياري)
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="client_id">العميل</Label>
                    <Select
                      value={formData.client_id?.toString() || 'none'}
                      onValueChange={(value) => handleInputChange('client_id', value === 'none' ? null : parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="اختر العميل">
                          <div className="flex items-center gap-2">
                            <Building className="h-4 w-4" />
                            <span>اختر العميل</span>
                          </div>
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">بدون عميل</SelectItem>
                        {clients.map((client: any) => (
                          <SelectItem key={client.id} value={client.id.toString()}>
                            {client.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="project_id">المشروع</Label>
                    <Select
                      value={formData.project_id?.toString() || 'none'}
                      onValueChange={(value) => handleInputChange('project_id', value === 'none' ? null : parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="اختر المشروع">
                          <div className="flex items-center gap-2">
                            <FolderOpen className="h-4 w-4" />
                            <span>اختر المشروع</span>
                          </div>
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">بدون مشروع</SelectItem>
                        {projects.map((project: any) => (
                          <SelectItem key={project.id} value={project.id.toString()}>
                            {project.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="sales_rep_id">مندوب المبيعات</Label>
                    <Select
                      value={formData.sales_rep_id?.toString() || 'none'}
                      onValueChange={(value) => handleInputChange('sales_rep_id', value === 'none' ? null : parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="اختر مندوب المبيعات">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4" />
                            <span>اختر مندوب المبيعات</span>
                          </div>
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">بدون مندوب</SelectItem>
                        {salesReps.map((rep: any) => (
                          <SelectItem key={rep.id} value={rep.id.toString()}>
                            {rep.first_name} {rep.last_name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Dates */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  التواريخ
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="invoice_date">تاريخ الفاتورة *</Label>
                    <Input
                      id="invoice_date"
                      type="date"
                      value={formData.invoice_date}
                      onChange={(e) => handleInputChange('invoice_date', e.target.value)}
                      className={errors.invoice_date ? 'border-red-500' : ''}
                    />
                    {errors.invoice_date && (
                      <p className="text-sm text-red-500 mt-1">{errors.invoice_date}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="due_date">تاريخ الاستحقاق *</Label>
                    <Input
                      id="due_date"
                      type="date"
                      value={formData.due_date}
                      onChange={(e) => handleInputChange('due_date', e.target.value)}
                      className={errors.due_date ? 'border-red-500' : ''}
                    />
                    {errors.due_date && (
                      <p className="text-sm text-red-500 mt-1">{errors.due_date}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="payment_date">تاريخ الدفع</Label>
                    <Input
                      id="payment_date"
                      type="date"
                      value={formData.payment_date}
                      onChange={(e) => handleInputChange('payment_date', e.target.value)}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      اتركه فارغاً إذا لم يتم الدفع بعد
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Form Actions */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-end gap-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                    disabled={loading}
                  >
                    إلغاء
                  </Button>
                  <Button
                    type="submit"
                    disabled={loading}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin ml-2" />
                        جاري الحفظ...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 ml-2" />
                        حفظ الإيراد
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </form>
        )}
      </div>
    </UnifiedLayout>
  );
}
