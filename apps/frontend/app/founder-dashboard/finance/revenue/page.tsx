'use client';

import { UnifiedLayout } from '@/components/layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { financeAPI } from '@/lib/api';
import { useAuthStore } from '@/lib/stores/auth-store';
import { showToast } from '@/lib/toast';
import {
    AlertTriangle,
    Calendar,
    Clock,
    DollarSign,
    Edit,
    Eye,
    Filter,
    Loader2,
    MoreHorizontal,
    Plus,
    Search,
    Trash2,
    TrendingUp
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

// Egyptian Revenue Dummy Data
const REVENUE_DUMMY_DATA = [
  {
    id: 1,
    title: 'إيرادات تطوير موقع شركة النيل للتكنولوجيا',
    amount: 45000,
    type: 'project_payment',
    status: 'received',
    invoice_date: '2024-01-22',
    due_date: '2024-02-22',
    client_name: 'شركة النيل للتكنولوجيا',
    project_name: 'تطوير موقع إلكتروني متكامل',
    invoice_number: 'INV-2024-001',
    is_overdue: false,
    payment_method: 'bank_transfer',
    tax_amount: 6300,
    net_amount: 38700
  },
  {
    id: 2,
    title: 'إيرادات حملة تسويقية لمطعم الأصالة',
    amount: 18000,
    type: 'consultation',
    status: 'received',
    invoice_date: '2024-01-21',
    due_date: '2024-02-21',
    client_name: 'مطعم الأصالة',
    project_name: 'حملة تسويق رقمي شاملة',
    invoice_number: 'INV-2024-002',
    is_overdue: false,
    payment_method: 'vodafone_cash',
    tax_amount: 2520,
    net_amount: 15480
  },
  {
    id: 3,
    title: 'إيرادات تطبيق جوال لمتجر الأناقة',
    amount: 32000,
    type: 'project_payment',
    status: 'pending',
    invoice_date: '2024-01-20',
    due_date: '2024-02-20',
    client_name: 'متجر الأناقة',
    project_name: 'تطبيق جوال للتجارة الإلكترونية',
    invoice_number: 'INV-2024-003',
    is_overdue: false,
    payment_method: 'credit_card',
    tax_amount: 4480,
    net_amount: 27520
  },
  {
    id: 4,
    title: 'إيرادات تصميم هوية بصرية لشركة الجيزة للأثاث',
    amount: 12000,
    type: 'project_payment',
    status: 'received',
    invoice_date: '2024-01-19',
    due_date: '2024-02-19',
    client_name: 'شركة الجيزة للأثاث',
    project_name: 'تصميم هوية بصرية شاملة',
    invoice_number: 'INV-2024-004',
    is_overdue: false,
    payment_method: 'fawry',
    tax_amount: 1680,
    net_amount: 10320
  },
  {
    id: 5,
    title: 'إيرادات نظام إدارة مخزون لصيدلية الشفاء',
    amount: 28000,
    type: 'project_payment',
    status: 'received',
    invoice_date: '2024-01-18',
    due_date: '2024-02-18',
    client_name: 'صيدلية الشفاء',
    project_name: 'نظام إدارة المخزون والمبيعات',
    invoice_number: 'INV-2024-005',
    is_overdue: false,
    payment_method: 'bank_transfer',
    tax_amount: 3920,
    net_amount: 24080
  },
  {
    id: 6,
    title: 'اشتراك استضافة شهري - شركة الدلتا للاستيراد',
    amount: 500,
    type: 'hosting',
    status: 'overdue',
    invoice_date: '2024-01-01',
    due_date: '2024-01-15',
    client_name: 'شركة الدلتا للاستيراد',
    project_name: 'خدمات الاستضافة الشهرية',
    invoice_number: 'INV-2024-006',
    is_overdue: true,
    payment_method: 'bank_transfer',
    tax_amount: 70,
    net_amount: 430
  }
];

const REVENUE_SUMMARY_DUMMY = {
  total_revenue: 135500,
  pending_revenue: 32000,
  overdue_count: 1,
  received_revenue: 103500,
  growth_rate: 18.5,
  monthly_target: 120000,
  achievement_rate: 112.9
};

export default function RevenueManagementPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [revenues, setRevenues] = useState([]);
  const [revenueSummary, setRevenueSummary] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && isAuthenticated) {
      fetchRevenueData();
    }
  }, [mounted, isAuthenticated, statusFilter, typeFilter]);

  const fetchRevenueData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Build filter parameters
      const params = {};
      if (statusFilter !== 'all') params.status = statusFilter;
      if (typeFilter !== 'all') params.type = typeFilter;
      if (searchTerm) params.search = searchTerm;

      // Fetch revenues
      const revenueResponse = await financeAPI.getRevenues(params);
      const revenueData = revenueResponse.results || revenueResponse || [];

      // Fetch revenue summary
      const summaryResponse = await financeAPI.getRevenueSummary();

      // Use real data if available, otherwise fallback to dummy data
      if (revenueData.length > 0) {
        setRevenues(revenueData);
        setRevenueSummary(summaryResponse || REVENUE_SUMMARY_DUMMY);
      } else {
        // Use dummy data when no real data is available
        setRevenues(REVENUE_DUMMY_DATA);
        setRevenueSummary(REVENUE_SUMMARY_DUMMY);
        console.log('Using dummy revenue data for demonstration');
      }

    } catch (err) {
      console.error('Error fetching revenue data:', err);
      // Use dummy data as fallback when API fails
      setRevenues(REVENUE_DUMMY_DATA);
      setRevenueSummary(REVENUE_SUMMARY_DUMMY);
      showToast.error('تم تحميل البيانات التجريبية - تحقق من اتصال الخادم');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    fetchRevenueData();
  };

  const handleAddRevenue = () => {
    router.push('/founder-dashboard/finance/revenue/add');
  };

  const handleEditRevenue = (revenue) => {
    router.push(`/founder-dashboard/finance/revenue/edit/${revenue.id}`);
  };

  const handleDeleteRevenue = async (revenue) => {
    if (confirm('هل أنت متأكد من حذف هذا الإيراد؟')) {
      try {
        await financeAPI.deleteRevenue(revenue.id);
        showToast.success('تم حذف الإيراد بنجاح');
        fetchRevenueData();
      } catch (err) {
        console.error('Error deleting revenue:', err);
        showToast.error('فشل في حذف الإيراد');
      }
    }
  };

  const handleViewRevenue = (revenue) => {
    router.push(`/founder-dashboard/finance/revenue/view/${revenue.id}`);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-EG');
  };

  const getStatusBadge = (status, isOverdue) => {
    if (isOverdue) {
      return <Badge variant="destructive">متأخر</Badge>;
    }
    
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-orange-600 border-orange-600">معلق</Badge>;
      case 'received':
        return <Badge variant="outline" className="text-green-600 border-green-600">مستلم</Badge>;
      case 'overdue':
        return <Badge variant="destructive">متأخر</Badge>;
      case 'cancelled':
        return <Badge variant="outline" className="text-red-600 border-red-600">ملغي</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getTypeDisplay = (type) => {
    const types = {
      'project_payment': 'دفعة مشروع',
      'subscription': 'اشتراك',
      'maintenance': 'صيانة',
      'consultation': 'استشارة',
      'hosting': 'استضافة',
      'domain': 'نطاق',
      'other': 'أخرى'
    };
    return types[type] || type;
  };

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل إدارة الإيرادات...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى إدارة الإيرادات</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <UnifiedLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
            <p className="text-gray-600">جاري تحميل بيانات الإيرادات...</p>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  if (error) {
    return (
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-red-500 mb-4">
                <TrendingUp className="h-12 w-12 mx-auto mb-4" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل البيانات</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={fetchRevenueData} className="bg-purple-600 hover:bg-purple-700">
                إعادة المحاولة
              </Button>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <TrendingUp className="h-8 w-8 text-green-600" />
              إدارة الإيرادات
            </h1>
            <p className="text-gray-600 mt-1">
              تتبع وإدارة جميع مصادر الإيرادات والفواتير
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Button onClick={handleAddRevenue} className="bg-green-600 hover:bg-green-700">
              <Plus className="h-4 w-4 ml-2" />
              إضافة إيراد جديد
            </Button>
          </div>
        </div>

        {/* Revenue Summary Cards */}
        {revenueSummary && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">إجمالي الإيرادات</CardTitle>
                <DollarSign className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(revenueSummary.total_revenue)}
                </div>
                <p className="text-xs text-gray-600">مستلم</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">إيرادات معلقة</CardTitle>
                <Clock className="h-4 w-4 text-orange-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {formatCurrency(revenueSummary.pending_revenue)}
                </div>
                <p className="text-xs text-gray-600">في انتظار الاستلام</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">فواتير متأخرة</CardTitle>
                <AlertTriangle className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {revenueSummary.overdue_count}
                </div>
                <p className="text-xs text-gray-600">تحتاج متابعة</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">نمو الإيرادات</CardTitle>
                <TrendingUp className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">+12.5%</div>
                <p className="text-xs text-gray-600">مقارنة بالشهر الماضي</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في الإيرادات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="pr-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="الحالة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الحالات</SelectItem>
                    <SelectItem value="pending">معلق</SelectItem>
                    <SelectItem value="received">مستلم</SelectItem>
                    <SelectItem value="overdue">متأخر</SelectItem>
                    <SelectItem value="cancelled">ملغي</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="النوع" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الأنواع</SelectItem>
                    <SelectItem value="project_payment">دفعة مشروع</SelectItem>
                    <SelectItem value="subscription">اشتراك</SelectItem>
                    <SelectItem value="maintenance">صيانة</SelectItem>
                    <SelectItem value="consultation">استشارة</SelectItem>
                    <SelectItem value="hosting">استضافة</SelectItem>
                    <SelectItem value="domain">نطاق</SelectItem>
                    <SelectItem value="other">أخرى</SelectItem>
                  </SelectContent>
                </Select>
                
                <Button onClick={handleSearch} variant="outline">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Revenue List */}
        <div className="grid grid-cols-1 gap-4">
          {revenues.map((revenue) => (
            <Card key={revenue.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-4 mb-2">
                      <h3 className="text-lg font-semibold">{revenue.title}</h3>
                      {getStatusBadge(revenue.status, revenue.is_overdue)}
                      <Badge variant="secondary">{getTypeDisplay(revenue.type)}</Badge>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600">
                      <div>
                        <span className="font-medium">المبلغ:</span>
                        <div className="text-lg font-bold text-green-600">
                          {formatCurrency(revenue.amount)}
                        </div>
                      </div>
                      
                      <div>
                        <span className="font-medium">العميل:</span>
                        <div>{revenue.client_name || 'غير محدد'}</div>
                      </div>
                      
                      <div>
                        <span className="font-medium">تاريخ الفاتورة:</span>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {formatDate(revenue.invoice_date)}
                        </div>
                      </div>
                      
                      <div>
                        <span className="font-medium">تاريخ الاستحقاق:</span>
                        <div className={`flex items-center gap-1 ${revenue.is_overdue ? 'text-red-600' : ''}`}>
                          <Calendar className="h-3 w-3" />
                          {formatDate(revenue.due_date)}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewRevenue(revenue)}>
                        <Eye className="h-4 w-4 ml-2" />
                        عرض التفاصيل
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditRevenue(revenue)}>
                        <Edit className="h-4 w-4 ml-2" />
                        تعديل
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => handleDeleteRevenue(revenue)}
                      >
                        <Trash2 className="h-4 w-4 ml-2" />
                        حذف
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {revenues.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد إيرادات</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                  ? 'لا توجد إيرادات تطابق معايير البحث المحددة'
                  : 'لم يتم إضافة أي إيرادات بعد'
                }
              </p>
              {!searchTerm && statusFilter === 'all' && typeFilter === 'all' && (
                <Button onClick={handleAddRevenue} className="bg-green-600 hover:bg-green-700">
                  <Plus className="h-4 w-4 ml-2" />
                  إضافة أول إيراد
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </UnifiedLayout>
  );
}
