'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { FounderBreadcrumb } from '@/components/ui/responsive-breadcrumb';
import {
  Users,
  Mail,
  Phone,
  MapPin,
  Building,
  Calendar,
  DollarSign,
  FolderOpen,
  Edit,
  ArrowRight,
  AlertCircle,
  Loader2,
  Globe,
  MessageSquare
} from 'lucide-react';
import { DEMO_CLIENTS } from '@/lib/demo-data';
import { formatRelativeTime } from '@mtbrmg/shared';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';
import { clientsAPI } from '@/lib/api';
import { showToast } from '@/lib/toast';

export default function ClientDetailPage() {
  const router = useRouter();
  const params = useParams();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [client, setClient] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    const loadClient = async () => {
      if (!isAuthenticated || !mounted || !params.id) return;

      setIsLoading(true);
      try {
        // Try to load from API first
        const response = await clientsAPI.getClient(params.id);
        setClient(response);
      } catch (error) {
        console.error('Error loading client:', error);
        // Fallback to demo data
        const demoClient = DEMO_CLIENTS.find(c => c.id === params.id);
        if (demoClient) {
          setClient(demoClient);
        } else {
          showToast.error('العميل غير موجود');
          router.push('/founder-dashboard/clients');
          return;
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadClient();
  }, [isAuthenticated, mounted, params.id, router]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">نشط</Badge>;
      case 'inactive':
        return <Badge className="bg-gray-100 text-gray-800">غير نشط</Badge>;
      case 'potential':
        return <Badge className="bg-blue-100 text-blue-800">محتمل</Badge>;
      case 'former':
        return <Badge className="bg-red-100 text-red-800">سابق</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 text-purple-600 mx-auto mb-4 animate-spin" />
          <p className="text-gray-600 mt-2">جاري تحميل تفاصيل العميل...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى تفاصيل العميل</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <Loader2 className="h-12 w-12 text-purple-600 mx-auto mb-4 animate-spin" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">جاري تحميل تفاصيل العميل...</h3>
              <p className="text-gray-600">يرجى الانتظار</p>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  if (!client) {
    return (
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">العميل غير موجود</h3>
              <p className="text-gray-600 mb-4">لم يتم العثور على العميل المطلوب</p>
              <Button onClick={() => router.push('/founder-dashboard/clients')}>
                العودة إلى العملاء
              </Button>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Responsive Breadcrumb */}
        <FounderBreadcrumb
          section="إدارة العملاء"
          sectionHref="/founder-dashboard/clients"
          current={client.name}
        />

        {/* Header */}
        <div className="flex justify-between items-start mb-8">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="h-8 w-8 text-blue-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{client.name}</h1>
              <p className="text-gray-600 mt-1">{client.company || 'عميل فردي'}</p>
              <div className="flex items-center gap-2 mt-3">
                {getStatusBadge(client.status)}
                <Badge variant="outline">
                  {client.type === 'individual' ? 'فردي' : 'شركة'}
                </Badge>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" onClick={() => router.push(`/founder-dashboard/clients/${client.id}/edit`)}>
              <Edit className="h-4 w-4 ml-2" />
              تعديل العميل
            </Button>
            <Button onClick={() => router.push('/founder-dashboard/clients')}>
              <ArrowRight className="h-4 w-4 ml-2" />
              العودة إلى العملاء
            </Button>
          </div>
        </div>

        {/* Client Details Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle>معلومات الاتصال</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center gap-3">
                    <Mail className="h-5 w-5 text-gray-400" />
                    <div>
                      <label className="text-sm font-medium text-gray-600">البريد الإلكتروني</label>
                      <p className="text-sm font-semibold">{client.email || 'غير محدد'}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="h-5 w-5 text-gray-400" />
                    <div>
                      <label className="text-sm font-medium text-gray-600">رقم الهاتف</label>
                      <p className="text-sm font-semibold">{client.phone || 'غير محدد'}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <MapPin className="h-5 w-5 text-gray-400" />
                    <div>
                      <label className="text-sm font-medium text-gray-600">العنوان</label>
                      <p className="text-sm font-semibold">{client.address || 'غير محدد'}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Building className="h-5 w-5 text-gray-400" />
                    <div>
                      <label className="text-sm font-medium text-gray-600">الشركة</label>
                      <p className="text-sm font-semibold">{client.company || 'غير محدد'}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Additional Information */}
            <Card>
              <CardHeader>
                <CardTitle>معلومات إضافية</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">تاريخ التسجيل</label>
                    <p className="text-sm font-semibold">
                      {client.created_at ? formatDate(client.created_at) : 'غير محدد'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">آخر تحديث</label>
                    <p className="text-sm font-semibold">
                      {client.updated_at ? formatDate(client.updated_at) : 'غير محدد'}
                    </p>
                  </div>
                </div>
                {client.notes && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">ملاحظات</label>
                    <p className="text-sm text-gray-700 mt-1">{client.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Statistics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  الإحصائيات
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-600">إجمالي المشاريع</label>
                  <p className="text-lg font-bold text-blue-600">
                    {client.total_projects || 0}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">إجمالي القيمة</label>
                  <p className="text-lg font-bold text-green-600">
                    {client.total_value ? formatCurrency(client.total_value) : 'غير محدد'}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>إجراءات سريعة</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  <FolderOpen className="h-4 w-4 ml-2" />
                  عرض المشاريع
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <MessageSquare className="h-4 w-4 ml-2" />
                  إرسال رسالة
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Globe className="h-4 w-4 ml-2" />
                  زيارة الموقع
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </UnifiedLayout>
  );
}
