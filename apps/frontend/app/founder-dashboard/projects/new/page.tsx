'use client';

import { UnifiedLayout } from '@/components/layout';
import { UnifiedProjectCreationForm } from '@/components/forms/unified-project-creation-form';
import { FounderBreadcrumb } from '@/components/ui/responsive-breadcrumb';
import { FolderOpen } from 'lucide-react';

export default function NewProjectPage() {
  return (
    <UnifiedLayout>
      <div className="max-w-6xl mx-auto py-8">
        {/* Responsive Breadcrumb Navigation */}
        <FounderBreadcrumb
          section="إدارة المشاريع"
          sectionHref="/founder-dashboard/projects"
          current="إضافة مشروع جديد"
        />

        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <FolderOpen className="h-8 w-8 text-purple-600" />
            إضافة مشروع جديد
          </h1>
          <p className="text-gray-600 mt-2">
            اختر طريقة إنشاء المشروع وأكمل المعلومات المطلوبة
          </p>
        </div>

        {/* Main Form */}
        <UnifiedProjectCreationForm />
      </div>
    </UnifiedLayout>
  );
}
