'use client';

import { UnifiedLayout } from '@/components/layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CurrencyConversionIndicator } from '@/components/ui/currency-conversion-indicator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { useAuthStore } from '@/lib/stores/auth-store';
import { useCurrency, useCurrencyStore } from '@/lib/stores/currency-store';
import { showToast } from '@/lib/toast';
import {
    Bell,
    CreditCard,
    Globe,
    Key,
    RefreshCw,
    Save,
    Settings,
    Shield,
    Upload,
    User
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function SettingsPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const { currentCurrency, setCurrency, refreshRates, isConverting, updateExchangeRate, getManualExchangeRate, resetToDefaultRates } = useCurrency();
  const [mounted, setMounted] = useState(false);

  // Settings state
  const [profileSettings, setProfileSettings] = useState({
    name: 'محمد يوسف',
    email: '<EMAIL>',
    phone: '+966501234567',
    company: 'MTBRMG Digital Agency',
    bio: 'مؤسس ومدير الوكالة الرقمية'
  });

  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    pushNotifications: true,
    projectUpdates: true,
    taskReminders: true,
    clientMessages: true,
    systemAlerts: true
  });

  const [systemSettings, setSystemSettings] = useState({
    language: 'ar',
    timezone: 'Africa/Cairo',
    dateFormat: 'dd/mm/yyyy',
    currency: currentCurrency
  });

  // Payment Gateway Settings state
  const [paymentGatewaySettings, setPaymentGatewaySettings] = useState({
    paymob: {
      enabled: true,
      apiKey: '',
      integrationId: '',
      iframeId: '',
      hmacSecret: '',
      sandbox: true
    },
    stripe: {
      enabled: false,
      publishableKey: '',
      secretKey: '',
      webhookSecret: '',
      sandbox: true
    },
    paypal: {
      enabled: false,
      clientId: '',
      clientSecret: '',
      sandbox: true
    }
  });

  const [showSecrets, setShowSecrets] = useState({
    paymobApiKey: false,
    paymobHmac: false,
    stripeSecret: false,
    stripeWebhook: false,
    paypalSecret: false
  });

  const [testingGateway, setTestingGateway] = useState(null);
  const [gatewayStatus, setGatewayStatus] = useState({
    paymob: null,
    stripe: null,
    paypal: null
  });

  useEffect(() => {
    setMounted(true);
    // Hydrate currency store
    useCurrencyStore.persist.rehydrate();
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      console.log('User not authenticated, redirecting to login');
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  // Load payment gateway settings on mount
  useEffect(() => {
    const loadPaymentGatewaySettings = async () => {
      if (mounted && isAuthenticated) {
        try {
          // Import the API here to avoid import issues
          const { settingsAPI } = await import('@/lib/api/settings');
          const settings = await settingsAPI.loadPaymentGatewaySettings();
          setPaymentGatewaySettings(settings);

          // Load gateway status
          const statusData = await settingsAPI.getPaymentGatewayStatus();
          const statusMap = {};
          statusData.gateways.forEach(gateway => {
            statusMap[gateway.gateway_type] = gateway.last_test_status;
          });
          setGatewayStatus(statusMap);
        } catch (error) {
          console.error('Error loading payment gateway settings:', error);
          // Don't show error toast for now since backend isn't ready
          // showToast.error('فشل في تحميل إعدادات بوابات الدفع');
          console.log('Using default payment gateway settings (backend not ready)');
        }
      }
    };

    loadPaymentGatewaySettings();
  }, [mounted, isAuthenticated]);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mt-2">جاري تحميل الإعدادات...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى الإعدادات</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  const handleSaveProfile = () => {
    // Handle profile save
    console.log('Saving profile settings:', profileSettings);
  };

  const handleSaveNotifications = () => {
    // Handle notifications save
    console.log('Saving notification settings:', notificationSettings);
  };

  const handleSaveSystem = async () => {
    try {
      // Update global currency store
      await setCurrency(systemSettings.currency);

      // Handle system save
      console.log('Saving system settings:', systemSettings);
      showToast.success('تم حفظ إعدادات النظام بنجاح');
    } catch (error) {
      console.error('Error saving system settings:', error);
      showToast.error('فشل في حفظ إعدادات النظام');
    }
  };

  const handleRefreshRates = async () => {
    try {
      await refreshRates(true);
      showToast.success('تم تحديث أسعار الصرف بنجاح');
    } catch (error) {
      console.error('Error refreshing rates:', error);
      showToast.error('فشل في تحديث أسعار الصرف');
    }
  };

  // Payment Gateway Handlers
  const handleSavePaymentGateways = async () => {
    try {
      const { settingsAPI } = await import('@/lib/api/settings');
      await settingsAPI.savePaymentGatewaySettings(paymentGatewaySettings);
      showToast.success('تم حفظ إعدادات بوابات الدفع بنجاح');
    } catch (error) {
      console.error('Error saving payment gateway settings:', error);
      if (error.response?.status === 404) {
        showToast.info('سيتم حفظ الإعدادات محلياً حتى يتم تشغيل الخادم');
        // Save to localStorage as fallback
        localStorage.setItem('paymentGatewaySettings', JSON.stringify(paymentGatewaySettings));
      } else {
        showToast.error('فشل في حفظ إعدادات بوابات الدفع');
      }
    }
  };

  const testGatewayConnection = async (gateway: string) => {
    setTestingGateway(gateway);
    try {
      const { settingsAPI } = await import('@/lib/api/settings');
      const result = await settingsAPI.testPaymentGateway(gateway);

      setGatewayStatus(prev => ({
        ...prev,
        [gateway]: result.success ? 'success' : 'error'
      }));

      if (result.success) {
        showToast.success(`تم اختبار اتصال ${gateway} بنجاح`);
      } else {
        showToast.error(`فشل في اختبار اتصال ${gateway}: ${result.message}`);
      }
    } catch (error) {
      console.error('Error testing gateway connection:', error);
      if (error.response?.status === 404) {
        // Simulate successful test for demo purposes
        setGatewayStatus(prev => ({
          ...prev,
          [gateway]: 'success'
        }));
        showToast.success(`تم اختبار اتصال ${gateway} بنجاح (وضع العرض)`);
      } else {
        setGatewayStatus(prev => ({
          ...prev,
          [gateway]: 'error'
        }));
        showToast.error(`فشل في اختبار اتصال ${gateway}`);
      }
    } finally {
      setTestingGateway(null);
    }
  };

  const toggleSecretVisibility = (field) => {
    setShowSecrets(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  return (
    <UnifiedLayout>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Settings className="h-8 w-8 text-purple-600" />
              الإعدادات
            </h1>
            <p className="text-gray-600 mt-1">
              إدارة إعدادات النظام والحساب الشخصي
            </p>
          </div>
        </div>

        {/* Settings Tabs */}
        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="profile" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              الملف الشخصي
            </TabsTrigger>
            <TabsTrigger value="notifications" className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              الإشعارات
            </TabsTrigger>
            <TabsTrigger value="security" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              الأمان
            </TabsTrigger>
            <TabsTrigger value="payments" className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              بوابات الدفع
            </TabsTrigger>
            <TabsTrigger value="system" className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              النظام
            </TabsTrigger>
          </TabsList>

          {/* Profile Settings */}
          <TabsContent value="profile">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات الملف الشخصي</CardTitle>
                <CardDescription>
                  إدارة معلومات الحساب الشخصي
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="name">الاسم الكامل</Label>
                    <Input
                      id="name"
                      value={profileSettings.name}
                      onChange={(e) => setProfileSettings({...profileSettings, name: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">البريد الإلكتروني</Label>
                    <Input
                      id="email"
                      type="email"
                      value={profileSettings.email}
                      onChange={(e) => setProfileSettings({...profileSettings, email: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">رقم الهاتف</Label>
                    <Input
                      id="phone"
                      value={profileSettings.phone}
                      onChange={(e) => setProfileSettings({...profileSettings, phone: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="company">اسم الشركة</Label>
                    <Input
                      id="company"
                      value={profileSettings.company}
                      onChange={(e) => setProfileSettings({...profileSettings, company: e.target.value})}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bio">نبذة شخصية</Label>
                  <Textarea
                    id="bio"
                    value={profileSettings.bio}
                    onChange={(e) => setProfileSettings({...profileSettings, bio: e.target.value})}
                    rows={3}
                  />
                </div>
                <div className="space-y-2">
                  <Label>الصورة الشخصية</Label>
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                      <User className="h-8 w-8 text-purple-600" />
                    </div>
                    <Button variant="outline">
                      <Upload className="h-4 w-4 ml-2" />
                      رفع صورة جديدة
                    </Button>
                  </div>
                </div>
                <Button onClick={handleSaveProfile}>
                  <Save className="h-4 w-4 ml-2" />
                  حفظ التغييرات
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Notification Settings */}
          <TabsContent value="notifications">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات الإشعارات</CardTitle>
                <CardDescription>
                  إدارة تفضيلات الإشعارات والتنبيهات
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>إشعارات البريد الإلكتروني</Label>
                      <p className="text-sm text-gray-600">تلقي الإشعارات عبر البريد الإلكتروني</p>
                    </div>
                    <Switch
                      checked={notificationSettings.emailNotifications}
                      onCheckedChange={(checked) => 
                        setNotificationSettings({...notificationSettings, emailNotifications: checked})
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>الإشعارات الفورية</Label>
                      <p className="text-sm text-gray-600">تلقي الإشعارات الفورية في المتصفح</p>
                    </div>
                    <Switch
                      checked={notificationSettings.pushNotifications}
                      onCheckedChange={(checked) => 
                        setNotificationSettings({...notificationSettings, pushNotifications: checked})
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>تحديثات المشاريع</Label>
                      <p className="text-sm text-gray-600">إشعارات عند تحديث حالة المشاريع</p>
                    </div>
                    <Switch
                      checked={notificationSettings.projectUpdates}
                      onCheckedChange={(checked) => 
                        setNotificationSettings({...notificationSettings, projectUpdates: checked})
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>تذكير المهام</Label>
                      <p className="text-sm text-gray-600">تذكير بالمهام المستحقة</p>
                    </div>
                    <Switch
                      checked={notificationSettings.taskReminders}
                      onCheckedChange={(checked) => 
                        setNotificationSettings({...notificationSettings, taskReminders: checked})
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>رسائل العملاء</Label>
                      <p className="text-sm text-gray-600">إشعارات عند تلقي رسائل من العملاء</p>
                    </div>
                    <Switch
                      checked={notificationSettings.clientMessages}
                      onCheckedChange={(checked) => 
                        setNotificationSettings({...notificationSettings, clientMessages: checked})
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>تنبيهات النظام</Label>
                      <p className="text-sm text-gray-600">تنبيهات مهمة حول النظام</p>
                    </div>
                    <Switch
                      checked={notificationSettings.systemAlerts}
                      onCheckedChange={(checked) => 
                        setNotificationSettings({...notificationSettings, systemAlerts: checked})
                      }
                    />
                  </div>
                </div>
                <Button onClick={handleSaveNotifications}>
                  <Save className="h-4 w-4 ml-2" />
                  حفظ التغييرات
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Security Settings */}
          <TabsContent value="security">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات الأمان</CardTitle>
                <CardDescription>
                  إدارة كلمة المرور وإعدادات الأمان
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="current-password">كلمة المرور الحالية</Label>
                    <Input id="current-password" type="password" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="new-password">كلمة المرور الجديدة</Label>
                    <Input id="new-password" type="password" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirm-password">تأكيد كلمة المرور الجديدة</Label>
                    <Input id="confirm-password" type="password" />
                  </div>
                </div>
                <Button>
                  <Key className="h-4 w-4 ml-2" />
                  تحديث كلمة المرور
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Payment Gateway Settings */}
          <TabsContent value="payments">
            <div className="space-y-6">
              {/* Paymob Gateway */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <img
                        src="/paymob_logo.jpg"
                        alt="Paymob Logo"
                        className="h-8 w-auto rounded"
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.nextSibling.style.display = 'flex';
                        }}
                      />
                      <div className="h-8 w-8 bg-green-100 rounded-lg items-center justify-center hidden">
                        <CreditCard className="h-5 w-5 text-green-600" />
                      </div>
                      <span>Paymob - بوابة الدفع المصرية</span>
                    </div>
                    <div className="mr-auto flex items-center gap-2">
                      {gatewayStatus.paymob === 'success' && (
                        <div className="flex items-center gap-1 text-green-600 text-sm">
                          <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                          متصل
                        </div>
                      )}
                      {gatewayStatus.paymob === 'error' && (
                        <div className="flex items-center gap-1 text-red-600 text-sm">
                          <div className="h-2 w-2 bg-red-500 rounded-full"></div>
                          خطأ في الاتصال
                        </div>
                      )}
                      <Switch
                        checked={paymentGatewaySettings.paymob.enabled}
                        onCheckedChange={(checked) =>
                          setPaymentGatewaySettings(prev => ({
                            ...prev,
                            paymob: { ...prev.paymob, enabled: checked }
                          }))
                        }
                      />
                    </div>
                  </CardTitle>
                  <CardDescription>
                    بوابة الدفع الرائدة في مصر - تدعم البطاقات الائتمانية والمحافظ الإلكترونية
                  </CardDescription>
                </CardHeader>
                {paymentGatewaySettings.paymob.enabled && (
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="paymob-api-key">API Key</Label>
                        <div className="relative">
                          <Input
                            id="paymob-api-key"
                            type={showSecrets.paymobApiKey ? "text" : "password"}
                            value={paymentGatewaySettings.paymob.apiKey}
                            onChange={(e) => setPaymentGatewaySettings(prev => ({
                              ...prev,
                              paymob: { ...prev.paymob, apiKey: e.target.value }
                            }))}
                            placeholder="أدخل Paymob API Key"
                            className="pr-10"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute left-0 top-0 h-full px-3"
                            onClick={() => toggleSecretVisibility('paymobApiKey')}
                          >
                            {showSecrets.paymobApiKey ? (
                              <div className="h-4 w-4">👁️</div>
                            ) : (
                              <div className="h-4 w-4">🙈</div>
                            )}
                          </Button>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="paymob-integration-id">Integration ID</Label>
                        <Input
                          id="paymob-integration-id"
                          value={paymentGatewaySettings.paymob.integrationId}
                          onChange={(e) => setPaymentGatewaySettings(prev => ({
                            ...prev,
                            paymob: { ...prev.paymob, integrationId: e.target.value }
                          }))}
                          placeholder="أدخل Integration ID"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="paymob-iframe-id">iFrame ID</Label>
                        <Input
                          id="paymob-iframe-id"
                          value={paymentGatewaySettings.paymob.iframeId}
                          onChange={(e) => setPaymentGatewaySettings(prev => ({
                            ...prev,
                            paymob: { ...prev.paymob, iframeId: e.target.value }
                          }))}
                          placeholder="أدخل iFrame ID"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="paymob-hmac">HMAC Secret</Label>
                        <div className="relative">
                          <Input
                            id="paymob-hmac"
                            type={showSecrets.paymobHmac ? "text" : "password"}
                            value={paymentGatewaySettings.paymob.hmacSecret}
                            onChange={(e) => setPaymentGatewaySettings(prev => ({
                              ...prev,
                              paymob: { ...prev.paymob, hmacSecret: e.target.value }
                            }))}
                            placeholder="أدخل HMAC Secret"
                            className="pr-10"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute left-0 top-0 h-full px-3"
                            onClick={() => toggleSecretVisibility('paymobHmac')}
                          >
                            {showSecrets.paymobHmac ? (
                              <div className="h-4 w-4">👁️</div>
                            ) : (
                              <div className="h-4 w-4">🙈</div>
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Label>وضع الاختبار (Sandbox)</Label>
                        <Switch
                          checked={paymentGatewaySettings.paymob.sandbox}
                          onCheckedChange={(checked) =>
                            setPaymentGatewaySettings(prev => ({
                              ...prev,
                              paymob: { ...prev.paymob, sandbox: checked }
                            }))
                          }
                        />
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => testGatewayConnection('paymob')}
                        disabled={testingGateway === 'paymob'}
                      >
                        {testingGateway === 'paymob' ? (
                          <>
                            <div className="h-4 w-4 animate-spin mr-2">⏳</div>
                            جاري الاختبار...
                          </>
                        ) : (
                          <>
                            <div className="h-4 w-4 mr-2">🧪</div>
                            اختبار الاتصال
                          </>
                        )}
                      </Button>
                    </div>

                    <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
                      <p className="font-medium mb-1">💡 نصائح للحصول على بيانات Paymob:</p>
                      <ul className="list-disc list-inside space-y-1 text-xs">
                        <li>قم بإنشاء حساب على <a href="https://accept.paymob.com" target="_blank" className="text-blue-600 hover:underline">accept.paymob.com</a></li>
                        <li>انتقل إلى Settings → Account Info للحصول على API Key</li>
                        <li>انتقل إلى Developers → Payment Integrations لإنشاء Integration</li>
                        <li>انتقل إلى Developers → iFrames لإنشاء iFrame</li>
                      </ul>
                    </div>
                  </CardContent>
                )}
              </Card>

              {/* Stripe Gateway */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <img
                        src="/stripe_logo.gif"
                        alt="Stripe Logo"
                        className="h-8 w-auto rounded"
                      />
                      <span>Stripe - المدفوعات الدولية</span>
                    </div>
                    <div className="mr-auto flex items-center gap-2">
                      {gatewayStatus.stripe === 'success' && (
                        <div className="flex items-center gap-1 text-green-600 text-sm">
                          <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                          متصل
                        </div>
                      )}
                      {gatewayStatus.stripe === 'error' && (
                        <div className="flex items-center gap-1 text-red-600 text-sm">
                          <div className="h-2 w-2 bg-red-500 rounded-full"></div>
                          خطأ في الاتصال
                        </div>
                      )}
                      <Switch
                        checked={paymentGatewaySettings.stripe.enabled}
                        onCheckedChange={(checked) =>
                          setPaymentGatewaySettings(prev => ({
                            ...prev,
                            stripe: { ...prev.stripe, enabled: checked }
                          }))
                        }
                      />
                    </div>
                  </CardTitle>
                  <CardDescription>
                    بوابة دفع دولية تدعم البطاقات الائتمانية والمدفوعات الرقمية عالمياً
                  </CardDescription>
                </CardHeader>
                {paymentGatewaySettings.stripe.enabled && (
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="stripe-publishable">Publishable Key</Label>
                        <Input
                          id="stripe-publishable"
                          value={paymentGatewaySettings.stripe.publishableKey}
                          onChange={(e) => setPaymentGatewaySettings(prev => ({
                            ...prev,
                            stripe: { ...prev.stripe, publishableKey: e.target.value }
                          }))}
                          placeholder="pk_test_..."
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="stripe-secret">Secret Key</Label>
                        <div className="relative">
                          <Input
                            id="stripe-secret"
                            type={showSecrets.stripeSecret ? "text" : "password"}
                            value={paymentGatewaySettings.stripe.secretKey}
                            onChange={(e) => setPaymentGatewaySettings(prev => ({
                              ...prev,
                              stripe: { ...prev.stripe, secretKey: e.target.value }
                            }))}
                            placeholder="sk_test_..."
                            className="pr-10"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute left-0 top-0 h-full px-3"
                            onClick={() => toggleSecretVisibility('stripeSecret')}
                          >
                            {showSecrets.stripeSecret ? (
                              <div className="h-4 w-4">👁️</div>
                            ) : (
                              <div className="h-4 w-4">🙈</div>
                            )}
                          </Button>
                        </div>
                      </div>
                      <div className="space-y-2 md:col-span-2">
                        <Label htmlFor="stripe-webhook">Webhook Secret</Label>
                        <div className="relative">
                          <Input
                            id="stripe-webhook"
                            type={showSecrets.stripeWebhook ? "text" : "password"}
                            value={paymentGatewaySettings.stripe.webhookSecret}
                            onChange={(e) => setPaymentGatewaySettings(prev => ({
                              ...prev,
                              stripe: { ...prev.stripe, webhookSecret: e.target.value }
                            }))}
                            placeholder="whsec_..."
                            className="pr-10"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute left-0 top-0 h-full px-3"
                            onClick={() => toggleSecretVisibility('stripeWebhook')}
                          >
                            {showSecrets.stripeWebhook ? (
                              <div className="h-4 w-4">👁️</div>
                            ) : (
                              <div className="h-4 w-4">🙈</div>
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Label>وضع الاختبار (Test Mode)</Label>
                        <Switch
                          checked={paymentGatewaySettings.stripe.sandbox}
                          onCheckedChange={(checked) =>
                            setPaymentGatewaySettings(prev => ({
                              ...prev,
                              stripe: { ...prev.stripe, sandbox: checked }
                            }))
                          }
                        />
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => testGatewayConnection('stripe')}
                        disabled={testingGateway === 'stripe'}
                      >
                        {testingGateway === 'stripe' ? (
                          <>
                            <div className="h-4 w-4 animate-spin mr-2">⏳</div>
                            جاري الاختبار...
                          </>
                        ) : (
                          <>
                            <div className="h-4 w-4 mr-2">🧪</div>
                            اختبار الاتصال
                          </>
                        )}
                      </Button>
                    </div>

                    <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
                      <p className="font-medium mb-1">💡 نصائح للحصول على بيانات Stripe:</p>
                      <ul className="list-disc list-inside space-y-1 text-xs">
                        <li>قم بإنشاء حساب على <a href="https://stripe.com" target="_blank" className="text-blue-600 hover:underline">stripe.com</a></li>
                        <li>انتقل إلى Developers → API keys للحصول على المفاتيح</li>
                        <li>انتقل إلى Developers → Webhooks لإنشاء webhook endpoint</li>
                      </ul>
                    </div>
                  </CardContent>
                )}
              </Card>

              {/* PayPal Gateway */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <img
                        src="/paypal.jpg"
                        alt="PayPal Logo"
                        className="h-8 w-auto rounded"
                      />
                      <span>PayPal - المدفوعات العالمية</span>
                    </div>
                    <div className="mr-auto flex items-center gap-2">
                      {gatewayStatus.paypal === 'success' && (
                        <div className="flex items-center gap-1 text-green-600 text-sm">
                          <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                          متصل
                        </div>
                      )}
                      {gatewayStatus.paypal === 'error' && (
                        <div className="flex items-center gap-1 text-red-600 text-sm">
                          <div className="h-2 w-2 bg-red-500 rounded-full"></div>
                          خطأ في الاتصال
                        </div>
                      )}
                      <Switch
                        checked={paymentGatewaySettings.paypal.enabled}
                        onCheckedChange={(checked) =>
                          setPaymentGatewaySettings(prev => ({
                            ...prev,
                            paypal: { ...prev.paypal, enabled: checked }
                          }))
                        }
                      />
                    </div>
                  </CardTitle>
                  <CardDescription>
                    منصة دفع عالمية تدعم PayPal والبطاقات الائتمانية في جميع أنحاء العالم
                  </CardDescription>
                </CardHeader>
                {paymentGatewaySettings.paypal.enabled && (
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="paypal-client-id">Client ID</Label>
                        <Input
                          id="paypal-client-id"
                          value={paymentGatewaySettings.paypal.clientId}
                          onChange={(e) => setPaymentGatewaySettings(prev => ({
                            ...prev,
                            paypal: { ...prev.paypal, clientId: e.target.value }
                          }))}
                          placeholder="أدخل PayPal Client ID"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="paypal-secret">Client Secret</Label>
                        <div className="relative">
                          <Input
                            id="paypal-secret"
                            type={showSecrets.paypalSecret ? "text" : "password"}
                            value={paymentGatewaySettings.paypal.clientSecret}
                            onChange={(e) => setPaymentGatewaySettings(prev => ({
                              ...prev,
                              paypal: { ...prev.paypal, clientSecret: e.target.value }
                            }))}
                            placeholder="أدخل PayPal Client Secret"
                            className="pr-10"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute left-0 top-0 h-full px-3"
                            onClick={() => toggleSecretVisibility('paypalSecret')}
                          >
                            {showSecrets.paypalSecret ? (
                              <div className="h-4 w-4">👁️</div>
                            ) : (
                              <div className="h-4 w-4">🙈</div>
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Label>وضع الاختبار (Sandbox)</Label>
                        <Switch
                          checked={paymentGatewaySettings.paypal.sandbox}
                          onCheckedChange={(checked) =>
                            setPaymentGatewaySettings(prev => ({
                              ...prev,
                              paypal: { ...prev.paypal, sandbox: checked }
                            }))
                          }
                        />
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => testGatewayConnection('paypal')}
                        disabled={testingGateway === 'paypal'}
                      >
                        {testingGateway === 'paypal' ? (
                          <>
                            <div className="h-4 w-4 animate-spin mr-2">⏳</div>
                            جاري الاختبار...
                          </>
                        ) : (
                          <>
                            <div className="h-4 w-4 mr-2">🧪</div>
                            اختبار الاتصال
                          </>
                        )}
                      </Button>
                    </div>

                    <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
                      <p className="font-medium mb-1">💡 نصائح للحصول على بيانات PayPal:</p>
                      <ul className="list-disc list-inside space-y-1 text-xs">
                        <li>قم بإنشاء حساب على <a href="https://developer.paypal.com" target="_blank" className="text-blue-600 hover:underline">developer.paypal.com</a></li>
                        <li>انتقل إلى My Apps & Credentials لإنشاء تطبيق جديد</li>
                        <li>احصل على Client ID و Client Secret من صفحة التطبيق</li>
                      </ul>
                    </div>
                  </CardContent>
                )}
              </Card>

              {/* Save Button */}
              <div className="flex justify-end">
                <Button onClick={handleSavePaymentGateways} className="bg-green-600 hover:bg-green-700">
                  <Save className="h-4 w-4 ml-2" />
                  حفظ إعدادات بوابات الدفع
                </Button>
              </div>
            </div>
          </TabsContent>

          {/* System Settings */}
          <TabsContent value="system">
            <Card>
              <CardHeader>
                <CardTitle>إعدادات النظام</CardTitle>
                <CardDescription>
                  إعدادات عامة للنظام واللغة
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="language">اللغة</Label>
                    <select 
                      id="language" 
                      className="w-full p-2 border rounded-md"
                      value={systemSettings.language}
                      onChange={(e) => setSystemSettings({...systemSettings, language: e.target.value})}
                    >
                      <option value="ar">العربية</option>
                      <option value="en">English</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="timezone">المنطقة الزمنية</Label>
                    <select 
                      id="timezone" 
                      className="w-full p-2 border rounded-md"
                      value={systemSettings.timezone}
                      onChange={(e) => setSystemSettings({...systemSettings, timezone: e.target.value})}
                    >
                      <option value="Africa/Cairo">القاهرة (GMT+2)</option>
                      <option value="Asia/Riyadh">الرياض (GMT+3)</option>
                      <option value="Asia/Dubai">دبي (GMT+4)</option>
                      <option value="UTC">UTC (GMT+0)</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="dateFormat">تنسيق التاريخ</Label>
                    <select 
                      id="dateFormat" 
                      className="w-full p-2 border rounded-md"
                      value={systemSettings.dateFormat}
                      onChange={(e) => setSystemSettings({...systemSettings, dateFormat: e.target.value})}
                    >
                      <option value="dd/mm/yyyy">يوم/شهر/سنة</option>
                      <option value="mm/dd/yyyy">شهر/يوم/سنة</option>
                      <option value="yyyy-mm-dd">سنة-شهر-يوم</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="currency">العملة</Label>
                    <select 
                      id="currency" 
                      className="w-full p-2 border rounded-md"
                      value={systemSettings.currency}
                      onChange={(e) => {
                        setSystemSettings({...systemSettings, currency: e.target.value});
                        // Immediately update global currency for live preview
                        setCurrency(e.target.value);
                      }}
                    >
                      <option value="EGP">جنيه مصري (EGP)</option>
                      <option value="USD">دولار أمريكي (USD)</option>
                      <option value="SAR">ريال سعودي (SAR)</option>
                      <option value="AED">درهم إماراتي (AED)</option>
                    </select>
                  </div>
                </div>

                {/* Manual Exchange Rate Management */}
                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-3">إدارة أسعار الصرف اليدوية</h4>
                  <p className="text-sm text-gray-600 mb-4">
                    قم بتعيين أسعار الصرف يدوياً لضمان دقة التحويلات. العملة الأساسية هي الجنيه المصري (EGP).
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    {/* USD Rate */}
                    <div className="space-y-2">
                      <Label htmlFor="usd-rate">1 دولار أمريكي = كم جنيه مصري</Label>
                      <div className="flex items-center gap-2">
                        <Input
                          id="usd-rate"
                          type="number"
                          step="0.01"
                          min="0"
                          value={getManualExchangeRate('USD')}
                          onChange={(e) => updateExchangeRate('USD', parseFloat(e.target.value) || 0)}
                          placeholder="مثال: 50"
                          className="text-left"
                        />
                        <span className="text-sm text-gray-500">ج.م</span>
                      </div>
                    </div>

                    {/* SAR Rate */}
                    <div className="space-y-2">
                      <Label htmlFor="sar-rate">1 ريال سعودي = كم جنيه مصري</Label>
                      <div className="flex items-center gap-2">
                        <Input
                          id="sar-rate"
                          type="number"
                          step="0.01"
                          min="0"
                          value={getManualExchangeRate('SAR')}
                          onChange={(e) => updateExchangeRate('SAR', parseFloat(e.target.value) || 0)}
                          placeholder="مثال: 13"
                          className="text-left"
                        />
                        <span className="text-sm text-gray-500">ج.م</span>
                      </div>
                    </div>

                    {/* AED Rate */}
                    <div className="space-y-2">
                      <Label htmlFor="aed-rate">1 درهم إماراتي = كم جنيه مصري</Label>
                      <div className="flex items-center gap-2">
                        <Input
                          id="aed-rate"
                          type="number"
                          step="0.01"
                          min="0"
                          value={getManualExchangeRate('AED')}
                          onChange={(e) => updateExchangeRate('AED', parseFloat(e.target.value) || 0)}
                          placeholder="مثال: 13.5"
                          className="text-left"
                        />
                        <span className="text-sm text-gray-500">ج.م</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-2 mb-4">
                    <Button
                      onClick={resetToDefaultRates}
                      variant="outline"
                      size="sm"
                    >
                      <RefreshCw className="h-4 w-4 ml-2" />
                      إعادة تعيين للقيم الافتراضية
                    </Button>
                  </div>

                  <div className="text-xs text-gray-600">
                    <p className="mb-1">• الأسعار المحددة هنا ستطبق فوراً على جميع أجزاء النظام</p>
                    <p className="mb-1">• يتم حفظ الأسعار تلقائياً عند التغيير</p>
                    <p>• مثال: إذا كان 1 دولار = 50 جنيه، فإن 100 دولار = 5000 جنيه</p>
                  </div>
                </div>

                {/* Currency Conversion Status */}
                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-3">حالة تحويل العملة</h4>
                  <CurrencyConversionIndicator showRefreshButton className="mb-3" />

                  <div className="flex gap-2">
                    <Button
                      onClick={handleRefreshRates}
                      variant="outline"
                      size="sm"
                      disabled={isConverting}
                    >
                      <RefreshCw className={`h-4 w-4 ml-2 ${isConverting ? 'animate-spin' : ''}`} />
                      تحديث أسعار الصرف (معطل - الوضع اليدوي)
                    </Button>
                  </div>

                  <p className="text-xs text-gray-600 mt-2">
                    النظام يعمل بالوضع اليدوي لأسعار الصرف. العملة الأساسية للنظام هي الجنيه المصري (EGP).
                  </p>
                </div>

                <Button onClick={handleSaveSystem} className="mt-4">
                  <Save className="h-4 w-4 ml-2" />
                  حفظ التغييرات
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </UnifiedLayout>
  );
}
