'use client';

import { UnifiedLayout } from '@/components/layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { quotationsAPI, type Quotation, type QuotationStats } from '@/lib/api';
import { AlertTriangle, CheckCircle, Clock, FileText, Plus, Search, XCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

const QuotationsPage = () => {
  const router = useRouter();
  const [quotations, setQuotations] = useState<Quotation[]>([]);
  const [stats, setStats] = useState<QuotationStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');

  useEffect(() => {
    fetchQuotations();
    fetchStats();
  }, [statusFilter, priorityFilter, searchTerm]);

  const fetchQuotations = async () => {
    try {
      setLoading(true);
      const params: any = {};
      
      if (statusFilter !== 'all') {
        params.status = statusFilter;
      }
      
      if (priorityFilter !== 'all') {
        params.priority = priorityFilter;
      }
      
      if (searchTerm) {
        params.search = searchTerm;
      }

      const data = await quotationsAPI.getQuotations(params);
      setQuotations(data.results || data);
    } catch (error) {
      console.error('Error fetching quotations:', error);
      toast.error('حدث خطأ في تحميل العروض');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const data = await quotationsAPI.getStats();
      setStats(data);
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { label: 'مسودة', variant: 'secondary' as const, icon: FileText },
      pending: { label: 'في الانتظار', variant: 'default' as const, icon: Clock },
      approved: { label: 'موافق عليه', variant: 'default' as const, icon: CheckCircle },
      rejected: { label: 'مرفوض', variant: 'destructive' as const, icon: XCircle },
      expired: { label: 'منتهي الصلاحية', variant: 'secondary' as const, icon: AlertTriangle },
      converted: { label: 'تم تحويله', variant: 'default' as const, icon: CheckCircle }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { label: 'منخفض', className: 'bg-green-100 text-green-800' },
      medium: { label: 'متوسط', className: 'bg-yellow-100 text-yellow-800' },
      high: { label: 'عالي', className: 'bg-orange-100 text-orange-800' },
      urgent: { label: 'عاجل', className: 'bg-red-100 text-red-800' }
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;

    return (
      <Badge className={config.className}>
        {config.label}
      </Badge>
    );
  };

  const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(parseFloat(amount));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG');
  };

  return (
    <UnifiedLayout>
      {/* Header - Enhanced Responsive */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
        <div className="flex-1">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mobile-truncate">إدارة العروض</h1>
          <p className="text-gray-600 mt-1 text-sm sm:text-base">إدارة عروض الأسعار والخدمات</p>
        </div>
        <Button
          onClick={() => router.push('/founder-dashboard/quotations/new')}
          className="flex items-center gap-2 touch-target w-fit"
        >
          <Plus className="w-4 h-4" />
          <span className="hidden sm:inline">عرض جديد</span>
          <span className="sm:hidden">جديد</span>
        </Button>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي العروض</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_quotations}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">في الانتظار</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.pending_quotations}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">معدل التحويل</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.conversion_rate}%</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي القيمة</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.total_value)}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>البحث والتصفية</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="البحث في العروض..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="draft">مسودة</SelectItem>
                <SelectItem value="pending">في الانتظار</SelectItem>
                <SelectItem value="approved">موافق عليه</SelectItem>
                <SelectItem value="rejected">مرفوض</SelectItem>
                <SelectItem value="expired">منتهي الصلاحية</SelectItem>
                <SelectItem value="converted">تم تحويله</SelectItem>
              </SelectContent>
            </Select>

            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="الأولوية" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الأولويات</SelectItem>
                <SelectItem value="low">منخفض</SelectItem>
                <SelectItem value="medium">متوسط</SelectItem>
                <SelectItem value="high">عالي</SelectItem>
                <SelectItem value="urgent">عاجل</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Quotations List */}
      <div className="space-y-4">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">جاري التحميل...</p>
          </div>
        ) : quotations.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد عروض</h3>
              <p className="text-gray-600 mb-4">لم يتم العثور على أي عروض أسعار</p>
              <Button onClick={() => router.push('/founder-dashboard/quotations/new')}>
                إنشاء عرض جديد
              </Button>
            </CardContent>
          </Card>
        ) : (
          quotations.map((quotation) => (
            <Card key={quotation.id} className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {quotation.title}
                      </h3>
                      {getStatusBadge(quotation.status)}
                      {getPriorityBadge(quotation.priority)}
                    </div>
                    <p className="text-sm text-gray-600 mb-2">
                      رقم العرض: {quotation.quotation_number}
                    </p>
                    <p className="text-sm text-gray-600">
                      العميل: {quotation.client.name} - {quotation.client.company || 'فردي'}
                    </p>
                  </div>
                  
                  <div className="text-left">
                    <div className="text-2xl font-bold text-gray-900 mb-1">
                      {formatCurrency(quotation.total_amount)}
                    </div>
                    <div className="text-sm text-gray-600">
                      {quotation.items_count} عنصر
                    </div>
                  </div>
                </div>

                <div className="flex justify-between items-center text-sm text-gray-600">
                  <div className="flex items-center gap-4">
                    <span>تاريخ الإنشاء: {formatDate(quotation.created_at)}</span>
                    <span>صالح حتى: {formatDate(quotation.valid_until)}</span>
                    {quotation.is_expired && (
                      <Badge variant="destructive" className="text-xs">
                        منتهي الصلاحية
                      </Badge>
                    )}
                  </div>
                  
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => router.push(`/founder-dashboard/quotations/${quotation.id}`)}
                  >
                    عرض التفاصيل
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </UnifiedLayout>
  );
};

export default QuotationsPage;
