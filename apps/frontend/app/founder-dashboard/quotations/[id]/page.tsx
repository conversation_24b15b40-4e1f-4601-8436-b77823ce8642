'use client';

import { ArrowRight, CheckCircle, Download, Edit, FileText, XCircle } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { UnifiedLayout } from '@/components/layout';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { quotationsAPI, type Quotation } from '@/lib/api';
import { toast } from 'sonner';

const QuotationDetailPage = () => {
  const params = useParams();
  const router = useRouter();
  const [quotation, setQuotation] = useState<Quotation | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (params.id) {
      fetchQuotation();
    }
  }, [params.id]);

  const fetchQuotation = async () => {
    try {
      setLoading(true);
      const data = await quotationsAPI.getQuotation(Number(params.id));
      setQuotation(data);
    } catch (error) {
      console.error('Error fetching quotation:', error);
      toast.error('حدث خطأ في تحميل العرض');
      router.push('/founder-dashboard/quotations');
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async () => {
    if (!quotation) return;

    try {
      await quotationsAPI.approveQuotation(quotation.id, 'approve');
      toast.success('تم الموافقة على العرض بنجاح');
      fetchQuotation();
    } catch (error) {
      console.error('Error approving quotation:', error);
      toast.error('حدث خطأ في الموافقة على العرض');
    }
  };

  const handleReject = async () => {
    if (!quotation) return;

    try {
      await quotationsAPI.approveQuotation(quotation.id, 'reject');
      toast.success('تم رفض العرض');
      fetchQuotation();
    } catch (error) {
      console.error('Error rejecting quotation:', error);
      toast.error('حدث خطأ في رفض العرض');
    }
  };

  const handleConvertToProject = async () => {
    if (!quotation) return;

    try {
      const result = await quotationsAPI.convertToProject(quotation.id);
      toast.success('تم تحويل العرض إلى مشروع بنجاح');
      router.push(`/founder-dashboard/projects/${result.project_id}`);
    } catch (error) {
      console.error('Error converting to project:', error);
      toast.error('حدث خطأ في تحويل العرض إلى مشروع');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { label: 'مسودة', variant: 'secondary' as const },
      pending: { label: 'في الانتظار', variant: 'default' as const },
      approved: { label: 'موافق عليه', variant: 'default' as const },
      rejected: { label: 'مرفوض', variant: 'destructive' as const },
      expired: { label: 'منتهي الصلاحية', variant: 'secondary' as const },
      converted: { label: 'تم تحويله', variant: 'default' as const }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;

    return (
      <Badge variant={config.variant}>
        {config.label}
      </Badge>
    );
  };

  const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(parseFloat(amount));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG');
  };

  if (loading) {
    return (
      <UnifiedLayout>
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">جاري التحميل...</p>
        </div>
      </UnifiedLayout>
    );
  }

  if (!quotation) {
    return (
      <UnifiedLayout>
        <div className="text-center py-8">
          <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">العرض غير موجود</h3>
          <p className="text-gray-600 mb-4">لم يتم العثور على العرض المطلوب</p>
          <Button onClick={() => router.push('/founder-dashboard/quotations')} className="touch-target">
            العودة إلى العروض
          </Button>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      {/* Page Header - Following GRS (Global Responsive System) */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center gap-2 w-fit touch-target"
          >
            <ArrowRight className="w-4 h-4" />
            رجوع
          </Button>
          <div className="flex-1 min-w-0">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mobile-truncate">{quotation.title}</h1>
            <p className="text-gray-600 mt-1 text-sm sm:text-base">رقم العرض: {quotation.quotation_number}</p>
          </div>
        </div>

        <div className="flex items-center gap-2 flex-wrap">
          {getStatusBadge(quotation.status)}
          {quotation.is_expired && (
            <Badge variant="destructive">منتهي الصلاحية</Badge>
          )}
        </div>
      </div>

      {/* Action Buttons - Enhanced Mobile Layout */}
      <div className="flex gap-2 flex-wrap">
        {quotation.status === 'pending' && !quotation.is_expired && (
          <>
            <Button onClick={handleApprove} className="flex items-center gap-2 touch-target">
              <CheckCircle className="w-4 h-4" />
              <span className="hidden sm:inline">موافقة</span>
              <span className="sm:hidden">موافقة</span>
            </Button>
            <Button variant="outline" onClick={handleReject} className="flex items-center gap-2 touch-target">
              <XCircle className="w-4 h-4" />
              <span className="hidden sm:inline">رفض</span>
              <span className="sm:hidden">رفض</span>
            </Button>
          </>
        )}

        {quotation.status === 'approved' && !quotation.converted_to_project && (
          <Button onClick={handleConvertToProject} className="flex items-center gap-2 touch-target">
            <FileText className="w-4 h-4" />
            <span className="hidden sm:inline">تحويل إلى مشروع</span>
            <span className="sm:hidden">تحويل</span>
          </Button>
        )}

        <Button variant="outline" className="flex items-center gap-2 touch-target">
          <Download className="w-4 h-4" />
          <span className="hidden sm:inline">تحميل PDF</span>
          <span className="sm:hidden">PDF</span>
        </Button>

        {quotation.status === 'draft' && (
          <Button variant="outline" className="flex items-center gap-2 touch-target">
            <Edit className="w-4 h-4" />
            <span className="hidden sm:inline">تعديل</span>
            <span className="sm:hidden">تعديل</span>
          </Button>
        )}
      </div>

      {/* Main Content Grid - Enhanced Responsive */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Client Information - CPR (Component Pattern Reusability) */}
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">معلومات العميل</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                  <span className="font-medium text-gray-600">الاسم:</span>
                  <span className="font-medium mobile-truncate">{quotation.client.name}</span>
                </div>
                <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                  <span className="font-medium text-gray-600">البريد الإلكتروني:</span>
                  <span className="mobile-truncate">{quotation.client.email}</span>
                </div>
                {quotation.client.company && (
                  <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                    <span className="font-medium text-gray-600">الشركة:</span>
                    <span className="mobile-truncate">{quotation.client.company}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Items Section - SMA (Shared Module Architecture) */}
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">عناصر العرض</CardTitle>
              <CardDescription>{quotation.items.length} عنصر</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {quotation.items.map((item, index) => (
                  <div key={item.id} className="border rounded-lg p-4 bg-gray-50/50">
                    <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-3 mb-3">
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm sm:text-base mobile-truncate">{item.service.name_ar}</h4>
                        <p className="text-xs sm:text-sm text-gray-600 mt-1">{item.description}</p>
                      </div>
                      <div className="text-left lg:text-right">
                        <div className="font-bold text-blue-600 text-sm sm:text-base">{formatCurrency(item.total_price)}</div>
                        <div className="text-xs sm:text-sm text-gray-600">
                          {item.quantity} × {formatCurrency(item.unit_price)}
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 text-xs sm:text-sm text-gray-600">
                      <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
                        <span>التعقيد: {item.complexity_level_display}</span>
                        <span>الساعات المقدرة: {item.estimated_hours}</span>
                      </div>
                      <Badge variant="outline" className="w-fit">{item.service.category_display}</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Description and Notes - Enhanced Layout */}
          {(quotation.description || quotation.notes) && (
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">تفاصيل إضافية</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {quotation.description && (
                  <div>
                    <h4 className="font-medium mb-2 text-sm sm:text-base">وصف العرض</h4>
                    <p className="text-gray-700 text-sm sm:text-base leading-relaxed">{quotation.description}</p>
                  </div>
                )}

                {quotation.notes && (
                  <div>
                    <h4 className="font-medium mb-2 text-sm sm:text-base">ملاحظات</h4>
                    <p className="text-gray-700 text-sm sm:text-base leading-relaxed">{quotation.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Terms and Conditions - Enhanced Layout */}
          {quotation.terms_conditions && (
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">الشروط والأحكام</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 whitespace-pre-line text-sm sm:text-base leading-relaxed">{quotation.terms_conditions}</p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar - BOP (Business Object Patterns) */}
        <div className="space-y-6">
          {/* Summary - Enhanced Financial Display */}
          <Card className="shadow-sm border-blue-200">
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl text-blue-700">ملخص العرض</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center text-sm sm:text-base">
                <span className="text-gray-600">المجموع الفرعي:</span>
                <span className="font-medium">{formatCurrency(quotation.subtotal)}</span>
              </div>

              {parseFloat(quotation.discount_amount) > 0 && (
                <div className="flex justify-between items-center text-green-600 text-sm sm:text-base">
                  <span>الخصم ({quotation.discount_percentage}%):</span>
                  <span className="font-medium">-{formatCurrency(quotation.discount_amount)}</span>
                </div>
              )}

              <div className="flex justify-between items-center text-sm sm:text-base">
                <span className="text-gray-600">الضريبة ({quotation.tax_percentage}%):</span>
                <span className="font-medium">{formatCurrency(quotation.tax_amount)}</span>
              </div>

              <Separator className="border-gray-300" />

              <div className="flex justify-between items-center text-lg sm:text-xl font-bold text-blue-700">
                <span>الإجمالي:</span>
                <span>{formatCurrency(quotation.total_amount)}</span>
              </div>
            </CardContent>
          </Card>

          {/* Details - Enhanced Information Display */}
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">تفاصيل العرض</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                <span className="text-gray-600 text-sm sm:text-base">الأولوية:</span>
                <span className="font-medium text-sm sm:text-base">{quotation.priority_display}</span>
              </div>

              <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                <span className="text-gray-600 text-sm sm:text-base">تاريخ الإنشاء:</span>
                <span className="font-medium text-sm sm:text-base">{formatDate(quotation.created_at)}</span>
              </div>

              <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                <span className="text-gray-600 text-sm sm:text-base">صالح حتى:</span>
                <span className="font-medium text-sm sm:text-base">{formatDate(quotation.valid_until)}</span>
              </div>

              {quotation.approved_at && (
                <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                  <span className="text-gray-600 text-sm sm:text-base">تاريخ الموافقة:</span>
                  <span className="font-medium text-sm sm:text-base">{formatDate(quotation.approved_at)}</span>
                </div>
              )}

              <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0">
                <span className="text-gray-600 text-sm sm:text-base">مندوب المبيعات:</span>
                <span className="font-medium text-sm sm:text-base mobile-truncate">{quotation.sales_rep.first_name} {quotation.sales_rep.last_name}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </UnifiedLayout>
  );
};

export default QuotationDetailPage;
