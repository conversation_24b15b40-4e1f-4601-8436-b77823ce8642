'use client';

import { <PERSON>R<PERSON>, Plus, Save, Trash2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';

import { UnifiedLayout } from '@/components/layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { quotationsAPI, serviceCatalogAPI, type CreateQuotationData, type ServiceCatalog } from '@/lib/api';
import { cn } from '@/lib/utils';

interface QuotationItem {
  service_id: number;
  service?: ServiceCatalog;
  description: string;
  quantity: number;
  unit_price: number;
  complexity_level: string;
  estimated_hours: number;
  total_price: number;
}

const NewQuotationPage = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [services, setServices] = useState<ServiceCatalog[]>([]);
  const [clients, setClients] = useState<any[]>([]);

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    client_id: '',
    priority: 'medium',
    discount_percentage: 0,
    tax_percentage: 14,
    valid_until: '',
    notes: '',
    terms_conditions: ''
  });

  const [items, setItems] = useState<QuotationItem[]>([]);

  useEffect(() => {
    fetchServices();
    fetchClients();

    // Set default validity date (30 days from now)
    const defaultDate = new Date();
    defaultDate.setDate(defaultDate.getDate() + 30);
    setFormData(prev => ({
      ...prev,
      valid_until: defaultDate.toISOString().split('T')[0]
    }));
  }, []);

  const fetchServices = async () => {
    try {
      const data = await serviceCatalogAPI.getServices({ is_active: true });
      setServices(data.results || data);
    } catch (error) {
      console.error('Error fetching services:', error);
      toast.error('حدث خطأ في تحميل الخدمات');
    }
  };

  const fetchClients = async () => {
    try {
      // Import clients API
      const { clientsAPI } = await import('@/lib/api');
      const data = await clientsAPI.getClients();
      setClients(data.results || data);
    } catch (error) {
      console.error('Error fetching clients:', error);
      toast.error('حدث خطأ في تحميل العملاء');
    }
  };

  const addItem = () => {
    const newItem: QuotationItem = {
      service_id: 0,
      description: '',
      quantity: 1,
      unit_price: 0,
      complexity_level: 'medium',
      estimated_hours: 8,
      total_price: 0
    };
    setItems([...items, newItem]);
  };

  const removeItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const updateItem = (index: number, field: keyof QuotationItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };

    // Auto-calculate total price
    if (field === 'quantity' || field === 'unit_price') {
      updatedItems[index].total_price = updatedItems[index].quantity * updatedItems[index].unit_price;
    }

    // Auto-fill from service
    if (field === 'service_id') {
      const service = services.find(s => s.id === value);
      if (service) {
        updatedItems[index].service = service;
        updatedItems[index].description = service.description_ar;
        updatedItems[index].unit_price = parseFloat(service.base_price);
        updatedItems[index].estimated_hours = service.estimated_hours_min;
        updatedItems[index].total_price = updatedItems[index].quantity * parseFloat(service.base_price);
      }
    }

    setItems(updatedItems);
  };

  const calculateTotals = () => {
    const subtotal = items.reduce((sum, item) => sum + item.total_price, 0);
    const discountAmount = subtotal * (formData.discount_percentage / 100);
    const afterDiscount = subtotal - discountAmount;
    const taxAmount = afterDiscount * (formData.tax_percentage / 100);
    const total = afterDiscount + taxAmount;

    return {
      subtotal,
      discountAmount,
      taxAmount,
      total
    };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title || !formData.client_id || items.length === 0) {
      toast.error('يرجى ملء جميع الحقول المطلوبة وإضافة عنصر واحد على الأقل');
      return;
    }

    setLoading(true);

    try {
      const quotationData: CreateQuotationData = {
        ...formData,
        client_id: parseInt(formData.client_id),
        items_data: items.map(item => ({
          service: item.service_id,
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unit_price,
          complexity_level: item.complexity_level,
          estimated_hours: item.estimated_hours
        }))
      };

      const result = await quotationsAPI.createQuotation(quotationData);
      toast.success('تم إنشاء العرض بنجاح');
      router.push(`/founder-dashboard/quotations/${result.id}`);
    } catch (error: any) {
      console.error('Error creating quotation:', error);
      toast.error(error.response?.data?.message || 'حدث خطأ في إنشاء العرض');
    } finally {
      setLoading(false);
    }
  };

  const totals = calculateTotals();

  return (
    <UnifiedLayout>
      {/* Page Header - Following GRS (Global Responsive System) */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-6">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
          className="flex items-center gap-2 w-fit touch-target"
        >
          <ArrowRight className="w-4 h-4" />
          رجوع
        </Button>
        <div className="flex-1">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mobile-truncate">إنشاء عرض سعر جديد</h1>
          <p className="text-gray-600 mt-1 text-sm sm:text-base">إنشاء عرض سعر احترافي للعملاء</p>
        </div>
      </div>

      {/* Form Section - Following VST (Visual Style Tokens) and GRS patterns */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information Card - CPR (Component Pattern Reusability) */}
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg sm:text-xl">المعلومات الأساسية</CardTitle>
            <CardDescription>معلومات العرض الأساسية</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Responsive Grid - GRS Implementation */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title" className="text-sm font-medium">عنوان العرض *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="مثال: تطوير موقع إلكتروني"
                  className="touch-target"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="client" className="text-sm font-medium">العميل *</Label>
                <Select value={formData.client_id} onValueChange={(value) => setFormData(prev => ({ ...prev, client_id: value }))}>
                  <SelectTrigger className="touch-target">
                    <SelectValue placeholder="اختر العميل" />
                  </SelectTrigger>
                  <SelectContent>
                    {clients.map((client) => (
                      <SelectItem key={client.id} value={client.id.toString()}>
                        {client.name} - {client.company || 'فردي'}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Description Field */}
            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium">وصف العرض</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="وصف تفصيلي للعرض..."
                rows={3}
                className="touch-target resize-none"
              />
            </div>

            {/* Priority and Settings Grid - Enhanced Responsive */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="priority" className="text-sm font-medium">الأولوية</Label>
                <Select value={formData.priority} onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}>
                  <SelectTrigger className="touch-target">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">منخفض</SelectItem>
                    <SelectItem value="medium">متوسط</SelectItem>
                    <SelectItem value="high">عالي</SelectItem>
                    <SelectItem value="urgent">عاجل</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="valid_until" className="text-sm font-medium">صالح حتى</Label>
                <Input
                  id="valid_until"
                  type="date"
                  value={formData.valid_until}
                  onChange={(e) => setFormData(prev => ({ ...prev, valid_until: e.target.value }))}
                  className="touch-target"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="tax_percentage" className="text-sm font-medium">نسبة الضريبة (%)</Label>
                <Input
                  id="tax_percentage"
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={formData.tax_percentage}
                  onChange={(e) => setFormData(prev => ({ ...prev, tax_percentage: parseFloat(e.target.value) || 0 }))}
                  className="touch-target"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Items Section - SMA (Shared Module Architecture) */}
        <Card className="shadow-sm">
          <CardHeader>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
              <div>
                <CardTitle className="text-lg sm:text-xl">عناصر العرض</CardTitle>
                <CardDescription>الخدمات والمنتجات المتضمنة في العرض</CardDescription>
              </div>
              <Button
                type="button"
                onClick={addItem}
                className="flex items-center gap-2 touch-target w-fit"
                size="sm"
              >
                <Plus className="w-4 h-4" />
                إضافة عنصر
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {items.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <div className="mb-4">
                  <Plus className="w-12 h-12 mx-auto text-gray-300" />
                </div>
                <p className="text-sm sm:text-base">لا توجد عناصر. اضغط "إضافة عنصر" لبدء إنشاء العرض.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {items.map((item, index) => (
                  <div key={index} className="border rounded-lg p-4 space-y-4 bg-gray-50/50">
                    <div className="flex justify-between items-center">
                      <h4 className="font-medium text-sm sm:text-base">عنصر {index + 1}</h4>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeItem(index)}
                        className="text-red-600 hover:text-red-700 touch-target"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>

                    {/* Item Fields Grid - Enhanced Responsive */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">الخدمة</Label>
                        <Select
                          value={item.service_id.toString()}
                          onValueChange={(value) => updateItem(index, 'service_id', parseInt(value))}
                        >
                          <SelectTrigger className="touch-target">
                            <SelectValue placeholder="اختر الخدمة" />
                          </SelectTrigger>
                          <SelectContent>
                            {services.map((service) => (
                              <SelectItem key={service.id} value={service.id.toString()}>
                                {service.name_ar}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium">الكمية</Label>
                        <Input
                          type="number"
                          min="1"
                          value={item.quantity}
                          onChange={(e) => updateItem(index, 'quantity', parseInt(e.target.value) || 1)}
                          className="touch-target"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium">سعر الوحدة (ج.م)</Label>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          value={item.unit_price}
                          onChange={(e) => updateItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                          className="touch-target"
                        />
                      </div>
                    </div>

                    {/* Description Field */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">الوصف</Label>
                      <Textarea
                        value={item.description}
                        onChange={(e) => updateItem(index, 'description', e.target.value)}
                        placeholder="وصف الخدمة..."
                        rows={2}
                        className="touch-target resize-none"
                      />
                    </div>

                    {/* Complexity and Hours Grid */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">مستوى التعقيد</Label>
                        <Select
                          value={item.complexity_level}
                          onValueChange={(value) => updateItem(index, 'complexity_level', value)}
                        >
                          <SelectTrigger className="touch-target">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="light">بسيط</SelectItem>
                            <SelectItem value="medium">متوسط</SelectItem>
                            <SelectItem value="complex">معقد</SelectItem>
                            <SelectItem value="enterprise">مؤسسي</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium">الساعات المقدرة</Label>
                        <Input
                          type="number"
                          min="1"
                          value={item.estimated_hours}
                          onChange={(e) => updateItem(index, 'estimated_hours', parseInt(e.target.value) || 1)}
                          className="touch-target"
                        />
                      </div>
                    </div>

                    {/* Item Total - Enhanced Display */}
                    <div className="flex justify-between items-center pt-2 border-t border-gray-200">
                      <span className="text-sm text-gray-600">إجمالي العنصر:</span>
                      <span className="text-lg font-bold text-blue-600">
                        {item.total_price.toLocaleString('ar-EG')} ج.م
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Totals Section - BOP (Business Object Patterns) */}
        {items.length > 0 && (
          <Card className="shadow-sm border-blue-200">
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl text-blue-700">ملخص العرض</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center text-sm sm:text-base">
                  <span className="text-gray-600">المجموع الفرعي:</span>
                  <span className="font-medium">{totals.subtotal.toLocaleString('ar-EG')} ج.م</span>
                </div>

                {formData.discount_percentage > 0 && (
                  <div className="flex justify-between items-center text-green-600 text-sm sm:text-base">
                    <span>الخصم ({formData.discount_percentage}%):</span>
                    <span className="font-medium">-{totals.discountAmount.toLocaleString('ar-EG')} ج.م</span>
                  </div>
                )}

                <div className="flex justify-between items-center text-sm sm:text-base">
                  <span className="text-gray-600">الضريبة ({formData.tax_percentage}%):</span>
                  <span className="font-medium">{totals.taxAmount.toLocaleString('ar-EG')} ج.م</span>
                </div>

                <hr className="border-gray-300" />

                <div className="flex justify-between items-center text-lg sm:text-xl font-bold text-blue-700">
                  <span>الإجمالي النهائي:</span>
                  <span>{totals.total.toLocaleString('ar-EG')} ج.م</span>
                </div>
              </div>

              {/* Discount Input */}
              <div className="mt-6 space-y-2">
                <Label htmlFor="discount_percentage" className="text-sm font-medium">نسبة الخصم (%)</Label>
                <Input
                  id="discount_percentage"
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={formData.discount_percentage}
                  onChange={(e) => setFormData(prev => ({ ...prev, discount_percentage: parseFloat(e.target.value) || 0 }))}
                  className="touch-target max-w-xs"
                />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Additional Information Section */}
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg sm:text-xl">معلومات إضافية</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="notes" className="text-sm font-medium">ملاحظات</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="ملاحظات إضافية..."
                rows={3}
                className="touch-target resize-none"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="terms_conditions" className="text-sm font-medium">الشروط والأحكام</Label>
              <Textarea
                id="terms_conditions"
                value={formData.terms_conditions}
                onChange={(e) => setFormData(prev => ({ ...prev, terms_conditions: e.target.value }))}
                placeholder="الشروط والأحكام..."
                rows={4}
                className="touch-target resize-none"
              />
            </div>
          </CardContent>
        </Card>

        {/* Submit Buttons - Enhanced Mobile Layout */}
        <div className="flex flex-col sm:flex-row justify-end gap-3 pt-6 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={loading}
            className="touch-target order-2 sm:order-1"
          >
            إلغاء
          </Button>
          <Button
            type="submit"
            disabled={loading}
            className={cn(
              "flex items-center justify-center gap-2 touch-target order-1 sm:order-2",
              loading && "cursor-not-allowed"
            )}
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>جاري الحفظ...</span>
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                <span>حفظ العرض</span>
              </>
            )}
          </Button>
        </div>
      </form>
    </UnifiedLayout>
  );
};

export default NewQuotationPage;
