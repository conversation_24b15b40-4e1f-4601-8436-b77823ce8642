'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  CheckSquare,
  Calendar,
  User,
  Clock,
  Flag,
  FolderOpen,
  Edit,
  Trash2,
  ArrowLeft,
  ExternalLink,
  TrendingUp,
  MessageSquare,
  Paperclip,
  CheckCircle,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';
import { DEMO_TASKS } from '@/lib/demo-data';
import { formatRelativeTime } from '@mtbrmg/shared';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';
import { useTask } from '@/lib/hooks/use-tasks';
import { EditTaskForm } from '@/components/forms/edit-task-form';
import { DeleteTaskDialog } from '@/components/dialogs/delete-task-dialog';
import { showToast } from '@/lib/toast';

export default function TaskDetailPage() {
  const router = useRouter();
  const params = useParams();
  const { isAuthenticated, user } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // API hook for task data
  const { data: apiTask, isLoading, error } = useTask(params.id as string);
  
  // Use API data if available, fallback to demo data
  const task = apiTask || DEMO_TASKS.find(t => t.id === params.id);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  useEffect(() => {
    if (mounted && !task && !isLoading) {
      showToast.error('المهمة غير موجودة');
      router.push('/founder-dashboard/tasks');
    }
  }, [task, mounted, isLoading, router]);

  if (!mounted || !isAuthenticated) {
    return null;
  }

  if (isLoading) {
    return (
      <UnifiedLayout>
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  if (!task) {
    return (
      <UnifiedLayout>
        <div className="max-w-4xl mx-auto text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">المهمة غير موجودة</h1>
          <Button onClick={() => router.push('/founder-dashboard/tasks')}>
            العودة إلى قائمة المهام
          </Button>
        </div>
      </UnifiedLayout>
    );
  }

  const getStatusBadge = (status: string) => {
    const statusMap = {
      'todo': { label: 'قائمة المهام', className: 'bg-gray-100 text-gray-800' },
      'in_progress': { label: 'قيد التنفيذ', className: 'bg-blue-100 text-blue-800' },
      'review': { label: 'مراجعة', className: 'bg-yellow-100 text-yellow-800' },
      'completed': { label: 'مكتملة', className: 'bg-green-100 text-green-800' },
      'cancelled': { label: 'ملغية', className: 'bg-red-100 text-red-800' }
    };
    const statusInfo = statusMap[status as keyof typeof statusMap] || statusMap.todo;
    return <Badge className={statusInfo.className}>{statusInfo.label}</Badge>;
  };

  const getPriorityBadge = (priority: string) => {
    const priorityMap = {
      'low': { label: 'منخفض', className: 'bg-green-100 text-green-800' },
      'medium': { label: 'متوسط', className: 'bg-yellow-100 text-yellow-800' },
      'high': { label: 'عالي', className: 'bg-orange-100 text-orange-800' },
      'urgent': { label: 'عاجل', className: 'bg-red-100 text-red-800' }
    };
    const priorityInfo = priorityMap[priority as keyof typeof priorityMap] || priorityMap.medium;
    return <Badge className={priorityInfo.className}>{priorityInfo.label}</Badge>;
  };

  const handleEdit = () => {
    setIsEditModalOpen(true);
  };

  const handleDelete = () => {
    setIsDeleteDialogOpen(true);
  };

  const handleBack = () => {
    router.push('/founder-dashboard/tasks');
  };

  return (
    <UnifiedLayout>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              العودة إلى المهام
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <CheckSquare className="h-8 w-8 text-purple-600" />
                {task.title}
              </h1>
              <p className="text-gray-600 mt-1">
                تفاصيل المهمة ومعلومات التنفيذ
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleEdit}
              className="flex items-center gap-2"
            >
              <Edit className="h-4 w-4" />
              تعديل
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDelete}
              className="flex items-center gap-2 text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
              حذف
            </Button>
          </div>
        </div>

        {/* Task Details */}
        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">معلومات أساسية</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">الوصف</h3>
                  <p className="text-gray-600">
                    {task.description || 'لا يوجد وصف متاح'}
                  </p>
                </div>
                
                <Separator />
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-2">
                    <Flag className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600">الحالة:</span>
                    {getStatusBadge(task.status)}
                  </div>
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600">الأولوية:</span>
                    {getPriorityBadge(task.priority)}
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600">الوقت المقدر:</span>
                    <span className="font-medium">{task.estimated_hours} ساعة</span>
                  </div>
                </div>

                <Separator />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600">تاريخ البداية:</span>
                    <span className="font-medium">
                      {task.start_date ? new Date(task.start_date).toLocaleDateString('ar-EG') : 'غير محدد'}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600">تاريخ الانتهاء:</span>
                    <span className="font-medium">
                      {task.due_date ? new Date(task.due_date).toLocaleDateString('ar-EG') : 'غير محدد'}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Assignment Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl flex items-center gap-2">
                <User className="h-5 w-5" />
                معلومات التكليف
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {task.project && (
                <div className="flex items-center gap-2">
                  <FolderOpen className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">المشروع:</span>
                  <span className="font-medium">
                    {task.project_name ||
                     (typeof task.project === 'object' && task.project?.name) ||
                     (typeof task.project === 'string' ? task.project : 'غير محدد')}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => router.push(`/founder-dashboard/projects/${
                      typeof task.project === 'object' ? task.project?.id : task.project
                    }`)}
                    className="ml-2"
                  >
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                </div>
              )}
              {task.assigned_to && (
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">مكلف إلى:</span>
                  <span className="font-medium">
                    {task.assigned_to_name ||
                     (Array.isArray(task.assigned_to)
                       ? task.assigned_to.join(', ')
                       : task.assigned_to)}
                  </span>
                </div>
              )}
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600">تم الإنشاء بواسطة:</span>
                <span className="font-medium">
                  {task.created_by_name ||
                   (typeof task.created_by === 'object' && task.created_by?.full_name) ||
                   (typeof task.created_by === 'object' && task.created_by?.username) ||
                   (typeof task.created_by === 'string' ? task.created_by : user?.username)}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600">تاريخ الإنشاء:</span>
                <span className="font-medium">
                  {task.created_at ? formatRelativeTime(task.created_at) : 'غير محدد'}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Progress and Time Tracking */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl flex items-center gap-2">
                <Clock className="h-5 w-5" />
                تتبع التقدم والوقت
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-600">التقدم</span>
                    <span className="text-sm font-medium">{task.progress || 0}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${task.progress || 0}%` }}
                    ></div>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">{task.estimated_hours || 0}</div>
                    <div className="text-sm text-gray-600">ساعات مقدرة</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{task.actual_hours || 0}</div>
                    <div className="text-sm text-gray-600">ساعات فعلية</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {task.estimated_hours ? Math.max(0, task.estimated_hours - (task.actual_hours || 0)) : 0}
                    </div>
                    <div className="text-sm text-gray-600">ساعات متبقية</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Edit Task Modal */}
        <EditTaskForm
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSubmit={(data) => {
            // Handle task update
            console.log('Update task:', data);

            // Prepare data for submission, converting "none" values to null
            const submitData = {
              ...data,
              project: data.project === 'none' ? null : data.project || null,
              assigned_to: data.assigned_to === 'none' ? null : data.assigned_to || null,
            };

            console.log('Submitting processed edit data:', submitData);
            // TODO: Implement actual task update API call
            setIsEditModalOpen(false);
            showToast.success('تم تحديث المهمة بنجاح');
          }}
          task={task}
        />

        {/* Delete Task Dialog */}
        <DeleteTaskDialog
          isOpen={isDeleteDialogOpen}
          onClose={() => setIsDeleteDialogOpen(false)}
          onConfirm={() => {
            // Handle task deletion
            console.log('Delete task:', task.id);
            setIsDeleteDialogOpen(false);
            showToast.success('تم حذف المهمة بنجاح');
            router.push('/founder-dashboard/tasks');
          }}
          task={task}
        />
      </div>
    </UnifiedLayout>
  );
}
