'use client';

import { UnifiedLayout } from '@/components/layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useContract, useContractMutations } from '@/lib/hooks/use-contracts';
import { useAuthStore } from '@/lib/stores/auth-store';
import { formatCurrency, formatDate } from '@/lib/utils';
import {
    CONTRACT_PRIORITY_OPTIONS,
    CONTRACT_STATUS_OPTIONS,
} from '@/types/contracts';
import {
    AlertTriangle,
    ArrowLeft,
    CheckCircle,
    Clock,
    DollarSign,
    Download,
    Edit,
    FileSignature,
    FileText,
    RefreshCw,
    User,
    XCircle
} from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function ContractDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);

  const contractId = params.id as string;
  const { data: contract, isLoading, error, refetch } = useContract(contractId);
  const { 
    activateContract, 
    completeContract, 
    generatePDF,
    deleteContract 
  } = useContractMutations();

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل تفاصيل العقد...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى تفاصيل العقد</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <UnifiedLayout>
        <div className="max-w-4xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
              <p className="text-gray-600">جاري تحميل تفاصيل العقد...</p>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  // Error state
  if (error || !contract) {
    return (
      <UnifiedLayout>
        <div className="max-w-4xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل العقد</h3>
              <p className="text-gray-600 mb-4">
                {error ? 'حدث خطأ أثناء تحميل تفاصيل العقد' : 'العقد غير موجود'}
              </p>
              <div className="flex gap-2 justify-center">
                <Button onClick={() => refetch()} variant="outline">
                  <RefreshCw className="h-4 w-4 ml-2" />
                  إعادة المحاولة
                </Button>
                <Button onClick={() => router.push('/founder-dashboard/contracts')}>
                  <ArrowLeft className="h-4 w-4 ml-2" />
                  العودة للعقود
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  // Handle contract actions
  const handleBack = () => {
    router.push('/founder-dashboard/contracts');
  };

  const handleEdit = () => {
    router.push(`/founder-dashboard/contracts/${contract.id}/edit`);
  };

  const handleActivate = async () => {
    activateContract.mutate(contract.id);
  };

  const handleComplete = async () => {
    if (confirm('هل أنت متأكد من إكمال هذا العقد؟')) {
      completeContract.mutate(contract.id);
    }
  };

  const handleDownloadPDF = async () => {
    generatePDF.mutate(contract.id);
  };

  const handleDelete = async () => {
    if (confirm('هل أنت متأكد من حذف هذا العقد؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      deleteContract.mutate(contract.id, {
        onSuccess: () => {
          router.push('/founder-dashboard/contracts');
        }
      });
    }
  };

  // Get status badge component
  const getStatusBadge = (status: string) => {
    const statusOption = CONTRACT_STATUS_OPTIONS.find(option => option.value === status);
    if (!statusOption) return <Badge variant="outline">{status}</Badge>;

    const colorClasses = {
      gray: 'bg-gray-100 text-gray-800',
      orange: 'bg-orange-100 text-orange-800',
      blue: 'bg-blue-100 text-blue-800',
      green: 'bg-green-100 text-green-800',
      purple: 'bg-purple-100 text-purple-800',
      red: 'bg-red-100 text-red-800',
    };

    return (
      <Badge className={colorClasses[statusOption.color as keyof typeof colorClasses]}>
        {statusOption.label}
      </Badge>
    );
  };

  // Get priority badge component
  const getPriorityBadge = (priority: string) => {
    const priorityOption = CONTRACT_PRIORITY_OPTIONS.find(option => option.value === priority);
    if (!priorityOption) return <Badge variant="outline">{priority}</Badge>;

    const colorClasses = {
      green: 'bg-green-100 text-green-800',
      yellow: 'bg-yellow-100 text-yellow-800',
      orange: 'bg-orange-100 text-orange-800',
      red: 'bg-red-100 text-red-800',
    };

    return (
      <Badge variant="outline" className={colorClasses[priorityOption.color as keyof typeof colorClasses]}>
        {priorityOption.label}
      </Badge>
    );
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'expired':
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      case 'cancelled':
        return <XCircle className="h-5 w-5 text-gray-600" />;
      case 'pending_signature':
        return <FileSignature className="h-5 w-5 text-blue-600" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-purple-600" />;
      case 'pending_review':
        return <Clock className="h-5 w-5 text-orange-600" />;
      default:
        return <FileText className="h-5 w-5 text-gray-600" />;
    }
  };

  return (
    <UnifiedLayout>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة للعقود
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                {getStatusIcon(contract.status)}
                {contract.contract_number}
              </h1>
              <p className="text-gray-600 mt-1">{contract.title}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {contract.status === 'draft' && (
              <Button onClick={handleEdit} variant="outline">
                <Edit className="h-4 w-4 ml-2" />
                تعديل
              </Button>
            )}
            {contract.status === 'pending_signature' && (
              <Button onClick={handleActivate} className="bg-blue-600 hover:bg-blue-700">
                <CheckCircle className="h-4 w-4 ml-2" />
                تفعيل العقد
              </Button>
            )}
            {contract.status === 'active' && (
              <Button onClick={handleComplete} className="bg-purple-600 hover:bg-purple-700">
                <CheckCircle className="h-4 w-4 ml-2" />
                إكمال العقد
              </Button>
            )}
            <Button onClick={handleDownloadPDF} variant="outline">
              <Download className="h-4 w-4 ml-2" />
              تحميل PDF
            </Button>
          </div>
        </div>

        {/* Contract Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">الحالة</p>
                  <div className="mt-2">{getStatusBadge(contract.status)}</div>
                </div>
                {getStatusIcon(contract.status)}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">الأولوية</p>
                  <div className="mt-2">{getPriorityBadge(contract.priority)}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          {contract.contract_value && (
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">قيمة العقد</p>
                    <p className="text-2xl font-bold text-green-600">
                      {formatCurrency(contract.contract_value)} ج.م
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Contract Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-purple-600" />
                معلومات العقد
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium text-gray-600">العنوان</p>
                <p className="text-gray-900">{contract.title}</p>
              </div>
              {contract.description && (
                <div>
                  <p className="text-sm font-medium text-gray-600">الوصف</p>
                  <p className="text-gray-900">{contract.description}</p>
                </div>
              )}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600">تاريخ البداية</p>
                  <p className="text-gray-900">{formatDate(contract.start_date)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">تاريخ النهاية</p>
                  <p className="text-gray-900">{formatDate(contract.end_date)}</p>
                </div>
              </div>
              {contract.signed_date && (
                <div>
                  <p className="text-sm font-medium text-gray-600">تاريخ التوقيع</p>
                  <p className="text-gray-900">{formatDate(contract.signed_date)}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Client and Project Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5 text-purple-600" />
                العميل والمشروع
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {contract.client && contract.client.name && (
                <div>
                  <p className="text-sm font-medium text-gray-600">العميل</p>
                  <p className="text-gray-900">{contract.client.name}</p>
                  {contract.client.email && (
                    <p className="text-sm text-gray-600">{contract.client.email}</p>
                  )}
                  {contract.client.phone && (
                    <p className="text-sm text-gray-600">{contract.client.phone}</p>
                  )}
                </div>
              )}
              {contract.project && contract.project.name && (
                <div>
                  <p className="text-sm font-medium text-gray-600">المشروع</p>
                  <p className="text-gray-900">{contract.project.name}</p>
                  {contract.project.type && (
                    <p className="text-sm text-gray-600">النوع: {contract.project.type}</p>
                  )}
                </div>
              )}
              {contract.template && contract.template.name && (
                <div>
                  <p className="text-sm font-medium text-gray-600">القالب المستخدم</p>
                  <p className="text-gray-900">{contract.template.name}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Contract Content */}
        {contract.content && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-purple-600" />
                محتوى العقد
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div
                className="prose max-w-none text-gray-900"
                dangerouslySetInnerHTML={{ __html: contract.content }}
              />
            </CardContent>
          </Card>
        )}

        {/* Terms and Conditions */}
        {contract.terms_conditions && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileSignature className="h-5 w-5 text-purple-600" />
                الشروط والأحكام
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div
                className="prose max-w-none text-gray-900"
                dangerouslySetInnerHTML={{ __html: contract.terms_conditions }}
              />
            </CardContent>
          </Card>
        )}

        {/* Renewal Information */}
        {contract.auto_renewal && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <RefreshCw className="h-5 w-5 text-purple-600" />
                معلومات التجديد
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600">التجديد التلقائي</p>
                  <p className="text-gray-900">{contract.auto_renewal ? 'مفعل' : 'غير مفعل'}</p>
                </div>
                {contract.renewal_period_months && (
                  <div>
                    <p className="text-sm font-medium text-gray-600">فترة التجديد</p>
                    <p className="text-gray-900">{contract.renewal_period_months} شهر</p>
                  </div>
                )}
                <div>
                  <p className="text-sm font-medium text-gray-600">أيام الإشعار قبل التجديد</p>
                  <p className="text-gray-900">{contract.renewal_notice_days} يوم</p>
                </div>
                {contract.needs_renewal_notice && (
                  <div>
                    <p className="text-sm font-medium text-orange-600">تنبيه</p>
                    <p className="text-orange-600">يحتاج إشعار تجديد</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Notes */}
        {contract.notes && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-purple-600" />
                ملاحظات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-900 whitespace-pre-wrap">{contract.notes}</p>
            </CardContent>
          </Card>
        )}

        {/* Actions */}
        {!['completed', 'cancelled'].includes(contract.status) && (
          <Card className="mt-6 border-red-200">
            <CardHeader>
              <CardTitle className="text-red-600">إجراءات خطيرة</CardTitle>
              <CardDescription>
                هذه الإجراءات لا يمكن التراجع عنها
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={handleDelete}
                variant="destructive"
                className="w-full"
              >
                <XCircle className="h-4 w-4 ml-2" />
                حذف العقد نهائياً
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </UnifiedLayout>
  );
}
