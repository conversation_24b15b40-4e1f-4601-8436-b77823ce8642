'use client';

import { UnifiedLayout } from '@/components/layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useClients } from '@/lib/hooks/use-clients';
import { useActiveContractTemplates, useContract, useContractMutations } from '@/lib/hooks/use-contracts';
import { useProjects } from '@/lib/hooks/use-projects';
import { useAuthStore } from '@/lib/stores/auth-store';
import {
    CONTRACT_PRIORITY_OPTIONS,
    ContractFormData,
    ContractPriority
} from '@/types/contracts';
import {
    AlertTriangle,
    ArrowLeft,
    Calendar,
    FileSignature,
    RefreshCw,
    Save,
    Settings,
    User
} from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

export default function ContractEditPage() {
  const params = useParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);

  const contractId = params.id as string;
  const { data: contract, isLoading, error } = useContract(contractId);
  const { updateContract } = useContractMutations();

  // Form setup
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
    reset
  } = useForm<ContractFormData>();

  // API hooks for dropdowns
  const { data: clientsData } = useClients({ page_size: 100 });
  const { data: projectsData } = useProjects({ page_size: 100 });
  const { data: templatesData } = useActiveContractTemplates();

  const clients = clientsData?.results || [];
  const projects = projectsData?.results || [];
  const templates = templatesData || [];

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  // Populate form when contract data is loaded
  useEffect(() => {
    if (contract) {
      reset({
        title: contract.title,
        description: contract.description || '',
        priority: contract.priority,
        client_id: contract.client?.id || '',
        project_id: contract.project?.id || '',
        template_id: contract.template?.id || '',
        assigned_to_id: contract.assigned_to?.id || '',
        content: contract.content,
        terms_conditions: contract.terms_conditions || '',
        contract_value: contract.contract_value || 0,
        start_date: contract.start_date,
        end_date: contract.end_date,
        auto_renewal: contract.auto_renewal,
        renewal_period_months: contract.renewal_period_months || 12,
        renewal_notice_days: contract.renewal_notice_days,
        notes: contract.notes || '',
      });
    }
  }, [contract, reset]);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل تعديل العقد...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى تعديل العقد</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <UnifiedLayout>
        <div className="max-w-4xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
              <p className="text-gray-600">جاري تحميل بيانات العقد...</p>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  // Error state
  if (error || !contract) {
    return (
      <UnifiedLayout>
        <div className="max-w-4xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل العقد</h3>
              <p className="text-gray-600 mb-4">
                {error ? 'حدث خطأ أثناء تحميل بيانات العقد' : 'العقد غير موجود'}
              </p>
              <Button onClick={() => router.push('/founder-dashboard/contracts')}>
                <ArrowLeft className="h-4 w-4 ml-2" />
                العودة للعقود
              </Button>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  // Check if contract can be edited
  if (contract.status !== 'draft') {
    return (
      <UnifiedLayout>
        <div className="max-w-4xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <AlertTriangle className="h-12 w-12 text-orange-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا يمكن تعديل العقد</h3>
              <p className="text-gray-600 mb-4">
                يمكن تعديل العقود في حالة المسودة فقط
              </p>
              <div className="flex gap-2 justify-center">
                <Button onClick={() => router.push(`/founder-dashboard/contracts/${contract.id}`)}>
                  عرض العقد
                </Button>
                <Button onClick={() => router.push('/founder-dashboard/contracts')} variant="outline">
                  <ArrowLeft className="h-4 w-4 ml-2" />
                  العودة للعقود
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  const handleBack = () => {
    router.push(`/founder-dashboard/contracts/${contract.id}`);
  };

  const handleManageTemplates = () => {
    router.push('/founder-dashboard/contracts/templates');
  };

  const onSubmit = async (data: ContractFormData) => {
    try {
      await updateContract.mutateAsync({
        id: contract.id,
        data
      });
      router.push(`/founder-dashboard/contracts/${contract.id}`);
    } catch (error) {
      console.error('Error updating contract:', error);
    }
  };

  const selectedClientId = watch('client_id');
  const clientProjects = projects.filter(project => 
    project.client_id?.toString() === selectedClientId
  );

  return (
    <UnifiedLayout>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة للعقد
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <FileSignature className="h-8 w-8 text-purple-600" />
                تعديل العقد
              </h1>
              <p className="text-gray-600 mt-1">
                تعديل بيانات العقد: {contract.contract_number}
              </p>
            </div>
          </div>
          <Button 
            variant="outline" 
            onClick={handleManageTemplates}
            className="border-purple-200 text-purple-700 hover:bg-purple-50"
          >
            <Settings className="h-4 w-4 ml-2" />
            إدارة القوالب
          </Button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileSignature className="h-5 w-5 text-purple-600" />
                المعلومات الأساسية
              </CardTitle>
              <CardDescription>
                معلومات العقد الأساسية والحالة
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">عنوان العقد *</Label>
                  <Input
                    id="title"
                    {...register('title', { required: 'عنوان العقد مطلوب' })}
                    placeholder="أدخل عنوان العقد"
                  />
                  {errors.title && (
                    <p className="text-sm text-red-600">{errors.title.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="priority">الأولوية *</Label>
                  <Select
                    value={watch('priority')}
                    onValueChange={(value) => setValue('priority', value as ContractPriority)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الأولوية" />
                    </SelectTrigger>
                    <SelectContent>
                      {CONTRACT_PRIORITY_OPTIONS.map((priority) => (
                        <SelectItem key={priority.value} value={priority.value}>
                          {priority.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.priority && (
                    <p className="text-sm text-red-600">{errors.priority.message}</p>
                  )}
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">الوصف</Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="وصف مختصر للعقد"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Client and Project */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5 text-purple-600" />
                العميل والمشروع
              </CardTitle>
              <CardDescription>
                اختر العميل والمشروع المرتبط بالعقد
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="client_id">العميل *</Label>
                  <Select
                    value={watch('client_id')}
                    onValueChange={(value) => {
                      setValue('client_id', value);
                      // Reset project when client changes
                      setValue('project_id', '');
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر العميل" />
                    </SelectTrigger>
                    <SelectContent>
                      {clients.map((client) => (
                        <SelectItem key={client.id} value={client.id.toString()}>
                          {client.name} - {client.company || client.email}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.client_id && (
                    <p className="text-sm text-red-600">{errors.client_id.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="project_id">المشروع (اختياري)</Label>
                  <Select
                    value={watch('project_id')}
                    onValueChange={(value) => setValue('project_id', value)}
                    disabled={!selectedClientId}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={selectedClientId ? "اختر المشروع" : "اختر العميل أولاً"} />
                    </SelectTrigger>
                    <SelectContent>
                      {clientProjects.map((project) => (
                        <SelectItem key={project.id} value={project.id.toString()}>
                          {project.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contract Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-purple-600" />
                تفاصيل العقد
              </CardTitle>
              <CardDescription>
                المحتوى والتواريخ والقيمة المالية
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="template_id">القالب (اختياري)</Label>
                <Select
                  value={watch('template_id')}
                  onValueChange={(value) => setValue('template_id', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر قالب العقد" />
                  </SelectTrigger>
                  <SelectContent>
                    {templates.map((template) => (
                      <SelectItem key={template.id} value={template.id}>
                        {template.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="content">محتوى العقد *</Label>
                <Textarea
                  id="content"
                  {...register('content', { required: 'محتوى العقد مطلوب' })}
                  placeholder="أدخل محتوى العقد"
                  rows={8}
                />
                {errors.content && (
                  <p className="text-sm text-red-600">{errors.content.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="terms_conditions">الشروط والأحكام</Label>
                <Textarea
                  id="terms_conditions"
                  {...register('terms_conditions')}
                  placeholder="أدخل الشروط والأحكام"
                  rows={6}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="contract_value">قيمة العقد (ج.م)</Label>
                  <Input
                    id="contract_value"
                    type="number"
                    step="0.01"
                    {...register('contract_value', { valueAsNumber: true })}
                    placeholder="0.00"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="start_date">تاريخ البداية *</Label>
                  <Input
                    id="start_date"
                    type="date"
                    {...register('start_date', { required: 'تاريخ البداية مطلوب' })}
                  />
                  {errors.start_date && (
                    <p className="text-sm text-red-600">{errors.start_date.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="end_date">تاريخ النهاية *</Label>
                  <Input
                    id="end_date"
                    type="date"
                    {...register('end_date', { required: 'تاريخ النهاية مطلوب' })}
                  />
                  {errors.end_date && (
                    <p className="text-sm text-red-600">{errors.end_date.message}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Renewal Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <RefreshCw className="h-5 w-5 text-purple-600" />
                إعدادات التجديد
              </CardTitle>
              <CardDescription>
                إعدادات التجديد التلقائي والإشعارات
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="auto_renewal"
                  checked={watch('auto_renewal')}
                  onCheckedChange={(checked) => setValue('auto_renewal', checked)}
                />
                <Label htmlFor="auto_renewal">تفعيل التجديد التلقائي</Label>
              </div>

              {watch('auto_renewal') && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="renewal_period_months">فترة التجديد (بالأشهر)</Label>
                    <Input
                      id="renewal_period_months"
                      type="number"
                      {...register('renewal_period_months', { valueAsNumber: true })}
                      placeholder="12"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="renewal_notice_days">أيام الإشعار قبل التجديد *</Label>
                    <Input
                      id="renewal_notice_days"
                      type="number"
                      {...register('renewal_notice_days', {
                        required: 'أيام الإشعار مطلوبة',
                        valueAsNumber: true
                      })}
                      placeholder="30"
                    />
                    {errors.renewal_notice_days && (
                      <p className="text-sm text-red-600">{errors.renewal_notice_days.message}</p>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Notes */}
          <Card>
            <CardHeader>
              <CardTitle>ملاحظات إضافية</CardTitle>
              <CardDescription>
                أي ملاحظات أو تفاصيل إضافية حول العقد
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Textarea
                {...register('notes')}
                placeholder="أدخل أي ملاحظات إضافية"
                rows={4}
              />
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" onClick={handleBack}>
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-purple-600 hover:bg-purple-700"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 ml-2" />
                  حفظ التغييرات
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </UnifiedLayout>
  );
}
