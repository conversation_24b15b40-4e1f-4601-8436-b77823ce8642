'use client';

import { UnifiedLayout } from '@/components/layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useClients } from '@/lib/hooks/use-clients';
import { useActiveContractTemplates, useContractMutations } from '@/lib/hooks/use-contracts';
import { useProjects } from '@/lib/hooks/use-projects';
import { useAuthStore } from '@/lib/stores/auth-store';
import {
    CONTRACT_PRIORITY_OPTIONS,
    ContractFormData,
    ContractPriority
} from '@/types/contracts';
import {
    ArrowLeft,
    Calendar,
    DollarSign,
    FileSignature,
    FileText,
    Save,
    Settings,
    User
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

export default function NewContractPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);

  // API hooks
  const { createContract } = useContractMutations();
  const { data: clientsData } = useClients();
  const { data: projectsData } = useProjects();
  const { data: templatesData } = useActiveContractTemplates();

  const clients = clientsData?.results || [];
  const projects = projectsData?.results || [];
  const templates = templatesData || [];

  // Form handling
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting }
  } = useForm<ContractFormData>({
    defaultValues: {
      priority: 'medium',
      auto_renewal: false,
      renewal_notice_days: 30,
      content: '',
    }
  });

  const selectedClientId = watch('client_id');
  const selectedTemplateId = watch('template_id');
  const autoRenewal = watch('auto_renewal');

  // Filter projects by selected client
  const filteredProjects = projects.filter(project => 
    !selectedClientId || project.client.toString() === selectedClientId
  );

  // Get selected template content
  const selectedTemplate = templates.find(template => template.id === selectedTemplateId);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  // Update content when template is selected
  useEffect(() => {
    if (selectedTemplate && selectedTemplate.content) {
      setValue('content', selectedTemplate.content);
    }
  }, [selectedTemplate, setValue]);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل إنشاء عقد جديد...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى إنشاء العقود</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  const onSubmit = async (data: ContractFormData) => {
    try {
      const contract = await createContract.mutateAsync(data);
      router.push(`/founder-dashboard/contracts/${contract.id}`);
    } catch (error) {
      console.error('Error creating contract:', error);
    }
  };

  const handleBack = () => {
    router.push('/founder-dashboard/contracts');
  };

  const handleManageTemplates = () => {
    router.push('/founder-dashboard/contracts/templates');
  };

  return (
    <UnifiedLayout>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة للعقود
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <FileSignature className="h-8 w-8 text-purple-600" />
                إنشاء عقد جديد
              </h1>
              <p className="text-gray-600 mt-1">
                إنشاء عقد جديد للعملاء والمشاريع
              </p>
            </div>
          </div>
          <Button 
            variant="outline" 
            onClick={handleManageTemplates}
            className="border-purple-200 text-purple-700 hover:bg-purple-50"
          >
            <Settings className="h-4 w-4 ml-2" />
            إدارة القوالب
          </Button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-purple-600" />
                المعلومات الأساسية
              </CardTitle>
              <CardDescription>
                أدخل المعلومات الأساسية للعقد
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">عنوان العقد *</Label>
                  <Input
                    id="title"
                    placeholder="مثال: عقد تطوير موقع إلكتروني"
                    {...register('title', { required: 'عنوان العقد مطلوب' })}
                  />
                  {errors.title && (
                    <p className="text-sm text-red-600">{errors.title.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="priority">الأولوية</Label>
                  <Select
                    value={watch('priority')}
                    onValueChange={(value) => setValue('priority', value as ContractPriority)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الأولوية" />
                    </SelectTrigger>
                    <SelectContent>
                      {CONTRACT_PRIORITY_OPTIONS.map((priority) => (
                        <SelectItem key={priority.value} value={priority.value}>
                          {priority.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">الوصف</Label>
                <Textarea
                  id="description"
                  placeholder="وصف مختصر للعقد..."
                  rows={3}
                  {...register('description')}
                />
              </div>
            </CardContent>
          </Card>

          {/* Client and Project */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5 text-purple-600" />
                العميل والمشروع
              </CardTitle>
              <CardDescription>
                اختر العميل والمشروع المرتبط بالعقد
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="client_id">العميل *</Label>
                  <Select
                    value={watch('client_id')}
                    onValueChange={(value) => {
                      setValue('client_id', value);
                      // Reset project when client changes
                      setValue('project_id', '');
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر العميل" />
                    </SelectTrigger>
                    <SelectContent>
                      {clients.map((client) => (
                        <SelectItem key={client.id} value={client.id.toString()}>
                          {client.name} - {client.company || client.email}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.client_id && (
                    <p className="text-sm text-red-600">{errors.client_id.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="project_id">المشروع (اختياري)</Label>
                  <Select
                    value={watch('project_id')}
                    onValueChange={(value) => setValue('project_id', value)}
                    disabled={!selectedClientId}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={selectedClientId ? "اختر المشروع" : "اختر العميل أولاً"} />
                    </SelectTrigger>
                    <SelectContent>
                      {filteredProjects.map((project) => (
                        <SelectItem key={project.id} value={project.id.toString()}>
                          {project.name} - {project.type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Template Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-purple-600" />
                قالب العقد
              </CardTitle>
              <CardDescription>
                اختر قالب العقد أو ابدأ من الصفر
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="template_id">قالب العقد (اختياري)</Label>
                <Select
                  value={watch('template_id')}
                  onValueChange={(value) => setValue('template_id', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر قالب العقد أو اتركه فارغاً للبدء من الصفر" />
                  </SelectTrigger>
                  <SelectContent>
                    {templates.map((template) => (
                      <SelectItem key={template.id} value={template.id}>
                        {template.name} - {template.template_type_display}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedTemplate && (
                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="font-medium text-blue-900 mb-2">معاينة القالب المحدد:</h4>
                  <p className="text-sm text-blue-700">{selectedTemplate.description}</p>
                  <p className="text-xs text-blue-600 mt-1">
                    النوع: {selectedTemplate.template_type_display}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Contract Content */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-purple-600" />
                محتوى العقد
              </CardTitle>
              <CardDescription>
                أدخل محتوى العقد والشروط والأحكام
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="content">محتوى العقد *</Label>
                <Textarea
                  id="content"
                  placeholder="أدخل محتوى العقد هنا..."
                  rows={8}
                  {...register('content', { required: 'محتوى العقد مطلوب' })}
                />
                {errors.content && (
                  <p className="text-sm text-red-600">{errors.content.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="terms_conditions">الشروط والأحكام</Label>
                <Textarea
                  id="terms_conditions"
                  placeholder="أدخل الشروط والأحكام الإضافية..."
                  rows={4}
                  {...register('terms_conditions')}
                />
              </div>
            </CardContent>
          </Card>

          {/* Financial Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-purple-600" />
                المعلومات المالية
              </CardTitle>
              <CardDescription>
                أدخل قيمة العقد والمعلومات المالية
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="contract_value">قيمة العقد (ج.م)</Label>
                <Input
                  id="contract_value"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  {...register('contract_value', {
                    valueAsNumber: true,
                    min: { value: 0, message: 'قيمة العقد يجب أن تكون أكبر من أو تساوي صفر' }
                  })}
                />
                {errors.contract_value && (
                  <p className="text-sm text-red-600">{errors.contract_value.message}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-purple-600" />
                الجدول الزمني
              </CardTitle>
              <CardDescription>
                حدد تواريخ بداية ونهاية العقد
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="start_date">تاريخ البداية *</Label>
                  <Input
                    id="start_date"
                    type="date"
                    {...register('start_date', { required: 'تاريخ البداية مطلوب' })}
                  />
                  {errors.start_date && (
                    <p className="text-sm text-red-600">{errors.start_date.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="end_date">تاريخ النهاية *</Label>
                  <Input
                    id="end_date"
                    type="date"
                    {...register('end_date', { required: 'تاريخ النهاية مطلوب' })}
                  />
                  {errors.end_date && (
                    <p className="text-sm text-red-600">{errors.end_date.message}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Renewal Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-purple-600" />
                إعدادات التجديد
              </CardTitle>
              <CardDescription>
                إعدادات التجديد التلقائي والإشعارات
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Switch
                  id="auto_renewal"
                  checked={autoRenewal}
                  onCheckedChange={(checked) => setValue('auto_renewal', checked)}
                />
                <Label htmlFor="auto_renewal">التجديد التلقائي</Label>
              </div>

              {autoRenewal && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="renewal_period_months">فترة التجديد (بالأشهر)</Label>
                    <Input
                      id="renewal_period_months"
                      type="number"
                      min="1"
                      max="60"
                      placeholder="12"
                      {...register('renewal_period_months', {
                        valueAsNumber: true,
                        min: { value: 1, message: 'فترة التجديد يجب أن تكون شهر واحد على الأقل' },
                        max: { value: 60, message: 'فترة التجديد لا يمكن أن تزيد عن 60 شهر' }
                      })}
                    />
                    {errors.renewal_period_months && (
                      <p className="text-sm text-red-600">{errors.renewal_period_months.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="renewal_notice_days">أيام الإشعار قبل التجديد</Label>
                    <Input
                      id="renewal_notice_days"
                      type="number"
                      min="1"
                      max="365"
                      placeholder="30"
                      {...register('renewal_notice_days', {
                        valueAsNumber: true,
                        min: { value: 1, message: 'أيام الإشعار يجب أن تكون يوم واحد على الأقل' },
                        max: { value: 365, message: 'أيام الإشعار لا يمكن أن تزيد عن 365 يوم' }
                      })}
                    />
                    {errors.renewal_notice_days && (
                      <p className="text-sm text-red-600">{errors.renewal_notice_days.message}</p>
                    )}
                  </div>
                </div>
              )}

              {!autoRenewal && (
                <div className="space-y-2">
                  <Label htmlFor="renewal_notice_days">أيام الإشعار قبل انتهاء العقد</Label>
                  <Input
                    id="renewal_notice_days"
                    type="number"
                    min="1"
                    max="365"
                    placeholder="30"
                    {...register('renewal_notice_days', {
                      valueAsNumber: true,
                      min: { value: 1, message: 'أيام الإشعار يجب أن تكون يوم واحد على الأقل' },
                      max: { value: 365, message: 'أيام الإشعار لا يمكن أن تزيد عن 365 يوم' }
                    })}
                  />
                  {errors.renewal_notice_days && (
                    <p className="text-sm text-red-600">{errors.renewal_notice_days.message}</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Additional Notes */}
          <Card>
            <CardHeader>
              <CardTitle>ملاحظات إضافية</CardTitle>
              <CardDescription>
                أي ملاحظات أو تعليقات إضافية حول العقد
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="notes">الملاحظات</Label>
                <Textarea
                  id="notes"
                  placeholder="أدخل أي ملاحظات إضافية..."
                  rows={3}
                  {...register('notes')}
                />
              </div>
            </CardContent>
          </Card>

          {/* Submit Buttons */}
          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" onClick={handleBack}>
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-purple-600 hover:bg-purple-700"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                  جاري الإنشاء...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 ml-2" />
                  إنشاء العقد
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </UnifiedLayout>
  );
}
