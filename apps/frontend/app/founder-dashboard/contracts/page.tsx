'use client';

import { UnifiedLayout } from '@/components/layout';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { useContractMutations, useContracts, useContractStats } from '@/lib/hooks/use-contracts';
import { useAuthStore } from '@/lib/stores/auth-store';
import { formatCurrency, formatDate } from '@/lib/utils';
import {
    Contract,
    CONTRACT_PRIORITY_OPTIONS,
    CONTRACT_STATUS_OPTIONS,
    ContractPriority,
    ContractStatus
} from '@/types/contracts';
import {
    AlertTriangle,
    Calendar,
    CheckCircle,
    Clock,
    DollarSign,
    Download,
    Edit,
    Eye,
    FileSignature,
    FileText,
    MoreHorizontal,
    Plus,
    RefreshCw,
    Search,
    Settings,
    User,
    XCircle
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function ContractsPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');

  // API hooks
  const { data: contractsData, isLoading, error, refetch } = useContracts({
    search: searchTerm || undefined,
    status: statusFilter !== 'all' ? [statusFilter as ContractStatus] : undefined,
    priority: priorityFilter !== 'all' ? [priorityFilter as ContractPriority] : undefined,
    page_size: 50,
    ordering: '-created_at'
  });

  const { data: stats } = useContractStats();
  const { 
    activateContract, 
    completeContract, 
    generatePDF,
    deleteContract 
  } = useContractMutations();

  const contracts = contractsData?.results?.filter(contract =>
    contract && contract.id && contract.contract_number && contract.title
  ) || [];

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل إدارة العقود...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى إدارة العقود</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  // Handle contract actions
  const handleCreateContract = () => {
    router.push('/founder-dashboard/contracts/new');
  };

  const handleViewContract = (contract: Contract) => {
    router.push(`/founder-dashboard/contracts/${contract.id}`);
  };

  const handleEditContract = (contract: Contract) => {
    router.push(`/founder-dashboard/contracts/${contract.id}/edit`);
  };

  const handleActivateContract = async (contract: Contract) => {
    activateContract.mutate(contract.id);
  };

  const handleCompleteContract = async (contract: Contract) => {
    if (confirm('هل أنت متأكد من إكمال هذا العقد؟')) {
      completeContract.mutate(contract.id);
    }
  };

  const handleDownloadPDF = async (contract: Contract) => {
    generatePDF.mutate(contract.id);
  };

  const handleDeleteContract = async (contract: Contract) => {
    if (confirm('هل أنت متأكد من حذف هذا العقد؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      deleteContract.mutate(contract.id);
    }
  };

  const handleManageTemplates = () => {
    router.push('/founder-dashboard/contracts/templates');
  };

  // Get status badge component
  const getStatusBadge = (status: ContractStatus) => {
    const statusOption = CONTRACT_STATUS_OPTIONS.find(option => option.value === status);
    if (!statusOption) return <Badge variant="outline">{status}</Badge>;

    const colorClasses = {
      gray: 'bg-gray-100 text-gray-800',
      orange: 'bg-orange-100 text-orange-800',
      blue: 'bg-blue-100 text-blue-800',
      green: 'bg-green-100 text-green-800',
      purple: 'bg-purple-100 text-purple-800',
      red: 'bg-red-100 text-red-800',
    };

    return (
      <Badge className={colorClasses[statusOption.color as keyof typeof colorClasses]}>
        {statusOption.label}
      </Badge>
    );
  };

  // Get priority badge component
  const getPriorityBadge = (priority: ContractPriority) => {
    const priorityOption = CONTRACT_PRIORITY_OPTIONS.find(option => option.value === priority);
    if (!priorityOption) return <Badge variant="outline">{priority}</Badge>;

    const colorClasses = {
      green: 'bg-green-100 text-green-800',
      yellow: 'bg-yellow-100 text-yellow-800',
      orange: 'bg-orange-100 text-orange-800',
      red: 'bg-red-100 text-red-800',
    };

    return (
      <Badge variant="outline" className={colorClasses[priorityOption.color as keyof typeof colorClasses]}>
        {priorityOption.label}
      </Badge>
    );
  };

  // Get status icon
  const getStatusIcon = (status: ContractStatus) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'expired':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-gray-600" />;
      case 'pending_signature':
        return <FileSignature className="h-4 w-4 text-blue-600" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-purple-600" />;
      case 'pending_review':
        return <Clock className="h-4 w-4 text-orange-600" />;
      default:
        return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <FileSignature className="h-8 w-8 text-purple-600" />
              إدارة العقود
            </h1>
            <p className="text-gray-600 mt-1">
              إدارة شاملة لجميع عقود العملاء والمشاريع
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Button 
              variant="outline" 
              onClick={handleManageTemplates}
              className="border-purple-200 text-purple-700 hover:bg-purple-50"
            >
              <Settings className="h-4 w-4 ml-2" />
              إدارة القوالب
            </Button>
            <Button onClick={handleCreateContract} className="bg-purple-600 hover:bg-purple-700">
              <Plus className="h-4 w-4 ml-2" />
              عقد جديد
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">إجمالي العقود</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.total_contracts}</p>
                  </div>
                  <FileText className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">العقود النشطة</p>
                    <p className="text-2xl font-bold text-green-600">{stats.active_contracts}</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">في انتظار التوقيع</p>
                    <p className="text-2xl font-bold text-blue-600">{stats.pending_signature}</p>
                  </div>
                  <FileSignature className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">إجمالي القيمة</p>
                    <p className="text-2xl font-bold text-purple-600">
                      {formatCurrency(stats.total_value)} ج.م
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في العقود (رقم العقد، العنوان، العميل)..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="حالة العقد" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الحالات</SelectItem>
                    {CONTRACT_STATUS_OPTIONS.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="الأولوية" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الأولويات</SelectItem>
                    {CONTRACT_PRIORITY_OPTIONS.map((priority) => (
                      <SelectItem key={priority.value} value={priority.value}>
                        {priority.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Loading State */}
        {isLoading && (
          <Card className="text-center py-12">
            <CardContent>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
              <p className="text-gray-600">جاري تحميل العقود...</p>
            </CardContent>
          </Card>
        )}

        {/* Error State */}
        {error && (
          <Card className="text-center py-12">
            <CardContent>
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل العقود</h3>
              <p className="text-gray-600 mb-4">حدث خطأ أثناء تحميل العقود. يرجى المحاولة مرة أخرى.</p>
              <Button onClick={() => refetch()} variant="outline">
                <RefreshCw className="h-4 w-4 ml-2" />
                إعادة المحاولة
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Contracts Grid */}
        {!isLoading && !error && (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {contracts.map((contract) => (
              <Card
                key={contract.id}
                className="hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => handleViewContract(contract)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(contract.status)}
                      <CardTitle className="text-lg">{contract.contract_number}</CardTitle>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          handleViewContract(contract);
                        }}>
                          <Eye className="h-4 w-4 ml-2" />
                          عرض التفاصيل
                        </DropdownMenuItem>
                        {contract.status === 'draft' && (
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            handleEditContract(contract);
                          }}>
                            <Edit className="h-4 w-4 ml-2" />
                            تعديل
                          </DropdownMenuItem>
                        )}
                        {contract.status === 'pending_signature' && (
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            handleActivateContract(contract);
                          }}>
                            <CheckCircle className="h-4 w-4 ml-2" />
                            تفعيل العقد
                          </DropdownMenuItem>
                        )}
                        {contract.status === 'active' && (
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            handleCompleteContract(contract);
                          }}>
                            <CheckCircle className="h-4 w-4 ml-2" />
                            إكمال العقد
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          handleDownloadPDF(contract);
                        }}>
                          <Download className="h-4 w-4 ml-2" />
                          تحميل PDF
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {!['completed', 'cancelled'].includes(contract.status) && (
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteContract(contract);
                            }}
                          >
                            <XCircle className="h-4 w-4 ml-2" />
                            حذف
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <CardDescription className="text-sm font-medium">
                    {contract.title}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {/* Status and Priority */}
                    <div className="flex items-center justify-between">
                      {getStatusBadge(contract.status)}
                      {getPriorityBadge(contract.priority)}
                    </div>

                    {/* Client Info */}
                    {contract.client && contract.client.name && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <User className="h-4 w-4" />
                        <span>{contract.client.name}</span>
                      </div>
                    )}

                    {/* Contract Value */}
                    {contract.contract_value && (
                      <div className="flex items-center gap-2 text-sm">
                        <DollarSign className="h-4 w-4 text-green-600" />
                        <span className="font-medium text-green-600">
                          {formatCurrency(contract.contract_value)} ج.م
                        </span>
                      </div>
                    )}

                    {/* Project Info */}
                    {contract.project && contract.project.name && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <FileText className="h-4 w-4" />
                        <span>{contract.project.name}</span>
                      </div>
                    )}

                    {/* Contract Duration */}
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Calendar className="h-4 w-4" />
                        <span>من: {formatDate(contract.start_date)}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Calendar className="h-4 w-4" />
                        <span className={contract.days_until_expiry <= 30 && contract.status === 'active' ? 'text-orange-600 font-medium' : 'text-gray-600'}>
                          إلى: {formatDate(contract.end_date)}
                          {contract.status === 'active' && contract.days_until_expiry <= 30 && (
                            ` (ينتهي خلال ${contract.days_until_expiry} يوم)`
                          )}
                        </span>
                      </div>
                    </div>

                    {/* Active Status Indicator */}
                    {contract.is_active && (
                      <div className="pt-2 border-t">
                        <div className="flex items-center gap-2 text-sm text-green-600">
                          <CheckCircle className="h-4 w-4" />
                          <span className="font-medium">العقد نشط حالياً</span>
                        </div>
                      </div>
                    )}

                    {/* Renewal Notice */}
                    {contract.needs_renewal_notice && (
                      <div className="pt-2 border-t">
                        <div className="flex items-center gap-2 text-sm text-orange-600">
                          <RefreshCw className="h-4 w-4" />
                          <span className="font-medium">يحتاج إشعار تجديد</span>
                        </div>
                      </div>
                    )}

                    {/* Template Info */}
                    {contract.template && contract.template.name && (
                      <div className="pt-2 border-t">
                        <div className="flex justify-between items-center text-sm">
                          <span className="text-gray-600">القالب:</span>
                          <span className="font-medium">{contract.template.name}</span>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!isLoading && !error && contracts.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <FileSignature className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد عقود</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || statusFilter !== 'all' || priorityFilter !== 'all'
                  ? 'لا توجد عقود تطابق معايير البحث المحددة'
                  : 'لم يتم إنشاء أي عقود بعد. ابدأ بإنشاء عقد جديد.'}
              </p>
              <Button onClick={handleCreateContract} className="bg-purple-600 hover:bg-purple-700">
                <Plus className="h-4 w-4 ml-2" />
                إنشاء عقد جديد
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </UnifiedLayout>
  );
}
