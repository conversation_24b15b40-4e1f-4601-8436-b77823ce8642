'use client';

import { UnifiedLayout } from '@/components/layout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useContractTemplates, useContractTemplateMutations } from '@/lib/hooks/use-contracts';
import { useAuthStore } from '@/lib/stores/auth-store';
import { ContractTemplate } from '@/types/contracts';
import { TemplatePreviewModal } from '@/components/contracts/template-preview-modal';
import {
  ArrowLeft,
  Plus,
  Search,
  Filter,
  FileText,
  Edit,
  Copy,
  Trash2,
  Eye,
  Settings,
  Globe,
  Smartphone,
  Monitor,
  ShoppingCart,
  Wrench,
  TrendingUp,
  Palette,
  Search as SearchIcon,
  Share2,
  AlertTriangle,
  RefreshCw,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function ContractTemplatesPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [previewTemplate, setPreviewTemplate] = useState<ContractTemplate | null>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  // API hooks
  const { data: templatesData, isLoading, error, refetch } = useContractTemplates();
  const { deleteTemplate } = useContractTemplateMutations();

  const templates = templatesData?.results || [];

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل قوالب العقود...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى قوالب العقود</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  // Template type icons mapping
  const getTemplateIcon = (type: string) => {
    const iconMap = {
      website: Globe,
      mobile_app: Smartphone,
      web_app: Monitor,
      ecommerce: ShoppingCart,
      maintenance: Wrench,
      marketing: TrendingUp,
      branding: Palette,
      seo: SearchIcon,
      social_media: Share2,
      custom: FileText,
    };
    return iconMap[type as keyof typeof iconMap] || FileText;
  };

  // Template type colors
  const getTemplateColor = (type: string) => {
    const colorMap = {
      website: 'bg-blue-100 text-blue-800',
      mobile_app: 'bg-green-100 text-green-800',
      web_app: 'bg-purple-100 text-purple-800',
      ecommerce: 'bg-orange-100 text-orange-800',
      maintenance: 'bg-gray-100 text-gray-800',
      marketing: 'bg-pink-100 text-pink-800',
      branding: 'bg-indigo-100 text-indigo-800',
      seo: 'bg-yellow-100 text-yellow-800',
      social_media: 'bg-cyan-100 text-cyan-800',
      custom: 'bg-slate-100 text-slate-800',
    };
    return colorMap[type as keyof typeof colorMap] || 'bg-gray-100 text-gray-800';
  };

  // Filter templates based on search and type
  const filteredTemplates = templates.filter((template) => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'all' || template.template_type === selectedType;
    return matchesSearch && matchesType;
  });

  // Handle actions
  const handleBack = () => {
    router.push('/founder-dashboard/contracts');
  };

  const handleCreateTemplate = () => {
    router.push('/founder-dashboard/contracts/templates/new');
  };

  const handleViewTemplate = (template: ContractTemplate) => {
    setPreviewTemplate(template);
    setIsPreviewOpen(true);
  };

  const handleViewDetails = (template: ContractTemplate) => {
    router.push(`/founder-dashboard/contracts/templates/${template.id}`);
  };

  const handleEditTemplate = (template: ContractTemplate) => {
    router.push(`/founder-dashboard/contracts/templates/${template.id}/edit`);
  };

  const handleCloneTemplate = (template: ContractTemplate) => {
    router.push(`/founder-dashboard/contracts/templates/new?clone=${template.id}`);
  };

  const handleDeleteTemplate = async (template: ContractTemplate) => {
    if (confirm(`هل أنت متأكد من حذف قالب "${template.name}"؟ هذا الإجراء لا يمكن التراجع عنه.`)) {
      deleteTemplate.mutate(template.id);
    }
  };

  const handleDownloadTemplate = (template: ContractTemplate) => {
    // Create a downloadable text file
    const element = document.createElement('a');
    const file = new Blob([template.content], { type: 'text/plain' });
    element.href = URL.createObjectURL(file);
    element.download = `${template.name}.txt`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const handleClosePreview = () => {
    setIsPreviewOpen(false);
    setPreviewTemplate(null);
  };

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة للعقود
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <Settings className="h-8 w-8 text-purple-600" />
                إدارة قوالب العقود
              </h1>
              <p className="text-gray-600 mt-1">
                إنشاء وإدارة قوالب العقود للخدمات المختلفة
              </p>
            </div>
          </div>
          <Button onClick={handleCreateTemplate} className="bg-purple-600 hover:bg-purple-700">
            <Plus className="h-4 w-4 ml-2" />
            قالب جديد
          </Button>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في القوالب..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <div className="sm:w-64">
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger>
                    <SelectValue placeholder="نوع القالب" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الأنواع</SelectItem>
                    <SelectItem value="website">تطوير موقع إلكتروني</SelectItem>
                    <SelectItem value="mobile_app">تطبيق جوال</SelectItem>
                    <SelectItem value="web_app">تطبيق ويب</SelectItem>
                    <SelectItem value="ecommerce">متجر إلكتروني</SelectItem>
                    <SelectItem value="maintenance">صيانة</SelectItem>
                    <SelectItem value="marketing">تسويق رقمي</SelectItem>
                    <SelectItem value="branding">هوية تجارية وتصميم</SelectItem>
                    <SelectItem value="seo">خدمات SEO</SelectItem>
                    <SelectItem value="social_media">إدارة وسائل التواصل</SelectItem>
                    <SelectItem value="custom">مخصص</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Loading State */}
        {isLoading && (
          <Card className="text-center py-12">
            <CardContent>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
              <p className="text-gray-600">جاري تحميل القوالب...</p>
            </CardContent>
          </Card>
        )}

        {/* Error State */}
        {error && (
          <Card className="text-center py-12">
            <CardContent>
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل القوالب</h3>
              <p className="text-gray-600 mb-4">حدث خطأ أثناء تحميل قوالب العقود. يرجى المحاولة مرة أخرى.</p>
              <Button onClick={() => refetch()} variant="outline">
                <RefreshCw className="h-4 w-4 ml-2" />
                إعادة المحاولة
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Templates Grid */}
        {!isLoading && !error && (
          <>
            {filteredTemplates.length === 0 ? (
              <Card className="text-center py-12">
                <CardContent>
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد قوالب</h3>
                  <p className="text-gray-600 mb-4">
                    {searchTerm || selectedType !== 'all' 
                      ? 'لم يتم العثور على قوالب تطابق معايير البحث'
                      : 'لم يتم إنشاء أي قوالب عقود بعد'
                    }
                  </p>
                  <Button onClick={handleCreateTemplate} className="bg-purple-600 hover:bg-purple-700">
                    <Plus className="h-4 w-4 ml-2" />
                    إنشاء أول قالب
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredTemplates.map((template) => {
                  const IconComponent = getTemplateIcon(template.template_type);
                  return (
                    <Card key={template.id} className="hover:shadow-lg transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-purple-100 rounded-lg">
                              <IconComponent className="h-5 w-5 text-purple-600" />
                            </div>
                            <div>
                              <CardTitle className="text-lg">{template.name}</CardTitle>
                              <Badge className={`mt-1 ${getTemplateColor(template.template_type)}`}>
                                {template.template_type_display}
                              </Badge>
                            </div>
                          </div>
                          <Badge variant={template.is_active ? 'default' : 'secondary'}>
                            {template.is_active ? 'نشط' : 'غير نشط'}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <CardDescription className="mb-4 line-clamp-2">
                          {template.description || 'لا يوجد وصف'}
                        </CardDescription>
                        
                        <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                          <span>تم الإنشاء: {new Date(template.created_at).toLocaleDateString('ar-EG')}</span>
                          {template.usage_count !== undefined && (
                            <span>الاستخدام: {template.usage_count} مرة</span>
                          )}
                        </div>

                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewTemplate(template)}
                            className="flex-1"
                          >
                            <Eye className="h-4 w-4 ml-1" />
                            معاينة
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewDetails(template)}
                            title="عرض التفاصيل"
                          >
                            <FileText className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEditTemplate(template)}
                            title="تعديل"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleCloneTemplate(template)}
                            title="نسخ"
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDeleteTemplate(template)}
                            className="text-red-600 hover:text-red-700"
                            title="حذف"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </>
        )}

        {/* Template Preview Modal */}
        <TemplatePreviewModal
          template={previewTemplate}
          isOpen={isPreviewOpen}
          onClose={handleClosePreview}
          onEdit={() => {
            if (previewTemplate) {
              handleClosePreview();
              handleEditTemplate(previewTemplate);
            }
          }}
          onClone={() => {
            if (previewTemplate) {
              handleClosePreview();
              handleCloneTemplate(previewTemplate);
            }
          }}
          onDownload={() => {
            if (previewTemplate) {
              handleDownloadTemplate(previewTemplate);
            }
          }}
        />
      </div>
    </UnifiedLayout>
  );
}
