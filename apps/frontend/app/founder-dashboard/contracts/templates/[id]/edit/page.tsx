'use client';

import { UnifiedLayout } from '@/components/layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useContractTemplate, useContractTemplateMutations } from '@/lib/hooks/use-contracts';
import { useAuthStore } from '@/lib/stores/auth-store';
import { ContractTemplateFormData } from '@/types/contracts';
import {
  ArrowLeft,
  FileText,
  Save,
  Eye,
  Globe,
  Smartphone,
  Monitor,
  ShoppingCart,
  Wrench,
  TrendingUp,
  Palette,
  Search as SearchIcon,
  Share2,
  AlertTriangle,
  RefreshCw,
} from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

export default function EditContractTemplatePage() {
  const params = useParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);

  const templateId = params.id as string;
  const { data: template, isLoading, error, refetch } = useContractTemplate(templateId);
  const { updateTemplate } = useContractTemplateMutations();

  // Form handling
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<ContractTemplateFormData>({
    defaultValues: {
      name: '',
      description: '',
      template_type: 'website',
      content: '',
      is_active: true,
    }
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  // Set form data when template loads
  useEffect(() => {
    if (template) {
      reset({
        name: template.name,
        description: template.description || '',
        template_type: template.template_type,
        content: template.content,
        is_active: template.is_active,
      });
    }
  }, [template, reset]);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل تعديل القالب...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى تعديل القوالب</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <UnifiedLayout>
        <div className="max-w-4xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
              <p className="text-gray-600">جاري تحميل القالب...</p>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  // Error state
  if (error || !template) {
    return (
      <UnifiedLayout>
        <div className="max-w-4xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل القالب</h3>
              <p className="text-gray-600 mb-4">
                {error ? 'حدث خطأ أثناء تحميل بيانات القالب' : 'القالب غير موجود'}
              </p>
              <div className="flex gap-2 justify-center">
                <Button onClick={() => refetch()} variant="outline">
                  <RefreshCw className="h-4 w-4 ml-2" />
                  إعادة المحاولة
                </Button>
                <Button onClick={() => router.push('/founder-dashboard/contracts/templates')}>
                  <ArrowLeft className="h-4 w-4 ml-2" />
                  العودة للقوالب
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  // Template type icons
  const getTemplateIcon = (type: string) => {
    const iconMap = {
      website: Globe,
      mobile_app: Smartphone,
      web_app: Monitor,
      ecommerce: ShoppingCart,
      maintenance: Wrench,
      marketing: TrendingUp,
      branding: Palette,
      seo: SearchIcon,
      social_media: Share2,
      custom: FileText,
    };
    return iconMap[type as keyof typeof iconMap] || FileText;
  };

  // Handle form submission
  const onSubmit = async (data: ContractTemplateFormData) => {
    try {
      await updateTemplate.mutateAsync({
        id: template.id,
        data
      });
      router.push(`/founder-dashboard/contracts/templates/${template.id}`);
    } catch (error) {
      console.error('Error updating template:', error);
    }
  };

  const handleBack = () => {
    router.push(`/founder-dashboard/contracts/templates/${template.id}`);
  };

  const handlePreview = () => {
    // TODO: Implement preview functionality
    console.log('Preview template:', watch());
  };

  const selectedType = watch('template_type');
  const IconComponent = getTemplateIcon(selectedType);

  return (
    <UnifiedLayout>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة للقالب
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <FileText className="h-8 w-8 text-purple-600" />
                تعديل قالب العقد
              </h1>
              <p className="text-gray-600 mt-1">
                تعديل بيانات القالب: {template.name}
              </p>
            </div>
          </div>
          <Button 
            variant="outline" 
            onClick={handlePreview}
            className="border-purple-200 text-purple-700 hover:bg-purple-50"
          >
            <Eye className="h-4 w-4 ml-2" />
            معاينة
          </Button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <IconComponent className="h-5 w-5 text-purple-600" />
                المعلومات الأساسية
              </CardTitle>
              <CardDescription>
                معلومات القالب الأساسية ونوع الخدمة
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">اسم القالب *</Label>
                  <Input
                    id="name"
                    {...register('name', { required: 'اسم القالب مطلوب' })}
                    placeholder="مثال: قالب عقد تطوير موقع إلكتروني"
                  />
                  {errors.name && (
                    <p className="text-sm text-red-600">{errors.name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="template_type">نوع الخدمة *</Label>
                  <Select
                    value={watch('template_type')}
                    onValueChange={(value) => setValue('template_type', value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر نوع الخدمة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="website">تطوير موقع إلكتروني</SelectItem>
                      <SelectItem value="mobile_app">تطبيق جوال</SelectItem>
                      <SelectItem value="web_app">تطبيق ويب</SelectItem>
                      <SelectItem value="ecommerce">متجر إلكتروني</SelectItem>
                      <SelectItem value="maintenance">صيانة وتطوير</SelectItem>
                      <SelectItem value="marketing">تسويق رقمي</SelectItem>
                      <SelectItem value="branding">هوية تجارية وتصميم</SelectItem>
                      <SelectItem value="seo">خدمات تحسين محركات البحث</SelectItem>
                      <SelectItem value="social_media">إدارة وسائل التواصل الاجتماعي</SelectItem>
                      <SelectItem value="custom">خدمة مخصصة</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">وصف القالب</Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="وصف مختصر لنوع العقد والخدمات المشمولة..."
                  rows={3}
                />
              </div>

              <div className="flex items-center space-x-2 space-x-reverse">
                <Switch
                  id="is_active"
                  checked={watch('is_active')}
                  onCheckedChange={(checked) => setValue('is_active', checked)}
                />
                <Label htmlFor="is_active">قالب نشط</Label>
              </div>
            </CardContent>
          </Card>

          {/* Template Content */}
          <Card>
            <CardHeader>
              <CardTitle>محتوى القالب</CardTitle>
              <CardDescription>
                محتوى العقد مع إمكانية استخدام متغيرات ديناميكية مثل {'{client_name}'}, {'{project_name}'}, {'{contract_value}'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="content">نص العقد *</Label>
                <Textarea
                  id="content"
                  {...register('content', { required: 'محتوى القالب مطلوب' })}
                  placeholder="اكتب محتوى العقد هنا..."
                  rows={20}
                  className="font-mono text-sm"
                />
                {errors.content && (
                  <p className="text-sm text-red-600">{errors.content.message}</p>
                )}
              </div>

              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">المتغيرات المتاحة:</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm text-blue-700">
                  <code>{'{client_name}'}</code>
                  <code>{'{client_email}'}</code>
                  <code>{'{client_phone}'}</code>
                  <code>{'{project_name}'}</code>
                  <code>{'{project_description}'}</code>
                  <code>{'{contract_value}'}</code>
                  <code>{'{start_date}'}</code>
                  <code>{'{end_date}'}</code>
                  <code>{'{company_name}'}</code>
                  <code>{'{company_address}'}</code>
                  <code>{'{contract_number}'}</code>
                  <code>{'{current_date}'}</code>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex items-center justify-end gap-4">
            <Button type="button" variant="outline" onClick={handleBack}>
              إلغاء
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="bg-purple-600 hover:bg-purple-700"
            >
              <Save className="h-4 w-4 ml-2" />
              {isSubmitting ? 'جاري الحفظ...' : 'حفظ التغييرات'}
            </Button>
          </div>
        </form>
      </div>
    </UnifiedLayout>
  );
}
