'use client';

import { UnifiedLayout } from '@/components/layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useContractTemplate, useContractTemplateMutations } from '@/lib/hooks/use-contracts';
import { useAuthStore } from '@/lib/stores/auth-store';
import {
  ArrowLeft,
  Edit,
  Copy,
  Trash2,
  FileText,
  Download,
  Eye,
  Globe,
  Smartphone,
  Monitor,
  ShoppingCart,
  Wrench,
  TrendingUp,
  Palette,
  Search as SearchIcon,
  Share2,
  AlertTriangle,
  RefreshCw,
  Calendar,
  User,
} from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function ContractTemplateDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);

  const templateId = params.id as string;
  const { data: template, isLoading, error, refetch } = useContractTemplate(templateId);
  const { deleteTemplate } = useContractTemplateMutations();

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل تفاصيل القالب...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى تفاصيل القالب</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <UnifiedLayout>
        <div className="max-w-4xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
              <p className="text-gray-600">جاري تحميل تفاصيل القالب...</p>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  // Error state
  if (error || !template) {
    return (
      <UnifiedLayout>
        <div className="max-w-4xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل القالب</h3>
              <p className="text-gray-600 mb-4">
                {error ? 'حدث خطأ أثناء تحميل تفاصيل القالب' : 'القالب غير موجود'}
              </p>
              <div className="flex gap-2 justify-center">
                <Button onClick={() => refetch()} variant="outline">
                  <RefreshCw className="h-4 w-4 ml-2" />
                  إعادة المحاولة
                </Button>
                <Button onClick={() => router.push('/founder-dashboard/contracts/templates')}>
                  <ArrowLeft className="h-4 w-4 ml-2" />
                  العودة للقوالب
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  // Template type icons
  const getTemplateIcon = (type: string) => {
    const iconMap = {
      website: Globe,
      mobile_app: Smartphone,
      web_app: Monitor,
      ecommerce: ShoppingCart,
      maintenance: Wrench,
      marketing: TrendingUp,
      branding: Palette,
      seo: SearchIcon,
      social_media: Share2,
      custom: FileText,
    };
    return iconMap[type as keyof typeof iconMap] || FileText;
  };

  // Template type colors
  const getTemplateColor = (type: string) => {
    const colorMap = {
      website: 'bg-blue-100 text-blue-800',
      mobile_app: 'bg-green-100 text-green-800',
      web_app: 'bg-purple-100 text-purple-800',
      ecommerce: 'bg-orange-100 text-orange-800',
      maintenance: 'bg-gray-100 text-gray-800',
      marketing: 'bg-pink-100 text-pink-800',
      branding: 'bg-indigo-100 text-indigo-800',
      seo: 'bg-yellow-100 text-yellow-800',
      social_media: 'bg-cyan-100 text-cyan-800',
      custom: 'bg-slate-100 text-slate-800',
    };
    return colorMap[type as keyof typeof colorMap] || 'bg-gray-100 text-gray-800';
  };

  // Handle actions
  const handleBack = () => {
    router.push('/founder-dashboard/contracts/templates');
  };

  const handleEdit = () => {
    router.push(`/founder-dashboard/contracts/templates/${template.id}/edit`);
  };

  const handleClone = () => {
    router.push(`/founder-dashboard/contracts/templates/new?clone=${template.id}`);
  };

  const handleDelete = async () => {
    if (confirm(`هل أنت متأكد من حذف قالب "${template.name}"؟ هذا الإجراء لا يمكن التراجع عنه.`)) {
      try {
        await deleteTemplate.mutateAsync(template.id);
        router.push('/founder-dashboard/contracts/templates');
      } catch (error) {
        console.error('Error deleting template:', error);
      }
    }
  };

  const handlePreview = () => {
    // TODO: Implement preview functionality
    console.log('Preview template:', template);
  };

  const handleDownload = () => {
    // TODO: Implement download functionality
    console.log('Download template:', template);
  };

  const IconComponent = getTemplateIcon(template.template_type);

  return (
    <UnifiedLayout>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة للقوالب
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <IconComponent className="h-8 w-8 text-purple-600" />
                {template.name}
              </h1>
              <div className="flex items-center gap-2 mt-1">
                <Badge className={getTemplateColor(template.template_type)}>
                  {template.template_type_display}
                </Badge>
                <Badge variant={template.is_active ? 'default' : 'secondary'}>
                  {template.is_active ? 'نشط' : 'غير نشط'}
                </Badge>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handlePreview}>
              <Eye className="h-4 w-4 ml-2" />
              معاينة
            </Button>
            <Button variant="outline" onClick={handleDownload}>
              <Download className="h-4 w-4 ml-2" />
              تحميل
            </Button>
            <Button variant="outline" onClick={handleEdit}>
              <Edit className="h-4 w-4 ml-2" />
              تعديل
            </Button>
            <Button variant="outline" onClick={handleClone}>
              <Copy className="h-4 w-4 ml-2" />
              نسخ
            </Button>
            <Button 
              variant="outline" 
              onClick={handleDelete}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4 ml-2" />
              حذف
            </Button>
          </div>
        </div>

        {/* Template Information */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">معلومات القالب</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-gray-600">تاريخ الإنشاء:</span>
                <span>{new Date(template.created_at).toLocaleDateString('ar-EG')}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-gray-600">آخر تحديث:</span>
                <span>{new Date(template.updated_at).toLocaleDateString('ar-EG')}</span>
              </div>
              {template.created_by_name && (
                <div className="flex items-center gap-2 text-sm">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">منشئ القالب:</span>
                  <span>{template.created_by_name}</span>
                </div>
              )}
              {template.usage_count !== undefined && (
                <div className="flex items-center gap-2 text-sm">
                  <FileText className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">مرات الاستخدام:</span>
                  <span>{template.usage_count}</span>
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="text-lg">وصف القالب</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 leading-relaxed">
                {template.description || 'لا يوجد وصف لهذا القالب.'}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Template Content */}
        <Card>
          <CardHeader>
            <CardTitle>محتوى القالب</CardTitle>
            <CardDescription>
              محتوى العقد مع المتغيرات الديناميكية
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-50 rounded-lg p-6 border">
              <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono leading-relaxed">
                {template.content}
              </pre>
            </div>
            
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">المتغيرات المستخدمة في هذا القالب:</h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm text-blue-700">
                {template.content.match(/\{[^}]+\}/g)?.map((variable, index) => (
                  <code key={index} className="bg-blue-100 px-2 py-1 rounded">
                    {variable}
                  </code>
                )) || <span className="text-blue-600">لا توجد متغيرات في هذا القالب</span>}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </UnifiedLayout>
  );
}
