'use client';

import { UnifiedLayout } from '@/components/layout';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { teamAPI } from '@/lib/api';
import { useAuthStore } from '@/lib/stores/auth-store';
import { showToast } from '@/lib/toast';
import {
    Bug,
    CheckCircle,
    Clock,
    Code,
    Edit,
    Eye,
    GitBranch,
    Loader2,
    Mail,
    MoreHorizontal,
    Search,
    Trash2,
    UserPlus
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

// Egyptian Developers Team Dummy Data
const DEVELOPERS_TEAM_DUMMY_DATA = [
  {
    id: 1,
    full_name: 'عمر أحمد محمد',
    position: 'مطور Full Stack أول',
    user: { email: '<EMAIL>', username: 'omar_ahmed' },
    employee_id: 'DEV001',
    department: 'development',
    status: 'active',
    hire_date: '2022-08-15',
    performance_score: 9.5,
    skills: ['React', 'Node.js', 'Python', 'Django', 'PostgreSQL', 'AWS'],
    specialization: 'Full Stack Development',
    experience_years: 5,
    completed_projects: 28,
    total_tasks_completed: 156,
    current_projects: 3,
    code_quality_score: 9.2,
    github_commits_this_month: 145,
    hours_logged_this_week: 42,
    certifications: ['AWS Solutions Architect', 'React Professional'],
    phone: '+20 ************'
  },
  {
    id: 2,
    full_name: 'سارة محمود علي',
    position: 'مطورة Frontend',
    user: { email: '<EMAIL>', username: 'sara_mahmoud' },
    employee_id: 'DEV002',
    department: 'development',
    status: 'active',
    hire_date: '2023-02-10',
    performance_score: 8.8,
    skills: ['React', 'TypeScript', 'Next.js', 'Tailwind CSS', 'Figma'],
    specialization: 'Frontend Development',
    experience_years: 3,
    completed_projects: 18,
    total_tasks_completed: 89,
    current_projects: 2,
    code_quality_score: 8.9,
    github_commits_this_month: 98,
    hours_logged_this_week: 40,
    certifications: ['React Advanced', 'TypeScript Professional'],
    phone: '+20 ************'
  },
  {
    id: 3,
    full_name: 'أحمد حسام الدين',
    position: 'مطور Backend',
    user: { email: '<EMAIL>', username: 'ahmed_hossam' },
    employee_id: 'DEV003',
    department: 'development',
    status: 'active',
    hire_date: '2023-05-20',
    performance_score: 8.6,
    skills: ['Python', 'Django', 'FastAPI', 'PostgreSQL', 'Redis', 'Docker'],
    specialization: 'Backend Development',
    experience_years: 4,
    completed_projects: 22,
    total_tasks_completed: 134,
    current_projects: 2,
    code_quality_score: 8.7,
    github_commits_this_month: 112,
    hours_logged_this_week: 38,
    certifications: ['Python Professional', 'Django Advanced'],
    phone: '+20 ************'
  },
  {
    id: 4,
    full_name: 'مريم عبد الرحمن',
    position: 'مطورة Mobile',
    user: { email: '<EMAIL>', username: 'mariam_abdelrahman' },
    employee_id: 'DEV004',
    department: 'development',
    status: 'active',
    hire_date: '2023-09-01',
    performance_score: 8.4,
    skills: ['React Native', 'Flutter', 'iOS', 'Android', 'Firebase'],
    specialization: 'Mobile Development',
    experience_years: 3,
    completed_projects: 12,
    total_tasks_completed: 67,
    current_projects: 2,
    code_quality_score: 8.5,
    github_commits_this_month: 87,
    hours_logged_this_week: 41,
    certifications: ['React Native Professional', 'Flutter Certified'],
    phone: '+20 ************'
  }
];

const DEVELOPERS_METRICS_DUMMY = {
  department_metrics: {
    active_projects: 9,
    completed_tasks: 446,
    bugs_resolved: 23,
    total_projects: 60,
    code_quality_avg: 8.8,
    deployment_success_rate: 96
  },
  active_members: 4,
  total_commits_this_month: 442,
  avg_performance_score: 8.8,
  total_hours_logged: 161,
  certifications_count: 8
};

export default function DevelopersTeamPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [developersTeamMembers, setDevelopersTeamMembers] = useState([]);
  const [developersStats, setDevelopersStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && isAuthenticated) {
      fetchDevelopersTeamData();
    }
  }, [mounted, isAuthenticated]);

  const fetchDevelopersTeamData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch developers team members using department-specific endpoint
      const teamResponse = await teamAPI.getDepartmentMembers('development');
      const teamMembers = teamResponse.results || teamResponse || [];

      // Fetch developers-specific stats
      const statsResponse = await teamAPI.getDepartmentStats('development');

      // Use real data if available, otherwise fallback to dummy data
      if (teamMembers.length > 0) {
        setDevelopersTeamMembers(teamMembers);
        setDevelopersStats(statsResponse || DEVELOPERS_METRICS_DUMMY);
      } else {
        // Use dummy data when no real data is available
        setDevelopersTeamMembers(DEVELOPERS_TEAM_DUMMY_DATA);
        setDevelopersStats(DEVELOPERS_METRICS_DUMMY);
        console.log('Using dummy developers team data for demonstration');
      }

    } catch (err) {
      console.error('Error fetching developers team data:', err);
      // Use dummy data as fallback when API fails
      setDevelopersTeamMembers(DEVELOPERS_TEAM_DUMMY_DATA);
      setDevelopersStats(DEVELOPERS_METRICS_DUMMY);
      showToast.error('تم تحميل البيانات التجريبية - تحقق من اتصال الخادم');
    } finally {
      setLoading(false);
    }
  };

  const filteredMembers = developersTeamMembers.filter(member =>
    member.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.user?.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.position?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddMember = () => {
    router.push('/founder-dashboard/team?add=development');
  };

  const handleEditMember = (member) => {
    router.push(`/founder-dashboard/team?edit=${member.id}`);
  };

  const handleDeleteMember = (member) => {
    // Implement delete functionality
    console.log('Delete member:', member);
  };

  const handleViewMember = (member) => {
    router.push(`/founder-dashboard/team?view=${member.id}`);
  };

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل فريق المطورين...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى فريق المطورين</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <UnifiedLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
            <p className="text-gray-600">جاري تحميل بيانات فريق المطورين...</p>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  if (error) {
    return (
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-red-500 mb-4">
                <Code className="h-12 w-12 mx-auto mb-4" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل البيانات</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={fetchDevelopersTeamData} className="bg-purple-600 hover:bg-purple-700">
                إعادة المحاولة
              </Button>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Code className="h-8 w-8 text-blue-600" />
              فريق المطورين
            </h1>
            <p className="text-gray-600 mt-1">
              إدارة وتتبع أداء فريق التطوير والمشاريع التقنية
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Button onClick={handleAddMember} className="bg-blue-600 hover:bg-blue-700">
              <UserPlus className="h-4 w-4 ml-2" />
              إضافة مطور جديد
            </Button>
          </div>
        </div>

        {/* Development Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المشاريع النشطة</CardTitle>
              <GitBranch className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {developersStats?.department_metrics?.active_projects || 0}
              </div>
              <p className="text-xs text-gray-600">+2 من الشهر الماضي</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المهام المكتملة</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {developersStats?.department_metrics?.completed_tasks || 0}
              </div>
              <p className="text-xs text-gray-600">+18% من الشهر الماضي</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">الأخطاء المحلولة</CardTitle>
              <Bug className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {developersStats?.department_metrics?.bugs_resolved || 0}
              </div>
              <p className="text-xs text-gray-600">-12% من الشهر الماضي</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">أعضاء الفريق</CardTitle>
              <Clock className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {developersStats?.active_members || developersTeamMembers.length}
              </div>
              <p className="text-xs text-gray-600">نشط</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في فريق المطورين..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Team Members Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMembers.map((member) => (
            <Card key={member.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-bold text-lg">
                        {member.full_name ? member.full_name.split(' ').map(n => n[0]).join('') : 'N/A'}
                      </span>
                    </div>
                    <div>
                      <CardTitle className="text-lg">{member.full_name || 'غير محدد'}</CardTitle>
                      <Badge variant="outline" className="text-blue-600 border-blue-600">
                        {member.position || 'مطور'}
                      </Badge>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewMember(member)}>
                        <Eye className="h-4 w-4 ml-2" />
                        عرض الملف الشخصي
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditMember(member)}>
                        <Edit className="h-4 w-4 ml-2" />
                        تعديل
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => handleDeleteMember(member)}
                      >
                        <Trash2 className="h-4 w-4 ml-2" />
                        حذف
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Mail className="h-4 w-4" />
                    <span>{member.user?.email || 'غير محدد'}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <GitBranch className="h-4 w-4" />
                    <span>المشاريع: {member.completed_projects || 0}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <CheckCircle className="h-4 w-4" />
                    <span>المهام: {member.total_tasks_completed || 0}</span>
                  </div>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {member.skills?.slice(0, 3).map((skill, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                    {member.skills?.length > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{member.skills.length - 3}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredMembers.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <Code className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا يوجد أعضاء في فريق المطورين</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm 
                  ? 'لا يوجد أعضاء فريق يطابقون معايير البحث المحددة'
                  : 'لم يتم إضافة أي أعضاء لفريق المطورين بعد'
                }
              </p>
              {!searchTerm && (
                <Button onClick={handleAddMember} className="bg-blue-600 hover:bg-blue-700">
                  <UserPlus className="h-4 w-4 ml-2" />
                  إضافة أول مطور
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </UnifiedLayout>
  );
}
