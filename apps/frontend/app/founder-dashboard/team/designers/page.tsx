'use client';

import { UnifiedLayout } from '@/components/layout';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { teamAPI } from '@/lib/api';
import { useAuthStore } from '@/lib/stores/auth-store';
import { showToast } from '@/lib/toast';
import {
    Edit,
    Eye,
    Image,
    Layers,
    Loader2,
    Mail,
    MoreHorizontal,
    Palette,
    Search,
    Star,
    Trash2,
    UserPlus,
    Zap
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

// Egyptian Designers Team Dummy Data
const DESIGNERS_TEAM_DUMMY_DATA = [
  {
    id: 1,
    full_name: 'ياسمين أحمد فؤاد',
    position: 'مصممة جرافيك أولى',
    user: { email: '<EMAIL>', username: 'yasmin_ahmed' },
    employee_id: 'DES001',
    department: 'design',
    status: 'active',
    hire_date: '2022-11-10',
    performance_score: 9.1,
    skills: ['Adobe Photoshop', 'Illustrator', 'InDesign', 'Figma', 'Brand Identity'],
    specialization: 'Brand Identity Design',
    experience_years: 4,
    completed_projects: 32,
    current_projects: 4,
    client_satisfaction: 9.3,
    portfolio_pieces: 85,
    phone: '+20 ************'
  },
  {
    id: 2,
    full_name: 'محمد عبد الرحمن سالم',
    position: 'مصمم UI/UX',
    user: { email: '<EMAIL>', username: 'mohamed_abdelrahman' },
    employee_id: 'DES002',
    department: 'design',
    status: 'active',
    hire_date: '2023-03-15',
    performance_score: 8.9,
    skills: ['Figma', 'Adobe XD', 'Sketch', 'Prototyping', 'User Research'],
    specialization: 'UI/UX Design',
    experience_years: 3,
    completed_projects: 24,
    current_projects: 3,
    client_satisfaction: 8.8,
    portfolio_pieces: 67,
    phone: '+20 ************'
  },
  {
    id: 3,
    full_name: 'دينا محمود حسن',
    position: 'مصممة ويب',
    user: { email: '<EMAIL>', username: 'dina_mahmoud' },
    employee_id: 'DES003',
    department: 'design',
    status: 'active',
    hire_date: '2023-07-01',
    performance_score: 8.6,
    skills: ['HTML/CSS', 'WordPress', 'Webflow', 'Responsive Design', 'Animation'],
    specialization: 'Web Design',
    experience_years: 2,
    completed_projects: 18,
    current_projects: 2,
    client_satisfaction: 8.7,
    portfolio_pieces: 45,
    phone: '+20 ************'
  },
  {
    id: 4,
    full_name: 'أمير سامي عبد الله',
    position: 'مصمم موشن جرافيك',
    user: { email: '<EMAIL>', username: 'amir_samy' },
    employee_id: 'DES004',
    department: 'design',
    status: 'active',
    hire_date: '2023-10-12',
    performance_score: 8.4,
    skills: ['After Effects', 'Premiere Pro', 'Cinema 4D', '3D Animation', 'Video Editing'],
    specialization: 'Motion Graphics',
    experience_years: 3,
    completed_projects: 15,
    current_projects: 3,
    client_satisfaction: 8.5,
    portfolio_pieces: 38,
    phone: '+20 ************'
  }
];

const DESIGNERS_METRICS_DUMMY = {
  department_metrics: {
    active_projects: 12,
    completed_projects: 89,
    client_satisfaction_avg: 8.8,
    on_time_delivery: 94,
    portfolio_pieces_total: 235
  },
  active_members: 4,
  total_designs_this_month: 67,
  avg_performance_score: 8.8,
  client_approvals_rate: 92
};

export default function DesignersTeamPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [designersTeamMembers, setDesignersTeamMembers] = useState([]);
  const [designersStats, setDesignersStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && isAuthenticated) {
      fetchDesignersTeamData();
    }
  }, [mounted, isAuthenticated]);

  const fetchDesignersTeamData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch designers team members using department-specific endpoint
      const teamResponse = await teamAPI.getDepartmentMembers('design');
      const teamMembers = teamResponse.results || teamResponse || [];

      // Fetch designers-specific stats
      const statsResponse = await teamAPI.getDepartmentStats('design');

      // Use real data if available, otherwise fallback to dummy data
      if (teamMembers.length > 0) {
        setDesignersTeamMembers(teamMembers);
        setDesignersStats(statsResponse || DESIGNERS_METRICS_DUMMY);
      } else {
        // Use dummy data when no real data is available
        setDesignersTeamMembers(DESIGNERS_TEAM_DUMMY_DATA);
        setDesignersStats(DESIGNERS_METRICS_DUMMY);
        console.log('Using dummy designers team data for demonstration');
      }

    } catch (err) {
      console.error('Error fetching designers team data:', err);
      // Use dummy data as fallback when API fails
      setDesignersTeamMembers(DESIGNERS_TEAM_DUMMY_DATA);
      setDesignersStats(DESIGNERS_METRICS_DUMMY);
      showToast.error('تم تحميل البيانات التجريبية - تحقق من اتصال الخادم');
    } finally {
      setLoading(false);
    }
  };

  const filteredMembers = designersTeamMembers.filter(member =>
    member.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.user?.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.position?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddMember = () => {
    router.push('/founder-dashboard/team?add=design');
  };

  const handleEditMember = (member) => {
    router.push(`/founder-dashboard/team?edit=${member.id}`);
  };

  const handleDeleteMember = (member) => {
    // Implement delete functionality
    console.log('Delete member:', member);
  };

  const handleViewMember = (member) => {
    router.push(`/founder-dashboard/team?view=${member.id}`);
  };

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل فريق المصممين...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى فريق المصممين</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <UnifiedLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
            <p className="text-gray-600">جاري تحميل بيانات فريق المصممين...</p>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  if (error) {
    return (
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-red-500 mb-4">
                <Palette className="h-12 w-12 mx-auto mb-4" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل البيانات</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={fetchDesignersTeamData} className="bg-purple-600 hover:bg-purple-700">
                إعادة المحاولة
              </Button>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Palette className="h-8 w-8 text-pink-600" />
              فريق مصممي الويب
            </h1>
            <p className="text-gray-600 mt-1">
              إدارة وتتبع أداء فريق التصميم والمشاريع الإبداعية
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Button onClick={handleAddMember} className="bg-pink-600 hover:bg-pink-700">
              <UserPlus className="h-4 w-4 ml-2" />
              إضافة مصمم جديد
            </Button>
          </div>
        </div>

        {/* Design Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">التصاميم المكتملة</CardTitle>
              <Image className="h-4 w-4 text-pink-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-pink-600">67</div>
              <p className="text-xs text-gray-600">+12% من الشهر الماضي</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">تقييم العملاء</CardTitle>
              <Star className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">4.8</div>
              <p className="text-xs text-gray-600">من 5 نجوم</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المشاريع النشطة</CardTitle>
              <Layers className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">15</div>
              <p className="text-xs text-gray-600">+3 من الشهر الماضي</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">أعضاء الفريق</CardTitle>
              <Zap className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">{designersTeamMembers.length}</div>
              <p className="text-xs text-gray-600">نشط</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في فريق المصممين..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Team Members Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMembers.map((member) => (
            <Card key={member.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center">
                      <span className="text-pink-600 font-bold text-lg">
                        {member.full_name ? member.full_name.split(' ').map(n => n[0]).join('') : 'N/A'}
                      </span>
                    </div>
                    <div>
                      <CardTitle className="text-lg">{member.full_name || 'غير محدد'}</CardTitle>
                      <Badge variant="outline" className="text-pink-600 border-pink-600">
                        {member.position || 'مصمم ويب'}
                      </Badge>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewMember(member)}>
                        <Eye className="h-4 w-4 ml-2" />
                        عرض الملف الشخصي
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditMember(member)}>
                        <Edit className="h-4 w-4 ml-2" />
                        تعديل
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => handleDeleteMember(member)}
                      >
                        <Trash2 className="h-4 w-4 ml-2" />
                        حذف
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Mail className="h-4 w-4" />
                    <span>{member.user?.email || 'غير محدد'}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Image className="h-4 w-4" />
                    <span>التصاميم: {member.completed_projects || 0}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Star className="h-4 w-4" />
                    <span>التقييم: 4.7/5</span>
                  </div>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {member.skills?.slice(0, 3).map((skill, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                    {member.skills?.length > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{member.skills.length - 3}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredMembers.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <Palette className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا يوجد أعضاء في فريق المصممين</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm 
                  ? 'لا يوجد أعضاء فريق يطابقون معايير البحث المحددة'
                  : 'لم يتم إضافة أي أعضاء لفريق المصممين بعد'
                }
              </p>
              {!searchTerm && (
                <Button onClick={handleAddMember} className="bg-pink-600 hover:bg-pink-700">
                  <UserPlus className="h-4 w-4 ml-2" />
                  إضافة أول مصمم
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </UnifiedLayout>
  );
}
