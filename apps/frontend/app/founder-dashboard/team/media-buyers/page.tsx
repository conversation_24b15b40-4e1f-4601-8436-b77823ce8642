'use client';

import { UnifiedLayout } from '@/components/layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { teamAPI } from '@/lib/api';
import { useAuthStore } from '@/lib/stores/auth-store';
import { showToast } from '@/lib/toast';
import { formatCurrency } from '@/lib/utils';
import {
    BarChart3,
    DollarSign,
    Edit,
    Eye,
    Loader2,
    Mail,
    MoreHorizontal,
    MousePointer,
    Search,
    Target,
    Trash2,
    TrendingUp,
    UserPlus
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

// Egyptian Media Buyers Team Dummy Data
const MEDIA_BUYERS_TEAM_DUMMY_DATA = [
  {
    id: 1,
    full_name: 'أحمد محمد الشريف',
    position: 'مدير شراء الإعلانات',
    user: { email: '<EMAIL>', username: 'ahmed_elsherif' },
    employee_id: 'MB001',
    department: 'media_buying',
    status: 'active',
    hire_date: '2022-09-20',
    performance_score: 9.3,
    skills: ['Facebook Ads', 'Google Ads', 'TikTok Ads', 'Analytics', 'Campaign Optimization'],
    specialization: 'Paid Social Media',
    experience_years: 5,
    campaigns_managed: 45,
    current_campaigns: 8,
    total_ad_spend: 250000,
    avg_roas: 4.2,
    conversion_rate: 3.8,
    phone: '+20 ************'
  },
  {
    id: 2,
    full_name: 'فاطمة عبد الرحمن نور',
    position: 'أخصائية إعلانات فيسبوك',
    user: { email: '<EMAIL>', username: 'fatma_nour' },
    employee_id: 'MB002',
    department: 'media_buying',
    status: 'active',
    hire_date: '2023-02-15',
    performance_score: 8.9,
    skills: ['Facebook Ads', 'Instagram Ads', 'Audience Research', 'Creative Testing', 'Pixel Setup'],
    specialization: 'Facebook & Instagram Advertising',
    experience_years: 3,
    campaigns_managed: 32,
    current_campaigns: 6,
    total_ad_spend: 180000,
    avg_roas: 3.9,
    conversion_rate: 3.5,
    phone: '+20 ************'
  },
  {
    id: 3,
    full_name: 'محمد حسام الدين علي',
    position: 'أخصائي إعلانات جوجل',
    user: { email: '<EMAIL>', username: 'mohamed_hossam_mb' },
    employee_id: 'MB003',
    department: 'media_buying',
    status: 'active',
    hire_date: '2023-06-10',
    performance_score: 8.7,
    skills: ['Google Ads', 'YouTube Ads', 'Search Campaigns', 'Shopping Ads', 'Analytics'],
    specialization: 'Google Advertising',
    experience_years: 4,
    campaigns_managed: 28,
    current_campaigns: 5,
    total_ad_spend: 160000,
    avg_roas: 4.1,
    conversion_rate: 4.2,
    phone: '+20 ************'
  },
  {
    id: 4,
    full_name: 'نورا سامي محمود',
    position: 'أخصائية تحليل الإعلانات',
    user: { email: '<EMAIL>', username: 'nora_samy_mb' },
    employee_id: 'MB004',
    department: 'media_buying',
    status: 'active',
    hire_date: '2023-11-01',
    performance_score: 8.5,
    skills: ['Data Analysis', 'Google Analytics', 'Facebook Analytics', 'Reporting', 'ROI Optimization'],
    specialization: 'Campaign Analytics',
    experience_years: 2,
    campaigns_managed: 20,
    current_campaigns: 4,
    total_ad_spend: 120000,
    avg_roas: 3.7,
    conversion_rate: 3.3,
    phone: '+20 ************'
  }
];

const MEDIA_BUYERS_METRICS_DUMMY = {
  department_metrics: {
    active_campaigns: 23,
    total_ad_spend: 710000,
    avg_roas: 4.0,
    total_conversions: 1250,
    avg_conversion_rate: 3.7,
    campaigns_this_month: 12
  },
  active_members: 4,
  total_campaigns_managed: 125,
  avg_performance_score: 8.9,
  client_satisfaction: 9.1
};

export default function MediaBuyersTeamPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [mediaBuyersTeamMembers, setMediaBuyersTeamMembers] = useState([]);
  const [mediaBuyersStats, setMediaBuyersStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && isAuthenticated) {
      fetchMediaBuyersTeamData();
    }
  }, [mounted, isAuthenticated]);

  const fetchMediaBuyersTeamData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch media buyers team members using department-specific endpoint
      const teamResponse = await teamAPI.getDepartmentMembers('media_buying');
      const teamMembers = teamResponse.results || teamResponse || [];

      // Fetch media buyers-specific stats
      const statsResponse = await teamAPI.getDepartmentStats('media_buying');

      // Use real data if available, otherwise fallback to dummy data
      if (teamMembers.length > 0) {
        setMediaBuyersTeamMembers(teamMembers);
        setMediaBuyersStats(statsResponse || MEDIA_BUYERS_METRICS_DUMMY);
      } else {
        // Use dummy data when no real data is available
        setMediaBuyersTeamMembers(MEDIA_BUYERS_TEAM_DUMMY_DATA);
        setMediaBuyersStats(MEDIA_BUYERS_METRICS_DUMMY);
        console.log('Using dummy media buyers team data for demonstration');
      }

    } catch (err) {
      console.error('Error fetching media buyers team data:', err);
      // Use dummy data as fallback when API fails
      setMediaBuyersTeamMembers(MEDIA_BUYERS_TEAM_DUMMY_DATA);
      setMediaBuyersStats(MEDIA_BUYERS_METRICS_DUMMY);
      showToast.error('تم تحميل البيانات التجريبية - تحقق من اتصال الخادم');
    } finally {
      setLoading(false);
    }
  };

  const filteredMembers = mediaBuyersTeamMembers.filter(member =>
    member.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.user?.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.position?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddMember = () => {
    router.push('/founder-dashboard/team?add=media_buying');
  };

  const handleEditMember = (member) => {
    router.push(`/founder-dashboard/team?edit=${member.id}`);
  };

  const handleDeleteMember = (member) => {
    // Implement delete functionality
    console.log('Delete member:', member);
  };

  const handleViewMember = (member) => {
    router.push(`/founder-dashboard/team?view=${member.id}`);
  };

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل فريق مشتري الإعلانات...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى فريق مشتري الإعلانات</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <UnifiedLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
            <p className="text-gray-600">جاري تحميل بيانات فريق مشتري الإعلانات...</p>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  if (error) {
    return (
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-red-500 mb-4">
                <Target className="h-12 w-12 mx-auto mb-4" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل البيانات</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={fetchMediaBuyersTeamData} className="bg-purple-600 hover:bg-purple-700">
                إعادة المحاولة
              </Button>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Target className="h-8 w-8 text-orange-600" />
              فريق مشتري الإعلانات
            </h1>
            <p className="text-gray-600 mt-1">
              إدارة وتتبع أداء فريق شراء الإعلانات والحملات التسويقية
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Button onClick={handleAddMember} className="bg-orange-600 hover:bg-orange-700">
              <UserPlus className="h-4 w-4 ml-2" />
              إضافة مشتري إعلانات
            </Button>
          </div>
        </div>

        {/* Media Buying Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الإنفاق</CardTitle>
              <DollarSign className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {formatCurrency(mediaBuyersStats?.department_metrics?.total_ad_spend || 710000, 'EGP')}
              </div>
              <p className="text-xs text-gray-600">+8% من الشهر الماضي</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">الحملات النشطة</CardTitle>
              <MousePointer className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {mediaBuyersStats?.department_metrics?.active_campaigns || 23}
              </div>
              <p className="text-xs text-gray-600">+3 من الشهر الماضي</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">متوسط العائد</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {mediaBuyersStats?.department_metrics?.avg_roas || 4.0}x
              </div>
              <p className="text-xs text-gray-600">+0.3 من الشهر الماضي</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">أعضاء الفريق</CardTitle>
              <BarChart3 className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">{mediaBuyersTeamMembers.length}</div>
              <p className="text-xs text-gray-600">نشط</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في فريق مشتري الإعلانات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Team Members Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMembers.map((member) => (
            <Card key={member.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                      <span className="text-orange-600 font-bold text-lg">
                        {member.full_name ? member.full_name.split(' ').map(n => n[0]).join('') : 'N/A'}
                      </span>
                    </div>
                    <div>
                      <CardTitle className="text-lg">{member.full_name || 'غير محدد'}</CardTitle>
                      <Badge variant="outline" className="text-orange-600 border-orange-600">
                        {member.position || 'مشتري إعلانات'}
                      </Badge>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewMember(member)}>
                        <Eye className="h-4 w-4 ml-2" />
                        عرض الملف الشخصي
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditMember(member)}>
                        <Edit className="h-4 w-4 ml-2" />
                        تعديل
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => handleDeleteMember(member)}
                      >
                        <Trash2 className="h-4 w-4 ml-2" />
                        حذف
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Mail className="h-4 w-4" />
                    <span>{member.user?.email || 'غير محدد'}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <DollarSign className="h-4 w-4" />
                    <span>الإنفاق: {formatCurrency(member.total_ad_spend || 120000, 'EGP')}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <TrendingUp className="h-4 w-4" />
                    <span>العائد: {member.avg_roas || 3.5}x</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Target className="h-4 w-4" />
                    <span>الحملات: {member.campaigns_managed || 15}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <BarChart3 className="h-4 w-4" />
                    <span>التحويل: {member.conversion_rate || 3.2}%</span>
                  </div>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {member.skills?.slice(0, 3).map((skill, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                    {member.skills?.length > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{member.skills.length - 3}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredMembers.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا يوجد أعضاء في فريق مشتري الإعلانات</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm 
                  ? 'لا يوجد أعضاء فريق يطابقون معايير البحث المحددة'
                  : 'لم يتم إضافة أي أعضاء لفريق مشتري الإعلانات بعد'
                }
              </p>
              {!searchTerm && (
                <Button onClick={handleAddMember} className="bg-orange-600 hover:bg-orange-700">
                  <UserPlus className="h-4 w-4 ml-2" />
                  إضافة أول مشتري إعلانات
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </UnifiedLayout>
  );
}
