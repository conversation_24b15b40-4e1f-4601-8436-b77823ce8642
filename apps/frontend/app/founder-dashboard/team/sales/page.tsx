'use client';

import { UnifiedLayout } from '@/components/layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { teamAPI } from '@/lib/api';
import { useAuthStore } from '@/lib/stores/auth-store';
import { showToast } from '@/lib/toast';
import { formatCurrency } from '@/lib/utils';
import {
    Award,
    DollarSign,
    Edit,
    Eye,
    Loader2,
    Mail,
    MoreHorizontal,
    Search,
    Target,
    Trash2,
    TrendingUp,
    UserPlus,
    Users
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

// Egyptian Sales Team Dummy Data
const SALES_TEAM_DUMMY_DATA = [
  {
    id: 1,
    full_name: 'أحمد محمد السيد',
    position: 'مدير المبيعات',
    user: { email: '<EMAIL>', username: 'ahmed_mohamed' },
    employee_id: 'EMP001',
    department: 'sales',
    status: 'active',
    hire_date: '2023-01-15',
    performance_score: 9.2,
    monthly_target: 50000,
    monthly_achieved: 47500,
    total_clients: 25,
    active_deals: 8,
    closed_deals_this_month: 12,
    commission_rate: 12.5,
    total_commission: 15750,
    skills: ['مبيعات B2B', 'إدارة العملاء', 'التفاوض', 'العروض التقديمية'],
    phone: '+20 ************',
    recent_activities: [
      { type: 'deal_closed', description: 'أغلق صفقة مع شركة النيل للتكنولوجيا', amount: 25000, date: '2024-01-20' },
      { type: 'client_meeting', description: 'اجتماع مع عميل جديد - مجموعة الأهرام', date: '2024-01-19' },
      { type: 'proposal_sent', description: 'إرسال عرض سعر لشركة المستقبل', amount: 18000, date: '2024-01-18' }
    ]
  },
  {
    id: 2,
    full_name: 'فاطمة أحمد علي',
    position: 'مندوبة مبيعات أولى',
    user: { email: '<EMAIL>', username: 'fatma_ahmed' },
    employee_id: 'EMP002',
    department: 'sales',
    status: 'active',
    hire_date: '2023-03-10',
    performance_score: 8.7,
    monthly_target: 35000,
    monthly_achieved: 38200,
    total_clients: 18,
    active_deals: 6,
    closed_deals_this_month: 9,
    commission_rate: 12.5,
    total_commission: 12250,
    skills: ['مبيعات رقمية', 'وسائل التواصل', 'خدمة العملاء', 'التسويق الإلكتروني'],
    phone: '+20 ************',
    recent_activities: [
      { type: 'deal_closed', description: 'أغلقت صفقة مع مطعم الأصالة', amount: 15000, date: '2024-01-21' },
      { type: 'lead_generated', description: 'عميل محتمل جديد من LinkedIn', date: '2024-01-20' },
      { type: 'follow_up', description: 'متابعة مع شركة الدلتا للاستيراد', date: '2024-01-19' }
    ]
  },
  {
    id: 3,
    full_name: 'محمد حسن إبراهيم',
    position: 'مندوب مبيعات',
    user: { email: '<EMAIL>', username: 'mohamed_hassan' },
    employee_id: 'EMP003',
    department: 'sales',
    status: 'active',
    hire_date: '2023-06-20',
    performance_score: 7.9,
    monthly_target: 30000,
    monthly_achieved: 28500,
    total_clients: 15,
    active_deals: 5,
    closed_deals_this_month: 7,
    commission_rate: 12.5,
    total_commission: 9500,
    skills: ['مبيعات هاتفية', 'إدارة الوقت', 'CRM', 'تحليل البيانات'],
    phone: '+20 ************',
    recent_activities: [
      { type: 'proposal_sent', description: 'عرض سعر لشركة الجيزة للأثاث', amount: 22000, date: '2024-01-21' },
      { type: 'client_call', description: 'مكالمة متابعة مع صيدلية الشفاء', date: '2024-01-20' },
      { type: 'deal_closed', description: 'أغلق صفقة مع مركز الأمل الطبي', amount: 12000, date: '2024-01-18' }
    ]
  },
  {
    id: 4,
    full_name: 'نورا سامي محمود',
    position: 'مندوبة مبيعات',
    user: { email: '<EMAIL>', username: 'nora_samy' },
    employee_id: 'EMP004',
    department: 'sales',
    status: 'active',
    hire_date: '2023-09-05',
    performance_score: 8.3,
    monthly_target: 25000,
    monthly_achieved: 26800,
    total_clients: 12,
    active_deals: 4,
    closed_deals_this_month: 6,
    commission_rate: 12.5,
    total_commission: 7200,
    skills: ['مبيعات التجارة الإلكترونية', 'التصميم', 'العروض التقديمية', 'إدارة المشاريع'],
    phone: '+20 ************',
    recent_activities: [
      { type: 'deal_closed', description: 'صفقة جديدة مع متجر الأناقة', amount: 16000, date: '2024-01-22' },
      { type: 'presentation', description: 'عرض تقديمي لشركة الإبداع للتسويق', date: '2024-01-21' },
      { type: 'lead_qualified', description: 'تأهيل عميل محتمل - مجموعة النجاح', date: '2024-01-20' }
    ]
  }
];

const SALES_METRICS_DUMMY = {
  department_metrics: {
    total_revenue: 140000,
    new_clients: 8,
    conversion_rate: 85,
    monthly_target: 140000,
    monthly_achieved: 140000,
    achievement_rate: 100
  },
  active_members: 4,
  total_commission_paid: 44700,
  pending_commission: 12300,
  avg_performance_score: 8.5,
  total_active_deals: 23,
  deals_closed_this_month: 34
};

export default function SalesTeamPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [salesTeamMembers, setSalesTeamMembers] = useState([]);
  const [salesStats, setSalesStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && isAuthenticated) {
      fetchSalesTeamData();
    }
  }, [mounted, isAuthenticated]);

  const fetchSalesTeamData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch sales team members using department-specific endpoint
      const teamResponse = await teamAPI.getDepartmentMembers('sales');
      const teamMembers = teamResponse.results || teamResponse || [];

      // Fetch sales-specific stats
      const statsResponse = await teamAPI.getDepartmentStats('sales');

      // Use real data if available, otherwise fallback to dummy data
      if (teamMembers.length > 0) {
        setSalesTeamMembers(teamMembers);
        setSalesStats(statsResponse || SALES_METRICS_DUMMY);
      } else {
        // Use dummy data when no real data is available
        setSalesTeamMembers(SALES_TEAM_DUMMY_DATA);
        setSalesStats(SALES_METRICS_DUMMY);
        console.log('Using dummy sales team data for demonstration');
      }

    } catch (err) {
      console.error('Error fetching sales team data:', err);
      // Use dummy data as fallback when API fails
      setSalesTeamMembers(SALES_TEAM_DUMMY_DATA);
      setSalesStats(SALES_METRICS_DUMMY);
      showToast.error('تم تحميل البيانات التجريبية - تحقق من اتصال الخادم');
    } finally {
      setLoading(false);
    }
  };

  const filteredMembers = salesTeamMembers.filter(member =>
    member.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.user?.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.position?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddMember = () => {
    router.push('/founder-dashboard/team?add=sales');
  };

  const handleEditMember = (member) => {
    router.push(`/founder-dashboard/team?edit=${member.id}`);
  };

  const handleDeleteMember = (member) => {
    // Implement delete functionality
    console.log('Delete member:', member);
  };

  const handleViewMember = (member) => {
    router.push(`/founder-dashboard/team?view=${member.id}`);
  };

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل فريق المبيعات...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى فريق المبيعات</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <UnifiedLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
            <p className="text-gray-600">جاري تحميل بيانات فريق المبيعات...</p>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  if (error) {
    return (
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-red-500 mb-4">
                <TrendingUp className="h-12 w-12 mx-auto mb-4" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل البيانات</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={fetchSalesTeamData} className="bg-purple-600 hover:bg-purple-700">
                إعادة المحاولة
              </Button>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <TrendingUp className="h-8 w-8 text-green-600" />
              فريق المبيعات
            </h1>
            <p className="text-gray-600 mt-1">
              إدارة وتتبع أداء فريق المبيعات والعمليات التجارية
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Button
              onClick={() => router.push('/founder-dashboard/team/sales/commissions')}
              variant="outline"
              className="border-green-600 text-green-600 hover:bg-green-50"
            >
              <DollarSign className="h-4 w-4 ml-2" />
              إدارة العمولات
            </Button>
            <Button onClick={handleAddMember} className="bg-green-600 hover:bg-green-700">
              <UserPlus className="h-4 w-4 ml-2" />
              إضافة عضو مبيعات
            </Button>
          </div>
        </div>

        {/* Sales Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي المبيعات</CardTitle>
              <DollarSign className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                ${salesStats?.department_metrics?.total_revenue?.toLocaleString() || '0'}
              </div>
              <p className="text-xs text-gray-600">+20.1% من الشهر الماضي</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">العملاء الجدد</CardTitle>
              <Users className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                +{salesStats?.department_metrics?.new_clients || 0}
              </div>
              <p className="text-xs text-gray-600">+15% من الشهر الماضي</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">معدل التحويل</CardTitle>
              <Target className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {salesStats?.department_metrics?.conversion_rate || 0}%
              </div>
              <p className="text-xs text-gray-600">+5% من الشهر الماضي</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">أعضاء الفريق</CardTitle>
              <Award className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {salesStats?.active_members || salesTeamMembers.length}
              </div>
              <p className="text-xs text-gray-600">نشط</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في فريق المبيعات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Team Members Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMembers.map((member) => (
            <Card key={member.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-green-600 font-bold text-lg">
                        {member.full_name ? member.full_name.split(' ').map(n => n[0]).join('') : 'N/A'}
                      </span>
                    </div>
                    <div>
                      <CardTitle className="text-lg">{member.full_name || 'غير محدد'}</CardTitle>
                      <Badge variant="outline" className="text-green-600 border-green-600">
                        {member.position || 'مندوب مبيعات'}
                      </Badge>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewMember(member)}>
                        <Eye className="h-4 w-4 ml-2" />
                        عرض الملف الشخصي
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditMember(member)}>
                        <Edit className="h-4 w-4 ml-2" />
                        تعديل
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => handleDeleteMember(member)}
                      >
                        <Trash2 className="h-4 w-4 ml-2" />
                        حذف
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Mail className="h-4 w-4" />
                    <span>{member.user?.email || 'غير محدد'}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <DollarSign className="h-4 w-4" />
                    <span>المبيعات: {formatCurrency(member.monthly_achieved || 12450, 'EGP')}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Target className="h-4 w-4" />
                    <span>الهدف: {formatCurrency(member.monthly_target || 25000, 'EGP')}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Award className="h-4 w-4" />
                    <span>الأداء: {member.performance_score || 8.0}/10</span>
                  </div>
                  {member.total_clients && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Users className="h-4 w-4" />
                      <span>العملاء: {member.total_clients}</span>
                    </div>
                  )}
                  {member.commission_rate && (
                    <div className="flex items-center gap-2 text-sm text-green-600">
                      <DollarSign className="h-4 w-4" />
                      <span>العمولة: {member.commission_rate}%</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredMembers.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا يوجد أعضاء في فريق المبيعات</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm 
                  ? 'لا يوجد أعضاء فريق يطابقون معايير البحث المحددة'
                  : 'لم يتم إضافة أي أعضاء لفريق المبيعات بعد'
                }
              </p>
              {!searchTerm && (
                <Button onClick={handleAddMember} className="bg-green-600 hover:bg-green-700">
                  <UserPlus className="h-4 w-4 ml-2" />
                  إضافة أول عضو مبيعات
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </UnifiedLayout>
  );
}
