'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  DollarSign,
  Search,
  Filter,
  CheckCircle,
  Clock,
  AlertTriangle,
  TrendingUp,
  Users,
  Calendar,
  MoreHorizontal,
  Eye,
  Check,
  X,
  Loader2
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';
import { commissionsAPI } from '@/lib/api';
import { showToast } from '@/lib/toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface Commission {
  id: string;
  sales_rep: {
    id: string;
    first_name: string;
    last_name: string;
  };
  client: {
    id: string;
    name: string;
  };
  project: {
    id: string;
    name: string;
  };
  project_amount: number;
  commission_percentage: number;
  commission_amount: number;
  status: 'pending' | 'approved' | 'paid' | 'cancelled';
  status_display: string;
  is_overdue: boolean;
  earned_date: string;
  approved_date?: string;
  paid_date?: string;
}

interface CommissionStats {
  total_commissions: number;
  pending_commissions: number;
  approved_commissions: number;
  paid_commissions: number;
  total_commission_amount: number;
  pending_commission_amount: number;
  paid_commission_amount: number;
  overdue_commissions: number;
}

export default function CommissionsPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [loading, setLoading] = useState(true);
  const [commissions, setCommissions] = useState<Commission[]>([]);
  const [stats, setStats] = useState<CommissionStats | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && isAuthenticated) {
      fetchCommissions();
      fetchStats();
    }
  }, [mounted, isAuthenticated]);

  const fetchCommissions = async () => {
    try {
      setLoading(true);
      const params: any = {};
      
      if (statusFilter !== 'all') {
        params.status = statusFilter;
      }
      
      if (searchTerm) {
        params.search = searchTerm;
      }

      const response = await commissionsAPI.getCommissions(params);
      setCommissions(response.results || response);
    } catch (error) {
      console.error('Error fetching commissions:', error);
      showToast.error('فشل في تحميل العمولات');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const statsData = await commissionsAPI.getCommissionStats();
      setStats(statsData);
    } catch (error) {
      console.error('Error fetching commission stats:', error);
    }
  };

  const handleApproveCommission = async (commissionId: string) => {
    try {
      setActionLoading(commissionId);
      await commissionsAPI.approveCommission(commissionId);
      showToast.success('تم الموافقة على العمولة بنجاح');
      fetchCommissions();
      fetchStats();
    } catch (error) {
      console.error('Error approving commission:', error);
      showToast.error('فشل في الموافقة على العمولة');
    } finally {
      setActionLoading(null);
    }
  };

  const handleMarkAsPaid = async (commissionId: string) => {
    try {
      setActionLoading(commissionId);
      await commissionsAPI.markCommissionAsPaid(commissionId);
      showToast.success('تم تسجيل دفع العمولة بنجاح');
      fetchCommissions();
      fetchStats();
    } catch (error) {
      console.error('Error marking commission as paid:', error);
      showToast.error('فشل في تسجيل دفع العمولة');
    } finally {
      setActionLoading(null);
    }
  };

  const getStatusBadge = (status: string, isOverdue: boolean) => {
    if (isOverdue && status !== 'paid') {
      return <Badge variant="destructive">متأخر</Badge>;
    }
    
    switch (status) {
      case 'pending':
        return <Badge variant="secondary">في الانتظار</Badge>;
      case 'approved':
        return <Badge variant="default">موافق عليها</Badge>;
      case 'paid':
        return <Badge variant="outline" className="text-green-600 border-green-600">مدفوعة</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">ملغية</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG');
  };

  const filteredCommissions = commissions.filter(commission => {
    const matchesSearch = searchTerm === '' || 
      commission.client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      commission.project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      `${commission.sales_rep.first_name} ${commission.sales_rep.last_name}`.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || commission.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  if (!mounted) {
    return null;
  }

  if (!isAuthenticated) {
    router.push('/login');
    return null;
  }

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <DollarSign className="h-8 w-8 text-green-600" />
              إدارة العمولات
            </h1>
            <p className="text-gray-600 mt-1">
              تتبع وإدارة عمولات فريق المبيعات
            </p>
          </div>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">إجمالي العمولات</CardTitle>
                <DollarSign className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(stats.total_commission_amount)}
                </div>
                <p className="text-xs text-gray-600">
                  {stats.total_commissions} عمولة
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">في الانتظار</CardTitle>
                <Clock className="h-4 w-4 text-yellow-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">
                  {formatCurrency(stats.pending_commission_amount)}
                </div>
                <p className="text-xs text-gray-600">
                  {stats.pending_commissions} عمولة
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">مدفوعة</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(stats.paid_commission_amount)}
                </div>
                <p className="text-xs text-gray-600">
                  {stats.paid_commissions} عمولة
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">متأخرة</CardTitle>
                <AlertTriangle className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {stats.overdue_commissions}
                </div>
                <p className="text-xs text-gray-600">عمولة متأخرة</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في العمولات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="فلترة حسب الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="pending">في الانتظار</SelectItem>
                  <SelectItem value="approved">موافق عليها</SelectItem>
                  <SelectItem value="paid">مدفوعة</SelectItem>
                  <SelectItem value="cancelled">ملغية</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={fetchCommissions} variant="outline">
                <Filter className="h-4 w-4 ml-2" />
                تطبيق الفلاتر
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Commissions List */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة العمولات</CardTitle>
            <CardDescription>
              جميع العمولات المستحقة لفريق المبيعات
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
              </div>
            ) : filteredCommissions.length === 0 ? (
              <div className="text-center py-8">
                <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد عمولات</h3>
                <p className="text-gray-600">لم يتم العثور على عمولات تطابق المعايير المحددة</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredCommissions.map((commission) => (
                  <div
                    key={commission.id}
                    className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-4 mb-2">
                          <div>
                            <h3 className="font-semibold text-gray-900">
                              {commission.project.name}
                            </h3>
                            <p className="text-sm text-gray-600">
                              العميل: {commission.client.name}
                            </p>
                          </div>
                          {getStatusBadge(commission.status, commission.is_overdue)}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">مندوب المبيعات:</span>
                            <p className="font-medium">
                              {commission.sales_rep.first_name} {commission.sales_rep.last_name}
                            </p>
                          </div>
                          <div>
                            <span className="text-gray-500">مبلغ المشروع:</span>
                            <p className="font-medium">{formatCurrency(commission.project_amount)}</p>
                          </div>
                          <div>
                            <span className="text-gray-500">نسبة العمولة:</span>
                            <p className="font-medium">{commission.commission_percentage}%</p>
                          </div>
                          <div>
                            <span className="text-gray-500">مبلغ العمولة:</span>
                            <p className="font-medium text-green-600">
                              {formatCurrency(commission.commission_amount)}
                            </p>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mt-2">
                          <div>
                            <span className="text-gray-500">تاريخ الاستحقاق:</span>
                            <p className="font-medium">{formatDate(commission.earned_date)}</p>
                          </div>
                          {commission.approved_date && (
                            <div>
                              <span className="text-gray-500">تاريخ الموافقة:</span>
                              <p className="font-medium">{formatDate(commission.approved_date)}</p>
                            </div>
                          )}
                          {commission.paid_date && (
                            <div>
                              <span className="text-gray-500">تاريخ الدفع:</span>
                              <p className="font-medium">{formatDate(commission.paid_date)}</p>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        {/* Action buttons for admin/sales_manager */}
                        {user?.role && ['admin', 'sales_manager'].includes(user.role) && (
                          <>
                            {commission.status === 'pending' && (
                              <Button
                                size="sm"
                                onClick={() => handleApproveCommission(commission.id)}
                                disabled={actionLoading === commission.id}
                                className="bg-green-600 hover:bg-green-700"
                              >
                                {actionLoading === commission.id ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Check className="h-4 w-4" />
                                )}
                                موافقة
                              </Button>
                            )}

                            {commission.status === 'approved' && (
                              <Button
                                size="sm"
                                onClick={() => handleMarkAsPaid(commission.id)}
                                disabled={actionLoading === commission.id}
                                className="bg-blue-600 hover:bg-blue-700"
                              >
                                {actionLoading === commission.id ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <DollarSign className="h-4 w-4" />
                                )}
                                تسجيل دفع
                              </Button>
                            )}
                          </>
                        )}

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Eye className="h-4 w-4 ml-2" />
                              عرض التفاصيل
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </UnifiedLayout>
  );
}
