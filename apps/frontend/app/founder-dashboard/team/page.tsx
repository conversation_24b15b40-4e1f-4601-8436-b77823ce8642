'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  UserCheck,
  Search,
  Plus,
  Mail,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  UserPlus,
  Loader2
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';
import { teamAPI, usersAPI } from '@/lib/api';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { AddTeamMemberForm } from '@/components/forms/add-team-member-form';
import { EditTeamMemberForm } from '@/components/forms/edit-team-member-form';
import { DeleteTeamMemberDialog } from '@/components/dialogs/delete-team-member-dialog';
import { TeamMemberDetailsDialog } from '@/components/dialogs/team-member-details-dialog';
import { showToast } from '@/lib/toast';

export default function TeamPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [teamMembers, setTeamMembers] = useState([]);
  const [teamStats, setTeamStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Modal states
  const [isAddMemberModalOpen, setIsAddMemberModalOpen] = useState(false);
  const [isEditMemberModalOpen, setIsEditMemberModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState(null);
  const [availableUsers, setAvailableUsers] = useState([]);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      console.log('User not authenticated, redirecting to login');
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  // Fetch team data
  useEffect(() => {
    if (mounted && isAuthenticated) {
      fetchTeamData();
    }
  }, [mounted, isAuthenticated]);

  const fetchTeamData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [membersResponse, statsResponse] = await Promise.all([
        teamAPI.getTeamMembers(),
        teamAPI.getTeamStats()
      ]);

      setTeamMembers(membersResponse.results || []);
      setTeamStats(statsResponse);
    } catch (err) {
      console.error('Error fetching team data:', err);
      setError('فشل في تحميل بيانات الفريق');
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableUsers = async () => {
    try {
      const response = await usersAPI.getUsers();
      // Filter out users who already have team member profiles
      const existingUserIds = teamMembers.map(member => member.user?.id);
      const availableUsers = (response.results || response).filter(
        user => !existingUserIds.includes(user.id)
      );
      setAvailableUsers(availableUsers);
    } catch (err) {
      console.error('Error fetching users:', err);
    }
  };

  // Handler functions
  const handleAddMember = async () => {
    await fetchAvailableUsers();
    setIsAddMemberModalOpen(true);
  };

  const handleEditMember = (member) => {
    setSelectedMember(member);
    setIsEditMemberModalOpen(true);
  };

  const handleDeleteMember = (member) => {
    setSelectedMember(member);
    setIsDeleteDialogOpen(true);
  };

  const handleViewMember = (member) => {
    setSelectedMember(member);
    setIsDetailsDialogOpen(true);
  };

  // CRUD operations
  const handleSubmitNewMember = async (memberData) => {
    try {
      console.log('Creating team member:', memberData);
      const createdMember = await teamAPI.createTeamMember(memberData);
      console.log('Team member created successfully:', createdMember);

      // Add to local state
      setTeamMembers(prev => [createdMember, ...prev]);

      // Close modal
      setIsAddMemberModalOpen(false);

      // Show success message
      showToast.success('تم إضافة عضو الفريق بنجاح! 🎉', 'تم إضافة العضو الجديد إلى الفريق بنجاح');

    } catch (error) {
      console.error('Error creating team member:', error);
      let errorMessage = 'حدث خطأ أثناء إضافة عضو الفريق. يرجى المحاولة مرة أخرى.';

      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else {
          const fieldErrors = Object.entries(error.response.data)
            .map(([field, errors]) => `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`)
            .join('\n');
          if (fieldErrors) {
            errorMessage = `خطأ في البيانات:\n${fieldErrors}`;
          }
        }
      }

      showToast.error('فشل في إضافة عضو الفريق', errorMessage);
    }
  };

  const handleSubmitEditMember = async (memberData) => {
    try {
      console.log('Updating team member:', selectedMember.id, memberData);
      const updatedMember = await teamAPI.updateTeamMember(selectedMember.id, memberData);
      console.log('Team member updated successfully:', updatedMember);

      // Update local state
      setTeamMembers(prev => prev.map(member =>
        member.id === selectedMember.id ? updatedMember : member
      ));

      // Close modal
      setIsEditMemberModalOpen(false);
      setSelectedMember(null);

      // Show success message
      showToast.success('تم تحديث بيانات عضو الفريق بنجاح! ✅', 'تم حفظ التغييرات بنجاح');

    } catch (error) {
      console.error('Error updating team member:', error);
      let errorMessage = 'حدث خطأ أثناء تحديث بيانات عضو الفريق. يرجى المحاولة مرة أخرى.';

      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        }
      }

      showToast.error('فشل في تحديث بيانات عضو الفريق', errorMessage);
    }
  };

  const handleConfirmDelete = async (memberId) => {
    try {
      console.log('Deleting team member:', memberId);
      await teamAPI.deleteTeamMember(memberId);
      console.log('Team member deleted successfully');

      // Remove from local state
      setTeamMembers(prev => prev.filter(member => member.id !== memberId));

      // Close dialog
      setIsDeleteDialogOpen(false);
      setSelectedMember(null);

      // Show success message
      showToast.success('تم حذف عضو الفريق بنجاح! 🗑️', 'تم إزالة العضو من الفريق نهائياً');

    } catch (error) {
      console.error('Error deleting team member:', error);
      let errorMessage = 'حدث خطأ أثناء حذف عضو الفريق. يرجى المحاولة مرة أخرى.';

      if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      }

      showToast.error('فشل في حذف عضو الفريق', errorMessage);
    }
  };

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل إدارة الفريق...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى إدارة الفريق</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  // Filter team members based on search and role
  const filteredMembers = teamMembers.filter(member => {
    const fullName = member.full_name || `${member.user?.first_name || ''} ${member.user?.last_name || ''}`.trim();
    const email = member.user?.email || '';
    const matchesSearch = fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.employee_id?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = roleFilter === 'all' || member.department === roleFilter;
    return matchesSearch && matchesRole;
  });

  const getDepartmentBadge = (department: string) => {
    switch (department) {
      case 'sales':
        return <Badge className="bg-blue-100 text-blue-800">المبيعات</Badge>;
      case 'media_buying':
        return <Badge className="bg-orange-100 text-orange-800">شراء الإعلانات</Badge>;
      case 'development':
        return <Badge className="bg-green-100 text-green-800">التطوير</Badge>;
      case 'design':
        return <Badge className="bg-pink-100 text-pink-800">التصميم</Badge>;
      case 'wordpress':
        return <Badge className="bg-purple-100 text-purple-800">ووردبريس</Badge>;
      case 'management':
        return <Badge className="bg-gray-100 text-gray-800">الإدارة</Badge>;
      default:
        return <Badge variant="outline">{department}</Badge>;
    }
  };



  if (loading) {
    return (
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <UserCheck className="h-8 w-8 text-purple-600" />
                إدارة الفريق
              </h1>
              <p className="text-gray-600 mt-1">إدارة شاملة لجميع أعضاء فريق الوكالة الرقمية</p>
            </div>
          </div>
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
              <p className="text-gray-600">جاري تحميل بيانات الفريق...</p>
            </div>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  if (error) {
    return (
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <UserCheck className="h-8 w-8 text-purple-600" />
                إدارة الفريق
              </h1>
              <p className="text-gray-600 mt-1">إدارة شاملة لجميع أعضاء فريق الوكالة الرقمية</p>
            </div>
          </div>
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-red-500 mb-4">
                <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل البيانات</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={fetchTeamData}>
                إعادة المحاولة
              </Button>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <UserCheck className="h-8 w-8 text-purple-600" />
              إدارة الفريق
            </h1>
            <p className="text-gray-600 mt-1">
              إدارة شاملة لجميع أعضاء فريق الوكالة الرقمية
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Button onClick={handleAddMember} className="bg-purple-600 hover:bg-purple-700">
              <UserPlus className="h-4 w-4 ml-2" />
              إضافة عضو جديد
            </Button>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في أعضاء الفريق..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={roleFilter === 'all' ? 'default' : 'outline'}
                  onClick={() => setRoleFilter('all')}
                >
                  الكل ({teamMembers.length})
                </Button>
                <Button
                  variant={roleFilter === 'development' ? 'default' : 'outline'}
                  onClick={() => setRoleFilter('development')}
                >
                  التطوير ({teamMembers.filter(m => m.department === 'development').length})
                </Button>
                <Button
                  variant={roleFilter === 'design' ? 'default' : 'outline'}
                  onClick={() => setRoleFilter('design')}
                >
                  التصميم ({teamMembers.filter(m => m.department === 'design').length})
                </Button>
                <Button
                  variant={roleFilter === 'sales' ? 'default' : 'outline'}
                  onClick={() => setRoleFilter('sales')}
                >
                  المبيعات ({teamMembers.filter(m => m.department === 'sales').length})
                </Button>
                <Button
                  variant={roleFilter === 'media_buying' ? 'default' : 'outline'}
                  onClick={() => setRoleFilter('media_buying')}
                >
                  شراء الإعلانات ({teamMembers.filter(m => m.department === 'media_buying').length})
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Team Members Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMembers.map((member) => (
            <Card key={member.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                      <span className="text-purple-600 font-bold text-lg">
                        {member.full_name ? member.full_name.split(' ').map(n => n[0]).join('') : 'N/A'}
                      </span>
                    </div>
                    <div>
                      <CardTitle className="text-lg">{member.full_name || 'غير محدد'}</CardTitle>
                      <div className="text-sm">{getDepartmentBadge(member.department)}</div>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewMember(member)}>
                        <Eye className="h-4 w-4 ml-2" />
                        عرض الملف الشخصي
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditMember(member)}>
                        <Edit className="h-4 w-4 ml-2" />
                        تعديل
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => handleDeleteMember(member)}
                      >
                        <Trash2 className="h-4 w-4 ml-2" />
                        حذف
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Mail className="h-4 w-4" />
                    <span>{member.user?.email || 'غير محدد'}</span>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">رقم الموظف:</span>
                    <span className="font-medium">{member.employee_id || 'غير محدد'}</span>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">المنصب:</span>
                    <span className="font-medium">{member.position || 'غير محدد'}</span>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">المشاريع المكتملة:</span>
                    <span className="font-medium">{member.completed_projects || 0}</span>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">المهام المكتملة:</span>
                    <span className="font-medium">{member.total_tasks_completed || 0}</span>
                  </div>

                  {member.performance_score && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">درجة الأداء:</span>
                      <span className="font-medium">{member.performance_score}/10</span>
                    </div>
                  )}

                  {member.skills && member.skills.length > 0 && (
                    <div className="text-sm">
                      <span className="text-gray-600">المهارات:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {member.skills.map((skill, index) => (
                          <span key={index} className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}


                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredMembers.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <UserCheck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا يوجد أعضاء فريق</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || roleFilter !== 'all' 
                  ? 'لا يوجد أعضاء فريق يطابقون معايير البحث المحددة'
                  : 'لم يتم إضافة أي أعضاء فريق بعد'
                }
              </p>
              {(!searchTerm && roleFilter === 'all') && (
                <Button onClick={handleAddMember} className="bg-purple-600 hover:bg-purple-700">
                  <UserPlus className="h-4 w-4 ml-2" />
                  إضافة أول عضو فريق
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Modals and Dialogs */}
      <AddTeamMemberForm
        isOpen={isAddMemberModalOpen}
        onClose={() => setIsAddMemberModalOpen(false)}
        onSubmit={handleSubmitNewMember}
        availableUsers={availableUsers}
      />

      <EditTeamMemberForm
        isOpen={isEditMemberModalOpen}
        onClose={() => {
          setIsEditMemberModalOpen(false);
          setSelectedMember(null);
        }}
        onSubmit={handleSubmitEditMember}
        member={selectedMember}
      />

      <DeleteTeamMemberDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => {
          setIsDeleteDialogOpen(false);
          setSelectedMember(null);
        }}
        onConfirm={handleConfirmDelete}
        member={selectedMember}
      />

      <TeamMemberDetailsDialog
        isOpen={isDetailsDialogOpen}
        onClose={() => {
          setIsDetailsDialogOpen(false);
          setSelectedMember(null);
        }}
        onEdit={() => {
          setIsDetailsDialogOpen(false);
          handleEditMember(selectedMember);
        }}
        onDelete={() => {
          setIsDetailsDialogOpen(false);
          handleDeleteMember(selectedMember);
        }}
        member={selectedMember}
      />
    </UnifiedLayout>
  );
}
