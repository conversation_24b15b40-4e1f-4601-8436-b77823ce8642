'use client';

import { useAuthStore } from '@/lib/stores/auth-store';
import { useRouter } from 'next/navigation';

export default function TestPage() {
  const { isAuthenticated, user, isLoading } = useAuthStore();
  const router = useRouter();

  const clearStorage = () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    useAuthStore.setState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    });
    alert('Storage cleared!');
  };

  const testLogin = async () => {
    try {
      await useAuthStore.getState().login({
        email: '<EMAIL>',
        password: 'demo123'
      });
      alert('Login successful!');
    } catch (error) {
      alert('Login failed: ' + error);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-blue-500">
      <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
        <h1 className="text-2xl font-bold text-black mb-4">Test Page Working!</h1>
        <p className="text-gray-600 mb-4">If you can see this, the frontend is working correctly.</p>

        <div className="space-y-4">
          <div className="bg-gray-100 p-4 rounded text-sm">
            <h3 className="font-bold mb-2">Auth State:</h3>
            <p>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</p>
            <p>User: {user ? user.username : 'None'}</p>
            <p>Loading: {isLoading ? 'Yes' : 'No'}</p>
            <p>Tokens: {typeof window !== 'undefined' ? (localStorage.getItem('access_token') ? 'Present' : 'Missing') : 'Server'}</p>
          </div>

          <div className="space-y-2">
            <button
              onClick={testLogin}
              className="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
            >
              Test Login
            </button>

            <button
              onClick={clearStorage}
              className="w-full bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
            >
              Clear Storage
            </button>

            <button
              onClick={() => router.push('/login')}
              className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Go to Login
            </button>

            <button
              onClick={() => router.push('/founder-dashboard')}
              className="w-full bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
            >
              Go to Dashboard
            </button>

            <button
              onClick={() => router.push('/')}
              className="w-full bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
            >
              Go to Home
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
