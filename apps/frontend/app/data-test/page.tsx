'use client';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { clientsAPI, projectsAPI, quotationsAPI, teamAPI } from '@/lib/api';
import { useAuthStore } from '@/lib/stores/auth-store';
import { useEffect, useState } from 'react';

export default function DataTestPage() {
  const { isAuthenticated, user } = useAuthStore();
  const [data, setData] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAllData = async () => {
    if (!isAuthenticated) {
      setError('Not authenticated - please login first');
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      console.log('Fetching data from APIs...');
      
      const [clients, projects, team, quotations] = await Promise.all([
        clientsAPI.getClients().catch(e => {
          console.error('Clients API error:', e);
          return { error: e.message, results: [] };
        }),
        projectsAPI.getProjects().catch(e => {
          console.error('Projects API error:', e);
          return { error: e.message, results: [] };
        }),
        teamAPI.getTeamMembers().catch(e => {
          console.error('Team API error:', e);
          return { error: e.message, results: [] };
        }),
        quotationsAPI.getQuotations().catch(e => {
          console.error('Quotations API error:', e);
          return { error: e.message, results: [] };
        })
      ]);

      console.log('API responses:', { clients, projects, team, quotations });

      setData({
        clients,
        projects,
        team,
        quotations
      });
    } catch (err: any) {
      console.error('Data fetch error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      fetchAllData();
    }
  }, [isAuthenticated]);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Egyptian Dummy Data Test</h1>
        <Button onClick={fetchAllData} disabled={loading}>
          {loading ? 'Loading...' : 'Refresh Data'}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-4">
          <h2 className="text-lg font-semibold mb-2">Authentication Status</h2>
          <p>Authenticated: {isAuthenticated ? '✅ Yes' : '❌ No'}</p>
          <p>User: {user?.username || 'None'}</p>
          {!isAuthenticated && (
            <p className="text-red-600 mt-2">
              Please <a href="/login" className="underline">login</a> to test data fetching
            </p>
          )}
        </Card>

        <Card className="p-4">
          <h2 className="text-lg font-semibold mb-2">Egyptian Data Summary</h2>
          <div className="space-y-1">
            <p>🏢 Clients: {data.clients?.results?.length || 0}</p>
            <p>📁 Projects: {data.projects?.results?.length || 0}</p>
            <p>👥 Team Members: {data.team?.results?.length || 0}</p>
            <p>📋 Quotations: {data.quotations?.results?.length || 0}</p>
          </div>
          {data.clients?.results?.length > 0 && (
            <p className="text-green-600 mt-2">✅ Egyptian dummy data loaded successfully!</p>
          )}
        </Card>
      </div>

      {error && (
        <Card className="p-4 border-red-200 bg-red-50">
          <h2 className="text-lg font-semibold text-red-800 mb-2">Error</h2>
          <p className="text-red-600">{error}</p>
        </Card>
      )}

      {/* Sample Data Preview */}
      {data.clients?.results?.length > 0 && (
        <Card className="p-4">
          <h2 className="text-lg font-semibold mb-2">Sample Egyptian Clients</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {data.clients.results.slice(0, 6).map((client: any, index: number) => (
              <div key={index} className="p-3 bg-gray-50 rounded">
                <p className="font-medium">{client.name}</p>
                <p className="text-sm text-gray-600">{client.email}</p>
                <p className="text-sm text-gray-600">{client.phone}</p>
                {client.company && (
                  <p className="text-sm text-blue-600">{client.company}</p>
                )}
              </div>
            ))}
          </div>
        </Card>
      )}

      {data.projects?.results?.length > 0 && (
        <Card className="p-4">
          <h2 className="text-lg font-semibold mb-2">Sample Egyptian Projects</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {data.projects.results.slice(0, 4).map((project: any, index: number) => (
              <div key={index} className="p-3 bg-gray-50 rounded">
                <p className="font-medium">{project.name}</p>
                <p className="text-sm text-gray-600">Status: {project.status}</p>
                <p className="text-sm text-gray-600">Budget: {project.budget} EGP</p>
                <p className="text-sm text-gray-600">Client: {project.client_name}</p>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Raw Data for Debugging */}
      <details className="space-y-4">
        <summary className="cursor-pointer font-semibold">Raw API Responses (for debugging)</summary>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {Object.entries(data).map(([key, value]: [string, any]) => (
            <Card key={key} className="p-4">
              <h2 className="text-lg font-semibold mb-2 capitalize">{key}</h2>
              <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-64">
                {JSON.stringify(value, null, 2)}
              </pre>
            </Card>
          ))}
        </div>
      </details>
    </div>
  );
}
