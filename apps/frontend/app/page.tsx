'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/lib/stores/auth-store';

export default function Home() {
  const router = useRouter();
  const { isAuthenticated, user, isLoading } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [debugInfo, setDebugInfo] = useState('');

  useEffect(() => {
    setMounted(true);
    console.log('Home page mounted');
  }, []);

  useEffect(() => {
    // Add debugging information
    const debug = `
      Mounted: ${mounted}
      IsAuthenticated: ${isAuthenticated}
      User: ${user ? user.username : 'null'}
      IsLoading: ${isLoading}
      Timestamp: ${new Date().toISOString()}
    `;
    setDebugInfo(debug);
    console.log('Home: Auth state changed:', { mounted, isAuthenticated, user, isLoading });

    if (mounted && !isLoading) {
      // Only navigate when not loading to avoid race conditions
      console.log('Home: Attempting navigation...', { isAuthenticated, isLoading });

      // Add a small delay to ensure auth state is stable
      const navigationTimer = setTimeout(() => {
        if (isAuthenticated && user) {
          console.log('Home: Redirecting to founder dashboard');
          router.push('/founder-dashboard');
        } else {
          console.log('Home: Redirecting to login');
          router.push('/login');
        }
      }, 100); // Small delay to ensure state stability

      return () => clearTimeout(navigationTimer);
    }
  }, [isAuthenticated, router, mounted, user, isLoading]);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center max-w-md">
        <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
        <p className="text-gray-600 mb-4">جاري التحميل...</p>

        {/* Debug information */}
        <div className="text-left bg-gray-100 p-4 rounded text-xs">
          <pre>{debugInfo}</pre>
        </div>

        {/* Manual navigation buttons for debugging */}
        <div className="mt-4 space-x-2">
          <button
            onClick={() => router.push('/login')}
            className="bg-blue-500 text-white px-4 py-2 rounded"
          >
            Go to Login
          </button>
          <button
            onClick={() => router.push('/founder-dashboard')}
            className="bg-green-500 text-white px-4 py-2 rounded"
          >
            Go to Dashboard
          </button>
          <button
            onClick={() => router.push('/test')}
            className="bg-gray-500 text-white px-4 py-2 rounded"
          >
            Test Page
          </button>
        </div>
      </div>
    </div>
  );
}
