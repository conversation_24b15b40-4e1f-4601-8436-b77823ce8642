'use client';

import { useState, useEffect } from 'react';
import { useCurrency } from '@/lib/stores/currency-store';
import { currencyConversionService } from '@/lib/services/currency-conversion';

export default function CurrencyTestPage() {
  const { currentCurrency, setCurrency, format, refreshRates, isConverting, updateExchangeRate, getManualExchangeRate, resetToDefaultRates } = useCurrency();
  const [testAmount, setTestAmount] = useState(1000);
  const [conversionResult, setConversionResult] = useState<any>(null);
  const [apiTestResult, setApiTestResult] = useState<string>('');
  const [manualRateTest, setManualRateTest] = useState({ currency: 'USD', rate: 50 });

  const testCurrencyConversion = async () => {
    try {
      const result = await currencyConversionService.convertCurrency(testAmount, 'EGP', currentCurrency);
      setConversionResult(result);
    } catch (error) {
      console.error('Conversion test failed:', error);
    }
  };

  const testApiDirectly = async () => {
    setApiTestResult('Testing API...');
    try {
      const rates = await currencyConversionService.getExchangeRates(true);
      setApiTestResult(`API Success: ${rates.source} - ${Object.keys(rates.rates).length} rates`);
    } catch (error) {
      setApiTestResult(`API Failed: ${error}`);
    }
  };

  useEffect(() => {
    testCurrencyConversion();
  }, [currentCurrency, testAmount]);

  return (
    <div className="min-h-screen p-8 bg-gray-100">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Currency System Test</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Currency Selection */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-bold mb-4">Currency Selection</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Current Currency:</label>
                <select 
                  value={currentCurrency} 
                  onChange={(e) => setCurrency(e.target.value)}
                  className="w-full p-2 border rounded"
                >
                  <option value="EGP">Egyptian Pound (EGP)</option>
                  <option value="USD">US Dollar (USD)</option>
                  <option value="SAR">Saudi Riyal (SAR)</option>
                  <option value="AED">UAE Dirham (AED)</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">Test Amount (EGP):</label>
                <input 
                  type="number" 
                  value={testAmount}
                  onChange={(e) => setTestAmount(Number(e.target.value))}
                  className="w-full p-2 border rounded"
                />
              </div>
            </div>
          </div>

          {/* Conversion Results */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-bold mb-4">Conversion Results</h2>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-600">Formatted Amount:</p>
                <p className="text-2xl font-bold">{format(testAmount)}</p>
              </div>
              
              {conversionResult && (
                <div>
                  <p className="text-sm text-gray-600">Conversion Details:</p>
                  <div className="bg-gray-50 p-3 rounded text-sm">
                    <p><strong>From:</strong> {conversionResult.fromCurrency}</p>
                    <p><strong>To:</strong> {conversionResult.toCurrency}</p>
                    <p><strong>Rate:</strong> {conversionResult.rate}</p>
                    <p><strong>Result:</strong> {conversionResult.convertedAmount}</p>
                    <p><strong>Source:</strong> {conversionResult.source}</p>
                    <p><strong>Success:</strong> {conversionResult.success ? 'Yes' : 'No'}</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Manual Rate Testing */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-bold mb-4">Manual Rate Testing</h2>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Currency:</label>
                  <select
                    value={manualRateTest.currency}
                    onChange={(e) => setManualRateTest({...manualRateTest, currency: e.target.value})}
                    className="w-full p-2 border rounded"
                  >
                    <option value="USD">USD</option>
                    <option value="SAR">SAR</option>
                    <option value="AED">AED</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">1 {manualRateTest.currency} = ? EGP:</label>
                  <input
                    type="number"
                    step="0.01"
                    value={manualRateTest.rate}
                    onChange={(e) => setManualRateTest({...manualRateTest, rate: parseFloat(e.target.value) || 0})}
                    className="w-full p-2 border rounded"
                  />
                </div>
              </div>

              <button
                onClick={() => updateExchangeRate(manualRateTest.currency, manualRateTest.rate)}
                className="w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600"
              >
                Set Manual Rate
              </button>

              <button
                onClick={resetToDefaultRates}
                className="w-full bg-orange-500 text-white p-2 rounded hover:bg-orange-600"
              >
                Reset to Default Rates
              </button>

              <div className="bg-gray-50 p-3 rounded text-sm">
                <h4 className="font-bold mb-2">Current Manual Rates:</h4>
                <p>USD: 1 USD = {getManualExchangeRate('USD')} EGP</p>
                <p>SAR: 1 SAR = {getManualExchangeRate('SAR')} EGP</p>
                <p>AED: 1 AED = {getManualExchangeRate('AED')} EGP</p>
              </div>
            </div>
          </div>

          {/* API Testing (Disabled) */}
          <div className="bg-white p-6 rounded-lg shadow opacity-50">
            <h2 className="text-xl font-bold mb-4">API Testing (Disabled)</h2>
            <div className="space-y-4">
              <button
                onClick={testApiDirectly}
                disabled
                className="w-full bg-gray-400 text-white p-2 rounded cursor-not-allowed"
              >
                Test CurrencyFreaks API (Disabled)
              </button>

              <button
                onClick={() => refreshRates(true)}
                disabled
                className="w-full bg-gray-400 text-white p-2 rounded cursor-not-allowed"
              >
                Refresh Exchange Rates (Disabled)
              </button>

              <div className="bg-gray-50 p-3 rounded text-sm">
                <p>API calls are disabled. System uses manual rates only.</p>
              </div>
            </div>
          </div>

          {/* System Status */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-bold mb-4">System Status</h2>
            <div className="space-y-2 text-sm">
              <p><strong>Current Currency:</strong> {currentCurrency}</p>
              <p><strong>Is Converting:</strong> {isConverting ? 'Yes' : 'No'}</p>
              <p><strong>Cache Status:</strong> {currencyConversionService.getCacheStatus().hasCache ? 'Available' : 'Empty'}</p>
            </div>
          </div>
        </div>

        <div className="mt-8 text-center">
          <a href="/" className="text-blue-500 underline">← Back to Home</a>
        </div>
      </div>
    </div>
  );
}
