'use client';

import { useState } from 'react';
import axios from 'axios';

export default function ApiTestPage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testDirectAxios = async () => {
    setLoading(true);
    setResult('Testing direct axios call...\n');
    
    try {
      const response = await axios.post('http://localhost:8000/api/v1/auth/login/', {
        username: 'founder',
        password: 'demo123'
      }, {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000,
        withCredentials: false
      });
      
      setResult(prev => prev + 'SUCCESS: ' + JSON.stringify(response.data, null, 2));
    } catch (error: any) {
      setResult(prev => prev + 'ERROR: ' + JSON.stringify({
        message: error.message,
        code: error.code,
        response: error.response?.data,
        status: error.response?.status,
        config: {
          url: error.config?.url,
          baseURL: error.config?.baseURL,
          method: error.config?.method,
          headers: error.config?.headers
        }
      }, null, 2));
    }
    setLoading(false);
  };

  const testHealthEndpoint = async () => {
    setLoading(true);
    setResult('Testing health endpoint...\n');
    
    try {
      const response = await axios.get('http://localhost:8000/api/health/', {
        timeout: 5000,
        withCredentials: false
      });
      
      setResult(prev => prev + 'HEALTH SUCCESS: ' + JSON.stringify(response.data, null, 2));
    } catch (error: any) {
      setResult(prev => prev + 'HEALTH ERROR: ' + JSON.stringify({
        message: error.message,
        code: error.code,
        response: error.response?.data,
        status: error.response?.status
      }, null, 2));
    }
    setLoading(false);
  };

  const testApiConfig = () => {
    setResult('API Configuration:\n' + JSON.stringify({
      NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
      NEXT_PUBLIC_API_URL_FALLBACK: process.env.NEXT_PUBLIC_API_URL_FALLBACK,
      NODE_ENV: process.env.NODE_ENV,
      window_location: typeof window !== 'undefined' ? window.location.href : 'SSR'
    }, null, 2));
  };

  return (
    <div className="min-h-screen p-8 bg-gray-100">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">API Connection Test</h1>
        
        <div className="space-y-4 mb-6">
          <button
            onClick={testApiConfig}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Show API Config
          </button>
          
          <button
            onClick={testHealthEndpoint}
            disabled={loading}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            Test Health Endpoint
          </button>
          
          <button
            onClick={testDirectAxios}
            disabled={loading}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
          >
            Test Direct Login API
          </button>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Results:</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
            {result || 'Click a button to test...'}
          </pre>
        </div>
      </div>
    </div>
  );
}
