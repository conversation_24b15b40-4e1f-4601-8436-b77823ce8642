import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import TeamPage from '../app/founder-dashboard/team/page';
import { teamAPI } from '../lib/api';

// Mock the API
jest.mock('../lib/api', () => ({
  teamAPI: {
    getTeamMembers: jest.fn(),
    getTeamStats: jest.fn(),
  },
}));

// Mock the auth store
jest.mock('../lib/stores/auth-store', () => ({
  useAuthStore: () => ({
    user: { id: 1, username: 'founder', role: 'admin' },
    isAuthenticated: true,
  }),
}));

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

// Mock the unified layout
jest.mock('../components/layout', () => ({
  UnifiedLayout: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="unified-layout">{children}</div>
  ),
}));

const mockTeamMembers = [
  {
    id: 1,
    user: {
      id: 1,
      email: '<EMAIL>',
      first_name: 'أحمد',
      last_name: 'محمد',
    },
    employee_id: 'EMP001',
    department: 'development',
    position: 'مطور ويب أول',
    full_name: 'أحمد محمد',
    completed_projects: 5,
    total_tasks_completed: 20,
    performance_score: 8.5,
    skills: ['React', 'Node.js', 'Python'],
  },
  {
    id: 2,
    user: {
      id: 2,
      email: '<EMAIL>',
      first_name: 'سارة',
      last_name: 'أحمد',
    },
    employee_id: 'EMP002',
    department: 'design',
    position: 'مصمم جرافيك',
    full_name: 'سارة أحمد',
    completed_projects: 3,
    total_tasks_completed: 15,
    performance_score: 9.0,
    skills: ['Photoshop', 'Illustrator', 'Figma'],
  },
];

const mockTeamStats = {
  total_members: 2,
  active_members: 2,
  inactive_members: 0,
  on_leave_members: 0,
  department_distribution: {
    development: 1,
    design: 1,
  },
  average_performance_score: 8.75,
  total_completed_projects: 8,
  total_completed_tasks: 35,
  top_performers: [],
};

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

const renderWithQueryClient = (component: React.ReactElement) => {
  const testQueryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={testQueryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('TeamPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (teamAPI.getTeamMembers as jest.Mock).mockResolvedValue({
      results: mockTeamMembers,
    });
    (teamAPI.getTeamStats as jest.Mock).mockResolvedValue(mockTeamStats);
  });

  it('renders team page with loading state initially', async () => {
    renderWithQueryClient(<TeamPage />);
    
    expect(screen.getByText('جاري تحميل بيانات الفريق...')).toBeInTheDocument();
  });

  it('renders team members after loading', async () => {
    renderWithQueryClient(<TeamPage />);

    await waitFor(() => {
      expect(screen.getByText('أحمد محمد')).toBeInTheDocument();
      expect(screen.getByText('سارة أحمد')).toBeInTheDocument();
    });

    expect(screen.getByText('EMP001')).toBeInTheDocument();
    expect(screen.getByText('EMP002')).toBeInTheDocument();
    expect(screen.getByText('مطور ويب أول')).toBeInTheDocument();
    expect(screen.getByText('مصمم جرافيك')).toBeInTheDocument();
  });

  it('displays team statistics', async () => {
    renderWithQueryClient(<TeamPage />);

    await waitFor(() => {
      expect(screen.getByText('الكل (2)')).toBeInTheDocument();
    });

    expect(screen.getByText('التطوير (1)')).toBeInTheDocument();
    expect(screen.getByText('التصميم (1)')).toBeInTheDocument();
  });

  it('filters team members by department', async () => {
    renderWithQueryClient(<TeamPage />);

    await waitFor(() => {
      expect(screen.getByText('أحمد محمد')).toBeInTheDocument();
      expect(screen.getByText('سارة أحمد')).toBeInTheDocument();
    });

    // Initially both members should be visible
    expect(screen.getByText('أحمد محمد')).toBeInTheDocument();
    expect(screen.getByText('سارة أحمد')).toBeInTheDocument();
  });

  it('displays skills for team members', async () => {
    renderWithQueryClient(<TeamPage />);

    await waitFor(() => {
      expect(screen.getByText('React')).toBeInTheDocument();
      expect(screen.getByText('Photoshop')).toBeInTheDocument();
    });

    expect(screen.getByText('Node.js')).toBeInTheDocument();
    expect(screen.getByText('Python')).toBeInTheDocument();
    expect(screen.getByText('Illustrator')).toBeInTheDocument();
    expect(screen.getByText('Figma')).toBeInTheDocument();
  });

  it('displays performance scores', async () => {
    renderWithQueryClient(<TeamPage />);

    await waitFor(() => {
      expect(screen.getByText('8.5/10')).toBeInTheDocument();
      expect(screen.getByText('9/10')).toBeInTheDocument();
    });
  });

  it('handles API error gracefully', async () => {
    (teamAPI.getTeamMembers as jest.Mock).mockRejectedValue(
      new Error('API Error')
    );
    (teamAPI.getTeamStats as jest.Mock).mockRejectedValue(
      new Error('API Error')
    );

    renderWithQueryClient(<TeamPage />);

    await waitFor(() => {
      expect(screen.getByText('خطأ في تحميل البيانات')).toBeInTheDocument();
      expect(screen.getByText('فشل في تحميل بيانات الفريق')).toBeInTheDocument();
    });

    expect(screen.getByText('إعادة المحاولة')).toBeInTheDocument();
  });

  it('displays empty state when no team members', async () => {
    (teamAPI.getTeamMembers as jest.Mock).mockResolvedValue({
      results: [],
    });
    (teamAPI.getTeamStats as jest.Mock).mockResolvedValue({
      ...mockTeamStats,
      total_members: 0,
    });

    renderWithQueryClient(<TeamPage />);

    await waitFor(() => {
      expect(screen.getByText('لا يوجد أعضاء فريق')).toBeInTheDocument();
      expect(screen.getByText('لم يتم إضافة أي أعضاء فريق بعد')).toBeInTheDocument();
    });

    expect(screen.getByText('إضافة أول عضو فريق')).toBeInTheDocument();
  });
});
