'use client';

import * as React from "react"
import { cn } from "@/lib/utils"
import { 
  Breadcrumb, 
  BreadcrumbList, 
  BreadcrumbItem, 
  BreadcrumbLink, 
  BreadcrumbPage, 
  BreadcrumbSeparator 
} from "./breadcrumb"

interface ResponsiveBreadcrumbProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Responsive Breadcrumb Wrapper
 * Follows the same responsive pattern as the dashboard header
 * - Mobile: mb-4, smaller text, compact spacing
 * - Tablet: mb-5, medium text
 * - Desktop: mb-6, full text, optimal spacing
 */
export function ResponsiveBreadcrumb({ children, className }: ResponsiveBreadcrumbProps) {
  return (
    <div className={cn("mb-4 sm:mb-5 lg:mb-6", className)}>
      <Breadcrumb>
        <BreadcrumbList>
          {children}
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  );
}

// Re-export all breadcrumb components for convenience
export {
  <PERSON><PERSON>crumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
}

// Common breadcrumb patterns for the MTBRMG ERP system
interface BreadcrumbPath {
  label: string;
  href?: string;
}

interface QuickBreadcrumbProps {
  paths: BreadcrumbPath[];
  className?: string;
}

/**
 * Quick Breadcrumb Component
 * Pre-configured for common MTBRMG ERP navigation patterns
 */
export function QuickBreadcrumb({ paths, className }: QuickBreadcrumbProps) {
  return (
    <ResponsiveBreadcrumb className={className}>
      {paths.map((path, index) => (
        <React.Fragment key={index}>
          <BreadcrumbItem>
            {path.href ? (
              <BreadcrumbLink href={path.href}>
                {path.label}
              </BreadcrumbLink>
            ) : (
              <BreadcrumbPage>
                {path.label}
              </BreadcrumbPage>
            )}
          </BreadcrumbItem>
          {index < paths.length - 1 && <BreadcrumbSeparator />}
        </React.Fragment>
      ))}
    </ResponsiveBreadcrumb>
  );
}

// Pre-defined breadcrumb patterns for common pages
export const BREADCRUMB_PATTERNS = {
  FOUNDER_DASHBOARD: [
    { label: "لوحة تحكم المؤسس", href: "/founder-dashboard" }
  ],
  
  CLIENTS: [
    { label: "لوحة تحكم المؤسس", href: "/founder-dashboard" },
    { label: "إدارة العملاء", href: "/founder-dashboard/clients" }
  ],
  
  PROJECTS: [
    { label: "لوحة تحكم المؤسس", href: "/founder-dashboard" },
    { label: "إدارة المشاريع", href: "/founder-dashboard/projects" }
  ],
  
  TEAM: [
    { label: "لوحة تحكم المؤسس", href: "/founder-dashboard" },
    { label: "إدارة الفريق", href: "/founder-dashboard/team" }
  ],
  
  TASKS: [
    { label: "لوحة تحكم المؤسس", href: "/founder-dashboard" },
    { label: "إدارة المهام", href: "/founder-dashboard/tasks" }
  ],
  
  FINANCE: [
    { label: "لوحة تحكم المؤسس", href: "/founder-dashboard" },
    { label: "المالية والمحاسبة", href: "/founder-dashboard/finance" }
  ]
} as const;

/**
 * Helper function to create breadcrumb paths
 */
export function createBreadcrumbPath(
  basePath: BreadcrumbPath[], 
  additionalPaths: BreadcrumbPath[]
): BreadcrumbPath[] {
  return [...basePath, ...additionalPaths];
}

/**
 * Founder Dashboard Breadcrumb
 * Specialized component for founder dashboard pages
 */
interface FounderBreadcrumbProps {
  section?: string;
  subsection?: string;
  current?: string;
  sectionHref?: string;
  subsectionHref?: string;
  className?: string;
}

export function FounderBreadcrumb({ 
  section, 
  subsection, 
  current, 
  sectionHref, 
  subsectionHref, 
  className 
}: FounderBreadcrumbProps) {
  const paths: BreadcrumbPath[] = [
    { label: "لوحة تحكم المؤسس", href: "/founder-dashboard" }
  ];

  if (section) {
    paths.push({ 
      label: section, 
      href: subsection || current ? sectionHref : undefined 
    });
  }

  if (subsection) {
    paths.push({ 
      label: subsection, 
      href: current ? subsectionHref : undefined 
    });
  }

  if (current) {
    paths.push({ label: current });
  }

  return <QuickBreadcrumb paths={paths} className={className} />;
}
