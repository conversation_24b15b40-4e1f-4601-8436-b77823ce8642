'use client';

import { useState, useEffect } from 'react';
import { RefreshCw, Clock, AlertTriangle, CheckCircle, Wifi, WifiOff } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useCurrency } from '@/lib/stores/currency-store';

interface ConversionIndicatorProps {
  className?: string;
  showRefreshButton?: boolean;
  compact?: boolean;
}

export function CurrencyConversionIndicator({ 
  className = '', 
  showRefreshButton = false,
  compact = false 
}: ConversionIndicatorProps) {
  const { 
    isConversionEnabled, 
    conversionStatus, 
    isConverting, 
    refreshRates,
    currentCurrency,
    baseCurrency 
  } = useCurrency();

  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  useEffect(() => {
    if (conversionStatus.lastUpdate) {
      setLastRefresh(new Date(conversionStatus.lastUpdate));
    }
  }, [conversionStatus.lastUpdate]);

  const handleRefresh = async () => {
    await refreshRates(true);
    setLastRefresh(new Date());
  };

  const getStatusIcon = () => {
    if (isConverting) {
      return <RefreshCw className="h-3 w-3 animate-spin" />;
    }

    switch (conversionStatus.source) {
      case 'api':
        return <Wifi className="h-3 w-3 text-green-600" />;
      case 'cache':
        return <Clock className="h-3 w-3 text-yellow-600" />;
      case 'fallback':
        return <WifiOff className="h-3 w-3 text-red-600" />;
      default:
        return <CheckCircle className="h-3 w-3 text-gray-600" />;
    }
  };

  const getStatusText = () => {
    if (isConverting) return 'جاري التحويل...';
    
    switch (conversionStatus.source) {
      case 'api':
        return 'أسعار حديثة';
      case 'cache':
        return 'أسعار محفوظة';
      case 'fallback':
        return 'أسعار احتياطية';
      default:
        return 'بدون تحويل';
    }
  };

  const getStatusColor = () => {
    switch (conversionStatus.source) {
      case 'api':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cache':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'fallback':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatLastUpdate = () => {
    if (!lastRefresh) return 'غير محدد';
    
    const now = new Date();
    const diffMs = now.getTime() - lastRefresh.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (diffHours > 0) {
      return `منذ ${diffHours} ساعة`;
    } else if (diffMinutes > 0) {
      return `منذ ${diffMinutes} دقيقة`;
    } else {
      return 'الآن';
    }
  };

  if (!isConversionEnabled) {
    return null; // Don't show indicator when no conversion is happening
  }

  if (compact) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge variant="outline" className={`${getStatusColor()} ${className}`}>
              {getStatusIcon()}
              <span className="mr-1 text-xs">محول</span>
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-sm">
              <p>محول من {baseCurrency} إلى {currentCurrency}</p>
              <p>الحالة: {getStatusText()}</p>
              <p>آخر تحديث: {formatLastUpdate()}</p>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Badge variant="outline" className={getStatusColor()}>
        {getStatusIcon()}
        <span className="mr-1 text-xs">{getStatusText()}</span>
      </Badge>
      
      <div className="text-xs text-gray-600">
        <span>محول من {baseCurrency} إلى {currentCurrency}</span>
        {lastRefresh && (
          <span className="block">آخر تحديث: {formatLastUpdate()}</span>
        )}
      </div>

      {showRefreshButton && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRefresh}
          disabled={isConverting}
          className="h-6 px-2"
        >
          <RefreshCw className={`h-3 w-3 ${isConverting ? 'animate-spin' : ''}`} />
        </Button>
      )}
    </div>
  );
}

interface ConvertedAmountDisplayProps {
  amount: number;
  fromCurrency?: string;
  toCurrency?: string;
  className?: string;
  showOriginal?: boolean;
}

export function ConvertedAmountDisplay({
  amount,
  fromCurrency,
  toCurrency,
  className = '',
  showOriginal = false
}: ConvertedAmountDisplayProps) {
  const { formatWithConversion, format, isConversionEnabled, baseCurrency, currentCurrency } = useCurrency();
  const [convertedValue, setConvertedValue] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const from = fromCurrency || baseCurrency;
  const to = toCurrency || currentCurrency;

  useEffect(() => {
    const convertAmount = async () => {
      if (!isConversionEnabled || from === to) {
        setConvertedValue(format(amount, to));
        return;
      }

      setIsLoading(true);
      try {
        const converted = await formatWithConversion(amount, from, to);
        setConvertedValue(converted);
      } catch (error) {
        console.error('Conversion error:', error);
        setConvertedValue(format(amount, to));
      } finally {
        setIsLoading(false);
      }
    };

    convertAmount();
  }, [amount, from, to, isConversionEnabled, formatWithConversion, format]);

  if (isLoading) {
    return (
      <div className={`flex items-center gap-1 ${className}`}>
        <RefreshCw className="h-3 w-3 animate-spin text-gray-400" />
        <span className="text-gray-400">جاري التحويل...</span>
      </div>
    );
  }

  return (
    <div className={className}>
      <span className="font-medium">{convertedValue}</span>
      
      {showOriginal && isConversionEnabled && from !== to && (
        <div className="text-xs text-gray-500 mt-1">
          الأصلي: {format(amount, from)}
        </div>
      )}
      
      {isConversionEnabled && (
        <CurrencyConversionIndicator compact className="mt-1" />
      )}
    </div>
  );
}
