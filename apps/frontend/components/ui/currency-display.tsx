'use client';

import { CURRENCY_CONFIG } from '@mtbrmg/shared';

interface CurrencyDisplayProps {
  amount: number;
  currency?: string;
  showSymbol?: boolean;
  showCode?: boolean;
  className?: string;
}

export function CurrencyDisplay({ 
  amount, 
  currency = CURRENCY_CONFIG.PRIMARY, 
  showSymbol = true,
  showCode = false,
  className = '' 
}: CurrencyDisplayProps) {
  const formatCurrency = (value: number, currencyCode: string): string => {
    const formatter = new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
    
    return formatter.format(value);
  };

  const displayValue = formatCurrency(amount, currency);

  return (
    <span className={className}>
      {displayValue}
      {showCode && ` (${currency})`}
    </span>
  );
}

interface CurrencyInputProps {
  value: number | string;
  onChange: (value: number) => void;
  currency?: string;
  placeholder?: string;
  className?: string;
  required?: boolean;
  min?: number;
  step?: number;
}

export function CurrencyInput({
  value,
  onChange,
  currency = CURRENCY_CONFIG.PRIMARY,
  placeholder = '0.00',
  className = '',
  required = false,
  min = 0,
  step = 0.01,
}: CurrencyInputProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const numValue = parseFloat(e.target.value) || 0;
    onChange(numValue);
  };

  return (
    <div className="relative">
      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
        {CURRENCY_CONFIG.SYMBOLS[currency as keyof typeof CURRENCY_CONFIG.SYMBOLS] || currency}
      </span>
      <input
        type="number"
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        className={`pr-12 ${className}`}
        required={required}
        min={min}
        step={step}
      />
    </div>
  );
}

interface CurrencySelectorProps {
  value: string;
  onChange: (currency: string) => void;
  currencies?: string[];
  className?: string;
}

export function CurrencySelector({
  value,
  onChange,
  currencies = CURRENCY_CONFIG.SUPPORTED,
  className = '',
}: CurrencySelectorProps) {
  return (
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className={`w-full p-2 border rounded-md ${className}`}
    >
      {currencies.map((curr) => (
        <option key={curr} value={curr}>
          {CURRENCY_CONFIG.LABELS[curr as keyof typeof CURRENCY_CONFIG.LABELS]} ({curr})
        </option>
      ))}
    </select>
  );
}
