'use client';

import { DollarSign, PoundSterling } from 'lucide-react';
import { useCurrency } from '@/lib/stores/currency-store';
import { CURRENCY_CONFIG } from '@mtbrmg/shared';

interface CurrencyIconProps {
  currency?: string;
  className?: string;
  size?: number;
}

export function CurrencyIcon({ currency, className = '', size }: CurrencyIconProps) {
  const { currentCurrency } = useCurrency();
  const curr = currency || currentCurrency;

  // Define icon mapping for different currencies
  const getIcon = (currencyCode: string) => {
    switch (currencyCode) {
      case 'USD':
        return <DollarSign className={className} style={size ? { width: size, height: size } : undefined} />;
      case 'EGP':
        // For Egyptian Pound, we'll use a custom styled component or PoundSterling as fallback
        return <PoundSterling className={className} style={size ? { width: size, height: size } : undefined} />;
      case 'SAR':
      case 'AED':
        // For Arabic currencies, use a generic currency symbol
        return (
          <div 
            className={`inline-flex items-center justify-center font-bold ${className}`}
            style={size ? { width: size, height: size, fontSize: size * 0.6 } : undefined}
          >
            {CURRENCY_CONFIG.SYMBOLS[currencyCode as keyof typeof CURRENCY_CONFIG.SYMBOLS]}
          </div>
        );
      default:
        return <DollarSign className={className} style={size ? { width: size, height: size } : undefined} />;
    }
  };

  return getIcon(curr);
}

// Specific currency icons for better semantic meaning
export function EgyptianPoundIcon({ className = '', size }: { className?: string; size?: number }) {
  return (
    <div 
      className={`inline-flex items-center justify-center font-bold text-green-600 ${className}`}
      style={size ? { width: size, height: size, fontSize: size * 0.6 } : undefined}
    >
      ج.م
    </div>
  );
}

export function USDollarIcon({ className = '', size }: { className?: string; size?: number }) {
  return <DollarSign className={`text-green-600 ${className}`} style={size ? { width: size, height: size } : undefined} />;
}

export function SaudiRiyalIcon({ className = '', size }: { className?: string; size?: number }) {
  return (
    <div 
      className={`inline-flex items-center justify-center font-bold text-green-600 ${className}`}
      style={size ? { width: size, height: size, fontSize: size * 0.6 } : undefined}
    >
      ر.س
    </div>
  );
}

export function UAEDirhamIcon({ className = '', size }: { className?: string; size?: number }) {
  return (
    <div 
      className={`inline-flex items-center justify-center font-bold text-green-600 ${className}`}
      style={size ? { width: size, height: size, fontSize: size * 0.6 } : undefined}
    >
      د.إ
    </div>
  );
}
