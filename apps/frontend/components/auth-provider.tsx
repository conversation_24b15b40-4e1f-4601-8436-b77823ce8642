'use client';

import { useEffect, useState } from 'react';
import { useAuthStore, initializeAuth } from '@/lib/stores/auth-store';

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, user, isLoading } = useAuthStore();
  const [isHydrated, setIsHydrated] = useState(false);
  const [authInitialized, setAuthInitialized] = useState(false);

  useEffect(() => {
    console.log('AuthProvider: Starting initialization');

    const initAuth = async () => {
      try {
        // Wait for auth initialization to complete before hydrating
        await initializeAuth();
        console.log('AuthProvider: Auth initialization completed');
        setAuthInitialized(true);
        setIsHydrated(true);
      } catch (error) {
        console.error('AuthProvider: Auth initialization failed:', error);
        // Even if auth fails, we should hydrate to allow login
        setAuthInitialized(true);
        setIsHydrated(true);
      }
    };

    initAuth();
  }, []);

  // Don't render children until both hydration and auth initialization are complete
  if (!isHydrated || !authInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل النظام...</p>
          <div className="mt-4 text-xs text-gray-500">
            <p>Auth State: {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}</p>
            <p>User: {user ? user.username : 'None'}</p>
            <p>Loading: {isLoading ? 'Yes' : 'No'}</p>
            <p>Hydrated: {isHydrated ? 'Yes' : 'No'}</p>
            <p>Auth Initialized: {authInitialized ? 'Yes' : 'No'}</p>
          </div>
        </div>
      </div>
    );
  }

  console.log('AuthProvider: Rendering children', { isAuthenticated, user, isLoading, authInitialized });
  return <>{children}</>;
}
