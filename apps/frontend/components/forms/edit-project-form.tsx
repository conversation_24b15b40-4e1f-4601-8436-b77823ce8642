'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Edit,
  Save,
  X,
  Loader2,
  FolderOpen,
  Users,
  Calendar,
  DollarSign,
  Globe,
  GitBranch,
  Target
} from 'lucide-react';

interface ProjectUpdateData {
  name: string;
  description: string;
  type: string;
  status: string;
  priority: string;
  client: string;
  start_date: string;
  end_date: string;
  deadline: string;
  budget: string;
  domains: string;
  repository_url: string;
  staging_url: string;
  production_url: string;
  progress: string;
}

interface EditProjectFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: ProjectUpdateData) => Promise<void>;
  project: any;
  clients: any[];
}

export function EditProjectForm({ isOpen, onClose, onSubmit, project, clients }: EditProjectFormProps) {
  const [formData, setFormData] = useState<ProjectUpdateData>({
    name: '',
    description: '',
    type: 'website',
    status: 'planning',
    priority: 'medium',
    client: '',
    start_date: '',
    end_date: '',
    deadline: '',
    budget: '',
    domains: '',
    repository_url: '',
    staging_url: '',
    production_url: '',
    progress: '0'
  });

  const [errors, setErrors] = useState<Partial<ProjectUpdateData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Update form data when project changes
  useEffect(() => {
    if (project) {
      setFormData({
        name: project.name || '',
        description: project.description || '',
        type: project.type || 'website',
        status: project.status || 'planning',
        priority: project.priority || 'medium',
        client: project.client?.toString() || '',
        start_date: project.start_date || '',
        end_date: project.end_date || '',
        deadline: project.deadline || '',
        budget: project.budget ? project.budget.toString() : '',
        domains: Array.isArray(project.domains) ? project.domains.join(', ') : '',
        repository_url: project.repository_url || '',
        staging_url: project.staging_url || '',
        production_url: project.production_url || '',
        progress: project.progress ? project.progress.toString() : '0'
      });
    }
  }, [project]);

  const validateForm = (): boolean => {
    const newErrors: Partial<ProjectUpdateData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'اسم المشروع مطلوب';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'وصف المشروع مطلوب';
    }

    if (!formData.client) {
      newErrors.client = 'يجب اختيار العميل';
    }

    if (!formData.start_date) {
      newErrors.start_date = 'تاريخ البداية مطلوب';
    }

    if (formData.progress && (parseInt(formData.progress) < 0 || parseInt(formData.progress) > 100)) {
      newErrors.progress = 'نسبة التقدم يجب أن تكون بين 0 و 100';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // Transform data for API
      const apiData = {
        ...formData,
        client: parseInt(formData.client),
        budget: formData.budget ? parseFloat(formData.budget) : null,
        domains: formData.domains ? formData.domains.split(',').map(d => d.trim()).filter(d => d) : [],
        progress: parseInt(formData.progress) || 0,
        end_date: formData.end_date || null,
        deadline: formData.deadline || null
      };

      await onSubmit(apiData);
      handleClose();
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setErrors({});
    setIsSubmitting(false);
    onClose();
  };

  const handleInputChange = (field: keyof ProjectUpdateData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  if (!project) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5 text-purple-600" />
            تعديل المشروع
          </DialogTitle>
          <DialogDescription>
            تعديل بيانات مشروع {project.name}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="flex items-center gap-2">
                <FolderOpen className="h-4 w-4" />
                اسم المشروع *
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="اسم المشروع"
              />
              {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="client" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                العميل *
              </Label>
              <Select value={formData.client} onValueChange={(value) => handleInputChange('client', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر العميل" />
                </SelectTrigger>
                <SelectContent>
                  {clients.map((client) => (
                    <SelectItem key={client.id} value={client.id.toString()}>
                      {client.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.client && <p className="text-sm text-red-600">{errors.client}</p>}
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">وصف المشروع *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="وصف تفصيلي للمشروع"
              rows={3}
            />
            {errors.description && <p className="text-sm text-red-600">{errors.description}</p>}
          </div>

          {/* Type, Status, Priority */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="type">نوع المشروع</Label>
              <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="website">موقع ويب</SelectItem>
                  <SelectItem value="mobile_app">تطبيق جوال</SelectItem>
                  <SelectItem value="web_app">تطبيق ويب</SelectItem>
                  <SelectItem value="ecommerce">متجر إلكتروني</SelectItem>
                  <SelectItem value="wordpress">ووردبريس</SelectItem>
                  <SelectItem value="maintenance">صيانة</SelectItem>
                  <SelectItem value="marketing">تسويق</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">حالة المشروع</Label>
              <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="planning">التخطيط</SelectItem>
                  <SelectItem value="development">التطوير</SelectItem>
                  <SelectItem value="testing">الاختبار</SelectItem>
                  <SelectItem value="deployment">النشر</SelectItem>
                  <SelectItem value="maintenance">الصيانة</SelectItem>
                  <SelectItem value="completed">مكتمل</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="priority">الأولوية</Label>
              <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">منخفضة</SelectItem>
                  <SelectItem value="medium">متوسطة</SelectItem>
                  <SelectItem value="high">عالية</SelectItem>
                  <SelectItem value="urgent">عاجلة</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Dates and Budget */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start_date" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                تاريخ البداية *
              </Label>
              <Input
                id="start_date"
                type="date"
                value={formData.start_date}
                onChange={(e) => handleInputChange('start_date', e.target.value)}
              />
              {errors.start_date && <p className="text-sm text-red-600">{errors.start_date}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="end_date">تاريخ النهاية</Label>
              <Input
                id="end_date"
                type="date"
                value={formData.end_date}
                onChange={(e) => handleInputChange('end_date', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="deadline">الموعد النهائي</Label>
              <Input
                id="deadline"
                type="date"
                value={formData.deadline}
                onChange={(e) => handleInputChange('deadline', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="budget" className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                الميزانية
              </Label>
              <Input
                id="budget"
                type="number"
                step="0.01"
                value={formData.budget}
                onChange={(e) => handleInputChange('budget', e.target.value)}
                placeholder="0.00"
              />
            </div>
          </div>

          {/* Progress */}
          <div className="space-y-2">
            <Label htmlFor="progress" className="flex items-center gap-2">
              <Target className="h-4 w-4" />
              نسبة التقدم (%)
            </Label>
            <Input
              id="progress"
              type="number"
              min="0"
              max="100"
              value={formData.progress}
              onChange={(e) => handleInputChange('progress', e.target.value)}
              placeholder="0"
            />
            {errors.progress && <p className="text-sm text-red-600">{errors.progress}</p>}
          </div>

          {/* URLs */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="domains" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                النطاقات (مفصولة بفواصل)
              </Label>
              <Input
                id="domains"
                value={formData.domains}
                onChange={(e) => handleInputChange('domains', e.target.value)}
                placeholder="example.com, www.example.com"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="repository_url" className="flex items-center gap-2">
                  <GitBranch className="h-4 w-4" />
                  رابط المستودع
                </Label>
                <Input
                  id="repository_url"
                  value={formData.repository_url}
                  onChange={(e) => handleInputChange('repository_url', e.target.value)}
                  placeholder="https://github.com/..."
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="staging_url">رابط الاختبار</Label>
                <Input
                  id="staging_url"
                  value={formData.staging_url}
                  onChange={(e) => handleInputChange('staging_url', e.target.value)}
                  placeholder="https://staging.example.com"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="production_url">رابط الإنتاج</Label>
                <Input
                  id="production_url"
                  value={formData.production_url}
                  onChange={(e) => handleInputChange('production_url', e.target.value)}
                  placeholder="https://example.com"
                />
              </div>
            </div>
          </div>
        </form>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
            <X className="h-4 w-4 ml-2" />
            إلغاء
          </Button>
          <Button type="submit" onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? (
              <Loader2 className="h-4 w-4 ml-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 ml-2" />
            )}
            {isSubmitting ? 'جاري التحديث...' : 'حفظ التغييرات'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
