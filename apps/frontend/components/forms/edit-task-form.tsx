'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  CheckSquare,
  Calendar,
  User,
  Clock,
  Flag,
  FolderOpen,
  Save,
  X,
  Loader2
} from 'lucide-react';
import { useProjects } from '@/lib/hooks/use-projects';
import { teamAPI } from '@/lib/api';
import { TaskFormData } from './add-task-form';

interface EditTaskFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (taskData: TaskFormData) => Promise<void>;
  task: any;
}

const TASK_CATEGORIES = [
  { value: 'light', label: 'خفيف (1-4 ساعات)' },
  { value: 'medium', label: 'متوسط (4-8 ساعات)' },
  { value: 'extreme', label: 'شاق (8+ ساعات)' }
];

const TASK_PRIORITIES = [
  { value: 'low', label: 'منخفض' },
  { value: 'medium', label: 'متوسط' },
  { value: 'high', label: 'عالي' },
  { value: 'urgent', label: 'عاجل' }
];

const TASK_STATUSES = [
  { value: 'pending', label: 'في الانتظار' },
  { value: 'in_progress', label: 'قيد التنفيذ' },
  { value: 'review', label: 'مراجعة' },
  { value: 'testing', label: 'اختبار' },
  { value: 'completed', label: 'مكتمل' },
  { value: 'cancelled', label: 'ملغي' }
];

export function EditTaskForm({ isOpen, onClose, onSubmit, task }: EditTaskFormProps) {
  const [formData, setFormData] = useState<TaskFormData>({
    title: '',
    description: '',
    category: 'medium',
    priority: 'medium',
    status: 'pending',
    project: 'none',
    assigned_to: 'none',
    estimated_hours: 1,
    start_date: '',
    due_date: ''
  });

  const [errors, setErrors] = useState<Partial<Record<keyof TaskFormData, string>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [teamMembers, setTeamMembers] = useState<any[]>([]);

  // Fetch projects and team members
  const { data: projectsData } = useProjects();
  const projects = projectsData?.results || projectsData || [];

  // Populate form with task data when task changes
  useEffect(() => {
    if (task && isOpen) {
      setFormData({
        title: task.title || '',
        description: task.description || '',
        category: task.category || 'medium',
        priority: task.priority || 'medium',
        status: task.status || 'pending',
        project: task.project || 'none',
        assigned_to: task.assigned_to || 'none',
        estimated_hours: task.estimated_hours || 1,
        start_date: task.start_date ? new Date(task.start_date).toISOString().slice(0, 16) : '',
        due_date: task.due_date ? new Date(task.due_date).toISOString().slice(0, 16) : ''
      });
    }
  }, [task, isOpen]);

  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        const response = await teamAPI.getTeamMembers();
        setTeamMembers(response.results || response || []);
      } catch (error) {
        console.error('Error fetching team members:', error);
      }
    };

    if (isOpen) {
      fetchTeamMembers();
    }
  }, [isOpen]);

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof TaskFormData, string>> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'عنوان المهمة مطلوب';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'وصف المهمة مطلوب';
    }

    if (formData.estimated_hours <= 0) {
      newErrors.estimated_hours = 'الساعات المقدرة يجب أن تكون أكبر من صفر';
    }

    if (!formData.due_date) {
      newErrors.due_date = 'تاريخ الاستحقاق مطلوب';
    }

    // Validate start date is before due date
    if (formData.start_date && formData.due_date && 
        new Date(formData.start_date) >= new Date(formData.due_date)) {
      newErrors.start_date = 'تاريخ البداية يجب أن يكون قبل تاريخ الاستحقاق';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
      handleClose();
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setErrors({});
    setIsSubmitting(false);
    onClose();
  };

  const handleInputChange = (field: keyof TaskFormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  if (!task) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <CheckSquare className="h-6 w-6 text-purple-600" />
            تعديل المهمة
          </DialogTitle>
          <DialogDescription>
            تعديل تفاصيل المهمة والمواعيد المطلوبة
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">المعلومات الأساسية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title">عنوان المهمة *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="أدخل عنوان المهمة"
                    className={errors.title ? 'border-red-500' : ''}
                  />
                  {errors.title && <p className="text-sm text-red-500 mt-1">{errors.title}</p>}
                </div>

                <div>
                  <Label htmlFor="estimated_hours">الساعات المقدرة *</Label>
                  <Input
                    id="estimated_hours"
                    type="number"
                    min="1"
                    value={formData.estimated_hours}
                    onChange={(e) => handleInputChange('estimated_hours', parseInt(e.target.value) || 1)}
                    placeholder="عدد الساعات المقدرة"
                    className={errors.estimated_hours ? 'border-red-500' : ''}
                  />
                  {errors.estimated_hours && <p className="text-sm text-red-500 mt-1">{errors.estimated_hours}</p>}
                </div>
              </div>

              <div>
                <Label htmlFor="description">وصف المهمة *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="أدخل وصف تفصيلي للمهمة"
                  rows={3}
                  className={errors.description ? 'border-red-500' : ''}
                />
                {errors.description && <p className="text-sm text-red-500 mt-1">{errors.description}</p>}
              </div>
            </CardContent>
          </Card>

          {/* Task Properties */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">خصائص المهمة</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="category">فئة المهمة</Label>
                  <Select value={formData.category} onValueChange={(value: any) => handleInputChange('category', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {TASK_CATEGORIES.map((category) => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="priority">الأولوية</Label>
                  <Select value={formData.priority} onValueChange={(value: any) => handleInputChange('priority', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {TASK_PRIORITIES.map((priority) => (
                        <SelectItem key={priority.value} value={priority.value}>
                          {priority.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="status">الحالة</Label>
                  <Select value={formData.status} onValueChange={(value: any) => handleInputChange('status', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {TASK_STATUSES.map((status) => (
                        <SelectItem key={status.value} value={status.value}>
                          {status.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Assignment and Project */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">التكليف والمشروع</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="project">المشروع (اختياري)</Label>
                  <Select value={formData.project} onValueChange={(value) => handleInputChange('project', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر مشروع">
                        <div className="flex items-center gap-2">
                          <FolderOpen className="h-4 w-4" />
                          {formData.project === 'none' ? 'بدون مشروع' :
                            formData.project ?
                            projects.find(p => p.id === formData.project)?.name || 'مشروع غير محدد' :
                            'اختر مشروع'
                          }
                        </div>
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">بدون مشروع</SelectItem>
                      {projects.map((project) => (
                        <SelectItem key={project.id} value={project.id}>
                          <div className="flex items-center gap-2">
                            <FolderOpen className="h-4 w-4" />
                            {project.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="assigned_to">مكلف إلى (اختياري)</Label>
                  <Select value={formData.assigned_to} onValueChange={(value) => handleInputChange('assigned_to', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر عضو فريق">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          {formData.assigned_to === 'none' ? 'غير مكلف' :
                            formData.assigned_to ?
                            teamMembers.find(m => m.id === formData.assigned_to)?.user?.first_name + ' ' +
                            teamMembers.find(m => m.id === formData.assigned_to)?.user?.last_name || 'عضو غير محدد' :
                            'اختر عضو فريق'
                          }
                        </div>
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">غير مكلف</SelectItem>
                      {teamMembers.map((member) => (
                        <SelectItem key={member.id} value={member.id}>
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4" />
                            {member.user?.first_name} {member.user?.last_name}
                            <span className="text-sm text-gray-500">({member.position})</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">الجدول الزمني</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="start_date">تاريخ البداية (اختياري)</Label>
                  <Input
                    id="start_date"
                    type="datetime-local"
                    value={formData.start_date}
                    onChange={(e) => handleInputChange('start_date', e.target.value)}
                    className={errors.start_date ? 'border-red-500' : ''}
                  />
                  {errors.start_date && <p className="text-sm text-red-500 mt-1">{errors.start_date}</p>}
                </div>

                <div>
                  <Label htmlFor="due_date">تاريخ الاستحقاق *</Label>
                  <Input
                    id="due_date"
                    type="datetime-local"
                    value={formData.due_date}
                    onChange={(e) => handleInputChange('due_date', e.target.value)}
                    className={errors.due_date ? 'border-red-500' : ''}
                  />
                  {errors.due_date && <p className="text-sm text-red-500 mt-1">{errors.due_date}</p>}
                </div>
              </div>
            </CardContent>
          </Card>
        </form>

        <DialogFooter>
          <div className="flex justify-end gap-4 w-full">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              <X className="h-4 w-4 ml-2" />
              إلغاء
            </Button>
            <Button
              type="submit"
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="bg-purple-600 hover:bg-purple-700"
            >
              {isSubmitting ? (
                <Loader2 className="h-4 w-4 ml-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 ml-2" />
              )}
              {isSubmitting ? 'جاري الحفظ...' : 'حفظ التعديلات'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
