'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Edit,
  Save,
  X,
  Loader2,
  Building,
  DollarSign,
  Phone,
  Award
} from 'lucide-react';

interface TeamMemberUpdateData {
  department: string;
  position: string;
  status: string;
  salary: string;
  skills: string;
  certifications: string;
  emergency_contact_name: string;
  emergency_contact_phone: string;
}

interface EditTeamMemberFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: TeamMemberUpdateData) => Promise<void>;
  member: any;
}

export function EditTeamMemberForm({ isOpen, onClose, onSubmit, member }: EditTeamMemberFormProps) {
  const [formData, setFormData] = useState<TeamMemberUpdateData>({
    department: 'development',
    position: '',
    status: 'active',
    salary: '',
    skills: '',
    certifications: '',
    emergency_contact_name: '',
    emergency_contact_phone: ''
  });

  const [errors, setErrors] = useState<Partial<TeamMemberUpdateData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Update form data when member changes
  useEffect(() => {
    if (member) {
      setFormData({
        department: member.department || 'development',
        position: member.position || '',
        status: member.status || 'active',
        salary: member.salary ? member.salary.toString() : '',
        skills: Array.isArray(member.skills) ? member.skills.join(', ') : '',
        certifications: Array.isArray(member.certifications) ? member.certifications.join(', ') : '',
        emergency_contact_name: member.emergency_contact_name || '',
        emergency_contact_phone: member.emergency_contact_phone || ''
      });
    }
  }, [member]);

  const validateForm = (): boolean => {
    const newErrors: Partial<TeamMemberUpdateData> = {};

    if (!formData.position.trim()) {
      newErrors.position = 'المنصب مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // Transform data for API
      const apiData = {
        ...formData,
        salary: formData.salary ? parseFloat(formData.salary) : null,
        skills: formData.skills ? formData.skills.split(',').map(s => s.trim()).filter(s => s) : [],
        certifications: formData.certifications ? formData.certifications.split(',').map(c => c.trim()).filter(c => c) : []
      };

      await onSubmit(apiData);
      handleClose();
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setErrors({});
    setIsSubmitting(false);
    onClose();
  };

  const handleInputChange = (field: keyof TeamMemberUpdateData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  if (!member) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5 text-purple-600" />
            تعديل بيانات عضو الفريق
          </DialogTitle>
          <DialogDescription>
            تعديل بيانات {member.full_name || 'عضو الفريق'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Department and Position */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="department" className="flex items-center gap-2">
                <Building className="h-4 w-4" />
                القسم
              </Label>
              <Select value={formData.department} onValueChange={(value) => handleInputChange('department', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sales">المبيعات</SelectItem>
                  <SelectItem value="media_buying">شراء الإعلانات</SelectItem>
                  <SelectItem value="development">التطوير</SelectItem>
                  <SelectItem value="design">التصميم</SelectItem>
                  <SelectItem value="wordpress">ووردبريس</SelectItem>
                  <SelectItem value="management">الإدارة</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="position">المنصب *</Label>
              <Input
                id="position"
                value={formData.position}
                onChange={(e) => handleInputChange('position', e.target.value)}
                placeholder="مثال: مطور واجهات أمامية"
              />
              {errors.position && <p className="text-sm text-red-600">{errors.position}</p>}
            </div>
          </div>

          {/* Status */}
          <div className="space-y-2">
            <Label htmlFor="status">الحالة</Label>
            <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">نشط</SelectItem>
                <SelectItem value="inactive">غير نشط</SelectItem>
                <SelectItem value="on_leave">في إجازة</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Salary */}
          <div className="space-y-2">
            <Label htmlFor="salary" className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              الراتب (اختياري)
            </Label>
            <Input
              id="salary"
              type="number"
              step="0.01"
              value={formData.salary}
              onChange={(e) => handleInputChange('salary', e.target.value)}
              placeholder="مثال: 5000.00"
            />
          </div>

          {/* Skills */}
          <div className="space-y-2">
            <Label htmlFor="skills" className="flex items-center gap-2">
              <Award className="h-4 w-4" />
              المهارات (اختياري)
            </Label>
            <Textarea
              id="skills"
              value={formData.skills}
              onChange={(e) => handleInputChange('skills', e.target.value)}
              placeholder="أدخل المهارات مفصولة بفواصل، مثال: React, TypeScript, Node.js"
              rows={3}
            />
          </div>

          {/* Certifications */}
          <div className="space-y-2">
            <Label htmlFor="certifications">الشهادات (اختياري)</Label>
            <Textarea
              id="certifications"
              value={formData.certifications}
              onChange={(e) => handleInputChange('certifications', e.target.value)}
              placeholder="أدخل الشهادات مفصولة بفواصل"
              rows={2}
            />
          </div>

          {/* Emergency Contact */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="emergency_contact_name">اسم جهة الاتصال الطارئ</Label>
              <Input
                id="emergency_contact_name"
                value={formData.emergency_contact_name}
                onChange={(e) => handleInputChange('emergency_contact_name', e.target.value)}
                placeholder="اسم الشخص للاتصال في حالة الطوارئ"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="emergency_contact_phone" className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                هاتف جهة الاتصال الطارئ
              </Label>
              <Input
                id="emergency_contact_phone"
                value={formData.emergency_contact_phone}
                onChange={(e) => handleInputChange('emergency_contact_phone', e.target.value)}
                placeholder="رقم الهاتف"
              />
            </div>
          </div>
        </form>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
            <X className="h-4 w-4 ml-2" />
            إلغاء
          </Button>
          <Button type="submit" onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? (
              <Loader2 className="h-4 w-4 ml-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 ml-2" />
            )}
            {isSubmitting ? 'جاري التحديث...' : 'حفظ التغييرات'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
