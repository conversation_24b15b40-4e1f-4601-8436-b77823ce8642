'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Users,
  UserPlus,
  FolderOpen,
  Save,
  X,
  Loader2
} from 'lucide-react';
import { projectsAPI, clientsAPI } from '@/lib/api';
import { showToast } from '@/lib/toast';
import { useAuthStore } from '@/lib/stores/auth-store';

// Import sub-components (to be created)
import { CreationModeSelector } from './unified-project-creation/creation-mode-selector';
import { ClientSection } from './unified-project-creation/client-section';
import { ProjectInformationSection } from './unified-project-creation/project-information-section';
import { TechnicalSpecsSection } from './unified-project-creation/technical-specs-section';
import { TimelineBudgetSection } from './unified-project-creation/timeline-budget-section';
import { CredentialsSection } from './unified-project-creation/credentials-section';

// Types
interface UnifiedFormData {
  creationMode: 'existing' | 'new';
  selectedClientId?: string;
  clientData: {
    name: string;
    email: string;
    phone: string;
    company: string;
    country: string;
    city: string;
    whatsapp: string;
    website?: string;
    address?: string;
    governorate?: string;
    mood: 'happy' | 'neutral' | 'concerned' | 'angry';
    notes?: string;
  };
  projectData: {
    name: string;
    description: string;
    type: 'website' | 'mobile_app' | 'web_app' | 'ecommerce' | 'wordpress' | 'maintenance' | 'marketing';
    status: 'planning' | 'development' | 'testing' | 'deployment' | 'maintenance' | 'completed' | 'cancelled';
    priority: 'low' | 'medium' | 'high' | 'urgent';
    url?: string;
    reference_urls: string;
    tech_stack: string[];
    assigned_team: string[];
    project_manager?: string;
    start_date: string;
    deadline: string;
    budget: number;
    payment_terms: string;
    // Credentials
    admin_url?: string;
    admin_username?: string;
    admin_password?: string;
    hosting_provider?: string;
    hosting_url?: string;
    hosting_username?: string;
    hosting_password?: string;
    // Email
    email_provider?: string;
    email_address?: string;
    email_password?: string;
    smtp_host?: string;
    smtp_port?: number;
    smtp_security?: string;
    smtp_username?: string;
    smtp_password?: string;
  };
}

interface ValidationErrors {
  [key: string]: string;
}

const initialFormData: UnifiedFormData = {
  creationMode: 'existing',
  selectedClientId: '',
  clientData: {
    name: '',
    email: '',
    phone: '',
    company: '',
    country: 'Egypt',
    city: '',
    whatsapp: '',
    website: '',
    address: '',
    governorate: '',
    mood: 'neutral',
    notes: ''
  },
  projectData: {
    name: '',
    description: '',
    type: 'website',
    status: 'planning',
    priority: 'medium',
    url: '',
    reference_urls: '',
    tech_stack: [],
    assigned_team: [],
    project_manager: '',
    start_date: '',
    deadline: '',
    budget: 0,
    payment_terms: '50% upfront, 50% on delivery',
    // Credentials
    admin_url: '',
    admin_username: '',
    admin_password: '',
    hosting_provider: '',
    hosting_url: '',
    hosting_username: '',
    hosting_password: '',
    // Email
    email_provider: '',
    email_address: '',
    email_password: '',
    smtp_host: '',
    smtp_port: 587,
    smtp_security: 'TLS',
    smtp_username: '',
    smtp_password: ''
  }
};

export function UnifiedProjectCreationForm() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [formData, setFormData] = useState<UnifiedFormData>(initialFormData);
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [clients, setClients] = useState<any[]>([]);
  const [showCredentials, setShowCredentials] = useState(false);
  const [showEmailCredentials, setShowEmailCredentials] = useState(false);

  // Load clients for existing client mode
  useEffect(() => {
    const loadClients = async () => {
      try {
        const clientsData = await clientsAPI.getClients();
        setClients(clientsData);
      } catch (error) {
        console.error('Error loading clients:', error);
      }
    };

    if (isAuthenticated) {
      loadClients();
    }
  }, [isAuthenticated]);

  // Validation function
  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {};

    // Validate creation mode specific fields
    if (formData.creationMode === 'existing') {
      if (!formData.selectedClientId) {
        newErrors.selectedClientId = 'يجب اختيار عميل من القائمة';
      }
    } else {
      // Validate client data for new client mode
      if (!formData.clientData.name.trim()) {
        newErrors.client_name = 'اسم العميل مطلوب';
      }
      if (!formData.clientData.email.trim()) {
        newErrors.client_email = 'البريد الإلكتروني مطلوب';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.clientData.email)) {
        newErrors.client_email = 'البريد الإلكتروني غير صحيح';
      }
      if (!formData.clientData.phone.trim()) {
        newErrors.client_phone = 'رقم الهاتف مطلوب';
      }
      if (!formData.clientData.company.trim()) {
        newErrors.client_company = 'اسم الشركة مطلوب';
      }
      if (!formData.clientData.city.trim()) {
        newErrors.client_city = 'المدينة مطلوبة';
      }
      if (!formData.clientData.whatsapp.trim()) {
        newErrors.client_whatsapp = 'رقم الواتساب مطلوب';
      }
    }

    // Validate project data
    if (!formData.projectData.name.trim()) {
      newErrors.project_name = 'اسم المشروع مطلوب';
    }
    if (!formData.projectData.description.trim()) {
      newErrors.project_description = 'وصف المشروع مطلوب';
    }
    if (!formData.projectData.start_date) {
      newErrors.project_start_date = 'تاريخ البداية مطلوب';
    }
    if (!formData.projectData.deadline) {
      newErrors.project_deadline = 'الموعد النهائي مطلوب';
    }
    if (formData.projectData.budget <= 0) {
      newErrors.project_budget = 'الميزانية يجب أن تكون أكبر من صفر';
    }
    if (!formData.projectData.reference_urls.trim()) {
      newErrors.project_reference_urls = 'روابط المراجع مطلوبة';
    }
    if (formData.projectData.tech_stack.length === 0) {
      newErrors.project_tech_stack = 'يجب اختيار تقنية واحدة على الأقل';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Form submission handler
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      showToast.error('يرجى تصحيح الأخطاء في النموذج');
      return;
    }

    setIsSubmitting(true);
    try {
      const submissionData = {
        creation_mode: formData.creationMode,
        ...(formData.creationMode === 'existing' 
          ? { client_id: parseInt(formData.selectedClientId!) }
          : { client_data: formData.clientData }
        ),
        project_data: formData.projectData
      };

      const result = await projectsAPI.createProjectWithClient(submissionData);
      
      // Success handling
      showToast.success('تم إنشاء المشروع بنجاح! 🎉', result.message);
      router.push('/founder-dashboard/projects');
      
    } catch (error: any) {
      console.error('Error creating project:', error);
      
      // Extract error message from API response
      let errorMessage = 'حدث خطأ أثناء إنشاء المشروع. يرجى المحاولة مرة أخرى.';
      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.details) {
        errorMessage = error.response.data.details;
      }
      
      showToast.error('فشل في إنشاء المشروع', errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Cancel handler
  const handleCancel = () => {
    router.push('/founder-dashboard/projects');
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Creation Mode Selection */}
      <CreationModeSelector 
        value={formData.creationMode}
        onChange={(mode) => setFormData(prev => ({ ...prev, creationMode: mode }))}
      />

      {/* Client Section */}
      <ClientSection 
        mode={formData.creationMode}
        clientData={formData.clientData}
        selectedClientId={formData.selectedClientId}
        clients={clients}
        onClientDataChange={(data) => setFormData(prev => ({ ...prev, clientData: data }))}
        onClientSelect={(id) => setFormData(prev => ({ ...prev, selectedClientId: id }))}
        errors={errors}
      />

      {/* Project Information Section */}
      <ProjectInformationSection 
        data={formData.projectData}
        onChange={(data) => setFormData(prev => ({ ...prev, projectData: { ...prev.projectData, ...data } }))}
        errors={errors}
      />

      {/* Technical Specifications Section */}
      <TechnicalSpecsSection 
        data={formData.projectData}
        onChange={(data) => setFormData(prev => ({ ...prev, projectData: { ...prev.projectData, ...data } }))}
        errors={errors}
      />

      {/* Timeline & Budget Section */}
      <TimelineBudgetSection 
        data={formData.projectData}
        onChange={(data) => setFormData(prev => ({ ...prev, projectData: { ...prev.projectData, ...data } }))}
        errors={errors}
      />

      {/* Credentials Section */}
      <CredentialsSection 
        data={formData.projectData}
        onChange={(data) => setFormData(prev => ({ ...prev, projectData: { ...prev.projectData, ...data } }))}
        showCredentials={showCredentials}
        showEmailCredentials={showEmailCredentials}
        onToggleCredentials={() => setShowCredentials(!showCredentials)}
        onToggleEmailCredentials={() => setShowEmailCredentials(!showEmailCredentials)}
        errors={errors}
      />

      {/* Form Actions */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-end gap-4">
            <Button 
              type="button" 
              variant="outline" 
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              <X className="h-4 w-4 ml-2" />
              إلغاء
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="bg-purple-600 hover:bg-purple-700"
            >
              {isSubmitting ? (
                <Loader2 className="h-4 w-4 ml-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 ml-2" />
              )}
              {isSubmitting ? 'جاري الحفظ...' : 'حفظ المشروع'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </form>
  );
}
