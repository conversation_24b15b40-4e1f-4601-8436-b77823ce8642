'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  UserPlus,
  Save,
  X,
  Loader2,
  User,
  Building,
  Calendar,
  DollarSign,
  Phone,
  Award
} from 'lucide-react';

interface TeamMemberFormData {
  user: string;
  employee_id: string;
  department: string;
  position: string;
  status: string;
  hire_date: string;
  salary: string;
  skills: string;
  certifications: string;
  emergency_contact_name: string;
  emergency_contact_phone: string;
}

interface AddTeamMemberFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: TeamMemberFormData) => Promise<void>;
  availableUsers: any[];
}

export function AddTeamMemberForm({ isOpen, onClose, onSubmit, availableUsers }: AddTeamMemberFormProps) {
  const [formData, setFormData] = useState<TeamMemberFormData>({
    user: '',
    employee_id: '',
    department: 'development',
    position: '',
    status: 'active',
    hire_date: '',
    salary: '',
    skills: '',
    certifications: '',
    emergency_contact_name: '',
    emergency_contact_phone: ''
  });

  const [errors, setErrors] = useState<Partial<TeamMemberFormData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: Partial<TeamMemberFormData> = {};

    if (!formData.user) {
      newErrors.user = 'يجب اختيار المستخدم';
    }

    if (!formData.employee_id.trim()) {
      newErrors.employee_id = 'رقم الموظف مطلوب';
    }

    if (!formData.position.trim()) {
      newErrors.position = 'المنصب مطلوب';
    }

    if (!formData.hire_date) {
      newErrors.hire_date = 'تاريخ التوظيف مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // Transform data for API
      const apiData = {
        ...formData,
        user: parseInt(formData.user),
        salary: formData.salary ? parseFloat(formData.salary) : null,
        skills: formData.skills ? formData.skills.split(',').map(s => s.trim()).filter(s => s) : [],
        certifications: formData.certifications ? formData.certifications.split(',').map(c => c.trim()).filter(c => c) : []
      };

      await onSubmit(apiData);
      handleClose();
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({
      user: '',
      employee_id: '',
      department: 'development',
      position: '',
      status: 'active',
      hire_date: '',
      salary: '',
      skills: '',
      certifications: '',
      emergency_contact_name: '',
      emergency_contact_phone: ''
    });
    setErrors({});
    setIsSubmitting(false);
    onClose();
  };

  const handleInputChange = (field: keyof TeamMemberFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5 text-purple-600" />
            إضافة عضو فريق جديد
          </DialogTitle>
          <DialogDescription>
            أضف عضو جديد إلى فريق الوكالة الرقمية
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* User Selection */}
          <div className="space-y-2">
            <Label htmlFor="user" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              المستخدم *
            </Label>
            <Select value={formData.user} onValueChange={(value) => handleInputChange('user', value)}>
              <SelectTrigger>
                <SelectValue placeholder="اختر المستخدم" />
              </SelectTrigger>
              <SelectContent>
                {availableUsers.map((user) => (
                  <SelectItem key={user.id} value={user.id.toString()}>
                    {user.first_name && user.last_name 
                      ? `${user.first_name} ${user.last_name}` 
                      : user.username} ({user.email})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.user && <p className="text-sm text-red-600">{errors.user}</p>}
          </div>

          {/* Employee ID */}
          <div className="space-y-2">
            <Label htmlFor="employee_id">رقم الموظف *</Label>
            <Input
              id="employee_id"
              value={formData.employee_id}
              onChange={(e) => handleInputChange('employee_id', e.target.value)}
              placeholder="مثال: EMP001"
            />
            {errors.employee_id && <p className="text-sm text-red-600">{errors.employee_id}</p>}
          </div>

          {/* Department and Position */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="department" className="flex items-center gap-2">
                <Building className="h-4 w-4" />
                القسم
              </Label>
              <Select value={formData.department} onValueChange={(value) => handleInputChange('department', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sales">المبيعات</SelectItem>
                  <SelectItem value="media_buying">شراء الإعلانات</SelectItem>
                  <SelectItem value="development">التطوير</SelectItem>
                  <SelectItem value="design">التصميم</SelectItem>
                  <SelectItem value="wordpress">ووردبريس</SelectItem>
                  <SelectItem value="management">الإدارة</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="position">المنصب *</Label>
              <Input
                id="position"
                value={formData.position}
                onChange={(e) => handleInputChange('position', e.target.value)}
                placeholder="مثال: مطور واجهات أمامية"
              />
              {errors.position && <p className="text-sm text-red-600">{errors.position}</p>}
            </div>
          </div>

          {/* Status and Hire Date */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">الحالة</Label>
              <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">نشط</SelectItem>
                  <SelectItem value="inactive">غير نشط</SelectItem>
                  <SelectItem value="on_leave">في إجازة</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="hire_date" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                تاريخ التوظيف *
              </Label>
              <Input
                id="hire_date"
                type="date"
                value={formData.hire_date}
                onChange={(e) => handleInputChange('hire_date', e.target.value)}
              />
              {errors.hire_date && <p className="text-sm text-red-600">{errors.hire_date}</p>}
            </div>
          </div>

          {/* Salary */}
          <div className="space-y-2">
            <Label htmlFor="salary" className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              الراتب (اختياري)
            </Label>
            <Input
              id="salary"
              type="number"
              step="0.01"
              value={formData.salary}
              onChange={(e) => handleInputChange('salary', e.target.value)}
              placeholder="مثال: 5000.00"
            />
          </div>

          {/* Skills */}
          <div className="space-y-2">
            <Label htmlFor="skills" className="flex items-center gap-2">
              <Award className="h-4 w-4" />
              المهارات (اختياري)
            </Label>
            <Textarea
              id="skills"
              value={formData.skills}
              onChange={(e) => handleInputChange('skills', e.target.value)}
              placeholder="أدخل المهارات مفصولة بفواصل، مثال: React, TypeScript, Node.js"
              rows={3}
            />
          </div>

          {/* Certifications */}
          <div className="space-y-2">
            <Label htmlFor="certifications">الشهادات (اختياري)</Label>
            <Textarea
              id="certifications"
              value={formData.certifications}
              onChange={(e) => handleInputChange('certifications', e.target.value)}
              placeholder="أدخل الشهادات مفصولة بفواصل"
              rows={2}
            />
          </div>

          {/* Emergency Contact */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="emergency_contact_name">اسم جهة الاتصال الطارئ</Label>
              <Input
                id="emergency_contact_name"
                value={formData.emergency_contact_name}
                onChange={(e) => handleInputChange('emergency_contact_name', e.target.value)}
                placeholder="اسم الشخص للاتصال في حالة الطوارئ"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="emergency_contact_phone" className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                هاتف جهة الاتصال الطارئ
              </Label>
              <Input
                id="emergency_contact_phone"
                value={formData.emergency_contact_phone}
                onChange={(e) => handleInputChange('emergency_contact_phone', e.target.value)}
                placeholder="رقم الهاتف"
              />
            </div>
          </div>
        </form>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
            <X className="h-4 w-4 ml-2" />
            إلغاء
          </Button>
          <Button type="submit" onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? (
              <Loader2 className="h-4 w-4 ml-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 ml-2" />
            )}
            {isSubmitting ? 'جاري الحفظ...' : 'حفظ عضو الفريق'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
