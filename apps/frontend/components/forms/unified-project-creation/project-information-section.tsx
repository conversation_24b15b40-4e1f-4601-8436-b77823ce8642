'use client';

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FolderOpen, FileText, Globe } from 'lucide-react';

interface ProjectData {
  name: string;
  description: string;
  type: 'website' | 'mobile_app' | 'web_app' | 'ecommerce' | 'wordpress' | 'maintenance' | 'marketing';
  status: 'planning' | 'development' | 'testing' | 'deployment' | 'maintenance' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  url?: string;
  reference_urls: string;
  [key: string]: any;
}

interface ProjectInformationSectionProps {
  data: ProjectData;
  onChange: (data: Partial<ProjectData>) => void;
  errors: { [key: string]: string };
}

const PROJECT_TYPES = [
  { value: 'website', label: 'موقع إلكتروني' },
  { value: 'mobile_app', label: 'تطبيق جوال' },
  { value: 'web_app', label: 'تطبيق ويب' },
  { value: 'ecommerce', label: 'متجر إلكتروني' },
  { value: 'wordpress', label: 'ووردبريس' },
  { value: 'maintenance', label: 'صيانة' },
  { value: 'marketing', label: 'تسويق' }
];

const PROJECT_STATUS = [
  { value: 'planning', label: 'التخطيط' },
  { value: 'development', label: 'التطوير' },
  { value: 'testing', label: 'الاختبار' },
  { value: 'deployment', label: 'النشر' },
  { value: 'maintenance', label: 'الصيانة' },
  { value: 'completed', label: 'مكتمل' },
  { value: 'cancelled', label: 'ملغي' }
];

const PROJECT_PRIORITY = [
  { value: 'low', label: 'منخفضة' },
  { value: 'medium', label: 'متوسطة' },
  { value: 'high', label: 'عالية' },
  { value: 'urgent', label: 'عاجلة' }
];

export function ProjectInformationSection({ data, onChange, errors }: ProjectInformationSectionProps) {
  
  const handleInputChange = (field: keyof ProjectData, value: string) => {
    onChange({ [field]: value });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FolderOpen className="h-5 w-5" />
          معلومات المشروع
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Basic Project Information */}
        <div>
          <h4 className="font-medium text-gray-900 mb-4">المعلومات الأساسية</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="project_name">اسم المشروع *</Label>
              <Input
                id="project_name"
                value={data.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="أدخل اسم المشروع"
                className={errors.project_name ? 'border-red-500' : ''}
              />
              {errors.project_name && <p className="text-sm text-red-500 mt-1">{errors.project_name}</p>}
            </div>

            <div>
              <Label htmlFor="project_type">نوع المشروع *</Label>
              <Select value={data.type} onValueChange={(value) => handleInputChange('type', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر نوع المشروع" />
                </SelectTrigger>
                <SelectContent>
                  {PROJECT_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="mt-4">
            <Label htmlFor="project_description">وصف المشروع *</Label>
            <div className="relative">
              <FileText className="absolute right-3 top-3 text-gray-400 h-4 w-4" />
              <Textarea
                id="project_description"
                value={data.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="أدخل وصف تفصيلي للمشروع..."
                className={`pr-10 ${errors.project_description ? 'border-red-500' : ''}`}
                rows={4}
              />
            </div>
            {errors.project_description && <p className="text-sm text-red-500 mt-1">{errors.project_description}</p>}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <Label htmlFor="project_status">حالة المشروع</Label>
              <Select value={data.status} onValueChange={(value) => handleInputChange('status', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر حالة المشروع" />
                </SelectTrigger>
                <SelectContent>
                  {PROJECT_STATUS.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="project_priority">الأولوية</Label>
              <Select value={data.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر الأولوية" />
                </SelectTrigger>
                <SelectContent>
                  {PROJECT_PRIORITY.map((priority) => (
                    <SelectItem key={priority.value} value={priority.value}>
                      {priority.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Project URLs */}
        <div>
          <h4 className="font-medium text-gray-900 mb-4">روابط المشروع</h4>
          <div className="space-y-4">
            <div>
              <Label htmlFor="project_url">رابط المشروع الحالي</Label>
              <div className="relative">
                <Globe className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  id="project_url"
                  value={data.url || ''}
                  onChange={(e) => handleInputChange('url', e.target.value)}
                  placeholder="https://example.com"
                  className="pr-10"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="project_reference_urls">روابط المراجع *</Label>
              <Textarea
                id="project_reference_urls"
                value={data.reference_urls}
                onChange={(e) => handleInputChange('reference_urls', e.target.value)}
                placeholder="أدخل روابط المواقع المرجعية (كل رابط في سطر منفصل)&#10;https://example1.com&#10;https://example2.com"
                className={errors.project_reference_urls ? 'border-red-500' : ''}
                rows={4}
              />
              {errors.project_reference_urls && <p className="text-sm text-red-500 mt-1">{errors.project_reference_urls}</p>}
              <p className="text-sm text-gray-500 mt-1">
                أدخل كل رابط في سطر منفصل. هذه الروابط ستساعد الفريق في فهم التصميم والوظائف المطلوبة.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
