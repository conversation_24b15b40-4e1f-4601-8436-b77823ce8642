'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Lock, Mail, Server, ChevronDown, ChevronRight, Eye, EyeOff } from 'lucide-react';
import { useState } from 'react';

interface ProjectData {
  admin_url?: string;
  admin_username?: string;
  admin_password?: string;
  hosting_provider?: string;
  hosting_url?: string;
  hosting_username?: string;
  hosting_password?: string;
  email_provider?: string;
  email_address?: string;
  email_password?: string;
  smtp_host?: string;
  smtp_port?: number;
  smtp_security?: string;
  smtp_username?: string;
  smtp_password?: string;
  [key: string]: any;
}

interface CredentialsSectionProps {
  data: ProjectData;
  onChange: (data: Partial<ProjectData>) => void;
  showCredentials: boolean;
  showEmailCredentials: boolean;
  onToggleCredentials: () => void;
  onToggleEmailCredentials: () => void;
  errors: { [key: string]: string };
}

const EMAIL_PROVIDERS = [
  { value: 'gmail', label: 'Gmail' },
  { value: 'outlook', label: 'Outlook' },
  { value: 'yahoo', label: 'Yahoo' },
  { value: 'custom_smtp', label: 'Custom SMTP' },
  { value: 'other', label: 'أخرى' }
];

const HOSTING_PROVIDERS = [
  { value: 'cpanel', label: 'cPanel' },
  { value: 'wordpress_com', label: 'WordPress.com' },
  { value: 'custom', label: 'استضافة مخصصة' },
  { value: 'other', label: 'أخرى' }
];

const SMTP_SECURITY = [
  { value: 'SSL', label: 'SSL' },
  { value: 'TLS', label: 'TLS' },
  { value: 'None', label: 'بدون تشفير' }
];

export function CredentialsSection({ 
  data, 
  onChange, 
  showCredentials, 
  showEmailCredentials,
  onToggleCredentials,
  onToggleEmailCredentials,
  errors 
}: CredentialsSectionProps) {
  
  const [showPasswords, setShowPasswords] = useState<{ [key: string]: boolean }>({});

  const handleInputChange = (field: keyof ProjectData, value: string | number) => {
    onChange({ [field]: value });
  };

  const togglePasswordVisibility = (field: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lock className="h-5 w-5" />
          بيانات الاعتماد (اختيارية)
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Project Credentials */}
        <Collapsible open={showCredentials} onOpenChange={onToggleCredentials}>
          <CollapsibleTrigger asChild>
            <Button variant="outline" className="w-full justify-between">
              <div className="flex items-center gap-2">
                <Server className="h-4 w-4" />
                بيانات اعتماد المشروع
              </div>
              {showCredentials ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="admin_url">رابط لوحة الإدارة</Label>
                <Input
                  id="admin_url"
                  value={data.admin_url || ''}
                  onChange={(e) => handleInputChange('admin_url', e.target.value)}
                  placeholder="https://example.com/admin"
                />
              </div>

              <div>
                <Label htmlFor="admin_username">اسم المستخدم</Label>
                <Input
                  id="admin_username"
                  value={data.admin_username || ''}
                  onChange={(e) => handleInputChange('admin_username', e.target.value)}
                  placeholder="admin"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="admin_password">كلمة المرور</Label>
              <div className="relative">
                <Input
                  id="admin_password"
                  type={showPasswords.admin_password ? 'text' : 'password'}
                  value={data.admin_password || ''}
                  onChange={(e) => handleInputChange('admin_password', e.target.value)}
                  placeholder="••••••••"
                  className="pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                  onClick={() => togglePasswordVisibility('admin_password')}
                >
                  {showPasswords.admin_password ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            {/* Hosting Information */}
            <div className="border-t pt-4">
              <h5 className="font-medium text-gray-900 mb-3">معلومات الاستضافة</h5>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="hosting_provider">مزود الاستضافة</Label>
                  <Select 
                    value={data.hosting_provider || ''} 
                    onValueChange={(value) => handleInputChange('hosting_provider', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر مزود الاستضافة" />
                    </SelectTrigger>
                    <SelectContent>
                      {HOSTING_PROVIDERS.map((provider) => (
                        <SelectItem key={provider.value} value={provider.value}>
                          {provider.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="hosting_url">رابط الاستضافة</Label>
                    <Input
                      id="hosting_url"
                      value={data.hosting_url || ''}
                      onChange={(e) => handleInputChange('hosting_url', e.target.value)}
                      placeholder="https://cpanel.example.com"
                    />
                  </div>

                  <div>
                    <Label htmlFor="hosting_username">اسم المستخدم</Label>
                    <Input
                      id="hosting_username"
                      value={data.hosting_username || ''}
                      onChange={(e) => handleInputChange('hosting_username', e.target.value)}
                      placeholder="username"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="hosting_password">كلمة مرور الاستضافة</Label>
                  <div className="relative">
                    <Input
                      id="hosting_password"
                      type={showPasswords.hosting_password ? 'text' : 'password'}
                      value={data.hosting_password || ''}
                      onChange={(e) => handleInputChange('hosting_password', e.target.value)}
                      placeholder="••••••••"
                      className="pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute left-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                      onClick={() => togglePasswordVisibility('hosting_password')}
                    >
                      {showPasswords.hosting_password ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Email Credentials */}
        <Collapsible open={showEmailCredentials} onOpenChange={onToggleEmailCredentials}>
          <CollapsibleTrigger asChild>
            <Button variant="outline" className="w-full justify-between">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                بيانات اعتماد البريد الإلكتروني
              </div>
              {showEmailCredentials ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email_provider">مزود البريد الإلكتروني</Label>
                <Select 
                  value={data.email_provider || ''} 
                  onValueChange={(value) => handleInputChange('email_provider', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر مزود البريد" />
                  </SelectTrigger>
                  <SelectContent>
                    {EMAIL_PROVIDERS.map((provider) => (
                      <SelectItem key={provider.value} value={provider.value}>
                        {provider.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="email_address">عنوان البريد الإلكتروني</Label>
                <Input
                  id="email_address"
                  type="email"
                  value={data.email_address || ''}
                  onChange={(e) => handleInputChange('email_address', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="email_password">كلمة مرور البريد الإلكتروني</Label>
              <div className="relative">
                <Input
                  id="email_password"
                  type={showPasswords.email_password ? 'text' : 'password'}
                  value={data.email_password || ''}
                  onChange={(e) => handleInputChange('email_password', e.target.value)}
                  placeholder="••••••••"
                  className="pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                  onClick={() => togglePasswordVisibility('email_password')}
                >
                  {showPasswords.email_password ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            {/* SMTP Settings (conditional) */}
            {data.email_provider === 'custom_smtp' && (
              <div className="border-t pt-4">
                <h5 className="font-medium text-gray-900 mb-3">إعدادات SMTP</h5>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="smtp_host">خادم SMTP</Label>
                    <Input
                      id="smtp_host"
                      value={data.smtp_host || ''}
                      onChange={(e) => handleInputChange('smtp_host', e.target.value)}
                      placeholder="smtp.example.com"
                    />
                  </div>

                  <div>
                    <Label htmlFor="smtp_port">منفذ SMTP</Label>
                    <Input
                      id="smtp_port"
                      type="number"
                      value={data.smtp_port || 587}
                      onChange={(e) => handleInputChange('smtp_port', parseInt(e.target.value) || 587)}
                      placeholder="587"
                    />
                  </div>

                  <div>
                    <Label htmlFor="smtp_security">نوع التشفير</Label>
                    <Select 
                      value={data.smtp_security || 'TLS'} 
                      onValueChange={(value) => handleInputChange('smtp_security', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="اختر نوع التشفير" />
                      </SelectTrigger>
                      <SelectContent>
                        {SMTP_SECURITY.map((security) => (
                          <SelectItem key={security.value} value={security.value}>
                            {security.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <div>
                    <Label htmlFor="smtp_username">اسم مستخدم SMTP</Label>
                    <Input
                      id="smtp_username"
                      value={data.smtp_username || ''}
                      onChange={(e) => handleInputChange('smtp_username', e.target.value)}
                      placeholder="username"
                    />
                  </div>

                  <div>
                    <Label htmlFor="smtp_password">كلمة مرور SMTP</Label>
                    <div className="relative">
                      <Input
                        id="smtp_password"
                        type={showPasswords.smtp_password ? 'text' : 'password'}
                        value={data.smtp_password || ''}
                        onChange={(e) => handleInputChange('smtp_password', e.target.value)}
                        placeholder="••••••••"
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute left-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                        onClick={() => togglePasswordVisibility('smtp_password')}
                      >
                        {showPasswords.smtp_password ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>

        {/* Security Notice */}
        <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-start gap-2">
            <Lock className="h-4 w-4 text-yellow-600 mt-0.5" />
            <div className="text-sm text-yellow-800">
              <p className="font-medium mb-1">ملاحظة أمنية:</p>
              <p>جميع كلمات المرور والبيانات الحساسة يتم تشفيرها وحفظها بشكل آمن. لن يتمكن أحد من رؤية هذه البيانات إلا المخولين بذلك.</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
