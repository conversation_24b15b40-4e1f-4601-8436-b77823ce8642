'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Users, Mail, Phone, Building, Globe, MapPin, FileText } from 'lucide-react';

interface ClientData {
  name: string;
  email: string;
  phone: string;
  company: string;
  country: string;
  city: string;
  whatsapp: string;
  website?: string;
  address?: string;
  governorate?: string;
  mood: 'happy' | 'neutral' | 'concerned' | 'angry';
  notes?: string;
}

interface ClientSectionProps {
  mode: 'existing' | 'new';
  clientData: ClientData;
  selectedClientId?: string;
  clients: any[];
  onClientDataChange: (data: ClientData) => void;
  onClientSelect: (id: string) => void;
  errors: { [key: string]: string };
}

const COUNTRIES = [
  { value: 'Egypt', label: 'مصر' },
  { value: 'Saudi Arabia', label: 'السعودية' },
  { value: 'UAE', label: 'الإمارات' },
  { value: 'Kuwait', label: 'الكويت' },
  { value: 'Qatar', label: 'قطر' },
  { value: 'Bahrain', label: 'البحرين' },
  { value: 'Oman', label: 'عمان' },
  { value: 'Jordan', label: 'الأردن' },
  { value: 'Lebanon', label: 'لبنان' },
  { value: 'Other', label: 'أخرى' }
];

const GOVERNORATES = [
  { value: 'cairo', label: 'القاهرة' },
  { value: 'alexandria', label: 'الإسكندرية' },
  { value: 'giza', label: 'الجيزة' },
  { value: 'qalyubia', label: 'القليوبية' },
  { value: 'port_said', label: 'بورسعيد' },
  { value: 'suez', label: 'السويس' },
  { value: 'luxor', label: 'الأقصر' },
  { value: 'aswan', label: 'أسوان' },
  { value: 'red_sea', label: 'البحر الأحمر' },
  { value: 'new_valley', label: 'الوادي الجديد' },
  { value: 'matrouh', label: 'مطروح' },
  { value: 'north_sinai', label: 'شمال سيناء' },
  { value: 'south_sinai', label: 'جنوب سيناء' },
  { value: 'dakahlia', label: 'الدقهلية' },
  { value: 'sharqia', label: 'الشرقية' },
  { value: 'monufia', label: 'المنوفية' },
  { value: 'beheira', label: 'البحيرة' },
  { value: 'kafr_el_sheikh', label: 'كفر الشيخ' },
  { value: 'gharbia', label: 'الغربية' },
  { value: 'damietta', label: 'دمياط' },
  { value: 'fayoum', label: 'الفيوم' },
  { value: 'beni_suef', label: 'بني سويف' },
  { value: 'minya', label: 'المنيا' },
  { value: 'asyut', label: 'أسيوط' },
  { value: 'sohag', label: 'سوهاج' },
  { value: 'qena', label: 'قنا' }
];

const MOOD_OPTIONS = [
  { value: 'happy', label: 'سعيد', color: 'bg-green-100 text-green-800' },
  { value: 'neutral', label: 'محايد', color: 'bg-gray-100 text-gray-800' },
  { value: 'concerned', label: 'قلق', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'angry', label: 'غاضب', color: 'bg-red-100 text-red-800' }
];

export function ClientSection({ 
  mode, 
  clientData, 
  selectedClientId, 
  clients, 
  onClientDataChange, 
  onClientSelect,
  errors 
}: ClientSectionProps) {
  
  const handleInputChange = (field: keyof ClientData, value: string) => {
    onClientDataChange({ ...clientData, [field]: value });
  };

  const selectedMood = MOOD_OPTIONS.find(mood => mood.value === clientData.mood);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          معلومات العميل
        </CardTitle>
      </CardHeader>
      <CardContent>
        {mode === 'existing' ? (
          // Existing Client Selection
          <div>
            <Label htmlFor="selectedClient">اختر العميل *</Label>
            <Select value={selectedClientId} onValueChange={onClientSelect}>
              <SelectTrigger className={errors.selectedClientId ? 'border-red-500' : ''}>
                <SelectValue placeholder="اختر العميل من القائمة" />
              </SelectTrigger>
              <SelectContent>
                {clients.length > 0 ? (
                  clients.map((client) => (
                    <SelectItem key={client.id} value={String(client.id)}>
                      {client.name} {client.company && `(${client.company})`}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-clients" disabled>
                    لا توجد عملاء متاحين
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
            {errors.selectedClientId && (
              <p className="text-sm text-red-500 mt-1">{errors.selectedClientId}</p>
            )}
            
            {/* Client Preview */}
            {selectedClientId && clients.length > 0 && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                {(() => {
                  const selectedClient = clients.find(c => String(c.id) === selectedClientId);
                  if (!selectedClient) return null;
                  
                  return (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">معاينة بيانات العميل</h4>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div><span className="font-medium">الاسم:</span> {selectedClient.name}</div>
                        <div><span className="font-medium">الشركة:</span> {selectedClient.company || 'غير محدد'}</div>
                        <div><span className="font-medium">البريد:</span> {selectedClient.email}</div>
                        <div><span className="font-medium">الهاتف:</span> {selectedClient.phone}</div>
                      </div>
                    </div>
                  );
                })()}
              </div>
            )}
          </div>
        ) : (
          // New Client Fields
          <div className="space-y-6">
            {/* Basic Information */}
            <div>
              <h4 className="font-medium text-gray-900 mb-4">المعلومات الأساسية</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="client_name">اسم العميل *</Label>
                  <Input
                    id="client_name"
                    value={clientData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="أدخل اسم العميل"
                    className={errors.client_name ? 'border-red-500' : ''}
                  />
                  {errors.client_name && <p className="text-sm text-red-500 mt-1">{errors.client_name}</p>}
                </div>

                <div>
                  <Label htmlFor="client_company">اسم الشركة *</Label>
                  <Input
                    id="client_company"
                    value={clientData.company}
                    onChange={(e) => handleInputChange('company', e.target.value)}
                    placeholder="أدخل اسم الشركة"
                    className={errors.client_company ? 'border-red-500' : ''}
                  />
                  {errors.client_company && <p className="text-sm text-red-500 mt-1">{errors.client_company}</p>}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div>
                  <Label htmlFor="client_email">البريد الإلكتروني *</Label>
                  <div className="relative">
                    <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      id="client_email"
                      type="email"
                      value={clientData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                      className={`pr-10 ${errors.client_email ? 'border-red-500' : ''}`}
                    />
                  </div>
                  {errors.client_email && <p className="text-sm text-red-500 mt-1">{errors.client_email}</p>}
                </div>

                <div>
                  <Label htmlFor="client_phone">رقم الهاتف *</Label>
                  <div className="relative">
                    <Phone className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      id="client_phone"
                      value={clientData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      placeholder="+20 ************"
                      className={`pr-10 ${errors.client_phone ? 'border-red-500' : ''}`}
                    />
                  </div>
                  {errors.client_phone && <p className="text-sm text-red-500 mt-1">{errors.client_phone}</p>}
                </div>
              </div>

              <div>
                <Label htmlFor="client_website">الموقع الإلكتروني</Label>
                <div className="relative">
                  <Globe className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="client_website"
                    value={clientData.website || ''}
                    onChange={(e) => handleInputChange('website', e.target.value)}
                    placeholder="https://www.company.com"
                    className="pr-10"
                  />
                </div>
              </div>
            </div>

            {/* Location Information */}
            <div>
              <h4 className="font-medium text-gray-900 mb-4">معلومات الموقع</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="client_country">الدولة *</Label>
                  <Select value={clientData.country} onValueChange={(value) => handleInputChange('country', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الدولة" />
                    </SelectTrigger>
                    <SelectContent>
                      {COUNTRIES.map((country) => (
                        <SelectItem key={country.value} value={country.value}>
                          {country.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="client_city">المدينة *</Label>
                  <Input
                    id="client_city"
                    value={clientData.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    placeholder="أدخل اسم المدينة"
                    className={errors.client_city ? 'border-red-500' : ''}
                  />
                  {errors.client_city && <p className="text-sm text-red-500 mt-1">{errors.client_city}</p>}
                </div>

                {clientData.country === 'Egypt' && (
                  <div>
                    <Label htmlFor="client_governorate">المحافظة</Label>
                    <Select value={clientData.governorate || ''} onValueChange={(value) => handleInputChange('governorate', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر المحافظة" />
                      </SelectTrigger>
                      <SelectContent>
                        {GOVERNORATES.map((gov) => (
                          <SelectItem key={gov.value} value={gov.value}>
                            {gov.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>

              <div className="mt-4">
                <Label htmlFor="client_whatsapp">رقم الواتساب *</Label>
                <Input
                  id="client_whatsapp"
                  value={clientData.whatsapp}
                  onChange={(e) => handleInputChange('whatsapp', e.target.value)}
                  placeholder="+20 ************"
                  className={errors.client_whatsapp ? 'border-red-500' : ''}
                />
                {errors.client_whatsapp && <p className="text-sm text-red-500 mt-1">{errors.client_whatsapp}</p>}
              </div>

              <div className="mt-4">
                <Label htmlFor="client_address">العنوان التفصيلي</Label>
                <div className="relative">
                  <MapPin className="absolute right-3 top-3 text-gray-400 h-4 w-4" />
                  <Textarea
                    id="client_address"
                    value={clientData.address || ''}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    placeholder="أدخل العنوان التفصيلي"
                    className="pr-10"
                    rows={2}
                  />
                </div>
              </div>
            </div>

            {/* Additional Information */}
            <div>
              <h4 className="font-medium text-gray-900 mb-4">معلومات إضافية</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="client_mood">حالة العميل</Label>
                  <Select value={clientData.mood} onValueChange={(value: any) => handleInputChange('mood', value)}>
                    <SelectTrigger>
                      <SelectValue>
                        {selectedMood && (
                          <div className="flex items-center gap-2">
                            <Badge className={selectedMood.color}>{selectedMood.label}</Badge>
                          </div>
                        )}
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      {MOOD_OPTIONS.map((mood) => (
                        <SelectItem key={mood.value} value={mood.value}>
                          <div className="flex items-center gap-2">
                            <Badge className={mood.color}>{mood.label}</Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="mt-4">
                <Label htmlFor="client_notes">ملاحظات</Label>
                <div className="relative">
                  <FileText className="absolute right-3 top-3 text-gray-400 h-4 w-4" />
                  <Textarea
                    id="client_notes"
                    value={clientData.notes || ''}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    placeholder="أضف أي ملاحظات حول العميل..."
                    className="pr-10"
                    rows={3}
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
