'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, UserPlus } from 'lucide-react';

interface CreationModeOption {
  value: 'existing' | 'new';
  label: string;
  description: string;
  icon: React.ComponentType<any>;
}

const CREATION_MODE_OPTIONS: CreationModeOption[] = [
  {
    value: 'existing',
    label: 'إنشاء مشروع لعميل موجود',
    description: 'اختر عميل من القائمة الموجودة وأضف مشروع جديد له',
    icon: Users
  },
  {
    value: 'new',
    label: 'إنشاء مشروع مع عميل جديد',
    description: 'أضف عميل جديد ومشروع في نفس الوقت',
    icon: UserPlus
  }
];

interface CreationModeSelectorProps {
  value: 'existing' | 'new';
  onChange: (mode: 'existing' | 'new') => void;
}

export function CreationModeSelector({ value, onChange }: CreationModeSelectorProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>اختر طريقة إنشاء المشروع</CardTitle>
        <CardDescription>
          حدد ما إذا كنت تريد إنشاء مشروع لعميل موجود أم إضافة عميل جديد
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {CREATION_MODE_OPTIONS.map((option) => {
            const IconComponent = option.icon;
            return (
              <div
                key={option.value}
                className={`relative cursor-pointer rounded-lg border p-4 transition-all ${
                  value === option.value
                    ? 'border-purple-500 bg-purple-50 ring-2 ring-purple-500'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => onChange(option.value)}
              >
                <div className="flex items-start space-x-3 space-x-reverse">
                  <input
                    type="radio"
                    name="creationMode"
                    value={option.value}
                    checked={value === option.value}
                    onChange={() => onChange(option.value)}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <IconComponent className="h-5 w-5 text-purple-600" />
                      <h3 className="font-medium text-gray-900">{option.label}</h3>
                    </div>
                    <p className="text-sm text-gray-600">{option.description}</p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
