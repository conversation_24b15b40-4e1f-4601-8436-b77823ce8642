'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Code, Users, X } from 'lucide-react';

interface ProjectData {
  tech_stack: string[];
  assigned_team: string[];
  project_manager?: string;
  [key: string]: any;
}

interface TechnicalSpecsSectionProps {
  data: ProjectData;
  onChange: (data: Partial<ProjectData>) => void;
  errors: { [key: string]: string };
}

const TECH_STACK_OPTIONS = [
  { value: 'react-nextjs', label: 'React/Next.js', category: 'Frontend' },
  { value: 'laravel-php', label: 'Laravel/PHP', category: 'Backend' },
  { value: 'python-django', label: 'Python/Django', category: 'Backend' },
  { value: 'nodejs-express', label: 'Node.js/Express', category: 'Backend' },
  { value: 'wordpress', label: 'WordPress', category: 'CMS' },
  { value: 'woocommerce', label: 'WooCommerce', category: 'E-commerce' },
  { value: 'shopify', label: 'Shopify', category: 'E-commerce' },
  { value: 'vuejs', label: 'Vue.js', category: 'Frontend' },
  { value: 'angular', label: 'Angular', category: 'Frontend' },
  { value: 'flutter', label: 'Flutter', category: 'Mobile' },
  { value: 'react-native', label: 'React Native', category: 'Mobile' },
  { value: 'custom', label: 'تطوير مخصص', category: 'Other' }
];

const TEAM_MEMBERS = [
  { id: '1', name: 'أحمد محمد', role: 'مطور فرونت إند', avatar: '👨‍💻' },
  { id: '2', name: 'فاطمة علي', role: 'مطورة باك إند', avatar: '👩‍💻' },
  { id: '3', name: 'محمد حسن', role: 'مصمم UI/UX', avatar: '🎨' },
  { id: '4', name: 'سارة أحمد', role: 'مطورة ووردبريس', avatar: '👩‍💻' },
  { id: '5', name: 'عمر خالد', role: 'مطور موبايل', avatar: '📱' },
  { id: '6', name: 'نور محمود', role: 'مختبرة جودة', avatar: '🔍' }
];

const PROJECT_MANAGERS = [
  { id: '1', name: 'أحمد محمد', role: 'مدير مشاريع أول' },
  { id: '2', name: 'فاطمة علي', role: 'مديرة مشاريع تقنية' },
  { id: '3', name: 'محمد حسن', role: 'مدير مشاريع إبداعية' }
];

export function TechnicalSpecsSection({ data, onChange, errors }: TechnicalSpecsSectionProps) {
  const [selectedTechStack, setSelectedTechStack] = useState('');
  const [selectedTeamMember, setSelectedTeamMember] = useState('');

  const handleAddTechStack = () => {
    if (selectedTechStack && !data.tech_stack.includes(selectedTechStack)) {
      onChange({
        tech_stack: [...data.tech_stack, selectedTechStack]
      });
      setSelectedTechStack('');
    }
  };

  const handleRemoveTechStack = (tech: string) => {
    onChange({
      tech_stack: data.tech_stack.filter(t => t !== tech)
    });
  };

  const handleAddTeamMember = () => {
    if (selectedTeamMember && !data.assigned_team.includes(selectedTeamMember)) {
      onChange({
        assigned_team: [...data.assigned_team, selectedTeamMember]
      });
      setSelectedTeamMember('');
    }
  };

  const handleRemoveTeamMember = (memberId: string) => {
    onChange({
      assigned_team: data.assigned_team.filter(id => id !== memberId)
    });
  };

  const handleProjectManagerChange = (managerId: string) => {
    onChange({
      project_manager: managerId
    });
  };

  const getTechStackLabel = (value: string) => {
    const tech = TECH_STACK_OPTIONS.find(t => t.value === value);
    return tech ? tech.label : value;
  };

  const getTeamMemberName = (id: string) => {
    const member = TEAM_MEMBERS.find(m => m.id === id);
    return member ? member.name : id;
  };

  const getTeamMemberRole = (id: string) => {
    const member = TEAM_MEMBERS.find(m => m.id === id);
    return member ? member.role : '';
  };

  const getTeamMemberAvatar = (id: string) => {
    const member = TEAM_MEMBERS.find(m => m.id === id);
    return member ? member.avatar : '👤';
  };

  // Group tech stack by category
  const groupedTechStack = TECH_STACK_OPTIONS.reduce((acc, tech) => {
    if (!acc[tech.category]) {
      acc[tech.category] = [];
    }
    acc[tech.category].push(tech);
    return acc;
  }, {} as Record<string, typeof TECH_STACK_OPTIONS>);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Code className="h-5 w-5" />
          المواصفات التقنية والفريق
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Tech Stack Selection */}
        <div>
          <h4 className="font-medium text-gray-900 mb-4">التقنيات المستخدمة *</h4>
          
          <div className="flex gap-2 mb-4">
            <Select value={selectedTechStack} onValueChange={setSelectedTechStack}>
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="اختر التقنية" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(groupedTechStack).map(([category, techs]) => (
                  <div key={category}>
                    <div className="px-2 py-1 text-sm font-medium text-gray-500 bg-gray-50">
                      {category}
                    </div>
                    {techs.map((tech) => (
                      <SelectItem key={tech.value} value={tech.value}>
                        {tech.label}
                      </SelectItem>
                    ))}
                  </div>
                ))}
              </SelectContent>
            </Select>
            <Button 
              type="button" 
              onClick={handleAddTechStack}
              disabled={!selectedTechStack}
              variant="outline"
            >
              إضافة
            </Button>
          </div>

          {/* Selected Tech Stack */}
          <div className="flex flex-wrap gap-2 mb-2">
            {data.tech_stack.map((tech) => (
              <Badge key={tech} variant="secondary" className="flex items-center gap-1">
                {getTechStackLabel(tech)}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-transparent"
                  onClick={() => handleRemoveTechStack(tech)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
          
          {errors.project_tech_stack && (
            <p className="text-sm text-red-500">{errors.project_tech_stack}</p>
          )}
          
          {data.tech_stack.length === 0 && (
            <p className="text-sm text-gray-500">لم يتم اختيار أي تقنيات بعد</p>
          )}
        </div>

        {/* Project Manager Selection */}
        <div>
          <h4 className="font-medium text-gray-900 mb-4">مدير المشروع</h4>
          <Select value={data.project_manager || ''} onValueChange={handleProjectManagerChange}>
            <SelectTrigger>
              <SelectValue placeholder="اختر مدير المشروع" />
            </SelectTrigger>
            <SelectContent>
              {PROJECT_MANAGERS.map((manager) => (
                <SelectItem key={manager.id} value={manager.id}>
                  <div className="flex items-center gap-2">
                    <span>{manager.name}</span>
                    <span className="text-sm text-gray-500">({manager.role})</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Team Assignment */}
        <div>
          <h4 className="font-medium text-gray-900 mb-4">أعضاء الفريق المكلفين</h4>
          
          <div className="flex gap-2 mb-4">
            <Select value={selectedTeamMember} onValueChange={setSelectedTeamMember}>
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="اختر عضو فريق" />
              </SelectTrigger>
              <SelectContent>
                {TEAM_MEMBERS.map((member) => (
                  <SelectItem key={member.id} value={member.id}>
                    <div className="flex items-center gap-2">
                      <span>{member.avatar}</span>
                      <div>
                        <div>{member.name}</div>
                        <div className="text-sm text-gray-500">{member.role}</div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button 
              type="button" 
              onClick={handleAddTeamMember}
              disabled={!selectedTeamMember}
              variant="outline"
            >
              إضافة
            </Button>
          </div>

          {/* Assigned Team Members */}
          <div className="space-y-2">
            {data.assigned_team.map((memberId) => (
              <div key={memberId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <span className="text-lg">{getTeamMemberAvatar(memberId)}</span>
                  <div>
                    <div className="font-medium">{getTeamMemberName(memberId)}</div>
                    <div className="text-sm text-gray-500">{getTeamMemberRole(memberId)}</div>
                  </div>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveTeamMember(memberId)}
                  className="text-red-500 hover:text-red-700 hover:bg-red-50"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
          
          {data.assigned_team.length === 0 && (
            <p className="text-sm text-gray-500 p-3 bg-gray-50 rounded-lg text-center">
              لم يتم تكليف أي أعضاء فريق بعد
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
