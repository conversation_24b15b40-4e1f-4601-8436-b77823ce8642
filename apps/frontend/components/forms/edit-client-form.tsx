'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Users,
  Mail,
  Phone,
  Building,
  Globe,
  MapPin,
  FileText,
  Save,
  X,
  Loader2,
  Edit
} from 'lucide-react';
import { ClientFormData } from './add-client-form';

interface EditClientFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (clientData: ClientFormData & { id: string }) => Promise<void>;
  client: any; // The client data to edit
}

const GOVERNORATES = [
  { value: 'cairo', label: 'القاهرة' },
  { value: 'alexandria', label: 'الإسكندرية' },
  { value: 'giza', label: 'الجيزة' },
  { value: 'qalyubia', label: 'القليوبية' },
  { value: 'port_said', label: 'بورسعيد' },
  { value: 'suez', label: 'السويس' },
  { value: 'luxor', label: 'الأقصر' },
  { value: 'aswan', label: 'أسوان' },
  { value: 'red_sea', label: 'البحر الأحمر' },
  { value: 'new_valley', label: 'الوادي الجديد' },
  { value: 'matrouh', label: 'مطروح' },
  { value: 'north_sinai', label: 'شمال سيناء' },
  { value: 'south_sinai', label: 'جنوب سيناء' },
  { value: 'dakahlia', label: 'الدقهلية' },
  { value: 'sharqia', label: 'الشرقية' },
  { value: 'monufia', label: 'المنوفية' },
  { value: 'beheira', label: 'البحيرة' },
  { value: 'kafr_el_sheikh', label: 'كفر الشيخ' },
  { value: 'gharbia', label: 'الغربية' },
  { value: 'damietta', label: 'دمياط' },
  { value: 'fayoum', label: 'الفيوم' },
  { value: 'beni_suef', label: 'بني سويف' },
  { value: 'minya', label: 'المنيا' },
  { value: 'asyut', label: 'أسيوط' },
  { value: 'sohag', label: 'سوهاج' },
  { value: 'qena', label: 'قنا' }
];

const MOOD_OPTIONS = [
  { value: 'happy', label: 'سعيد', color: 'bg-green-100 text-green-800' },
  { value: 'neutral', label: 'محايد', color: 'bg-gray-100 text-gray-800' },
  { value: 'concerned', label: 'قلق', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'angry', label: 'غاضب', color: 'bg-red-100 text-red-800' }
];

export function EditClientForm({ isOpen, onClose, onSubmit, client }: EditClientFormProps) {
  const [formData, setFormData] = useState<ClientFormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    website: '',
    address: '',
    governorate: '',
    mood: 'neutral',
    notes: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Partial<ClientFormData>>({});

  // Populate form when client data changes
  useEffect(() => {
    if (client && isOpen) {
      setFormData({
        name: client.name || '',
        email: client.email || '',
        phone: client.phone || '',
        company: client.company || '',
        website: client.website || '',
        address: client.address || '',
        governorate: client.governorate || '',
        mood: client.mood || 'neutral',
        notes: client.notes || ''
      });
    }
  }, [client, isOpen]);

  const validateForm = (): boolean => {
    const newErrors: Partial<ClientFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'اسم العميل مطلوب';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'البريد الإلكتروني مطلوب';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'رقم الهاتف مطلوب';
    }

    if (!formData.company.trim()) {
      newErrors.company = 'اسم الشركة مطلوب';
    }

    if (!formData.governorate) {
      newErrors.governorate = 'المحافظة مطلوبة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit({ ...formData, id: client.id });
      handleClose();
    } catch (error) {
      console.error('Error updating client:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setErrors({});
    setIsSubmitting(false);
    onClose();
  };

  const handleInputChange = (field: keyof ClientFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const selectedMood = MOOD_OPTIONS.find(mood => mood.value === formData.mood);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5 text-purple-600" />
            تعديل بيانات العميل
          </DialogTitle>
          <DialogDescription>
            قم بتعديل المعلومات أدناه لتحديث بيانات العميل
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">المعلومات الأساسية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-name">اسم العميل *</Label>
                  <Input
                    id="edit-name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="أدخل اسم العميل"
                    className={errors.name ? 'border-red-500' : ''}
                  />
                  {errors.name && <p className="text-sm text-red-500 mt-1">{errors.name}</p>}
                </div>

                <div>
                  <Label htmlFor="edit-company">اسم الشركة *</Label>
                  <Input
                    id="edit-company"
                    value={formData.company}
                    onChange={(e) => handleInputChange('company', e.target.value)}
                    placeholder="أدخل اسم الشركة"
                    className={errors.company ? 'border-red-500' : ''}
                  />
                  {errors.company && <p className="text-sm text-red-500 mt-1">{errors.company}</p>}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-email">البريد الإلكتروني *</Label>
                  <div className="relative">
                    <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      id="edit-email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                      className={`pr-10 ${errors.email ? 'border-red-500' : ''}`}
                    />
                  </div>
                  {errors.email && <p className="text-sm text-red-500 mt-1">{errors.email}</p>}
                </div>

                <div>
                  <Label htmlFor="edit-phone">رقم الهاتف *</Label>
                  <div className="relative">
                    <Phone className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      id="edit-phone"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      placeholder="+20 ************"
                      className={`pr-10 ${errors.phone ? 'border-red-500' : ''}`}
                    />
                  </div>
                  {errors.phone && <p className="text-sm text-red-500 mt-1">{errors.phone}</p>}
                </div>
              </div>

              <div>
                <Label htmlFor="edit-website">الموقع الإلكتروني</Label>
                <div className="relative">
                  <Globe className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="edit-website"
                    value={formData.website}
                    onChange={(e) => handleInputChange('website', e.target.value)}
                    placeholder="https://www.company.com"
                    className="pr-10"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Location & Additional Info */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">الموقع والمعلومات الإضافية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-governorate">المحافظة *</Label>
                  <Select value={formData.governorate} onValueChange={(value) => handleInputChange('governorate', value)}>
                    <SelectTrigger className={errors.governorate ? 'border-red-500' : ''}>
                      <SelectValue placeholder="اختر المحافظة" />
                    </SelectTrigger>
                    <SelectContent>
                      {GOVERNORATES.map((gov) => (
                        <SelectItem key={gov.value} value={gov.value}>
                          {gov.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.governorate && <p className="text-sm text-red-500 mt-1">{errors.governorate}</p>}
                </div>

                <div>
                  <Label htmlFor="edit-mood">حالة العميل</Label>
                  <Select value={formData.mood} onValueChange={(value: any) => handleInputChange('mood', value)}>
                    <SelectTrigger>
                      <SelectValue>
                        {selectedMood && (
                          <div className="flex items-center gap-2">
                            <Badge className={selectedMood.color}>{selectedMood.label}</Badge>
                          </div>
                        )}
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      {MOOD_OPTIONS.map((mood) => (
                        <SelectItem key={mood.value} value={mood.value}>
                          <div className="flex items-center gap-2">
                            <Badge className={mood.color}>{mood.label}</Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="edit-address">العنوان التفصيلي</Label>
                <div className="relative">
                  <MapPin className="absolute right-3 top-3 text-gray-400 h-4 w-4" />
                  <Textarea
                    id="edit-address"
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    placeholder="أدخل العنوان التفصيلي"
                    className="pr-10"
                    rows={2}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="edit-notes">ملاحظات</Label>
                <div className="relative">
                  <FileText className="absolute right-3 top-3 text-gray-400 h-4 w-4" />
                  <Textarea
                    id="edit-notes"
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    placeholder="أضف أي ملاحظات حول العميل..."
                    className="pr-10"
                    rows={3}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </form>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
            <X className="h-4 w-4 ml-2" />
            إلغاء
          </Button>
          <Button type="submit" onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? (
              <Loader2 className="h-4 w-4 ml-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 ml-2" />
            )}
            {isSubmitting ? 'جاري التحديث...' : 'حفظ التغييرات'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
