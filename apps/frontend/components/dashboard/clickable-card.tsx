'use client';

import { ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  ExternalLink, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle,
  Clock,
  CheckCircle,
  Users,
  FolderOpen,
  DollarSign
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatCurrency, formatRelativeTime, getStatusColor } from '@mtbrmg/shared';

interface ClickableCardProps {
  title: string;
  description?: string;
  icon?: React.ComponentType<any>;
  href?: string;
  onClick?: () => void;
  children: ReactNode;
  className?: string;
  hoverable?: boolean;
  badge?: string;
  badgeVariant?: 'default' | 'secondary' | 'destructive' | 'outline';
}

export function ClickableCard({
  title,
  description,
  icon: Icon,
  href,
  onClick,
  children,
  className = '',
  hoverable = true,
  badge,
  badgeVariant = 'secondary'
}: ClickableCardProps) {
  const router = useRouter();

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else if (href) {
      router.push(href);
    }
  };

  const isClickable = !!(href || onClick);

  return (
    <Card 
      className={cn(
        'transition-all duration-200',
        isClickable && hoverable && 'cursor-pointer hover:shadow-lg hover:scale-[1.02]',
        className
      )}
      onClick={isClickable ? handleClick : undefined}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="flex items-center gap-2">
          {Icon && <Icon className="h-5 w-5 text-muted-foreground" />}
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
          {badge && (
            <Badge variant={badgeVariant} className="text-xs">
              {badge}
            </Badge>
          )}
        </div>
        {isClickable && hoverable && (
          <ExternalLink className="h-4 w-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity" />
        )}
      </CardHeader>
      <CardContent>
        {description && (
          <CardDescription className="mb-3">{description}</CardDescription>
        )}
        {children}
      </CardContent>
    </Card>
  );
}

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  changeLabel?: string;
  icon?: React.ComponentType<any>;
  href?: string;
  onClick?: () => void;
  progress?: number;
  target?: string | number;
  className?: string;
  valuePrefix?: string;
  valueSuffix?: string;
  trend?: 'up' | 'down' | 'neutral';
}

export function MetricCard({
  title,
  value,
  change,
  changeLabel,
  icon: Icon,
  href,
  onClick,
  progress,
  target,
  className = '',
  valuePrefix = '',
  valueSuffix = '',
  trend
}: MetricCardProps) {
  const getTrendIcon = () => {
    if (trend === 'up' || (change && change > 0)) {
      return <TrendingUp className="h-3 w-3 text-green-600" />;
    } else if (trend === 'down' || (change && change < 0)) {
      return <TrendingDown className="h-3 w-3 text-red-600" />;
    }
    return null;
  };

  const getTrendColor = () => {
    if (trend === 'up' || (change && change > 0)) {
      return 'text-green-600';
    } else if (trend === 'down' || (change && change < 0)) {
      return 'text-red-600';
    }
    return 'text-gray-600';
  };

  return (
    <ClickableCard
      title={title}
      icon={Icon}
      href={href}
      onClick={onClick}
      className={className}
    >
      <div className="space-y-2">
        <div className="text-2xl font-bold">
          {valuePrefix}{typeof value === 'number' ? value.toLocaleString() : value}{valueSuffix}
        </div>
        
        {(change !== undefined || changeLabel) && (
          <div className={`flex items-center text-xs ${getTrendColor()}`}>
            {getTrendIcon()}
            <span className="ml-1">
              {change !== undefined && `${change > 0 ? '+' : ''}${change}%`}
              {changeLabel && ` ${changeLabel}`}
            </span>
          </div>
        )}

        {progress !== undefined && (
          <div className="space-y-1">
            <Progress value={progress} className="h-2" />
            {target && (
              <p className="text-xs text-muted-foreground">
                الهدف: {typeof target === 'number' ? target.toLocaleString() : target}
              </p>
            )}
          </div>
        )}
      </div>
    </ClickableCard>
  );
}

interface ProjectCardProps {
  project: {
    id: string;
    name: string;
    description: string;
    status: string;
    progress: number;
    budget?: number;
    deadline?: string;
    client_name?: string;
  };
  onClick?: (project: any) => void;
  className?: string;
}

export function ProjectCard({ project, onClick, className = '' }: ProjectCardProps) {
  const handleClick = () => {
    if (onClick) {
      onClick(project);
    }
  };

  const isOverdue = project.deadline && new Date(project.deadline) < new Date() && project.status !== 'completed';

  return (
    <ClickableCard
      title={project.name}
      href={`/founder-dashboard/projects/${project.id}`}
      onClick={handleClick}
      className={className}
      badge={isOverdue ? 'متأخر' : undefined}
      badgeVariant={isOverdue ? 'destructive' : 'secondary'}
    >
      <div className="space-y-3">
        <p className="text-sm text-gray-600">{project.description}</p>
        
        <div className="flex items-center gap-2">
          <Badge className={getStatusColor(project.status)}>
            {project.status === 'planning' && 'تخطيط'}
            {project.status === 'development' && 'تطوير'}
            {project.status === 'testing' && 'اختبار'}
            {project.status === 'completed' && 'مكتمل'}
            {project.status === 'on_hold' && 'معلق'}
          </Badge>
          
          {project.client_name && (
            <span className="text-xs text-gray-500">
              العميل: {project.client_name}
            </span>
          )}
        </div>

        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span>التقدم</span>
            <span>{project.progress}%</span>
          </div>
          <Progress value={project.progress} className="h-2" />
        </div>

        <div className="flex justify-between items-center text-xs text-gray-600">
          {project.budget && (
            <span>الميزانية: {formatCurrency(project.budget)}</span>
          )}
          {project.deadline && (
            <span className={isOverdue ? 'text-red-600' : ''}>
              <Clock className="h-3 w-3 inline ml-1" />
              {formatRelativeTime(project.deadline)}
            </span>
          )}
        </div>
      </div>
    </ClickableCard>
  );
}

interface TaskCardProps {
  task: {
    id: string;
    title: string;
    description: string;
    priority: string;
    status: string;
    due_date?: string;
    assigned_to?: string[];
    project_name?: string;
  };
  onClick?: (task: any) => void;
  className?: string;
}

export function TaskCard({ task, onClick, className = '' }: TaskCardProps) {
  const handleClick = () => {
    if (onClick) {
      onClick(task);
    }
  };

  const isOverdue = task.due_date && new Date(task.due_date) < new Date() && task.status !== 'completed';
  const isUrgent = task.priority === 'urgent';

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <ClickableCard
      title={task.title}
      href={`/founder-dashboard/tasks/${task.id}`}
      onClick={handleClick}
      className={cn(
        className,
        isUrgent && 'border-red-200 bg-red-50',
        isOverdue && 'border-orange-200 bg-orange-50'
      )}
      badge={isOverdue ? 'متأخر' : isUrgent ? 'عاجل' : undefined}
      badgeVariant={isOverdue || isUrgent ? 'destructive' : 'secondary'}
    >
      <div className="space-y-3">
        <p className="text-sm text-gray-600">{task.description}</p>
        
        <div className="flex items-center gap-2 flex-wrap">
          <Badge className={getPriorityColor(task.priority)}>
            {task.priority === 'urgent' && 'عاجل'}
            {task.priority === 'high' && 'عالي'}
            {task.priority === 'medium' && 'متوسط'}
            {task.priority === 'low' && 'منخفض'}
          </Badge>
          
          <Badge variant="outline">
            {task.status === 'pending' && 'معلق'}
            {task.status === 'in_progress' && 'قيد التنفيذ'}
            {task.status === 'completed' && 'مكتمل'}
            {task.status === 'cancelled' && 'ملغي'}
          </Badge>
        </div>

        {task.project_name && (
          <p className="text-xs text-gray-500">
            المشروع: {task.project_name}
          </p>
        )}

        <div className="flex justify-between items-center text-xs text-gray-600">
          {task.assigned_to && task.assigned_to.length > 0 && (
            <span>
              <Users className="h-3 w-3 inline ml-1" />
              {task.assigned_to.length} مُعيَّن
            </span>
          )}
          {task.due_date && (
            <span className={isOverdue ? 'text-red-600' : ''}>
              <Clock className="h-3 w-3 inline ml-1" />
              {formatRelativeTime(task.due_date)}
            </span>
          )}
        </div>
      </div>
    </ClickableCard>
  );
}

interface TeamMemberCardProps {
  member: {
    id: string;
    name: string;
    role: string;
    department: string;
    avatar?: string;
    performance_score?: number;
    active_tasks?: number;
    completed_projects?: number;
  };
  onClick?: (member: any) => void;
  className?: string;
}

export function TeamMemberCard({ member, onClick, className = '' }: TeamMemberCardProps) {
  const handleClick = () => {
    if (onClick) {
      onClick(member);
    }
  };

  const getRoleDisplayName = (role: string) => {
    const roleMap: Record<string, string> = {
      'sales_manager': 'مدير المبيعات',
      'developer': 'مطور',
      'designer': 'مصمم',
      'media_buyer': 'مشتري إعلانات',
      'wordpress_developer': 'مطور ووردبريس'
    };
    return roleMap[role] || role;
  };

  return (
    <ClickableCard
      title={member.name}
      href={`/founder-dashboard/team/${member.department}/${member.id}`}
      onClick={handleClick}
      className={className}
    >
      <div className="space-y-3">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold">
            {member.avatar ? (
              <img src={member.avatar} alt={member.name} className="w-full h-full rounded-full object-cover" />
            ) : (
              member.name.charAt(0)
            )}
          </div>
          <div>
            <h4 className="font-medium">{member.name}</h4>
            <p className="text-sm text-gray-600">{getRoleDisplayName(member.role)}</p>
          </div>
        </div>

        {member.performance_score !== undefined && (
          <div className="space-y-1">
            <div className="flex justify-between text-xs">
              <span>الأداء</span>
              <span>{member.performance_score}%</span>
            </div>
            <Progress value={member.performance_score} className="h-2" />
          </div>
        )}

        <div className="flex justify-between text-xs text-gray-600">
          {member.active_tasks !== undefined && (
            <span>المهام النشطة: {member.active_tasks}</span>
          )}
          {member.completed_projects !== undefined && (
            <span>المشاريع المكتملة: {member.completed_projects}</span>
          )}
        </div>
      </div>
    </ClickableCard>
  );
}
