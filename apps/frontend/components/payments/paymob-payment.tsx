'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  CreditCard,
  Smartphone,
  Building,
  Loader2,
  CheckCircle,
  XCircle,
  AlertCircle,
  ExternalLink,
  Wallet
} from 'lucide-react';
import { paymobAPI, PaymobPaymentIntent, PaymobPaymentStatus } from '@/lib/api/paymob';
import { showToast } from '@/lib/toast';
import { formatCurrency } from '@/lib/utils';

interface PaymobPaymentProps {
  invoiceId: string | number;
  amount: number;
  currency?: string;
  invoiceNumber?: string;
  clientName?: string;
  onPaymentSuccess?: (data: any) => void;
  onPaymentError?: (error: any) => void;
  onPaymentCancel?: () => void;
  disabled?: boolean;
  className?: string;
}

export function PaymobPayment({
  invoiceId,
  amount,
  currency = 'EGP',
  invoiceNumber,
  clientName,
  onPaymentSuccess,
  onPaymentError,
  onPaymentCancel,
  disabled = false,
  className = ''
}: PaymobPaymentProps) {
  const [loading, setLoading] = useState(false);
  const [paymentIntent, setPaymentIntent] = useState<PaymobPaymentIntent | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<PaymobPaymentStatus | null>(null);
  const [showDialog, setShowDialog] = useState(false);

  const handleCreatePaymentIntent = async () => {
    try {
      setLoading(true);
      const intent = await paymobAPI.createPaymentIntent(invoiceId);
      setPaymentIntent(intent);
      showToast.success('تم إنشاء رابط الدفع بنجاح');
    } catch (error: any) {
      console.error('Error creating payment intent:', error);
      showToast.error(error.message || 'فشل في إنشاء رابط الدفع');
      onPaymentError?.(error);
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentIframe = () => {
    if (!paymentIntent?.payment_url) return;

    paymobAPI.openPaymentIframe(paymentIntent.payment_url, {
      onSuccess: (data) => {
        showToast.success('تم الدفع بنجاح!');
        setPaymentStatus({ status: 'success', ...data });
        onPaymentSuccess?.(data);
        setShowDialog(false);
      },
      onError: (error) => {
        showToast.error('فشل في عملية الدفع');
        setPaymentStatus({ status: 'failed', ...error });
        onPaymentError?.(error);
      },
      onCancel: () => {
        showToast.info('تم إلغاء عملية الدفع');
        setPaymentStatus({ status: 'cancelled' });
        onPaymentCancel?.();
      }
    });
  };

  const handlePaymentWindow = () => {
    if (!paymentIntent?.payment_url) return;

    paymobAPI.openPaymentWindow(paymentIntent.payment_url, {
      onSuccess: (data) => {
        showToast.success('تم الدفع بنجاح!');
        setPaymentStatus({ status: 'success', ...data });
        onPaymentSuccess?.(data);
        setShowDialog(false);
      },
      onError: (error) => {
        showToast.error('فشل في عملية الدفع');
        setPaymentStatus({ status: 'failed', ...error });
        onPaymentError?.(error);
      },
      onCancel: () => {
        showToast.info('تم إلغاء عملية الدفع');
        setPaymentStatus({ status: 'cancelled' });
        onPaymentCancel?.();
      }
    });
  };

  const handlePaymentRedirect = () => {
    if (!paymentIntent?.payment_url) return;
    paymobAPI.redirectToPayment(paymentIntent.payment_url);
  };

  const getPaymentStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-600" />;
      case 'cancelled':
        return <AlertCircle className="h-5 w-5 text-yellow-600" />;
      default:
        return <AlertCircle className="h-5 w-5 text-blue-600" />;
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  return (
    <Dialog open={showDialog} onOpenChange={setShowDialog}>
      <DialogTrigger asChild>
        <Button
          onClick={() => setShowDialog(true)}
          disabled={disabled || loading}
          className={`bg-green-600 hover:bg-green-700 ${className}`}
        >
          {loading ? (
            <Loader2 className="h-4 w-4 ml-2 animate-spin" />
          ) : (
            <CreditCard className="h-4 w-4 ml-2" />
          )}
          دفع بـ Paymob
        </Button>
      </DialogTrigger>

      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Wallet className="h-5 w-5 text-green-600" />
            الدفع الإلكتروني
          </DialogTitle>
          <DialogDescription>
            ادفع فاتورتك بأمان عبر بوابة Paymob
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Invoice Details */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">تفاصيل الفاتورة</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {invoiceNumber && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">رقم الفاتورة:</span>
                  <span className="font-medium">{invoiceNumber}</span>
                </div>
              )}
              {clientName && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">العميل:</span>
                  <span className="font-medium">{clientName}</span>
                </div>
              )}
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">المبلغ:</span>
                <span className="font-bold text-green-600">
                  {formatCurrency(amount, currency)}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Payment Status */}
          {paymentStatus && (
            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center gap-2">
                  {getPaymentStatusIcon(paymentStatus.status)}
                  <Badge className={getPaymentStatusColor(paymentStatus.status)}>
                    {paymobAPI.getPaymentStatusName(paymentStatus.status)}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Payment Actions */}
          {!paymentIntent ? (
            <Button
              onClick={handleCreatePaymentIntent}
              disabled={loading}
              className="w-full bg-green-600 hover:bg-green-700"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 ml-2 animate-spin" />
              ) : (
                <CreditCard className="h-4 w-4 ml-2" />
              )}
              إنشاء رابط الدفع
            </Button>
          ) : (
            <div className="space-y-3">
              <div className="text-sm text-gray-600 text-center">
                اختر طريقة الدفع المفضلة لديك
              </div>

              {/* Payment Methods */}
              <div className="grid grid-cols-1 gap-2">
                <Button
                  onClick={handlePaymentIframe}
                  variant="outline"
                  className="justify-start"
                >
                  <CreditCard className="h-4 w-4 ml-2" />
                  الدفع في نافذة منبثقة
                </Button>

                <Button
                  onClick={handlePaymentWindow}
                  variant="outline"
                  className="justify-start"
                >
                  <ExternalLink className="h-4 w-4 ml-2" />
                  الدفع في نافذة جديدة
                </Button>

                <Button
                  onClick={handlePaymentRedirect}
                  variant="outline"
                  className="justify-start"
                >
                  <Building className="h-4 w-4 ml-2" />
                  الانتقال لصفحة الدفع
                </Button>
              </div>

              {/* Supported Payment Methods */}
              <div className="text-xs text-gray-500 text-center">
                <div className="mb-2">طرق الدفع المدعومة:</div>
                <div className="flex flex-wrap justify-center gap-2">
                  <Badge variant="secondary" className="text-xs">
                    <CreditCard className="h-3 w-3 ml-1" />
                    بطاقات ائتمان
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    <Smartphone className="h-3 w-3 ml-1" />
                    فودافون كاش
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    <Wallet className="h-3 w-3 ml-1" />
                    فوري
                  </Badge>
                </div>
              </div>
            </div>
          )}

          {/* Security Notice */}
          <div className="text-xs text-gray-500 text-center bg-gray-50 p-3 rounded">
            🔒 جميع المعاملات محمية بتشفير SSL وتتم معالجتها بأمان عبر Paymob
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default PaymobPayment;
