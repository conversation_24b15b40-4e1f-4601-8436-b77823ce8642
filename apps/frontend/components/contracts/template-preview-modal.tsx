'use client';

import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ContractTemplate } from '@/types/contracts';
import { 
  Download, 
  Copy, 
  Edit, 
  X,
  Globe,
  Smartphone,
  Monitor,
  ShoppingCart,
  Wrench,
  TrendingUp,
  Palette,
  Search as SearchIcon,
  Share2,
  FileText,
} from 'lucide-react';

interface TemplatePreviewModalProps {
  template: ContractTemplate | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: () => void;
  onClone?: () => void;
  onDownload?: () => void;
}

export function TemplatePreviewModal({
  template,
  isOpen,
  onClose,
  onEdit,
  onClone,
  onDownload,
}: TemplatePreviewModalProps) {
  if (!template) return null;

  // Template type icons
  const getTemplateIcon = (type: string) => {
    const iconMap = {
      website: Globe,
      mobile_app: Smartphone,
      web_app: Monitor,
      ecommerce: ShoppingCart,
      maintenance: Wrench,
      marketing: TrendingUp,
      branding: Palette,
      seo: SearchIcon,
      social_media: Share2,
      custom: FileText,
    };
    return iconMap[type as keyof typeof iconMap] || FileText;
  };

  // Template type colors
  const getTemplateColor = (type: string) => {
    const colorMap = {
      website: 'bg-blue-100 text-blue-800',
      mobile_app: 'bg-green-100 text-green-800',
      web_app: 'bg-purple-100 text-purple-800',
      ecommerce: 'bg-orange-100 text-orange-800',
      maintenance: 'bg-gray-100 text-gray-800',
      marketing: 'bg-pink-100 text-pink-800',
      branding: 'bg-indigo-100 text-indigo-800',
      seo: 'bg-yellow-100 text-yellow-800',
      social_media: 'bg-cyan-100 text-cyan-800',
      custom: 'bg-slate-100 text-slate-800',
    };
    return colorMap[type as keyof typeof colorMap] || 'bg-gray-100 text-gray-800';
  };

  // Sample data for preview
  const sampleData = {
    client_name: 'شركة التقنية المتقدمة',
    client_email: '<EMAIL>',
    client_phone: '+20 10 1234 5678',
    client_address: 'القاهرة الجديدة، مصر',
    project_name: 'موقع إلكتروني تجاري',
    project_description: 'تطوير موقع إلكتروني متكامل لعرض المنتجات والخدمات',
    contract_value: '25,000',
    start_date: '2024-01-15',
    end_date: '2024-03-15',
    company_name: 'MTBRMG للحلول الرقمية',
    company_address: 'القاهرة، مصر',
    company_phone: '+20 11 9876 5432',
    company_email: '<EMAIL>',
    contract_number: 'CON-2024-001',
    current_date: new Date().toLocaleDateString('ar-EG'),
  };

  // Replace variables in template content
  const getPreviewContent = (content: string) => {
    let previewContent = content;
    Object.entries(sampleData).forEach(([key, value]) => {
      const regex = new RegExp(`\\{${key}\\}`, 'g');
      previewContent = previewContent.replace(regex, value);
    });
    return previewContent;
  };

  const IconComponent = getTemplateIcon(template.template_type);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <IconComponent className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <DialogTitle className="text-xl">{template.name}</DialogTitle>
                <div className="flex items-center gap-2 mt-1">
                  <Badge className={getTemplateColor(template.template_type)}>
                    {template.template_type_display}
                  </Badge>
                  <Badge variant={template.is_active ? 'default' : 'secondary'}>
                    {template.is_active ? 'نشط' : 'غير نشط'}
                  </Badge>
                </div>
              </div>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          <DialogDescription>
            معاينة القالب مع بيانات تجريبية
          </DialogDescription>
        </DialogHeader>

        {/* Template Description */}
        {template.description && (
          <div className="flex-shrink-0 p-4 bg-gray-50 rounded-lg mb-4">
            <p className="text-sm text-gray-700">{template.description}</p>
          </div>
        )}

        {/* Preview Content */}
        <div className="flex-1 overflow-auto">
          <div className="bg-white border rounded-lg p-6 shadow-sm">
            <div className="prose prose-sm max-w-none">
              <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono leading-relaxed">
                {getPreviewContent(template.content)}
              </pre>
            </div>
          </div>
        </div>

        {/* Variables Used */}
        <div className="flex-shrink-0 mt-4 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">المتغيرات المستخدمة في هذا القالب:</h4>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm text-blue-700">
            {template.content.match(/\{[^}]+\}/g)?.map((variable, index) => (
              <code key={index} className="bg-blue-100 px-2 py-1 rounded">
                {variable}
              </code>
            )) || <span className="text-blue-600">لا توجد متغيرات في هذا القالب</span>}
          </div>
        </div>

        {/* Actions */}
        <div className="flex-shrink-0 flex items-center justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={onDownload}>
            <Download className="h-4 w-4 ml-2" />
            تحميل
          </Button>
          <Button variant="outline" onClick={onClone}>
            <Copy className="h-4 w-4 ml-2" />
            نسخ
          </Button>
          <Button onClick={onEdit} className="bg-purple-600 hover:bg-purple-700">
            <Edit className="h-4 w-4 ml-2" />
            تعديل
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
