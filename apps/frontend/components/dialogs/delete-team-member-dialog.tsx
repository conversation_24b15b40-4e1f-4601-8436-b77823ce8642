'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import {
  AlertTriangle,
  Trash2,
  X,
  Loader2,
  User,
  Building,
  Mail,
  Phone,
  Calendar,
  Award
} from 'lucide-react';

interface DeleteTeamMemberDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (memberId: string) => Promise<void>;
  member: any;
}

export function DeleteTeamMemberDialog({ isOpen, onClose, onConfirm, member }: DeleteTeamMemberDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleConfirm = async () => {
    if (!member) return;

    setIsDeleting(true);
    try {
      await onConfirm(member.id);
      handleClose();
    } catch (error) {
      console.error('Error deleting team member:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    setIsDeleting(false);
    onClose();
  };

  if (!member) return null;

  const getDepartmentBadge = (department: string) => {
    switch (department) {
      case 'sales':
        return <Badge className="bg-blue-100 text-blue-800">المبيعات</Badge>;
      case 'media_buying':
        return <Badge className="bg-orange-100 text-orange-800">شراء الإعلانات</Badge>;
      case 'development':
        return <Badge className="bg-green-100 text-green-800">التطوير</Badge>;
      case 'design':
        return <Badge className="bg-pink-100 text-pink-800">التصميم</Badge>;
      case 'wordpress':
        return <Badge className="bg-purple-100 text-purple-800">ووردبريس</Badge>;
      case 'management':
        return <Badge className="bg-gray-100 text-gray-800">الإدارة</Badge>;
      default:
        return <Badge variant="outline">{department}</Badge>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">نشط</Badge>;
      case 'inactive':
        return <Badge className="bg-red-100 text-red-800">غير نشط</Badge>;
      case 'on_leave':
        return <Badge className="bg-yellow-100 text-yellow-800">في إجازة</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            تأكيد حذف عضو الفريق
          </DialogTitle>
          <DialogDescription>
            هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع بيانات عضو الفريق نهائياً.
          </DialogDescription>
        </DialogHeader>

        {/* Member Details */}
        <div className="space-y-4 py-4">
          <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-purple-600 font-bold text-lg">
                {member.full_name ? member.full_name.split(' ').map(n => n[0]).join('') : 'N/A'}
              </span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-lg">{member.full_name || 'غير محدد'}</h3>
              <div className="flex items-center gap-2 mt-1">
                {getDepartmentBadge(member.department)}
                {getStatusBadge(member.status)}
              </div>
            </div>
          </div>

          <div className="space-y-3 text-sm">
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-gray-500" />
              <span className="text-gray-600">البريد الإلكتروني:</span>
              <span className="font-medium">{member.user?.email || 'غير محدد'}</span>
            </div>

            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-gray-500" />
              <span className="text-gray-600">رقم الموظف:</span>
              <span className="font-medium">{member.employee_id || 'غير محدد'}</span>
            </div>

            <div className="flex items-center gap-2">
              <Building className="h-4 w-4 text-gray-500" />
              <span className="text-gray-600">المنصب:</span>
              <span className="font-medium">{member.position || 'غير محدد'}</span>
            </div>

            {member.hire_date && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-gray-600">تاريخ التوظيف:</span>
                <span className="font-medium">
                  {new Date(member.hire_date).toLocaleDateString('ar-EG')}
                </span>
              </div>
            )}

            <div className="flex items-center gap-2">
              <Award className="h-4 w-4 text-gray-500" />
              <span className="text-gray-600">المشاريع المكتملة:</span>
              <span className="font-medium">{member.completed_projects || 0}</span>
            </div>

            <div className="flex items-center gap-2">
              <Award className="h-4 w-4 text-gray-500" />
              <span className="text-gray-600">المهام المكتملة:</span>
              <span className="font-medium">{member.total_tasks_completed || 0}</span>
            </div>

            {member.emergency_contact_name && (
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-gray-500" />
                <span className="text-gray-600">جهة الاتصال الطارئ:</span>
                <span className="font-medium">{member.emergency_contact_name}</span>
              </div>
            )}
          </div>

          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2 text-red-800">
              <AlertTriangle className="h-4 w-4" />
              <span className="font-medium">تحذير</span>
            </div>
            <p className="text-red-700 text-sm mt-1">
              سيتم حذف جميع البيانات المرتبطة بهذا العضو بما في ذلك:
            </p>
            <ul className="text-red-700 text-sm mt-2 list-disc list-inside">
              <li>معلومات الملف الشخصي</li>
              <li>سجل الأداء والمقاييس</li>
              <li>تاريخ المشاريع والمهام</li>
              <li>بيانات الاتصال الطارئ</li>
            </ul>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button 
            type="button" 
            variant="outline" 
            onClick={handleClose} 
            disabled={isDeleting}
          >
            <X className="h-4 w-4 ml-2" />
            إلغاء
          </Button>
          <Button 
            type="button" 
            variant="destructive" 
            onClick={handleConfirm} 
            disabled={isDeleting}
          >
            {isDeleting ? (
              <Loader2 className="h-4 w-4 ml-2 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4 ml-2" />
            )}
            {isDeleting ? 'جاري الحذف...' : 'تأكيد الحذف'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
