'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  User,
  Building,
  Mail,
  Phone,
  Calendar,
  DollarSign,
  Award,
  Edit,
  Trash2,
  X,
  ExternalLink,
  TrendingUp,
  Clock,
  Target
} from 'lucide-react';

interface TeamMemberDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onEdit: () => void;
  onDelete: () => void;
  member: any;
}

export function TeamMemberDetailsDialog({ 
  isOpen, 
  onClose, 
  onEdit, 
  onDelete, 
  member 
}: TeamMemberDetailsDialogProps) {
  if (!member) return null;

  const getDepartmentBadge = (department: string) => {
    switch (department) {
      case 'sales':
        return <Badge className="bg-blue-100 text-blue-800">المبيعات</Badge>;
      case 'media_buying':
        return <Badge className="bg-orange-100 text-orange-800">شراء الإعلانات</Badge>;
      case 'development':
        return <Badge className="bg-green-100 text-green-800">التطوير</Badge>;
      case 'design':
        return <Badge className="bg-pink-100 text-pink-800">التصميم</Badge>;
      case 'wordpress':
        return <Badge className="bg-purple-100 text-purple-800">ووردبريس</Badge>;
      case 'management':
        return <Badge className="bg-gray-100 text-gray-800">الإدارة</Badge>;
      default:
        return <Badge variant="outline">{department}</Badge>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">نشط</Badge>;
      case 'inactive':
        return <Badge className="bg-red-100 text-red-800">غير نشط</Badge>;
      case 'on_leave':
        return <Badge className="bg-yellow-100 text-yellow-800">في إجازة</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatCurrency = (amount: number) => {
    if (!amount) return 'غير محدد';
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'غير محدد';
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const calculateTenure = (hireDate: string) => {
    if (!hireDate) return 'غير محدد';
    const hire = new Date(hireDate);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - hire.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const years = Math.floor(diffDays / 365);
    const months = Math.floor((diffDays % 365) / 30);
    
    if (years > 0) {
      return `${years} سنة${months > 0 ? ` و ${months} شهر` : ''}`;
    } else if (months > 0) {
      return `${months} شهر`;
    } else {
      return `${diffDays} يوم`;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5 text-purple-600" />
            ملف عضو الفريق
          </DialogTitle>
          <DialogDescription>
            عرض تفاصيل {member.full_name || 'عضو الفريق'}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Header Card */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                  <span className="text-purple-600 font-bold text-xl">
                    {member.full_name ? member.full_name.split(' ').map(n => n[0]).join('') : 'N/A'}
                  </span>
                </div>
                <div className="flex-1">
                  <CardTitle className="text-2xl">{member.full_name || 'غير محدد'}</CardTitle>
                  <CardDescription className="text-lg">{member.position || 'غير محدد'}</CardDescription>
                  <div className="flex items-center gap-2 mt-2">
                    {getDepartmentBadge(member.department)}
                    {getStatusBadge(member.status)}
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  المعلومات الأساسية
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">البريد الإلكتروني:</span>
                  <span className="font-medium">{member.user?.email || 'غير محدد'}</span>
                </div>

                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">رقم الموظف:</span>
                  <span className="font-medium">{member.employee_id || 'غير محدد'}</span>
                </div>

                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">تاريخ التوظيف:</span>
                  <span className="font-medium">{formatDate(member.hire_date)}</span>
                </div>

                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">مدة الخدمة:</span>
                  <span className="font-medium">{calculateTenure(member.hire_date)}</span>
                </div>

                {member.salary && (
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-gray-500" />
                    <span className="text-gray-600">الراتب:</span>
                    <span className="font-medium">{formatCurrency(member.salary)}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Performance Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  مقاييس الأداء
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">المشاريع المكتملة:</span>
                  <span className="font-medium">{member.completed_projects || 0}</span>
                </div>

                <div className="flex items-center gap-2">
                  <Award className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">المهام المكتملة:</span>
                  <span className="font-medium">{member.total_tasks_completed || 0}</span>
                </div>

                {member.performance_score && (
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-gray-500" />
                    <span className="text-gray-600">درجة الأداء:</span>
                    <span className="font-medium">{member.performance_score}/10</span>
                  </div>
                )}

                {member.productivity_score !== undefined && (
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-gray-500" />
                    <span className="text-gray-600">نقاط الإنتاجية:</span>
                    <span className="font-medium">{member.productivity_score}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Skills and Certifications */}
            {(member.skills?.length > 0 || member.certifications?.length > 0) && (
              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Award className="h-5 w-5" />
                    المهارات والشهادات
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {member.skills?.length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">المهارات:</h4>
                      <div className="flex flex-wrap gap-2">
                        {member.skills.map((skill, index) => (
                          <Badge key={index} variant="secondary">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {member.certifications?.length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">الشهادات:</h4>
                      <div className="flex flex-wrap gap-2">
                        {member.certifications.map((cert, index) => (
                          <Badge key={index} variant="outline">
                            {cert}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Emergency Contact */}
            {(member.emergency_contact_name || member.emergency_contact_phone) && (
              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Phone className="h-5 w-5" />
                    جهة الاتصال الطارئ
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {member.emergency_contact_name && (
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-500" />
                      <span className="text-gray-600">الاسم:</span>
                      <span className="font-medium">{member.emergency_contact_name}</span>
                    </div>
                  )}

                  {member.emergency_contact_phone && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-gray-500" />
                      <span className="text-gray-600">الهاتف:</span>
                      <span className="font-medium">{member.emergency_contact_phone}</span>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button type="button" variant="outline" onClick={onClose}>
            <X className="h-4 w-4 ml-2" />
            إغلاق
          </Button>
          <Button type="button" variant="outline" onClick={onEdit}>
            <Edit className="h-4 w-4 ml-2" />
            تعديل
          </Button>
          <Button type="button" variant="destructive" onClick={onDelete}>
            <Trash2 className="h-4 w-4 ml-2" />
            حذف
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
