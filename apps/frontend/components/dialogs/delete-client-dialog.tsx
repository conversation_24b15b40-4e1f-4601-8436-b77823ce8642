'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import {
  AlertTriangle,
  Trash2,
  X,
  Loader2,
  Users,
  Building,
  Mail,
  Phone
} from 'lucide-react';

interface DeleteClientDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (clientId: string) => Promise<void>;
  client: any; // The client to delete
}

export function DeleteClientDialog({ isOpen, onClose, onConfirm, client }: DeleteClientDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleConfirm = async () => {
    if (!client) return;

    setIsDeleting(true);
    try {
      await onConfirm(client.id);
      handleClose();
    } catch (error) {
      console.error('Error deleting client:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    setIsDeleting(false);
    onClose();
  };

  if (!client) return null;

  const getMoodColor = (mood: string) => {
    switch (mood) {
      case 'happy': return 'bg-green-100 text-green-800';
      case 'concerned': return 'bg-yellow-100 text-yellow-800';
      case 'angry': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getMoodLabel = (mood: string) => {
    switch (mood) {
      case 'happy': return 'سعيد';
      case 'concerned': return 'قلق';
      case 'angry': return 'غاضب';
      default: return 'محايد';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            تأكيد حذف العميل
          </DialogTitle>
          <DialogDescription>
            هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع بيانات العميل نهائياً.
          </DialogDescription>
        </DialogHeader>

        {/* Client Information Summary */}
        <div className="bg-gray-50 rounded-lg p-4 space-y-3">
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-gray-500" />
            <span className="font-semibold">{client.name}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <Building className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-600">{client.company}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <Mail className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-600">{client.email}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-600">{client.phone}</span>
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">الحالة:</span>
            <Badge className={getMoodColor(client.mood)}>
              {getMoodLabel(client.mood)}
            </Badge>
          </div>

          {client.total_projects > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded p-2 mt-3">
              <p className="text-sm text-yellow-800">
                ⚠️ هذا العميل لديه {client.total_projects} مشروع مرتبط به
              </p>
            </div>
          )}
        </div>

        {/* Warning Message */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <div className="flex items-start gap-2">
            <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-red-700">
              <p className="font-semibold mb-1">تحذير مهم:</p>
              <ul className="space-y-1 text-xs">
                <li>• سيتم حذف جميع بيانات العميل نهائياً</li>
                <li>• سيتم حذف جميع المشاريع المرتبطة بهذا العميل</li>
                <li>• لا يمكن استرداد البيانات بعد الحذف</li>
                <li>• سيتم إشعار فريق العمل بهذا التغيير</li>
              </ul>
            </div>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button 
            type="button" 
            variant="outline" 
            onClick={handleClose} 
            disabled={isDeleting}
          >
            <X className="h-4 w-4 ml-2" />
            إلغاء
          </Button>
          <Button 
            type="button" 
            variant="destructive" 
            onClick={handleConfirm} 
            disabled={isDeleting}
          >
            {isDeleting ? (
              <Loader2 className="h-4 w-4 ml-2 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4 ml-2" />
            )}
            {isDeleting ? 'جاري الحذف...' : 'تأكيد الحذف'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
