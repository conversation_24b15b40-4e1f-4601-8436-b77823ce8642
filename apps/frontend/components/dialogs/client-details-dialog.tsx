'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Users,
  Building,
  Mail,
  Phone,
  Globe,
  MapPin,
  Calendar,
  DollarSign,
  FolderOpen,
  FileText,
  Edit,
  Trash2,
  X,
  ExternalLink
} from 'lucide-react';
import { formatRelativeTime } from '@mtbrmg/shared';

interface ClientDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onEdit: () => void;
  onDelete: () => void;
  client: any; // The client to display
}

export function ClientDetailsDialog({ 
  isOpen, 
  onClose, 
  onEdit, 
  onDelete, 
  client 
}: ClientDetailsDialogProps) {
  if (!client) return null;

  const getMoodColor = (mood: string) => {
    switch (mood) {
      case 'happy': return 'bg-green-100 text-green-800';
      case 'concerned': return 'bg-yellow-100 text-yellow-800';
      case 'angry': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getMoodLabel = (mood: string) => {
    switch (mood) {
      case 'happy': return 'سعيد';
      case 'concerned': return 'قلق';
      case 'angry': return 'غاضب';
      default: return 'محايد';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5 text-purple-600" />
            تفاصيل العميل
          </DialogTitle>
          <DialogDescription>
            عرض شامل لجميع معلومات العميل والمشاريع المرتبطة
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center justify-between">
                <span>المعلومات الأساسية</span>
                <Badge className={getMoodColor(client.mood)}>
                  {getMoodLabel(client.mood)}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <Users className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">اسم العميل</p>
                    <p className="font-semibold">{client.name}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Building className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">الشركة</p>
                    <p className="font-semibold">{client.company}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">البريد الإلكتروني</p>
                    <p className="font-semibold text-blue-600">{client.email}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">رقم الهاتف</p>
                    <p className="font-semibold">{client.phone}</p>
                  </div>
                </div>

                {client.website && (
                  <div className="flex items-center gap-3">
                    <Globe className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">الموقع الإلكتروني</p>
                      <a 
                        href={client.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="font-semibold text-blue-600 hover:underline flex items-center gap-1"
                      >
                        {client.website}
                        <ExternalLink className="h-3 w-3" />
                      </a>
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-3">
                  <MapPin className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">الموقع</p>
                    <p className="font-semibold">{client.governorate}</p>
                    {client.address && (
                      <p className="text-sm text-gray-600 mt-1">{client.address}</p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <FolderOpen className="h-8 w-8 text-blue-600" />
                  <div>
                    <p className="text-2xl font-bold">{client.total_projects || 0}</p>
                    <p className="text-sm text-gray-600">إجمالي المشاريع</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <DollarSign className="h-8 w-8 text-green-600" />
                  <div>
                    <p className="text-2xl font-bold">{formatCurrency(client.total_revenue || 0)}</p>
                    <p className="text-sm text-gray-600">إجمالي الإيرادات</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Calendar className="h-8 w-8 text-purple-600" />
                  <div>
                    <p className="text-sm font-bold">
                      {client.last_contact_date 
                        ? formatRelativeTime(client.last_contact_date)
                        : 'لم يتم التواصل بعد'
                      }
                    </p>
                    <p className="text-sm text-gray-600">آخر تواصل</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Notes */}
          {client.notes && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  الملاحظات
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 whitespace-pre-wrap">{client.notes}</p>
              </CardContent>
            </Card>
          )}

          {/* Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">التاريخ</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-3 text-sm">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-gray-500">تم إنشاء الحساب:</span>
                <span className="font-semibold">
                  {client.created_at 
                    ? formatRelativeTime(client.created_at)
                    : 'غير محدد'
                  }
                </span>
              </div>
              
              {client.updated_at && client.updated_at !== client.created_at && (
                <div className="flex items-center gap-3 text-sm">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-500">آخر تحديث:</span>
                  <span className="font-semibold">
                    {formatRelativeTime(client.updated_at)}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="gap-2">
          <Button type="button" variant="outline" onClick={onClose}>
            <X className="h-4 w-4 ml-2" />
            إغلاق
          </Button>
          <Button type="button" variant="outline" onClick={onEdit}>
            <Edit className="h-4 w-4 ml-2" />
            تعديل
          </Button>
          <Button type="button" variant="destructive" onClick={onDelete}>
            <Trash2 className="h-4 w-4 ml-2" />
            حذف
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
