'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  FolderOpen,
  Users,
  Calendar,
  DollarSign,
  Target,
  Globe,
  GitBranch,
  ExternalLink,
  Edit,
  Trash2,
  X,
  Clock,
  TrendingUp
} from 'lucide-react';

interface ProjectDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onEdit: () => void;
  onDelete: () => void;
  project: any;
}

export function ProjectDetailsDialog({ 
  isOpen, 
  onClose, 
  onEdit, 
  onDelete, 
  project 
}: ProjectDetailsDialogProps) {
  if (!project) return null;

  const getTypeBadge = (type: string) => {
    switch (type) {
      case 'website':
        return <Badge className="bg-blue-100 text-blue-800">موقع ويب</Badge>;
      case 'mobile_app':
        return <Badge className="bg-green-100 text-green-800">تطبيق جوال</Badge>;
      case 'web_app':
        return <Badge className="bg-purple-100 text-purple-800">تطبيق ويب</Badge>;
      case 'ecommerce':
        return <Badge className="bg-orange-100 text-orange-800">متجر إلكتروني</Badge>;
      case 'wordpress':
        return <Badge className="bg-indigo-100 text-indigo-800">ووردبريس</Badge>;
      case 'maintenance':
        return <Badge className="bg-gray-100 text-gray-800">صيانة</Badge>;
      case 'marketing':
        return <Badge className="bg-pink-100 text-pink-800">تسويق</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'planning':
        return <Badge className="bg-yellow-100 text-yellow-800">التخطيط</Badge>;
      case 'development':
        return <Badge className="bg-blue-100 text-blue-800">التطوير</Badge>;
      case 'testing':
        return <Badge className="bg-orange-100 text-orange-800">الاختبار</Badge>;
      case 'deployment':
        return <Badge className="bg-purple-100 text-purple-800">النشر</Badge>;
      case 'maintenance':
        return <Badge className="bg-gray-100 text-gray-800">الصيانة</Badge>;
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">مكتمل</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'low':
        return <Badge className="bg-gray-100 text-gray-800">منخفضة</Badge>;
      case 'medium':
        return <Badge className="bg-yellow-100 text-yellow-800">متوسطة</Badge>;
      case 'high':
        return <Badge className="bg-orange-100 text-orange-800">عالية</Badge>;
      case 'urgent':
        return <Badge className="bg-red-100 text-red-800">عاجلة</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  const formatCurrency = (amount: number) => {
    if (!amount) return 'غير محدد';
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'غير محدد';
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const calculateDaysRemaining = (deadline: string) => {
    if (!deadline) return null;
    const deadlineDate = new Date(deadline);
    const today = new Date();
    const diffTime = deadlineDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return `متأخر بـ ${Math.abs(diffDays)} يوم`;
    } else if (diffDays === 0) {
      return 'ينتهي اليوم';
    } else {
      return `${diffDays} يوم متبقي`;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5 text-purple-600" />
            تفاصيل المشروع
          </DialogTitle>
          <DialogDescription>
            عرض تفاصيل مشروع {project.name}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Header Card */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center">
                  <FolderOpen className="h-8 w-8 text-purple-600" />
                </div>
                <div className="flex-1">
                  <CardTitle className="text-2xl">{project.name}</CardTitle>
                  <CardDescription className="text-lg mt-1">{project.description}</CardDescription>
                  <div className="flex items-center gap-2 mt-3">
                    {getTypeBadge(project.type)}
                    {getStatusBadge(project.status)}
                    {getPriorityBadge(project.priority)}
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Progress Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                تقدم المشروع
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">نسبة الإنجاز</span>
                  <span className="text-sm font-bold">{project.progress || 0}%</span>
                </div>
                <Progress value={project.progress || 0} className="h-3" />
                {project.deadline && (
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <span className="text-gray-600">الموعد النهائي:</span>
                    <span className="font-medium">{formatDate(project.deadline)}</span>
                    <span className={`font-medium ${
                      calculateDaysRemaining(project.deadline)?.includes('متأخر') 
                        ? 'text-red-600' 
                        : 'text-green-600'
                    }`}>
                      ({calculateDaysRemaining(project.deadline)})
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  المعلومات الأساسية
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">العميل:</span>
                  <span className="font-medium">{project.client_name || 'غير محدد'}</span>
                </div>

                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">تاريخ البداية:</span>
                  <span className="font-medium">{formatDate(project.start_date)}</span>
                </div>

                {project.end_date && (
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="text-gray-600">تاريخ النهاية:</span>
                    <span className="font-medium">{formatDate(project.end_date)}</span>
                  </div>
                )}

                {project.budget && (
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-gray-500" />
                    <span className="text-gray-600">الميزانية:</span>
                    <span className="font-medium">{formatCurrency(project.budget)}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Technical Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  المعلومات التقنية
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {project.domains && project.domains.length > 0 && (
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <Globe className="h-4 w-4 text-gray-500" />
                      <span className="text-gray-600">النطاقات:</span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {project.domains.map((domain, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {domain}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {project.repository_url && (
                  <div className="flex items-center gap-2">
                    <GitBranch className="h-4 w-4 text-gray-500" />
                    <span className="text-gray-600">المستودع:</span>
                    <a 
                      href={project.repository_url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-purple-600 hover:text-purple-800 flex items-center gap-1"
                    >
                      <span className="font-medium">عرض الكود</span>
                      <ExternalLink className="h-3 w-3" />
                    </a>
                  </div>
                )}

                {project.staging_url && (
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-gray-500" />
                    <span className="text-gray-600">رابط الاختبار:</span>
                    <a 
                      href={project.staging_url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-purple-600 hover:text-purple-800 flex items-center gap-1"
                    >
                      <span className="font-medium">عرض الموقع</span>
                      <ExternalLink className="h-3 w-3" />
                    </a>
                  </div>
                )}

                {project.production_url && (
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-gray-500" />
                    <span className="text-gray-600">رابط الإنتاج:</span>
                    <a 
                      href={project.production_url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-purple-600 hover:text-purple-800 flex items-center gap-1"
                    >
                      <span className="font-medium">عرض الموقع</span>
                      <ExternalLink className="h-3 w-3" />
                    </a>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Team Information */}
          {project.assigned_team && project.assigned_team.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  فريق العمل
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {project.assigned_team.map((member, index) => (
                    <Badge key={index} variant="outline" className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {member.full_name || member.username}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                إحصائيات المشروع
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{project.total_tasks || 0}</div>
                  <div className="text-sm text-gray-600">إجمالي المهام</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{project.completed_tasks || 0}</div>
                  <div className="text-sm text-gray-600">المهام المكتملة</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{project.pending_tasks || 0}</div>
                  <div className="text-sm text-gray-600">المهام المعلقة</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{project.team_members_count || 0}</div>
                  <div className="text-sm text-gray-600">أعضاء الفريق</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="gap-2">
          <Button type="button" variant="outline" onClick={onClose}>
            <X className="h-4 w-4 ml-2" />
            إغلاق
          </Button>
          <Button type="button" variant="outline" onClick={onEdit}>
            <Edit className="h-4 w-4 ml-2" />
            تعديل
          </Button>
          <Button type="button" variant="destructive" onClick={onDelete}>
            <Trash2 className="h-4 w-4 ml-2" />
            حذف
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
