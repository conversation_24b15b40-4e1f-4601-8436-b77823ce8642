'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertTriangle,
  Trash2,
  X,
  Loader2,
  FolderOpen,
  Users,
  Calendar,
  DollarSign,
  Target,
  Globe
} from 'lucide-react';

interface DeleteProjectDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (projectId: string) => Promise<void>;
  project: any;
}

export function DeleteProjectDialog({ isOpen, onClose, onConfirm, project }: DeleteProjectDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleConfirm = async () => {
    if (!project) return;

    setIsDeleting(true);
    try {
      await onConfirm(project.id);
      handleClose();
    } catch (error) {
      console.error('Error deleting project:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    setIsDeleting(false);
    onClose();
  };

  if (!project) return null;

  const getTypeBadge = (type: string) => {
    switch (type) {
      case 'website':
        return <Badge className="bg-blue-100 text-blue-800">موقع ويب</Badge>;
      case 'mobile_app':
        return <Badge className="bg-green-100 text-green-800">تطبيق جوال</Badge>;
      case 'web_app':
        return <Badge className="bg-purple-100 text-purple-800">تطبيق ويب</Badge>;
      case 'ecommerce':
        return <Badge className="bg-orange-100 text-orange-800">متجر إلكتروني</Badge>;
      case 'wordpress':
        return <Badge className="bg-indigo-100 text-indigo-800">ووردبريس</Badge>;
      case 'maintenance':
        return <Badge className="bg-gray-100 text-gray-800">صيانة</Badge>;
      case 'marketing':
        return <Badge className="bg-pink-100 text-pink-800">تسويق</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'planning':
        return <Badge className="bg-yellow-100 text-yellow-800">التخطيط</Badge>;
      case 'development':
        return <Badge className="bg-blue-100 text-blue-800">التطوير</Badge>;
      case 'testing':
        return <Badge className="bg-orange-100 text-orange-800">الاختبار</Badge>;
      case 'deployment':
        return <Badge className="bg-purple-100 text-purple-800">النشر</Badge>;
      case 'maintenance':
        return <Badge className="bg-gray-100 text-gray-800">الصيانة</Badge>;
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">مكتمل</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'low':
        return <Badge className="bg-gray-100 text-gray-800">منخفضة</Badge>;
      case 'medium':
        return <Badge className="bg-yellow-100 text-yellow-800">متوسطة</Badge>;
      case 'high':
        return <Badge className="bg-orange-100 text-orange-800">عالية</Badge>;
      case 'urgent':
        return <Badge className="bg-red-100 text-red-800">عاجلة</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  const formatCurrency = (amount: number) => {
    if (!amount) return 'غير محدد';
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'غير محدد';
    return new Date(dateString).toLocaleDateString('ar-EG');
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            تأكيد حذف المشروع
          </DialogTitle>
          <DialogDescription>
            هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع بيانات المشروع نهائياً.
          </DialogDescription>
        </DialogHeader>

        {/* Project Details */}
        <div className="space-y-4 py-4">
          <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <FolderOpen className="h-6 w-6 text-purple-600" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-lg">{project.name}</h3>
              <div className="flex items-center gap-2 mt-1">
                {getTypeBadge(project.type)}
                {getStatusBadge(project.status)}
                {getPriorityBadge(project.priority)}
              </div>
            </div>
          </div>

          <div className="space-y-3 text-sm">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-gray-500" />
              <span className="text-gray-600">العميل:</span>
              <span className="font-medium">{project.client_name || 'غير محدد'}</span>
            </div>

            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="text-gray-600">تاريخ البداية:</span>
              <span className="font-medium">{formatDate(project.start_date)}</span>
            </div>

            {project.deadline && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-gray-600">الموعد النهائي:</span>
                <span className="font-medium">{formatDate(project.deadline)}</span>
              </div>
            )}

            {project.budget && (
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-gray-500" />
                <span className="text-gray-600">الميزانية:</span>
                <span className="font-medium">{formatCurrency(project.budget)}</span>
              </div>
            )}

            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-gray-500" />
              <span className="text-gray-600">نسبة التقدم:</span>
              <span className="font-medium">{project.progress || 0}%</span>
            </div>

            {project.domains && project.domains.length > 0 && (
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4 text-gray-500" />
                <span className="text-gray-600">النطاقات:</span>
                <span className="font-medium">{project.domains.join(', ')}</span>
              </div>
            )}
          </div>

          {project.description && (
            <div className="p-3 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-1">الوصف:</h4>
              <p className="text-gray-700 text-sm">{project.description}</p>
            </div>
          )}

          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2 text-red-800">
              <AlertTriangle className="h-4 w-4" />
              <span className="font-medium">تحذير</span>
            </div>
            <p className="text-red-700 text-sm mt-1">
              سيتم حذف جميع البيانات المرتبطة بهذا المشروع بما في ذلك:
            </p>
            <ul className="text-red-700 text-sm mt-2 list-disc list-inside">
              <li>معلومات المشروع الأساسية</li>
              <li>المهام المرتبطة بالمشروع</li>
              <li>سجل التقدم والتحديثات</li>
              <li>الملفات والمرفقات</li>
              <li>تقارير الأداء</li>
            </ul>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button 
            type="button" 
            variant="outline" 
            onClick={handleClose} 
            disabled={isDeleting}
          >
            <X className="h-4 w-4 ml-2" />
            إلغاء
          </Button>
          <Button 
            type="button" 
            variant="destructive" 
            onClick={handleConfirm} 
            disabled={isDeleting}
          >
            {isDeleting ? (
              <Loader2 className="h-4 w-4 ml-2 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4 ml-2" />
            )}
            {isDeleting ? 'جاري الحذف...' : 'تأكيد الحذف'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
