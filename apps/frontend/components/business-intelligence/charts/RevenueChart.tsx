"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { TrendingUp, TrendingDown, DollarSign } from 'lucide-react';
import { businessIntelligenceApi } from '@/lib/api/business-intelligence';

interface RevenueData {
  period: string;
  period_display: string;
  revenue: number;
  start_date: string;
  end_date: string;
}

interface RevenueChartProps {
  data: RevenueData[];
  title?: string;
  showTrend?: boolean;
}

export default function RevenueChart({ data, title = "اتجاه الإيرادات", showTrend = true }: RevenueChartProps) {
  // Calculate trend
  const currentRevenue = data[data.length - 1]?.revenue || 0;
  const previousRevenue = data[data.length - 2]?.revenue || 0;
  const trendPercentage = previousRevenue > 0 ? ((currentRevenue - previousRevenue) / previousRevenue) * 100 : 0;
  const isPositiveTrend = trendPercentage >= 0;

  // Find max revenue for scaling
  const maxRevenue = Math.max(...data.map(item => item.revenue));

  return (
    <Card className="w-full" dir="rtl">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              {title}
            </CardTitle>
            <CardDescription>
              تطور الإيرادات خلال الفترة الماضية
            </CardDescription>
          </div>
          {showTrend && (
            <div className={`flex items-center gap-1 text-sm ${isPositiveTrend ? 'text-green-600' : 'text-red-600'}`}>
              {isPositiveTrend ? (
                <TrendingUp className="h-4 w-4" />
              ) : (
                <TrendingDown className="h-4 w-4" />
              )}
              <span>{Math.abs(trendPercentage).toFixed(1)}%</span>
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Chart Container */}
        <div className="space-y-4">
          {/* Current Period Summary */}
          <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
            <div>
              <p className="text-sm text-gray-600">الإيرادات الحالية</p>
              <p className="text-2xl font-bold text-gray-900">
                {businessIntelligenceApi.formatCurrency(currentRevenue)}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600">الإيرادات السابقة</p>
              <p className="text-lg font-semibold text-gray-700">
                {businessIntelligenceApi.formatCurrency(previousRevenue)}
              </p>
            </div>
          </div>

          {/* Simple Bar Chart */}
          <div className="space-y-2">
            {data.slice(-6).map((item, index) => (
              <div key={item.period} className="flex items-center gap-3">
                <div className="w-20 text-sm text-gray-600 text-right">
                  {item.period_display.split(' ')[0]}
                </div>
                <div className="flex-1 relative">
                  <div className="h-8 bg-gray-200 rounded-md overflow-hidden">
                    <div 
                      className="h-full bg-blue-500 transition-all duration-500 ease-out"
                      style={{ 
                        width: `${maxRevenue > 0 ? (item.revenue / maxRevenue) * 100 : 0}%` 
                      }}
                    />
                  </div>
                  <div className="absolute inset-y-0 right-2 flex items-center">
                    <span className="text-xs font-medium text-white">
                      {businessIntelligenceApi.formatCurrency(item.revenue)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Total Revenue */}
          <div className="pt-4 border-t">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">إجمالي الإيرادات</span>
              <span className="text-lg font-bold text-gray-900">
                {businessIntelligenceApi.formatCurrency(
                  data.reduce((sum, item) => sum + item.revenue, 0)
                )}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
