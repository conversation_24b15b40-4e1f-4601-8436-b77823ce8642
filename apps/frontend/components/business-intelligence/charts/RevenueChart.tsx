"use client";

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { businessIntelligenceApi } from '@/lib/api/business-intelligence';
import {
    BarChart3,
    Calendar,
    DollarSign,
    Download,
    LineChart,
    PieChart,
    RefreshCw,
    Target,
    TrendingDown,
    TrendingUp
} from 'lucide-react';
import { useState } from 'react';
import {
    Area,
    AreaChart,
    Bar,
    CartesianGrid,
    Line,
    BarChart as RechartsBarChart,
    LineChart as RechartsLineChart,
    ResponsiveContainer,
    XAxis,
    YAxis
} from 'recharts';

interface RevenueData {
  period: string;
  period_display: string;
  revenue: number;
  start_date: string;
  end_date: string;
  growth_rate?: number;
  target?: number;
  forecast?: number;
}

interface RevenueChartProps {
  data: RevenueData[];
  title?: string;
  showTrend?: boolean;
  chartType?: 'line' | 'bar' | 'area';
  showTargets?: boolean;
  showGrowthRate?: boolean;
  showForecast?: boolean;
  height?: number;
  interactive?: boolean;
}

const chartConfig = {
  revenue: {
    label: "الإيرادات",
    color: "hsl(var(--chart-1))",
  },
  target: {
    label: "الهدف",
    color: "hsl(var(--chart-2))",
  },
  forecast: {
    label: "التوقعات",
    color: "hsl(var(--chart-3))",
  },
  growth: {
    label: "معدل النمو",
    color: "hsl(var(--chart-4))",
  },
};

export default function RevenueChart({
  data,
  title = "اتجاه الإيرادات",
  showTrend = true,
  chartType = 'area',
  showTargets = false,
  showGrowthRate = false,
  showForecast = false,
  height = 300,
  interactive = true
}: RevenueChartProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [currentChartType, setCurrentChartType] = useState(chartType);
  const [selectedPeriod, setSelectedPeriod] = useState<string | null>(null);

  // Handle empty data
  if (!data || data.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد بيانات إيرادات</h3>
          <p className="text-gray-600">لا توجد بيانات إيرادات متاحة للعرض</p>
        </CardContent>
      </Card>
    );
  }

  // Calculate analytics
  const currentRevenue = data[data.length - 1]?.revenue || 0;
  const previousRevenue = data[data.length - 2]?.revenue || 0;
  const trendPercentage = previousRevenue > 0 ? ((currentRevenue - previousRevenue) / previousRevenue) * 100 : 0;
  const isPositiveTrend = trendPercentage >= 0;

  // Calculate comprehensive statistics
  const totalRevenue = data.reduce((sum, item) => sum + item.revenue, 0);
  const averageRevenue = totalRevenue / data.length;
  const maxRevenue = Math.max(...data.map(item => item.revenue));
  const minRevenue = Math.min(...data.map(item => item.revenue));
  const volatility = Math.sqrt(data.reduce((sum, item) => sum + Math.pow(item.revenue - averageRevenue, 2), 0) / data.length);

  // Prepare chart data with enhanced features
  const chartData = data.map((item, index) => {
    const growthRate = index > 0 ? ((item.revenue - data[index - 1].revenue) / data[index - 1].revenue) * 100 : 0;
    return {
      period: item.period_display.split(' ')[0],
      fullPeriod: item.period_display,
      revenue: item.revenue,
      target: item.target || averageRevenue * 1.1, // Default target 10% above average
      forecast: item.forecast || (index === data.length - 1 ? item.revenue * 1.05 : undefined), // Simple forecast
      growth_rate: item.growth_rate || growthRate,
      date: item.start_date,
    };
  });

  // Event handlers
  const handleExport = async () => {
    setIsLoading(true);
    try {
      // TODO: Implement export functionality
      console.log('Exporting revenue chart data...');
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      // TODO: Implement refresh functionality
      console.log('Refreshing revenue data...');
    } catch (error) {
      console.error('Refresh failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleChartTypeChange = (type: 'line' | 'bar' | 'area') => {
    setCurrentChartType(type);
  };

  return (
    <Card className="w-full" dir="rtl">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              {title}
            </CardTitle>
            <CardDescription>
              تطور الإيرادات خلال الفترة الماضية
            </CardDescription>
          </div>

          <div className="flex items-center gap-2">
            {/* Chart Type Selector */}
            {interactive && (
              <div className="flex items-center gap-1 border rounded-lg p-1">
                <Button
                  variant={currentChartType === 'area' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleChartTypeChange('area')}
                  className="h-8 w-8 p-0"
                >
                  <BarChart3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={currentChartType === 'line' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleChartTypeChange('line')}
                  className="h-8 w-8 p-0"
                >
                  <LineChart className="h-4 w-4" />
                </Button>
                <Button
                  variant={currentChartType === 'bar' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleChartTypeChange('bar')}
                  className="h-8 w-8 p-0"
                >
                  <PieChart className="h-4 w-4" />
                </Button>
              </div>
            )}

            {/* Trend Indicator */}
            {showTrend && (
              <Badge variant={isPositiveTrend ? 'default' : 'destructive'} className="flex items-center gap-1">
                {isPositiveTrend ? (
                  <TrendingUp className="h-3 w-3" />
                ) : (
                  <TrendingDown className="h-3 w-3" />
                )}
                <span>{Math.abs(trendPercentage).toFixed(1)}%</span>
              </Badge>
            )}

            {/* Action Buttons */}
            {interactive && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={isLoading}
                  className="h-8 w-8 p-0"
                >
                  <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExport}
                  disabled={isLoading}
                  className="h-8 w-8 p-0"
                >
                  <Download className="h-4 w-4" />
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-6">
          {/* Statistics Summary */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-gray-600">الإيرادات الحالية</p>
              <p className="text-xl font-bold text-blue-600">
                {businessIntelligenceApi.formatCurrency(currentRevenue)}
              </p>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <p className="text-sm text-gray-600">المتوسط</p>
              <p className="text-xl font-bold text-green-600">
                {businessIntelligenceApi.formatCurrency(averageRevenue)}
              </p>
            </div>
            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <p className="text-sm text-gray-600">الأعلى</p>
              <p className="text-xl font-bold text-purple-600">
                {businessIntelligenceApi.formatCurrency(maxRevenue)}
              </p>
            </div>
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <p className="text-sm text-gray-600">الإجمالي</p>
              <p className="text-xl font-bold text-orange-600">
                {businessIntelligenceApi.formatCurrency(totalRevenue)}
              </p>
            </div>
          </div>

          {/* Advanced Chart */}
          <div className="h-80">
            <ChartContainer config={chartConfig}>
              <ResponsiveContainer width="100%" height="100%">
                {currentChartType === 'line' && (
                  <RechartsLineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Line
                      type="monotone"
                      dataKey="revenue"
                      stroke="var(--color-revenue)"
                      strokeWidth={3}
                      dot={{ fill: "var(--color-revenue)", strokeWidth: 2, r: 4 }}
                    />
                    {showTargets && (
                      <Line
                        type="monotone"
                        dataKey="target"
                        stroke="var(--color-target)"
                        strokeDasharray="5 5"
                        strokeWidth={2}
                      />
                    )}
                    {showForecast && (
                      <Line
                        type="monotone"
                        dataKey="forecast"
                        stroke="var(--color-forecast)"
                        strokeDasharray="3 3"
                        strokeWidth={2}
                      />
                    )}
                  </RechartsLineChart>
                )}

                {currentChartType === 'bar' && (
                  <RechartsBarChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Bar dataKey="revenue" fill="var(--color-revenue)" radius={[4, 4, 0, 0]} />
                    {showTargets && (
                      <Bar dataKey="target" fill="var(--color-target)" opacity={0.5} radius={[4, 4, 0, 0]} />
                    )}
                  </RechartsBarChart>
                )}

                {currentChartType === 'area' && (
                  <AreaChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Area
                      type="monotone"
                      dataKey="revenue"
                      stroke="var(--color-revenue)"
                      fill="var(--color-revenue)"
                      fillOpacity={0.3}
                      strokeWidth={3}
                    />
                    {showTargets && (
                      <Area
                        type="monotone"
                        dataKey="target"
                        stroke="var(--color-target)"
                        fill="var(--color-target)"
                        fillOpacity={0.1}
                        strokeDasharray="5 5"
                      />
                    )}
                  </AreaChart>
                )}
              </ResponsiveContainer>
            </ChartContainer>
          </div>

          {/* Growth Rate Indicators */}
          {showGrowthRate && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-3 border rounded-lg">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Target className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium">معدل النمو الحالي</span>
                </div>
                <p className={`text-lg font-bold ${isPositiveTrend ? 'text-green-600' : 'text-red-600'}`}>
                  {trendPercentage.toFixed(1)}%
                </p>
              </div>
              <div className="text-center p-3 border rounded-lg">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Calendar className="h-4 w-4 text-purple-600" />
                  <span className="text-sm font-medium">التقلبات</span>
                </div>
                <p className="text-lg font-bold text-purple-600">
                  {businessIntelligenceApi.formatCurrency(volatility)}
                </p>
              </div>
              <div className="text-center p-3 border rounded-lg">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <TrendingUp className="h-4 w-4 text-orange-600" />
                  <span className="text-sm font-medium">الأداء مقابل الهدف</span>
                </div>
                <p className="text-lg font-bold text-orange-600">
                  {((currentRevenue / (averageRevenue * 1.1)) * 100).toFixed(1)}%
                </p>
              </div>
            </div>
          )}

          {/* Selected Period Details */}
          {selectedPeriod && (
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">تفاصيل الفترة المحددة</h4>
              <p className="text-sm text-gray-600">
                {/* TODO: Add selected period details */}
                الفترة: {selectedPeriod}
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
