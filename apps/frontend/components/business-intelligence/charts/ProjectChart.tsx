"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Briefcase, Clock, CheckCircle, AlertCircle } from 'lucide-react';

interface ProjectStatusData {
  status: string;
  status_display: string;
  count: number;
  color: string;
}

interface ProjectPerformanceData {
  period: string;
  period_display: string;
  projects_started: number;
  projects_completed: number;
  avg_completion_days: number;
  completion_rate: number;
}

interface ProjectChartProps {
  statusDistribution: ProjectStatusData[];
  performanceTrend: ProjectPerformanceData[];
}

export default function ProjectChart({ statusDistribution, performanceTrend }: ProjectChartProps) {
  // Calculate totals
  const totalProjects = statusDistribution.reduce((sum, item) => sum + item.count, 0);
  
  // Get latest performance data
  const latestPerformance = performanceTrend[performanceTrend.length - 1];
  const previousPerformance = performanceTrend[performanceTrend.length - 2];
  
  const completionRateTrend = latestPerformance && previousPerformance 
    ? latestPerformance.completion_rate - previousPerformance.completion_rate 
    : 0;

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6" dir="rtl">
      {/* Project Status Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Briefcase className="h-5 w-5" />
            توزيع حالة المشاريع
          </CardTitle>
          <CardDescription>
            إجمالي المشاريع: {totalProjects}
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-4">
            {/* Pie Chart Alternative - Horizontal Bars */}
            {statusDistribution.map((item) => {
              const percentage = totalProjects > 0 ? (item.count / totalProjects) * 100 : 0;
              
              return (
                <div key={item.status} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-900">
                      {item.status_display}
                    </span>
                    <span className="text-sm text-gray-600">
                      {item.count} ({percentage.toFixed(1)}%)
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div 
                      className="h-3 rounded-full transition-all duration-500 ease-out"
                      style={{ 
                        width: `${percentage}%`,
                        backgroundColor: item.color 
                      }}
                    />
                  </div>
                </div>
              );
            })}
          </div>
          
          {/* Status Icons */}
          <div className="mt-6 grid grid-cols-2 gap-4">
            <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-green-900">مكتملة</p>
                <p className="text-xs text-green-700">
                  {statusDistribution.find(s => s.status === 'completed')?.count || 0}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
              <Clock className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-blue-900">قيد التنفيذ</p>
                <p className="text-xs text-blue-700">
                  {statusDistribution.find(s => s.status === 'in_progress')?.count || 0}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Project Performance Trend */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                أداء المشاريع
              </CardTitle>
              <CardDescription>
                معدل إنجاز المشاريع شهرياً
              </CardDescription>
            </div>
            {latestPerformance && (
              <div className={`text-sm ${completionRateTrend >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {completionRateTrend >= 0 ? '+' : ''}{completionRateTrend.toFixed(1)}%
              </div>
            )}
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-4">
            {/* Performance Metrics */}
            {latestPerformance && (
              <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                <div>
                  <p className="text-sm text-gray-600">معدل الإنجاز</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {latestPerformance.completion_rate.toFixed(1)}%
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">متوسط أيام الإنجاز</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {latestPerformance.avg_completion_days}
                  </p>
                </div>
              </div>
            )}

            {/* Trend Chart */}
            <div className="space-y-3">
              {performanceTrend.slice(-6).map((item) => (
                <div key={item.period} className="space-y-1">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">
                      {item.period_display.split(' ')[0]}
                    </span>
                    <span className="text-sm font-medium text-gray-900">
                      {item.completion_rate.toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="h-2 bg-blue-500 rounded-full transition-all duration-500 ease-out"
                      style={{ width: `${Math.min(item.completion_rate, 100)}%` }}
                    />
                  </div>
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>بدء: {item.projects_started}</span>
                    <span>إنجاز: {item.projects_completed}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Project Summary */}
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle>ملخص المشاريع</CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 mb-1">
                {performanceTrend.reduce((sum, item) => sum + item.projects_started, 0)}
              </div>
              <div className="text-sm text-blue-800">إجمالي المشاريع المبدوءة</div>
            </div>
            
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600 mb-1">
                {performanceTrend.reduce((sum, item) => sum + item.projects_completed, 0)}
              </div>
              <div className="text-sm text-green-800">إجمالي المشاريع المكتملة</div>
            </div>
            
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600 mb-1">
                {latestPerformance ? latestPerformance.avg_completion_days : 0}
              </div>
              <div className="text-sm text-yellow-800">متوسط أيام الإنجاز</div>
            </div>
            
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600 mb-1">
                {latestPerformance ? latestPerformance.completion_rate.toFixed(1) : 0}%
              </div>
              <div className="text-sm text-purple-800">معدل الإنجاز الحالي</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
