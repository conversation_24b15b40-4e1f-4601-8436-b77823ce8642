"use client";

import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, TrendingUp, Star } from 'lucide-react';
import { businessIntelligenceApi } from '@/lib/api/business-intelligence';

interface ClientAcquisitionData {
  period: string;
  period_display: string;
  new_clients: number;
  start_date: string;
  end_date: string;
}

interface TopClient {
  id: number;
  name: string;
  company: string;
  total_revenue: number;
  projects_count: number;
  last_project_date: string | null;
}

interface ClientSatisfactionData {
  average_satisfaction: number;
  total_responses: number;
  satisfaction_distribution: {
    excellent: number;
    good: number;
    average: number;
    poor: number;
    very_poor: number;
  };
}

interface ClientChartProps {
  acquisitionData: ClientAcquisitionData[];
  topClients: TopClient[];
  satisfactionData: ClientSatisfactionData;
}

export default function ClientChart({ acquisitionData, topClients, satisfactionData }: ClientChartProps) {
  // Calculate acquisition trend
  const currentClients = acquisitionData[acquisitionData.length - 1]?.new_clients || 0;
  const previousClients = acquisitionData[acquisitionData.length - 2]?.new_clients || 0;
  const trendPercentage = previousClients > 0 ? ((currentClients - previousClients) / previousClients) * 100 : 0;
  const isPositiveTrend = trendPercentage >= 0;

  // Find max clients for scaling
  const maxClients = Math.max(...acquisitionData.map(item => item.new_clients));

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6" dir="rtl">
      {/* Client Acquisition Trend */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                اكتساب العملاء
              </CardTitle>
              <CardDescription>
                عدد العملاء الجدد شهرياً
              </CardDescription>
            </div>
            <div className={`flex items-center gap-1 text-sm ${isPositiveTrend ? 'text-green-600' : 'text-red-600'}`}>
              <TrendingUp className="h-4 w-4" />
              <span>{Math.abs(trendPercentage).toFixed(1)}%</span>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-3">
            {acquisitionData.slice(-6).map((item) => (
              <div key={item.period} className="flex items-center gap-3">
                <div className="w-16 text-sm text-gray-600 text-right">
                  {item.period_display.split(' ')[0]}
                </div>
                <div className="flex-1 relative">
                  <div className="h-6 bg-gray-200 rounded-md overflow-hidden">
                    <div 
                      className="h-full bg-green-500 transition-all duration-500 ease-out"
                      style={{ 
                        width: `${maxClients > 0 ? (item.new_clients / maxClients) * 100 : 0}%` 
                      }}
                    />
                  </div>
                  <div className="absolute inset-y-0 right-2 flex items-center">
                    <span className="text-xs font-medium text-white">
                      {item.new_clients}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-4 pt-4 border-t">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">إجمالي العملاء الجدد</span>
              <span className="text-lg font-bold text-gray-900">
                {acquisitionData.reduce((sum, item) => sum + item.new_clients, 0)}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Top Clients */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            أفضل العملاء
          </CardTitle>
          <CardDescription>
            العملاء الأكثر إيراداً هذا الشهر
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-3">
            {topClients.slice(0, 5).map((client, index) => (
              <div key={client.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold text-blue-600">
                      {index + 1}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{client.name}</p>
                    {client.company && (
                      <p className="text-sm text-gray-600">{client.company}</p>
                    )}
                    <p className="text-xs text-gray-500">
                      {client.projects_count} مشروع
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-gray-900">
                    {businessIntelligenceApi.formatCurrency(client.total_revenue)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Client Satisfaction */}
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            رضا العملاء
          </CardTitle>
          <CardDescription>
            متوسط رضا العملاء: {satisfactionData.average_satisfaction.toFixed(1)}%
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {Object.entries(satisfactionData.satisfaction_distribution).map(([level, count]) => {
              const labels = {
                excellent: 'ممتاز',
                good: 'جيد',
                average: 'متوسط',
                poor: 'ضعيف',
                very_poor: 'ضعيف جداً'
              };
              
              const colors = {
                excellent: 'bg-green-500',
                good: 'bg-blue-500',
                average: 'bg-yellow-500',
                poor: 'bg-orange-500',
                very_poor: 'bg-red-500'
              };
              
              const percentage = satisfactionData.total_responses > 0 
                ? (count / satisfactionData.total_responses) * 100 
                : 0;
              
              return (
                <div key={level} className="text-center">
                  <div className={`w-full h-20 ${colors[level as keyof typeof colors]} rounded-lg mb-2 flex items-end justify-center pb-2`}>
                    <span className="text-white font-bold text-sm">{count}</span>
                  </div>
                  <p className="text-sm font-medium text-gray-900">
                    {labels[level as keyof typeof labels]}
                  </p>
                  <p className="text-xs text-gray-600">
                    {percentage.toFixed(1)}%
                  </p>
                </div>
              );
            })}
          </div>
          
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">إجمالي الردود</span>
              <span className="font-bold text-gray-900">{satisfactionData.total_responses}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
