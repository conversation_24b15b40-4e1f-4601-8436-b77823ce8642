"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  <PERSON><PERSON>hart3, 
  <PERSON><PERSON><PERSON>, 
  TrendingUp, 
  Table, 
  Gauge,
  Edit, 
  Trash2, 
  RefreshCw,
  Eye,
  EyeOff,
  Move,
  Settings,
  Maximize2,
  Minimize2,
  Download
} from 'lucide-react';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  ResponsiveContainer, 
  <PERSON><PERSON><PERSON> as <PERSON><PERSON>rts<PERSON>ar<PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON> as RechartsPie<PERSON>hart,
  Cell,
  Pie
} from 'recharts';
import { businessIntelligenceApi, DashboardWidget } from '@/lib/api/business-intelligence';

interface AdvancedWidgetProps {
  widget: DashboardWidget;
  onEdit?: (widget: DashboardWidget) => void;
  onDelete?: (widget: DashboardWidget) => void;
  onToggleVisibility?: (widget: DashboardWidget) => void;
  onRefresh?: (widget: DashboardWidget) => void;
  isDragging?: boolean;
  isResizing?: boolean;
  interactive?: boolean;
}

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];

const chartConfig = {
  value: {
    label: "القيمة",
    color: "hsl(var(--chart-1))",
  },
  target: {
    label: "الهدف",
    color: "hsl(var(--chart-2))",
  },
};

export default function AdvancedWidget({ 
  widget, 
  onEdit, 
  onDelete, 
  onToggleVisibility, 
  onRefresh,
  isDragging = false,
  isResizing = false,
  interactive = true
}: AdvancedWidgetProps) {
  const [widgetData, setWidgetData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadWidgetData();
  }, [widget.id]);

  const loadWidgetData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await businessIntelligenceApi.getWidgetData(widget.id);
      setWidgetData(response.data);
    } catch (error) {
      console.error('Error loading widget data:', error);
      setError('فشل في تحميل بيانات الودجت');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    await loadWidgetData();
    if (onRefresh) {
      onRefresh(widget);
    }
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit(widget);
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(widget);
    }
  };

  const handleToggleVisibility = () => {
    if (onToggleVisibility) {
      onToggleVisibility(widget);
    }
  };

  const handleExport = async () => {
    try {
      // TODO: Implement widget data export
      console.log('Exporting widget data:', widget.title);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const getWidgetIcon = () => {
    switch (widget.widget_type) {
      case 'kpi_metric':
        return <Gauge className="h-5 w-5" />;
      case 'chart':
        return widget.chart_type === 'pie' ? <PieChart className="h-5 w-5" /> : <BarChart3 className="h-5 w-5" />;
      case 'table':
        return <Table className="h-5 w-5" />;
      case 'trend':
        return <TrendingUp className="h-5 w-5" />;
      default:
        return <BarChart3 className="h-5 w-5" />;
    }
  };

  const renderWidgetContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-32">
          <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex flex-col items-center justify-center h-32 text-center">
          <div className="text-red-500 mb-2">⚠️</div>
          <p className="text-sm text-red-600">{error}</p>
          <Button variant="outline" size="sm" onClick={handleRefresh} className="mt-2">
            إعادة المحاولة
          </Button>
        </div>
      );
    }

    if (!widgetData) {
      return (
        <div className="flex items-center justify-center h-32 text-gray-500">
          <p className="text-sm">لا توجد بيانات متاحة</p>
        </div>
      );
    }

    switch (widget.widget_type) {
      case 'kpi_metric':
        return renderKPIMetric();
      case 'chart':
        return renderChart();
      case 'table':
        return renderTable();
      case 'trend':
        return renderTrend();
      default:
        return renderDefault();
    }
  };

  const renderKPIMetric = () => {
    const { value, target, unit, trend, change } = widgetData;
    const isPositiveTrend = change >= 0;

    return (
      <div className="text-center space-y-2">
        <div className="text-3xl font-bold text-gray-900">
          {businessIntelligenceApi.formatNumber(value)}
          {unit === 'percentage' && '%'}
          {unit === 'currency' && ' ج.م'}
        </div>
        {target && (
          <div className="text-sm text-gray-600">
            الهدف: {businessIntelligenceApi.formatNumber(target)}
            {unit === 'percentage' && '%'}
            {unit === 'currency' && ' ج.م'}
          </div>
        )}
        {change !== undefined && (
          <Badge variant={isPositiveTrend ? 'default' : 'destructive'} className="flex items-center gap-1 w-fit mx-auto">
            {isPositiveTrend ? (
              <TrendingUp className="h-3 w-3" />
            ) : (
              <TrendingUp className="h-3 w-3 rotate-180" />
            )}
            <span>{Math.abs(change).toFixed(1)}%</span>
          </Badge>
        )}
      </div>
    );
  };

  const renderChart = () => {
    const { data } = widgetData;
    if (!data || data.length === 0) {
      return <div className="text-center text-gray-500">لا توجد بيانات للرسم البياني</div>;
    }

    const height = isExpanded ? 300 : 200;

    return (
      <div style={{ height }}>
        <ChartContainer config={chartConfig}>
          <ResponsiveContainer width="100%" height="100%">
            {widget.chart_type === 'pie' ? (
              <RechartsPieChart>
                <Pie
                  data={data}
                  cx="50%"
                  cy="50%"
                  outerRadius={60}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {data.map((entry: any, index: number) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <ChartTooltip content={<ChartTooltipContent />} />
              </RechartsPieChart>
            ) : widget.chart_type === 'line' ? (
              <RechartsLineChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Line type="monotone" dataKey="value" stroke="var(--color-value)" strokeWidth={2} />
              </RechartsLineChart>
            ) : (
              <RechartsBarChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Bar dataKey="value" fill="var(--color-value)" radius={[4, 4, 0, 0]} />
              </RechartsBarChart>
            )}
          </ResponsiveContainer>
        </ChartContainer>
      </div>
    );
  };

  const renderTable = () => {
    const { headers, rows } = widgetData;
    if (!headers || !rows || rows.length === 0) {
      return <div className="text-center text-gray-500">لا توجد بيانات للجدول</div>;
    }

    return (
      <div className="overflow-x-auto">
        <table className="w-full text-sm">
          <thead>
            <tr className="border-b">
              {headers.map((header: string, index: number) => (
                <th key={index} className="text-right p-2 font-medium text-gray-700">
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {rows.slice(0, isExpanded ? rows.length : 5).map((row: any[], rowIndex: number) => (
              <tr key={rowIndex} className="border-b border-gray-100">
                {row.map((cell: any, cellIndex: number) => (
                  <td key={cellIndex} className="p-2 text-gray-600">
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
        {!isExpanded && rows.length > 5 && (
          <div className="text-center mt-2">
            <Button variant="ghost" size="sm" onClick={() => setIsExpanded(true)}>
              عرض المزيد ({rows.length - 5} صف)
            </Button>
          </div>
        )}
      </div>
    );
  };

  const renderTrend = () => {
    const { current, previous, trend, percentage } = widgetData;
    const isPositive = trend === 'up';

    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div>
            <div className="text-2xl font-bold text-gray-900">
              {businessIntelligenceApi.formatNumber(current)}
            </div>
            <div className="text-sm text-gray-600">
              السابق: {businessIntelligenceApi.formatNumber(previous)}
            </div>
          </div>
          <Badge variant={isPositive ? 'default' : 'destructive'} className="flex items-center gap-1">
            <TrendingUp className={`h-3 w-3 ${!isPositive ? 'rotate-180' : ''}`} />
            <span>{Math.abs(percentage).toFixed(1)}%</span>
          </Badge>
        </div>
      </div>
    );
  };

  const renderDefault = () => {
    return (
      <div className="text-center text-gray-500">
        <div className="mb-2">{getWidgetIcon()}</div>
        <p className="text-sm">نوع ودجت غير مدعوم</p>
      </div>
    );
  };

  return (
    <Card 
      className={`
        transition-all duration-200 
        ${isDragging ? 'opacity-50 scale-95' : ''} 
        ${isResizing ? 'ring-2 ring-blue-500' : ''}
        ${!widget.is_visible ? 'opacity-60' : ''}
        hover:shadow-md
      `}
      style={{
        width: widget.width || 'auto',
        height: widget.height || 'auto'
      }}
    >
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            {getWidgetIcon()}
            <div className="min-w-0 flex-1">
              <CardTitle className="text-sm font-medium truncate">{widget.title}</CardTitle>
              {widget.description && (
                <CardDescription className="text-xs truncate">{widget.description}</CardDescription>
              )}
            </div>
          </div>
          
          {interactive && (
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="h-6 w-6 p-0"
              >
                {isExpanded ? <Minimize2 className="h-3 w-3" /> : <Maximize2 className="h-3 w-3" />}
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRefresh}
                disabled={isLoading}
                className="h-6 w-6 p-0"
              >
                <RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleExport}
                className="h-6 w-6 p-0"
              >
                <Download className="h-3 w-3" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleToggleVisibility}
                className="h-6 w-6 p-0"
              >
                {widget.is_visible ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleEdit}
                className="h-6 w-6 p-0"
              >
                <Edit className="h-3 w-3" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDelete}
                className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 cursor-move"
              >
                <Move className="h-3 w-3" />
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        {renderWidgetContent()}
      </CardContent>
    </Card>
  );
}
