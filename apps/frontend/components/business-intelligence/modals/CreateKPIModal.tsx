"use client";

import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Target, 
  TrendingUp, 
  Calculator, 
  Settings, 
  Palette, 
  Clock,
  Eye,
  EyeOff,
  Save,
  X
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { businessIntelligenceApi, KPIMetric } from '@/lib/api/business-intelligence';

interface CreateKPIModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (kpi: KPIMetric) => void;
  editingKPI?: KPIMetric | null;
}

const metricTypes = [
  { value: 'revenue', label: 'الإيرادات', icon: '💰' },
  { value: 'profit', label: 'الربح', icon: '📈' },
  { value: 'client_count', label: 'عدد العملاء', icon: '👥' },
  { value: 'project_count', label: 'عدد المشاريع', icon: '📋' },
  { value: 'task_completion', label: 'إنجاز المهام', icon: '✅' },
  { value: 'team_productivity', label: 'إنتاجية الفريق', icon: '⚡' },
  { value: 'client_satisfaction', label: 'رضا العملاء', icon: '😊' },
  { value: 'custom', label: 'مخصص', icon: '🔧' }
];

const frequencies = [
  { value: 'daily', label: 'يومي' },
  { value: 'weekly', label: 'أسبوعي' },
  { value: 'monthly', label: 'شهري' },
  { value: 'quarterly', label: 'ربع سنوي' },
  { value: 'yearly', label: 'سنوي' }
];

const units = [
  { value: 'currency', label: 'عملة (ج.م)' },
  { value: 'number', label: 'رقم' },
  { value: 'percentage', label: 'نسبة مئوية (%)' },
  { value: 'ratio', label: 'نسبة' },
  { value: 'score', label: 'نقاط' }
];

const colorOptions = [
  { value: '#3B82F6', label: 'أزرق', color: 'bg-blue-500' },
  { value: '#10B981', label: 'أخضر', color: 'bg-green-500' },
  { value: '#F59E0B', label: 'برتقالي', color: 'bg-orange-500' },
  { value: '#EF4444', label: 'أحمر', color: 'bg-red-500' },
  { value: '#8B5CF6', label: 'بنفسجي', color: 'bg-purple-500' },
  { value: '#06B6D4', label: 'سماوي', color: 'bg-cyan-500' },
  { value: '#84CC16', label: 'أخضر فاتح', color: 'bg-lime-500' },
  { value: '#F97316', label: 'برتقالي داكن', color: 'bg-orange-600' }
];

export default function CreateKPIModal({ isOpen, onClose, onSuccess, editingKPI }: CreateKPIModalProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');

  // Form state
  const [formData, setFormData] = useState({
    name: editingKPI?.name || '',
    description: editingKPI?.description || '',
    metric_type: editingKPI?.metric_type || '',
    target_value: editingKPI?.target_value?.toString() || '',
    unit: editingKPI?.unit || 'number',
    frequency: editingKPI?.frequency || 'monthly',
    calculation_query: editingKPI?.calculation_query || '',
    is_automated: editingKPI?.is_automated || true,
    is_visible: editingKPI?.is_visible !== false,
    color_code: editingKPI?.color_code || '#3B82F6',
    display_order: editingKPI?.display_order?.toString() || '0'
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: string, value: string | boolean | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'اسم المؤشر مطلوب';
    }

    if (!formData.metric_type) {
      newErrors.metric_type = 'نوع المؤشر مطلوب';
    }

    if (formData.target_value && isNaN(Number(formData.target_value))) {
      newErrors.target_value = 'القيمة المستهدفة يجب أن تكون رقم';
    }

    if (formData.metric_type === 'custom' && !formData.calculation_query.trim()) {
      newErrors.calculation_query = 'استعلام الحساب مطلوب للمؤشرات المخصصة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى تصحيح الأخطاء والمحاولة مرة أخرى",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const submitData = {
        ...formData,
        target_value: formData.target_value ? Number(formData.target_value) : null,
        display_order: Number(formData.display_order)
      };

      let response;
      if (editingKPI) {
        response = await businessIntelligenceApi.updateKPI(editingKPI.id, submitData);
      } else {
        response = await businessIntelligenceApi.createKPI(submitData);
      }

      toast({
        title: editingKPI ? "تم التحديث" : "تم الإنشاء",
        description: editingKPI ? "تم تحديث المؤشر بنجاح" : "تم إنشاء المؤشر بنجاح",
      });

      onSuccess(response.data);
      onClose();
    } catch (error) {
      console.error('Error saving KPI:', error);
      toast({
        title: "خطأ",
        description: editingKPI ? "فشل في تحديث المؤشر" : "فشل في إنشاء المؤشر",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
    }
  };

  const selectedMetricType = metricTypes.find(type => type.value === formData.metric_type);
  const selectedColor = colorOptions.find(color => color.value === formData.color_code);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto" dir="rtl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            {editingKPI ? 'تعديل مؤشر الأداء' : 'إنشاء مؤشر أداء جديد'}
          </DialogTitle>
          <DialogDescription>
            {editingKPI 
              ? 'قم بتعديل بيانات مؤشر الأداء الرئيسي'
              : 'قم بإنشاء مؤشر أداء رئيسي جديد لمتابعة أهداف شركتك'
            }
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              البيانات الأساسية
            </TabsTrigger>
            <TabsTrigger value="calculation" className="flex items-center gap-2">
              <Calculator className="h-4 w-4" />
              الحساب والهدف
            </TabsTrigger>
            <TabsTrigger value="display" className="flex items-center gap-2">
              <Palette className="h-4 w-4" />
              العرض والتنسيق
            </TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">اسم المؤشر *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="مثال: إجمالي الإيرادات الشهرية"
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="metric_type">نوع المؤشر *</Label>
                <Select value={formData.metric_type} onValueChange={(value) => handleInputChange('metric_type', value)}>
                  <SelectTrigger className={errors.metric_type ? 'border-red-500' : ''}>
                    <SelectValue placeholder="اختر نوع المؤشر" />
                  </SelectTrigger>
                  <SelectContent>
                    {metricTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex items-center gap-2">
                          <span>{type.icon}</span>
                          <span>{type.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.metric_type && <p className="text-sm text-red-500">{errors.metric_type}</p>}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">الوصف</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="وصف مختصر لمؤشر الأداء وأهميته..."
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="unit">وحدة القياس</Label>
                <Select value={formData.unit} onValueChange={(value) => handleInputChange('unit', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {units.map((unit) => (
                      <SelectItem key={unit.value} value={unit.value}>
                        {unit.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="frequency">تكرار التحديث</Label>
                <Select value={formData.frequency} onValueChange={(value) => handleInputChange('frequency', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {frequencies.map((freq) => (
                      <SelectItem key={freq.value} value={freq.value}>
                        {freq.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="calculation" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="target_value">القيمة المستهدفة</Label>
              <Input
                id="target_value"
                type="number"
                value={formData.target_value}
                onChange={(e) => handleInputChange('target_value', e.target.value)}
                placeholder="مثال: 100000"
                className={errors.target_value ? 'border-red-500' : ''}
              />
              {errors.target_value && <p className="text-sm text-red-500">{errors.target_value}</p>}
            </div>

            {formData.metric_type === 'custom' && (
              <div className="space-y-2">
                <Label htmlFor="calculation_query">استعلام الحساب *</Label>
                <Textarea
                  id="calculation_query"
                  value={formData.calculation_query}
                  onChange={(e) => handleInputChange('calculation_query', e.target.value)}
                  placeholder="SELECT SUM(amount) FROM invoices WHERE status='paid'"
                  rows={4}
                  className={errors.calculation_query ? 'border-red-500' : ''}
                />
                {errors.calculation_query && <p className="text-sm text-red-500">{errors.calculation_query}</p>}
                <p className="text-sm text-gray-600">
                  استعلام SQL لحساب قيمة المؤشر تلقائياً
                </p>
              </div>
            )}

            <div className="flex items-center space-x-2 space-x-reverse">
              <Switch
                id="is_automated"
                checked={formData.is_automated}
                onCheckedChange={(checked) => handleInputChange('is_automated', checked)}
              />
              <Label htmlFor="is_automated" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                حساب تلقائي
              </Label>
            </div>
          </TabsContent>

          <TabsContent value="display" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>لون المؤشر</Label>
                <div className="grid grid-cols-4 gap-2">
                  {colorOptions.map((color) => (
                    <button
                      key={color.value}
                      type="button"
                      onClick={() => handleInputChange('color_code', color.value)}
                      className={`p-3 rounded-lg border-2 transition-all ${
                        formData.color_code === color.value 
                          ? 'border-gray-900 scale-105' 
                          : 'border-gray-200 hover:border-gray-400'
                      }`}
                    >
                      <div className={`w-full h-6 rounded ${color.color}`} />
                      <p className="text-xs mt-1">{color.label}</p>
                    </button>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="display_order">ترتيب العرض</Label>
                <Input
                  id="display_order"
                  type="number"
                  value={formData.display_order}
                  onChange={(e) => handleInputChange('display_order', e.target.value)}
                  placeholder="0"
                  min="0"
                />
              </div>
            </div>

            <div className="flex items-center space-x-2 space-x-reverse">
              <Switch
                id="is_visible"
                checked={formData.is_visible}
                onCheckedChange={(checked) => handleInputChange('is_visible', checked)}
              />
              <Label htmlFor="is_visible" className="flex items-center gap-2">
                {formData.is_visible ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                مرئي في لوحة التحكم
              </Label>
            </div>

            {/* Preview */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">معاينة المؤشر</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="p-4 border rounded-lg" style={{ borderColor: formData.color_code }}>
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium">{formData.name || 'اسم المؤشر'}</h3>
                    <Badge style={{ backgroundColor: formData.color_code, color: 'white' }}>
                      {selectedMetricType?.icon} {selectedMetricType?.label}
                    </Badge>
                  </div>
                  <p className="text-2xl font-bold" style={{ color: formData.color_code }}>
                    {formData.target_value || '0'}
                    {formData.unit === 'percentage' && '%'}
                    {formData.unit === 'currency' && ' ج.م'}
                  </p>
                  <p className="text-sm text-gray-600">{formData.description || 'وصف المؤشر'}</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            <X className="h-4 w-4 ml-2" />
            إلغاء
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            <Save className="h-4 w-4 ml-2" />
            {isLoading ? 'جاري الحفظ...' : (editingKPI ? 'تحديث' : 'إنشاء')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
