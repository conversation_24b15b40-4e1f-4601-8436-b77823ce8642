"use client";

import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  FileText, 
  Calendar as CalendarIcon, 
  Settings, 
  Filter, 
  Clock,
  Users,
  Save,
  X,
  Download,
  Eye,
  BarChart3,
  PieChart,
  TrendingUp
} from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { useToast } from '@/components/ui/use-toast';
import { businessIntelligenceApi, AnalyticsReport } from '@/lib/api/business-intelligence';

interface CreateReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (report: AnalyticsReport) => void;
  editingReport?: AnalyticsReport | null;
}

const reportTypes = [
  { 
    value: 'executive_dashboard', 
    label: 'لوحة المدير التنفيذي', 
    icon: '📊',
    description: 'تقرير شامل لجميع مؤشرات الأداء الرئيسية'
  },
  { 
    value: 'revenue_analysis', 
    label: 'تحليل الإيرادات', 
    icon: '💰',
    description: 'تحليل مفصل للإيرادات والاتجاهات المالية'
  },
  { 
    value: 'client_analysis', 
    label: 'تحليل العملاء', 
    icon: '👥',
    description: 'تحليل سلوك العملاء ومعدلات الرضا'
  },
  { 
    value: 'project_performance', 
    label: 'أداء المشاريع', 
    icon: '📋',
    description: 'تقرير عن حالة وأداء المشاريع'
  },
  { 
    value: 'team_performance', 
    label: 'أداء الفريق', 
    icon: '⚡',
    description: 'تحليل إنتاجية وأداء أعضاء الفريق'
  },
  { 
    value: 'financial_overview', 
    label: 'نظرة مالية شاملة', 
    icon: '📈',
    description: 'تقرير مالي شامل للشركة'
  },
  { 
    value: 'custom_report', 
    label: 'تقرير مخصص', 
    icon: '🔧',
    description: 'تقرير مخصص حسب المتطلبات'
  }
];

const periodTypes = [
  { value: 'daily', label: 'يومي' },
  { value: 'weekly', label: 'أسبوعي' },
  { value: 'monthly', label: 'شهري' },
  { value: 'quarterly', label: 'ربع سنوي' },
  { value: 'yearly', label: 'سنوي' },
  { value: 'custom', label: 'فترة مخصصة' }
];

const scheduleFrequencies = [
  { value: 'daily', label: 'يومياً' },
  { value: 'weekly', label: 'أسبوعياً' },
  { value: 'monthly', label: 'شهرياً' },
  { value: 'quarterly', label: 'ربع سنوي' }
];

export default function CreateReportModal({ isOpen, onClose, onSuccess, editingReport }: CreateReportModalProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');

  // Form state
  const [formData, setFormData] = useState({
    title: editingReport?.title || '',
    description: editingReport?.description || '',
    report_type: editingReport?.report_type || '',
    period_type: editingReport?.period_type || 'monthly',
    start_date: editingReport?.start_date || '',
    end_date: editingReport?.end_date || '',
    is_scheduled: editingReport?.is_scheduled || false,
    schedule_frequency: editingReport?.schedule_frequency || '',
    shared_with_ids: editingReport?.shared_with?.map(user => user.id) || [],
    filters: editingReport?.filters || {},
    configuration: editingReport?.configuration || {}
  });

  const [startDate, setStartDate] = useState<Date | undefined>(
    formData.start_date ? new Date(formData.start_date) : undefined
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    formData.end_date ? new Date(formData.end_date) : undefined
  );

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleDateChange = (field: 'start_date' | 'end_date', date: Date | undefined) => {
    if (field === 'start_date') {
      setStartDate(date);
      setFormData(prev => ({ 
        ...prev, 
        start_date: date ? format(date, 'yyyy-MM-dd') : '' 
      }));
    } else {
      setEndDate(date);
      setFormData(prev => ({ 
        ...prev, 
        end_date: date ? format(date, 'yyyy-MM-dd') : '' 
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'عنوان التقرير مطلوب';
    }

    if (!formData.report_type) {
      newErrors.report_type = 'نوع التقرير مطلوب';
    }

    if (formData.period_type === 'custom') {
      if (!formData.start_date) {
        newErrors.start_date = 'تاريخ البداية مطلوب للفترة المخصصة';
      }
      if (!formData.end_date) {
        newErrors.end_date = 'تاريخ النهاية مطلوب للفترة المخصصة';
      }
      if (formData.start_date && formData.end_date && new Date(formData.start_date) >= new Date(formData.end_date)) {
        newErrors.end_date = 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية';
      }
    }

    if (formData.is_scheduled && !formData.schedule_frequency) {
      newErrors.schedule_frequency = 'تكرار الجدولة مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى تصحيح الأخطاء والمحاولة مرة أخرى",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const submitData = {
        ...formData,
        shared_with: formData.shared_with_ids
      };

      let response;
      if (editingReport) {
        response = await businessIntelligenceApi.updateReport(editingReport.id, submitData);
      } else {
        response = await businessIntelligenceApi.createReport(submitData);
      }

      toast({
        title: editingReport ? "تم التحديث" : "تم الإنشاء",
        description: editingReport ? "تم تحديث التقرير بنجاح" : "تم إنشاء التقرير بنجاح",
      });

      onSuccess(response.data);
      onClose();
    } catch (error) {
      console.error('Error saving report:', error);
      toast({
        title: "خطأ",
        description: editingReport ? "فشل في تحديث التقرير" : "فشل في إنشاء التقرير",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
    }
  };

  const selectedReportType = reportTypes.find(type => type.value === formData.report_type);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto" dir="rtl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {editingReport ? 'تعديل التقرير التحليلي' : 'إنشاء تقرير تحليلي جديد'}
          </DialogTitle>
          <DialogDescription>
            {editingReport 
              ? 'قم بتعديل بيانات التقرير التحليلي'
              : 'قم بإنشاء تقرير تحليلي جديد لمتابعة أداء شركتك'
            }
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              البيانات الأساسية
            </TabsTrigger>
            <TabsTrigger value="period" className="flex items-center gap-2">
              <CalendarIcon className="h-4 w-4" />
              الفترة الزمنية
            </TabsTrigger>
            <TabsTrigger value="filters" className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              المرشحات
            </TabsTrigger>
            <TabsTrigger value="schedule" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              الجدولة والمشاركة
            </TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">عنوان التقرير *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="مثال: تقرير الأداء الشهري - يناير 2024"
                className={errors.title ? 'border-red-500' : ''}
              />
              {errors.title && <p className="text-sm text-red-500">{errors.title}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">وصف التقرير</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="وصف مختصر لمحتوى التقرير وأهدافه..."
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="report_type">نوع التقرير *</Label>
              <Select value={formData.report_type} onValueChange={(value) => handleInputChange('report_type', value)}>
                <SelectTrigger className={errors.report_type ? 'border-red-500' : ''}>
                  <SelectValue placeholder="اختر نوع التقرير" />
                </SelectTrigger>
                <SelectContent>
                  {reportTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      <div className="flex items-start gap-3 py-2">
                        <span className="text-lg">{type.icon}</span>
                        <div>
                          <div className="font-medium">{type.label}</div>
                          <div className="text-sm text-gray-600">{type.description}</div>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.report_type && <p className="text-sm text-red-500">{errors.report_type}</p>}
            </div>

            {selectedReportType && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <span>{selectedReportType.icon}</span>
                    {selectedReportType.label}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">{selectedReportType.description}</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="period" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="period_type">نوع الفترة</Label>
              <Select value={formData.period_type} onValueChange={(value) => handleInputChange('period_type', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {periodTypes.map((period) => (
                    <SelectItem key={period.value} value={period.value}>
                      {period.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {formData.period_type === 'custom' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>تاريخ البداية *</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={`w-full justify-start text-left font-normal ${errors.start_date ? 'border-red-500' : ''}`}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {startDate ? format(startDate, 'PPP', { locale: ar }) : 'اختر التاريخ'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={startDate}
                        onSelect={(date) => handleDateChange('start_date', date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  {errors.start_date && <p className="text-sm text-red-500">{errors.start_date}</p>}
                </div>

                <div className="space-y-2">
                  <Label>تاريخ النهاية *</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={`w-full justify-start text-left font-normal ${errors.end_date ? 'border-red-500' : ''}`}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {endDate ? format(endDate, 'PPP', { locale: ar }) : 'اختر التاريخ'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={endDate}
                        onSelect={(date) => handleDateChange('end_date', date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  {errors.end_date && <p className="text-sm text-red-500">{errors.end_date}</p>}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="filters" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">مرشحات التقرير</CardTitle>
                <CardDescription>
                  قم بتخصيص البيانات المراد تضمينها في التقرير
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center py-8 text-gray-500">
                  <Filter className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p>سيتم إضافة خيارات المرشحات المتقدمة قريباً</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="schedule" className="space-y-4">
            <div className="flex items-center space-x-2 space-x-reverse">
              <Switch
                id="is_scheduled"
                checked={formData.is_scheduled}
                onCheckedChange={(checked) => handleInputChange('is_scheduled', checked)}
              />
              <Label htmlFor="is_scheduled" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                جدولة التقرير تلقائياً
              </Label>
            </div>

            {formData.is_scheduled && (
              <div className="space-y-2">
                <Label htmlFor="schedule_frequency">تكرار الجدولة *</Label>
                <Select value={formData.schedule_frequency} onValueChange={(value) => handleInputChange('schedule_frequency', value)}>
                  <SelectTrigger className={errors.schedule_frequency ? 'border-red-500' : ''}>
                    <SelectValue placeholder="اختر تكرار الجدولة" />
                  </SelectTrigger>
                  <SelectContent>
                    {scheduleFrequencies.map((freq) => (
                      <SelectItem key={freq.value} value={freq.value}>
                        {freq.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.schedule_frequency && <p className="text-sm text-red-500">{errors.schedule_frequency}</p>}
              </div>
            )}

            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  مشاركة التقرير
                </CardTitle>
                <CardDescription>
                  اختر الأشخاص الذين يمكنهم الوصول لهذا التقرير
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-gray-500">
                  <Users className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p>سيتم إضافة خيارات المشاركة قريباً</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            <X className="h-4 w-4 ml-2" />
            إلغاء
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            <Save className="h-4 w-4 ml-2" />
            {isLoading ? 'جاري الحفظ...' : (editingReport ? 'تحديث' : 'إنشاء')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
