"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  Download, 
  Eye, 
  RefreshCw,
  Plus,
  Calendar,
  Clock
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

import { businessIntelligenceApi, AnalyticsReport } from '@/lib/api/business-intelligence';

export default function AnalyticsReports() {
  const [reports, setReports] = useState<AnalyticsReport[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadReports();
  }, []);

  const loadReports = async () => {
    try {
      setLoading(true);
      const response = await businessIntelligenceApi.getReports();
      setReports(response.data.results);
    } catch (error) {
      console.error('Error loading reports:', error);
      toast({
        title: "خطأ",
        description: "فشل في تحميل التقارير",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (reportId: number, fileType: 'pdf' | 'excel') => {
    try {
      const response = await businessIntelligenceApi.downloadReport(reportId, fileType);
      // Open download URL in new tab
      window.open(response.data.download_url, '_blank');
      toast({
        title: "تم التحميل",
        description: `تم تحميل ${response.data.file_name} بنجاح`,
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحميل التقرير",
        variant: "destructive",
      });
    }
  };

  const handleView = async (reportId: number) => {
    try {
      const response = await businessIntelligenceApi.viewReport(reportId);
      // TODO: Open report viewer modal
      console.log('View report:', response.data);
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في عرض التقرير",
        variant: "destructive",
      });
    }
  };

  const handleRegenerate = async (reportId: number) => {
    try {
      await businessIntelligenceApi.regenerateReport(reportId);
      await loadReports(); // Reload to get updated status
      toast({
        title: "تم بدء إعادة الإنشاء",
        description: "سيتم إعادة إنشاء التقرير قريباً",
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في إعادة إنشاء التقرير",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">التقارير التحليلية</h3>
          <p className="text-sm text-gray-600">
            {reports.length} تقرير متاح
          </p>
        </div>
        <Button size="sm">
          <Plus className="h-4 w-4 ml-2" />
          إنشاء تقرير جديد
        </Button>
      </div>

      {/* Reports List */}
      {reports.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد تقارير</h3>
            <p className="text-gray-600 mb-4">ابدأ بإنشاء تقرير تحليلي جديد</p>
            <Button>
              <Plus className="h-4 w-4 ml-2" />
              إنشاء تقرير جديد
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {reports.map((report) => (
            <Card key={report.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <CardTitle className="text-lg font-semibold">{report.title}</CardTitle>
                    <CardDescription className="text-sm text-gray-600">
                      {report.report_type_display}
                    </CardDescription>
                  </div>
                  <Badge variant={businessIntelligenceApi.getStatusBadgeVariant(report.status)}>
                    {report.status_display}
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Period */}
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Calendar className="h-4 w-4" />
                  <span>{report.start_date} - {report.end_date}</span>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">المشاهدات:</span>
                    <span className="font-medium ml-1">{report.view_count}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">التحميلات:</span>
                    <span className="font-medium ml-1">{report.download_count}</span>
                  </div>
                </div>

                {/* Generated Date */}
                {report.generated_at && (
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Clock className="h-4 w-4" />
                    <span>تم الإنشاء: {new Date(report.generated_at).toLocaleDateString('ar-EG')}</span>
                  </div>
                )}

                {/* File Size */}
                {report.file_size_mb > 0 && (
                  <div className="text-sm text-gray-600">
                    حجم الملف: {report.file_size_mb} ميجابايت
                  </div>
                )}

                {/* Actions */}
                <div className="flex gap-2 pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleView(report.id)}
                    className="flex-1"
                  >
                    <Eye className="h-4 w-4 ml-2" />
                    عرض
                  </Button>
                  
                  {report.pdf_file && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDownload(report.id, 'pdf')}
                    >
                      <Download className="h-4 w-4 ml-2" />
                      PDF
                    </Button>
                  )}
                  
                  {report.excel_file && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDownload(report.id, 'excel')}
                    >
                      <Download className="h-4 w-4 ml-2" />
                      Excel
                    </Button>
                  )}
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRegenerate(report.id)}
                  >
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </div>

                {/* Scheduled Badge */}
                {report.is_scheduled && (
                  <Badge variant="outline" className="w-fit">
                    <Clock className="h-3 w-3 ml-1" />
                    مجدول
                  </Badge>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
