"use client";

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import {
    AlertCircle,
    CheckCircle,
    Clock,
    DollarSign,
    RefreshCw,
    Target,
    TrendingDown,
    TrendingUp
} from 'lucide-react';
import React, { useEffect, useState } from 'react';

import { businessIntelligenceApi, ExecutiveDashboardData } from '@/lib/api/business-intelligence';

interface KPICardProps {
  kpi: {
    id: number;
    name: string;
    value: number;
    target?: number;
    unit: string;
    growth: number;
    status: string;
    color: string;
  };
}

const KPICard: React.FC<KPICardProps> = ({ kpi }) => {
  const getIcon = (unit: string) => {
    switch (unit) {
      case 'currency':
        return <DollarSign className="h-5 w-5" />;
      case 'number':
        return <Target className="h-5 w-5" />;
      case 'percentage':
        return <TrendingUp className="h-5 w-5" />;
      default:
        return <Target className="h-5 w-5" />;
    }
  };

  const formatValue = (value: number, unit: string) => {
    switch (unit) {
      case 'currency':
        return businessIntelligenceApi.formatCurrency(value);
      case 'percentage':
        return businessIntelligenceApi.formatPercentage(value);
      default:
        return businessIntelligenceApi.formatNumber(value);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'achieved':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'on_track':
        return <TrendingUp className="h-4 w-4 text-blue-600" />;
      case 'behind':
        return <Clock className="h-4 w-4 text-orange-600" />;
      case 'critical':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Target className="h-4 w-4 text-gray-600" />;
    }
  };

  const targetAchievement = kpi.target ? (kpi.value / kpi.target) * 100 : 0;

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2" style={{ color: kpi.color }}>
            {getIcon(kpi.unit)}
            <span className="text-sm font-medium text-gray-600">{kpi.name}</span>
          </div>
          {getStatusIcon(kpi.status)}
        </div>

        <div className="space-y-2">
          <div className="text-2xl font-bold text-gray-900">
            {formatValue(kpi.value, kpi.unit)}
          </div>

          {kpi.target && (
            <div className="space-y-1">
              <div className="flex justify-between text-sm text-gray-600">
                <span>الهدف: {formatValue(kpi.target, kpi.unit)}</span>
                <span>{targetAchievement.toFixed(1)}%</span>
              </div>
              <Progress value={Math.min(targetAchievement, 100)} className="h-2" />
            </div>
          )}

          <div className="flex items-center gap-1">
            {kpi.growth >= 0 ? (
              <TrendingUp className="h-4 w-4 text-green-600" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-600" />
            )}
            <span className={`text-sm font-medium ${kpi.growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {kpi.growth >= 0 ? '+' : ''}{kpi.growth.toFixed(1)}%
            </span>
            <span className="text-sm text-gray-500">من الشهر الماضي</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default function ExecutiveDashboard() {
  const [dashboardData, setDashboardData] = useState<ExecutiveDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const response = await businessIntelligenceApi.getExecutiveDashboard();
      setDashboardData(response.data);
    } catch (error) {
      console.error('Error loading executive dashboard:', error);
      toast({
        title: "خطأ",
        description: "فشل في تحميل بيانات لوحة المدير التنفيذي",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      await loadDashboardData();
      toast({
        title: "تم التحديث",
        description: "تم تحديث بيانات لوحة التحكم بنجاح",
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث البيانات",
        variant: "destructive",
      });
    } finally {
      setRefreshing(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">لا توجد بيانات متاحة</p>
      </div>
    );
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">نظرة عامة على الأداء</h2>
          <p className="text-sm text-gray-600">
            آخر تحديث: {new Date(dashboardData.generated_at).toLocaleString('ar-EG')}
          </p>
        </div>
        <Button
          onClick={handleRefresh}
          disabled={refreshing}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`h-4 w-4 ml-2 ${refreshing ? 'animate-spin' : ''}`} />
          تحديث
        </Button>
      </div>

      {/* KPI Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {dashboardData.kpi_summary.map((kpi) => (
          <KPICard key={kpi.id} kpi={kpi} />
        ))}
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Revenue Overview */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600">الإيرادات الشهرية</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {businessIntelligenceApi.formatCurrency(dashboardData.revenue_overview.current_revenue)}
            </div>
            <div className="flex items-center gap-1 mt-1">
              {dashboardData.revenue_overview.growth_percentage >= 0 ? (
                <TrendingUp className="h-4 w-4 text-green-600" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-600" />
              )}
              <span className={`text-sm font-medium ${
                dashboardData.revenue_overview.growth_percentage >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {dashboardData.revenue_overview.growth_percentage >= 0 ? '+' : ''}
                {dashboardData.revenue_overview.growth_percentage.toFixed(1)}%
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Client Overview */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600">العملاء النشطين</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {businessIntelligenceApi.formatNumber(dashboardData.client_overview.total_clients)}
            </div>
            <div className="text-sm text-gray-600 mt-1">
              +{dashboardData.client_overview.new_clients_this_month} عميل جديد هذا الشهر
            </div>
          </CardContent>
        </Card>

        {/* Project Overview */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600">المشاريع النشطة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {businessIntelligenceApi.formatNumber(dashboardData.project_overview.active_projects)}
            </div>
            <div className="text-sm text-gray-600 mt-1">
              من أصل {dashboardData.project_overview.total_projects} مشروع
            </div>
          </CardContent>
        </Card>

        {/* Team Overview */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600">أعضاء الفريق</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {businessIntelligenceApi.formatNumber(dashboardData.team_overview.total_team_members)}
            </div>
            <div className="text-sm text-gray-600 mt-1">
              {dashboardData.team_overview.tasks_completed_this_month} مهمة مكتملة هذا الشهر
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Financial Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            الملخص المالي
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-sm font-medium text-green-800">الإيرادات</div>
              <div className="text-2xl font-bold text-green-900">
                {businessIntelligenceApi.formatCurrency(dashboardData.financial_summary.revenue)}
              </div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-sm font-medium text-orange-800">العمولات</div>
              <div className="text-2xl font-bold text-orange-900">
                {businessIntelligenceApi.formatCurrency(dashboardData.financial_summary.commissions)}
              </div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-sm font-medium text-blue-800">الأرباح</div>
              <div className="text-2xl font-bold text-blue-900">
                {businessIntelligenceApi.formatCurrency(dashboardData.financial_summary.profit)}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Analytics Charts */}
      <div className="space-y-6">
        {/* Revenue Analytics */}
        {dashboardData.revenue_trend && dashboardData.revenue_trend.length > 0 && (
          <RevenueChart
            data={dashboardData.revenue_trend}
            title="اتجاه الإيرادات الشهرية"
            showTrend={true}
          />
        )}

        {/* Client Analytics */}
        {dashboardData.client_acquisition_trend && dashboardData.top_clients && dashboardData.client_satisfaction && (
          <ClientChart
            acquisitionData={dashboardData.client_acquisition_trend}
            topClients={dashboardData.top_clients}
            satisfactionData={dashboardData.client_satisfaction}
          />
        )}

        {/* Project Analytics */}
        {dashboardData.project_status_distribution && dashboardData.project_performance_trend && (
          <ProjectChart
            statusDistribution={dashboardData.project_status_distribution}
            performanceTrend={dashboardData.project_performance_trend}
          />
        )}
      </div>

      {/* System Health */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            حالة النظام
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-sm font-medium text-gray-600">الحالة</div>
              <Badge variant={dashboardData.system_health.status === 'healthy' ? 'default' : 'destructive'}>
                {dashboardData.system_health.status === 'healthy' ? 'سليم' : 'خطأ'}
              </Badge>
            </div>
            <div className="text-center">
              <div className="text-sm font-medium text-gray-600">وقت التشغيل</div>
              <div className="text-lg font-semibold">{dashboardData.system_health.uptime}</div>
            </div>
            <div className="text-center">
              <div className="text-sm font-medium text-gray-600">زمن الاستجابة</div>
              <div className="text-lg font-semibold">{dashboardData.system_health.response_time}</div>
            </div>
            <div className="text-center">
              <div className="text-sm font-medium text-gray-600">المستخدمون النشطون</div>
              <div className="text-lg font-semibold">{dashboardData.system_health.active_users}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
