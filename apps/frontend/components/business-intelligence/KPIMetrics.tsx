"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Edit, 
  Trash2, 
  RefreshCw,
  Plus,
  Eye,
  EyeOff,
  Calculator
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

import { businessIntelligenceApi, KPIMetric } from '@/lib/api/business-intelligence';

interface KPICardProps {
  kpi: KPIMetric;
  onEdit: (kpi: KPIMetric) => void;
  onDelete: (id: number) => void;
  onToggleVisibility: (id: number, visible: boolean) => void;
  onUpdateValue: (id: number, value: number) => void;
}

const KPICard: React.FC<KPICardProps> = ({ 
  kpi, 
  onEdit, 
  onDelete, 
  onToggleVisibility, 
  onUpdateValue 
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [newValue, setNewValue] = useState(kpi.current_value.toString());

  const formatValue = (value: number, unit: string) => {
    switch (unit) {
      case 'currency':
        return businessIntelligenceApi.formatCurrency(value);
      case 'percentage':
        return businessIntelligenceApi.formatPercentage(value);
      default:
        return businessIntelligenceApi.formatNumber(value);
    }
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'achieved': 'bg-green-100 text-green-800',
      'on_track': 'bg-blue-100 text-blue-800',
      'behind': 'bg-orange-100 text-orange-800',
      'critical': 'bg-red-100 text-red-800',
      'no_target': 'bg-gray-100 text-gray-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      'achieved': 'محقق',
      'on_track': 'على المسار',
      'behind': 'متأخر',
      'critical': 'حرج',
      'no_target': 'بدون هدف',
    };
    return labels[status] || status;
  };

  const handleUpdateValue = () => {
    const value = parseFloat(newValue);
    if (!isNaN(value)) {
      onUpdateValue(kpi.id, value);
      setIsEditing(false);
    }
  };

  const targetAchievement = kpi.target_achievement_percentage || 0;

  return (
    <Card className={`${!kpi.is_visible ? 'opacity-50' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold">{kpi.name}</CardTitle>
            <CardDescription className="text-sm text-gray-600">
              {kpi.metric_type_display} • {kpi.frequency_display}
            </CardDescription>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onToggleVisibility(kpi.id, !kpi.is_visible)}
            >
              {kpi.is_visible ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(kpi)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(kpi.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Current Value */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-600">القيمة الحالية</span>
            <Badge className={getStatusColor(kpi.status)}>
              {getStatusLabel(kpi.status)}
            </Badge>
          </div>
          
          {isEditing && !kpi.is_automated ? (
            <div className="flex gap-2">
              <Input
                type="number"
                value={newValue}
                onChange={(e) => setNewValue(e.target.value)}
                className="flex-1"
              />
              <Button size="sm" onClick={handleUpdateValue}>
                حفظ
              </Button>
              <Button size="sm" variant="outline" onClick={() => setIsEditing(false)}>
                إلغاء
              </Button>
            </div>
          ) : (
            <div className="flex justify-between items-center">
              <span className="text-2xl font-bold" style={{ color: kpi.color_code }}>
                {formatValue(kpi.current_value, kpi.unit)}
              </span>
              {!kpi.is_automated && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsEditing(true)}
                >
                  <Edit className="h-4 w-4" />
                </Button>
              )}
            </div>
          )}
        </div>

        {/* Target Progress */}
        {kpi.target_value && (
          <div>
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>الهدف: {formatValue(kpi.target_value, kpi.unit)}</span>
              <span>{targetAchievement.toFixed(1)}%</span>
            </div>
            <Progress value={Math.min(targetAchievement, 100)} className="h-2" />
          </div>
        )}

        {/* Growth */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {kpi.growth_percentage >= 0 ? (
              <TrendingUp className="h-4 w-4 text-green-600" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-600" />
            )}
            <span className={`text-sm font-medium ${
              kpi.growth_percentage >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {kpi.growth_percentage >= 0 ? '+' : ''}{kpi.growth_percentage.toFixed(1)}%
            </span>
          </div>
          
          <div className="text-sm text-gray-500">
            {kpi.last_calculated ? (
              `آخر تحديث: ${new Date(kpi.last_calculated).toLocaleDateString('ar-EG')}`
            ) : (
              'لم يتم الحساب بعد'
            )}
          </div>
        </div>

        {/* Automation Badge */}
        {kpi.is_automated && (
          <Badge variant="outline" className="w-fit">
            <Calculator className="h-3 w-3 ml-1" />
            تلقائي
          </Badge>
        )}
      </CardContent>
    </Card>
  );
};

export default function KPIMetrics() {
  const [kpis, setKpis] = useState<KPIMetric[]>([]);
  const [loading, setLoading] = useState(true);
  const [calculating, setCalculating] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadKPIs();
  }, []);

  const loadKPIs = async () => {
    try {
      setLoading(true);
      const response = await businessIntelligenceApi.getKPIs();
      setKpis(response.data);
    } catch (error) {
      console.error('Error loading KPIs:', error);
      toast({
        title: "خطأ",
        description: "فشل في تحميل مؤشرات الأداء",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCalculateAll = async () => {
    try {
      setCalculating(true);
      const response = await businessIntelligenceApi.calculateKPIs();
      await loadKPIs(); // Reload to get updated values
      toast({
        title: "تم التحديث",
        description: response.data.message,
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في حساب مؤشرات الأداء",
        variant: "destructive",
      });
    } finally {
      setCalculating(false);
    }
  };

  const handleEdit = (kpi: KPIMetric) => {
    // TODO: Open edit modal
    console.log('Edit KPI:', kpi);
  };

  const handleDelete = async (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذا المؤشر؟')) {
      try {
        await businessIntelligenceApi.deleteKPI(id);
        setKpis(kpis.filter(kpi => kpi.id !== id));
        toast({
          title: "تم الحذف",
          description: "تم حذف المؤشر بنجاح",
        });
      } catch (error) {
        toast({
          title: "خطأ",
          description: "فشل في حذف المؤشر",
          variant: "destructive",
        });
      }
    }
  };

  const handleToggleVisibility = async (id: number, visible: boolean) => {
    try {
      await businessIntelligenceApi.updateKPI(id, { is_visible: visible });
      setKpis(kpis.map(kpi => 
        kpi.id === id ? { ...kpi, is_visible: visible } : kpi
      ));
      toast({
        title: "تم التحديث",
        description: `تم ${visible ? 'إظهار' : 'إخفاء'} المؤشر`,
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث المؤشر",
        variant: "destructive",
      });
    }
  };

  const handleUpdateValue = async (id: number, value: number) => {
    try {
      await businessIntelligenceApi.updateKPIValue(id, value);
      setKpis(kpis.map(kpi => 
        kpi.id === id ? { ...kpi, current_value: value } : kpi
      ));
      toast({
        title: "تم التحديث",
        description: "تم تحديث قيمة المؤشر بنجاح",
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث قيمة المؤشر",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">مؤشرات الأداء الرئيسية</h3>
          <p className="text-sm text-gray-600">
            {kpis.length} مؤشر • {kpis.filter(kpi => kpi.is_visible).length} مرئي
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            onClick={handleCalculateAll}
            disabled={calculating}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 ml-2 ${calculating ? 'animate-spin' : ''}`} />
            حساب جميع المؤشرات
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 ml-2" />
            إضافة مؤشر جديد
          </Button>
        </div>
      </div>

      {/* KPI Grid */}
      {kpis.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مؤشرات أداء</h3>
            <p className="text-gray-600 mb-4">ابدأ بإضافة مؤشرات الأداء الرئيسية لشركتك</p>
            <Button>
              <Plus className="h-4 w-4 ml-2" />
              إضافة مؤشر جديد
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {kpis.map((kpi) => (
            <KPICard
              key={kpi.id}
              kpi={kpi}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onToggleVisibility={handleToggleVisibility}
              onUpdateValue={handleUpdateValue}
            />
          ))}
        </div>
      )}
    </div>
  );
}
