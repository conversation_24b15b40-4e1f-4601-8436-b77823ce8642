"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Activity, 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  Clock, 
  AlertCircle,
  CheckCircle,
  Settings,
  Pause,
  Play,
  Signal
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { businessIntelligenceApi } from '@/lib/api/business-intelligence';

interface RealTimeUpdatesProps {
  onDataUpdate?: (data: any) => void;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface ConnectionStatus {
  isConnected: boolean;
  lastUpdate: Date | null;
  updateCount: number;
  errors: number;
}

interface UpdateSettings {
  enabled: boolean;
  interval: number;
  autoRetry: boolean;
  maxRetries: number;
}

const refreshIntervals = [
  { value: 5000, label: '5 ثوان' },
  { value: 10000, label: '10 ثوان' },
  { value: 30000, label: '30 ثانية' },
  { value: 60000, label: 'دقيقة واحدة' },
  { value: 300000, label: '5 دقائق' },
  { value: 600000, label: '10 دقائق' }
];

export default function RealTimeUpdates({ 
  onDataUpdate, 
  autoRefresh = true, 
  refreshInterval = 30000 
}: RealTimeUpdatesProps) {
  const { toast } = useToast();
  
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    isConnected: false,
    lastUpdate: null,
    updateCount: 0,
    errors: 0
  });

  const [settings, setSettings] = useState<UpdateSettings>({
    enabled: autoRefresh,
    interval: refreshInterval,
    autoRetry: true,
    maxRetries: 3
  });

  const [isLoading, setIsLoading] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null);

  // Fetch latest data
  const fetchData = useCallback(async () => {
    if (!settings.enabled) return;

    setIsLoading(true);
    try {
      const response = await businessIntelligenceApi.getExecutiveDashboard();
      
      setConnectionStatus(prev => ({
        ...prev,
        isConnected: true,
        lastUpdate: new Date(),
        updateCount: prev.updateCount + 1,
      }));

      setRetryCount(0);

      if (onDataUpdate) {
        onDataUpdate(response.data);
      }

    } catch (error) {
      console.error('Real-time update failed:', error);
      
      setConnectionStatus(prev => ({
        ...prev,
        isConnected: false,
        errors: prev.errors + 1
      }));

      if (settings.autoRetry && retryCount < settings.maxRetries) {
        setRetryCount(prev => prev + 1);
        setTimeout(() => fetchData(), 5000); // Retry after 5 seconds
      } else {
        toast({
          title: "خطأ في التحديث التلقائي",
          description: "فشل في تحديث البيانات. يرجى المحاولة يدوياً.",
          variant: "destructive",
        });
      }
    } finally {
      setIsLoading(false);
    }
  }, [settings, retryCount, onDataUpdate, toast]);

  // Setup interval
  useEffect(() => {
    if (settings.enabled && settings.interval > 0) {
      const id = setInterval(fetchData, settings.interval);
      setIntervalId(id);
      
      // Initial fetch
      fetchData();

      return () => {
        if (id) clearInterval(id);
      };
    } else {
      if (intervalId) {
        clearInterval(intervalId);
        setIntervalId(null);
      }
    }
  }, [settings.enabled, settings.interval, fetchData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [intervalId]);

  const handleToggleUpdates = (enabled: boolean) => {
    setSettings(prev => ({ ...prev, enabled }));
    
    if (!enabled && intervalId) {
      clearInterval(intervalId);
      setIntervalId(null);
      setConnectionStatus(prev => ({ ...prev, isConnected: false }));
    }
  };

  const handleIntervalChange = (interval: string) => {
    setSettings(prev => ({ ...prev, interval: parseInt(interval) }));
  };

  const handleManualRefresh = () => {
    fetchData();
  };

  const handleToggleAutoRetry = (autoRetry: boolean) => {
    setSettings(prev => ({ ...prev, autoRetry }));
  };

  const getConnectionStatusColor = () => {
    if (!settings.enabled) return 'text-gray-500';
    if (connectionStatus.isConnected) return 'text-green-500';
    return 'text-red-500';
  };

  const getConnectionStatusIcon = () => {
    if (!settings.enabled) return <Pause className="h-4 w-4" />;
    if (isLoading) return <RefreshCw className="h-4 w-4 animate-spin" />;
    if (connectionStatus.isConnected) return <Wifi className="h-4 w-4" />;
    return <WifiOff className="h-4 w-4" />;
  };

  const formatLastUpdate = () => {
    if (!connectionStatus.lastUpdate) return 'لم يتم التحديث بعد';
    
    const now = new Date();
    const diff = now.getTime() - connectionStatus.lastUpdate.getTime();
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) return `منذ ${hours} ساعة`;
    if (minutes > 0) return `منذ ${minutes} دقيقة`;
    return `منذ ${seconds} ثانية`;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            <CardTitle className="text-lg">التحديثات المباشرة</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <div className={`flex items-center gap-1 ${getConnectionStatusColor()}`}>
              {getConnectionStatusIcon()}
              <span className="text-sm font-medium">
                {!settings.enabled ? 'متوقف' : connectionStatus.isConnected ? 'متصل' : 'غير متصل'}
              </span>
            </div>
          </div>
        </div>
        <CardDescription>
          إعدادات التحديث التلقائي للبيانات والتحليلات
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Main Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 space-x-reverse">
            <Switch
              id="enable-updates"
              checked={settings.enabled}
              onCheckedChange={handleToggleUpdates}
            />
            <Label htmlFor="enable-updates" className="flex items-center gap-2">
              {settings.enabled ? <Play className="h-4 w-4" /> : <Pause className="h-4 w-4" />}
              تفعيل التحديث التلقائي
            </Label>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={handleManualRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ml-2 ${isLoading ? 'animate-spin' : ''}`} />
            تحديث يدوي
          </Button>
        </div>

        {/* Settings */}
        {settings.enabled && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="refresh-interval">فترة التحديث</Label>
              <Select value={settings.interval.toString()} onValueChange={handleIntervalChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {refreshIntervals.map((interval) => (
                    <SelectItem key={interval.value} value={interval.value.toString()}>
                      {interval.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Switch
                  id="auto-retry"
                  checked={settings.autoRetry}
                  onCheckedChange={handleToggleAutoRetry}
                />
                <Label htmlFor="auto-retry" className="flex items-center gap-2">
                  <RefreshCw className="h-4 w-4" />
                  إعادة المحاولة التلقائية
                </Label>
              </div>
            </div>
          </div>
        )}

        {/* Status Information */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Clock className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-600">آخر تحديث</span>
            </div>
            <p className="text-xs text-blue-700">{formatLastUpdate()}</p>
          </div>

          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="flex items-center justify-center gap-1 mb-1">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-600">التحديثات</span>
            </div>
            <p className="text-xs text-green-700">{connectionStatus.updateCount}</p>
          </div>

          <div className="text-center p-3 bg-red-50 rounded-lg">
            <div className="flex items-center justify-center gap-1 mb-1">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm font-medium text-red-600">الأخطاء</span>
            </div>
            <p className="text-xs text-red-700">{connectionStatus.errors}</p>
          </div>

          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Signal className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium text-purple-600">الحالة</span>
            </div>
            <Badge 
              variant={connectionStatus.isConnected ? 'default' : 'destructive'}
              className="text-xs"
            >
              {connectionStatus.isConnected ? 'نشط' : 'غير نشط'}
            </Badge>
          </div>
        </div>

        {/* Error Information */}
        {connectionStatus.errors > 0 && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm font-medium text-red-600">تحذيرات الاتصال</span>
            </div>
            <p className="text-xs text-red-700">
              تم رصد {connectionStatus.errors} خطأ في الاتصال. 
              {settings.autoRetry && retryCount > 0 && ` جاري المحاولة ${retryCount}/${settings.maxRetries}`}
            </p>
          </div>
        )}

        {/* Performance Tips */}
        <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Settings className="h-4 w-4 text-gray-600" />
            <span className="text-sm font-medium text-gray-600">نصائح الأداء</span>
          </div>
          <ul className="text-xs text-gray-700 space-y-1">
            <li>• استخدم فترات تحديث أطول (5-10 دقائق) لتوفير استهلاك البيانات</li>
            <li>• قم بإيقاف التحديث التلقائي عند عدم الحاجة إليه</li>
            <li>• تأكد من استقرار الاتصال بالإنترنت لتجنب الأخطاء</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
