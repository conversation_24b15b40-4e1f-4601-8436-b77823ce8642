"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, 
  PieChart, 
  TrendingUp, 
  Table, 
  Gauge,
  Edit, 
  Trash2, 
  Plus,
  Eye,
  EyeOff,
  Move
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

import { businessIntelligenceApi, DashboardWidget } from '@/lib/api/business-intelligence';

const getWidgetIcon = (widgetType: string) => {
  switch (widgetType) {
    case 'kpi_card':
      return <TrendingUp className="h-5 w-5" />;
    case 'chart':
      return <BarChart3 className="h-5 w-5" />;
    case 'table':
      return <Table className="h-5 w-5" />;
    case 'progress_bar':
      return <TrendingUp className="h-5 w-5" />;
    case 'gauge':
      return <Gauge className="h-5 w-5" />;
    default:
      return <BarChart3 className="h-5 w-5" />;
  }
};

const getChartIcon = (chartType?: string) => {
  switch (chartType) {
    case 'line':
      return <TrendingUp className="h-4 w-4" />;
    case 'bar':
      return <BarChart3 className="h-4 w-4" />;
    case 'pie':
      return <PieChart className="h-4 w-4" />;
    case 'doughnut':
      return <PieChart className="h-4 w-4" />;
    default:
      return <BarChart3 className="h-4 w-4" />;
  }
};

interface WidgetCardProps {
  widget: DashboardWidget;
  onEdit: (widget: DashboardWidget) => void;
  onDelete: (id: number) => void;
  onToggleVisibility: (id: number, visible: boolean) => void;
}

const WidgetCard: React.FC<WidgetCardProps> = ({ 
  widget, 
  onEdit, 
  onDelete, 
  onToggleVisibility 
}) => {
  return (
    <Card className={`${!widget.is_visible ? 'opacity-50' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-2">
            {getWidgetIcon(widget.widget_type)}
            <div>
              <CardTitle className="text-lg font-semibold">{widget.title}</CardTitle>
              <CardDescription className="text-sm text-gray-600">
                {widget.widget_type_display}
                {widget.chart_type && (
                  <span className="flex items-center gap-1 mt-1">
                    {getChartIcon(widget.chart_type)}
                    {widget.chart_type_display}
                  </span>
                )}
              </CardDescription>
            </div>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onToggleVisibility(widget.id, !widget.is_visible)}
            >
              {widget.is_visible ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(widget)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(widget.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Dashboard */}
        <div>
          <span className="text-sm font-medium text-gray-600">لوحة التحكم:</span>
          <span className="text-sm text-gray-900 ml-2">{widget.dashboard_name}</span>
        </div>

        {/* Position and Size */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">الموضع:</span>
            <span className="font-medium ml-1">({widget.position_x}, {widget.position_y})</span>
          </div>
          <div>
            <span className="text-gray-600">الحجم:</span>
            <span className="font-medium ml-1">{widget.width} × {widget.height}</span>
          </div>
        </div>

        {/* Data Source */}
        <div>
          <span className="text-sm font-medium text-gray-600">مصدر البيانات:</span>
          <span className="text-sm text-gray-900 ml-2">{widget.data_source}</span>
        </div>

        {/* Refresh Interval */}
        <div>
          <span className="text-sm font-medium text-gray-600">فترة التحديث:</span>
          <span className="text-sm text-gray-900 ml-2">{widget.refresh_interval} ثانية</span>
        </div>

        {/* Created Date */}
        <div className="text-sm text-gray-600">
          تم الإنشاء: {new Date(widget.created_at).toLocaleDateString('ar-EG')}
        </div>

        {/* Actions */}
        <div className="flex gap-2 pt-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEdit(widget)}
            className="flex-1"
          >
            <Edit className="h-4 w-4 ml-2" />
            تعديل
          </Button>
          <Button
            variant="outline"
            size="sm"
          >
            <Move className="h-4 w-4 ml-2" />
            نقل
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default function DashboardWidgets() {
  const [widgets, setWidgets] = useState<DashboardWidget[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadWidgets();
  }, []);

  const loadWidgets = async () => {
    try {
      setLoading(true);
      const response = await businessIntelligenceApi.getWidgets();
      setWidgets(response.data.results);
    } catch (error) {
      console.error('Error loading widgets:', error);
      toast({
        title: "خطأ",
        description: "فشل في تحميل الودجتات",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (widget: DashboardWidget) => {
    // TODO: Open edit modal
    console.log('Edit widget:', widget);
  };

  const handleDelete = async (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذا الودجت؟')) {
      try {
        await businessIntelligenceApi.deleteWidget(id);
        setWidgets(widgets.filter(widget => widget.id !== id));
        toast({
          title: "تم الحذف",
          description: "تم حذف الودجت بنجاح",
        });
      } catch (error) {
        toast({
          title: "خطأ",
          description: "فشل في حذف الودجت",
          variant: "destructive",
        });
      }
    }
  };

  const handleToggleVisibility = async (id: number, visible: boolean) => {
    try {
      await businessIntelligenceApi.updateWidget(id, { is_visible: visible });
      setWidgets(widgets.map(widget => 
        widget.id === id ? { ...widget, is_visible: visible } : widget
      ));
      toast({
        title: "تم التحديث",
        description: `تم ${visible ? 'إظهار' : 'إخفاء'} الودجت`,
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث الودجت",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">ودجتات لوحة التحكم</h3>
          <p className="text-sm text-gray-600">
            {widgets.length} ودجت • {widgets.filter(w => w.is_visible).length} مرئي
          </p>
        </div>
        <Button size="sm">
          <Plus className="h-4 w-4 ml-2" />
          إضافة ودجت جديد
        </Button>
      </div>

      {/* Widgets Grid */}
      {widgets.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد ودجتات</h3>
            <p className="text-gray-600 mb-4">ابدأ بإضافة ودجتات لتخصيص لوحة التحكم</p>
            <Button>
              <Plus className="h-4 w-4 ml-2" />
              إضافة ودجت جديد
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {widgets.map((widget) => (
            <WidgetCard
              key={widget.id}
              widget={widget}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onToggleVisibility={handleToggleVisibility}
            />
          ))}
        </div>
      )}
    </div>
  );
}
