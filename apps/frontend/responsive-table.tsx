"use client"

import { useState } from "react"
import { ChevronDown, ChevronLeft, ChevronRight, Download, Filter, MoreHorizontal, Search } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// بيانات المنتجات للعرض في الجدول
const products = [
  {
    id: "PRD-001",
    name: "هاتف ذكي - موديل X",
    category: "الإلكترونيات",
    price: 1299.99,
    stock: 45,
    rating: 4.8,
    sales: 1245,
    date: "2024-05-15",
    status: "متوفر",
    supplier: "شركة التقنية المتطورة",
  },
  {
    id: "PRD-002",
    name: "حاسوب محمول برو",
    category: "الإلكترونيات",
    price: 2499.99,
    stock: 23,
    rating: 4.9,
    sales: 876,
    date: "2024-05-10",
    status: "متوفر",
    supplier: "شركة التكنولوجيا العالمية",
  },
  {
    id: "PRD-003",
    name: "سماعات لاسلكية",
    category: "الإلكترونيات",
    price: 199.99,
    stock: 78,
    rating: 4.5,
    sales: 2345,
    date: "2024-05-08",
    status: "متوفر",
    supplier: "شركة الصوتيات المتقدمة",
  },
  {
    id: "PRD-004",
    name: "كاميرا احترافية",
    category: "التصوير",
    price: 899.99,
    stock: 12,
    rating: 4.7,
    sales: 432,
    date: "2024-05-05",
    status: "منخفض",
    supplier: "شركة التصوير الرقمي",
  },
  {
    id: "PRD-005",
    name: "شاشة عرض 4K",
    category: "الإلكترونيات",
    price: 599.99,
    stock: 34,
    rating: 4.6,
    sales: 765,
    date: "2024-05-01",
    status: "متوفر",
    supplier: "شركة العرض المتطورة",
  },
  {
    id: "PRD-006",
    name: "طابعة ليزر ملونة",
    category: "المكتب",
    price: 349.99,
    stock: 0,
    rating: 4.3,
    sales: 543,
    date: "2024-04-28",
    status: "غير متوفر",
    supplier: "شركة الطباعة الرقمية",
  },
  {
    id: "PRD-007",
    name: "ساعة ذكية",
    category: "الإلكترونيات",
    price: 299.99,
    stock: 56,
    rating: 4.4,
    sales: 1876,
    date: "2024-04-25",
    status: "متوفر",
    supplier: "شركة الأجهزة الذكية",
  },
]

// تعريف أعمدة الجدول
const columns = [
  { id: "id", label: "رقم المنتج", priority: 1 },
  { id: "name", label: "اسم المنتج", priority: 1 },
  { id: "category", label: "الفئة", priority: 2 },
  { id: "price", label: "السعر", priority: 1 },
  { id: "stock", label: "المخزون", priority: 2 },
  { id: "rating", label: "التقييم", priority: 3 },
  { id: "sales", label: "المبيعات", priority: 2 },
  { id: "date", label: "تاريخ الإضافة", priority: 3 },
  { id: "status", label: "الحالة", priority: 1 },
  { id: "supplier", label: "المورد", priority: 3 },
]

export default function ResponsiveTable() {
  const [activeProduct, setActiveProduct] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<"table" | "cards" | "compact">("table")

  // تنسيق السعر
  const formatPrice = (price: number) => {
    return `${price.toLocaleString()} ر.س`
  }

  // تنسيق التاريخ
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr)
    return new Intl.DateTimeFormat("ar-SA", { year: "numeric", month: "short", day: "numeric" }).format(date)
  }

  // تحديد لون الحالة
  const getStatusColor = (status: string) => {
    switch (status) {
      case "متوفر":
        return "bg-green-100 text-green-800"
      case "منخفض":
        return "bg-yellow-100 text-yellow-800"
      case "غير متوفر":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  // عرض الجدول التقليدي (للديسكتوب)
  const DesktopTable = () => (
    <div className="w-full overflow-auto">
      <Table dir="rtl">
        <TableHeader>
          <TableRow>
            {columns.map((column) => (
              <TableHead key={column.id} className="whitespace-nowrap font-bold">
                {column.label}
              </TableHead>
            ))}
            <TableHead className="w-[80px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {products.map((product) => (
            <TableRow key={product.id}>
              <TableCell className="font-medium">{product.id}</TableCell>
              <TableCell>{product.name}</TableCell>
              <TableCell>{product.category}</TableCell>
              <TableCell>{formatPrice(product.price)}</TableCell>
              <TableCell>{product.stock}</TableCell>
              <TableCell>
                <div className="flex items-center">
                  <span className="text-yellow-500 ml-1">★</span>
                  {product.rating}
                </div>
              </TableCell>
              <TableCell>{product.sales.toLocaleString()}</TableCell>
              <TableCell>{formatDate(product.date)}</TableCell>
              <TableCell>
                <Badge className={getStatusColor(product.status)}>{product.status}</Badge>
              </TableCell>
              <TableCell className="max-w-[150px] truncate" title={product.supplier}>
                {product.supplier}
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>تعديل</DropdownMenuItem>
                    <DropdownMenuItem>عرض التفاصيل</DropdownMenuItem>
                    <DropdownMenuItem className="text-red-600">حذف</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )

  // عرض البطاقات (للموبايل)
  const MobileCards = () => (
    <div className="space-y-4">
      {products.map((product) => (
        <Card key={product.id} className="overflow-hidden">
          <div className="p-4">
            <div className="flex justify-between items-start mb-3">
              <div>
                <h3 className="font-bold text-lg">{product.name}</h3>
                <p className="text-sm text-gray-500">{product.id}</p>
              </div>
              <Badge className={getStatusColor(product.status)}>{product.status}</Badge>
            </div>

            <div className="grid grid-cols-2 gap-3 mb-3">
              <div>
                <p className="text-sm text-gray-500">السعر</p>
                <p className="font-semibold">{formatPrice(product.price)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">المخزون</p>
                <p className="font-semibold">{product.stock}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">الفئة</p>
                <p className="font-semibold">{product.category}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">المبيعات</p>
                <p className="font-semibold">{product.sales.toLocaleString()}</p>
              </div>
            </div>

            <Button
              variant="outline"
              className="w-full text-sm"
              onClick={() => setActiveProduct(activeProduct === product.id ? null : product.id)}
            >
              {activeProduct === product.id ? "إخفاء التفاصيل" : "عرض المزيد"}
              <ChevronDown
                className={`mr-1 h-4 w-4 transition-transform ${activeProduct === product.id ? "rotate-180" : ""}`}
              />
            </Button>

            {activeProduct === product.id && (
              <div className="mt-3 pt-3 border-t border-gray-100 grid grid-cols-2 gap-3">
                <div>
                  <p className="text-sm text-gray-500">التقييم</p>
                  <div className="flex items-center">
                    <span className="text-yellow-500 ml-1">★</span>
                    <span>{product.rating}</span>
                  </div>
                </div>
                <div>
                  <p className="text-sm text-gray-500">تاريخ الإضافة</p>
                  <p className="font-semibold">{formatDate(product.date)}</p>
                </div>
                <div className="col-span-2">
                  <p className="text-sm text-gray-500">المورد</p>
                  <p className="font-semibold">{product.supplier}</p>
                </div>
                <div className="col-span-2 flex gap-2 mt-2">
                  <Button size="sm" variant="outline" className="flex-1">
                    تعديل
                  </Button>
                  <Button size="sm" variant="destructive" className="flex-1">
                    حذف
                  </Button>
                </div>
              </div>
            )}
          </div>
        </Card>
      ))}
    </div>
  )

  // عرض الجدول المدمج (للتابلت)
  const TabletCompactTable = () => (
    <div className="w-full overflow-auto">
      <Table dir="rtl">
        <TableHeader>
          <TableRow>
            {columns
              .filter((col) => col.priority <= 2)
              .map((column) => (
                <TableHead key={column.id} className="whitespace-nowrap font-bold">
                  {column.label}
                </TableHead>
              ))}
            <TableHead className="w-[80px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {products.map((product) => (
            <TableRow key={product.id}>
              <TableCell className="font-medium">{product.id}</TableCell>
              <TableCell>{product.name}</TableCell>
              <TableCell>{product.category}</TableCell>
              <TableCell>{formatPrice(product.price)}</TableCell>
              <TableCell>{product.stock}</TableCell>
              <TableCell>{product.sales.toLocaleString()}</TableCell>
              <TableCell>
                <Badge className={getStatusColor(product.status)}>{product.status}</Badge>
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>تعديل</DropdownMenuItem>
                    <DropdownMenuItem>عرض التفاصيل</DropdownMenuItem>
                    <DropdownMenuItem className="text-red-600">حذف</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )

  return (
    <div className="container mx-auto py-8 px-4 font-arabic" dir="rtl">
      <div className="mb-8">
        <h1 className="text-2xl font-bold mb-2">إدارة المنتجات</h1>
        <p className="text-gray-500">عرض وإدارة جميع المنتجات في النظام</p>
      </div>

      {/* أدوات التحكم */}
      <div className="flex flex-col md:flex-row gap-4 mb-6 justify-between">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input placeholder="البحث عن منتج..." className="w-full sm:w-64 pr-10" />
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            تصفية
          </Button>
        </div>

        <div className="flex gap-2">
          {/* أزرار تبديل طريقة العرض */}
          <div className="hidden md:flex border rounded-md overflow-hidden">
            <Button
              variant={viewMode === "table" ? "default" : "ghost"}
              size="sm"
              className="rounded-none border-0"
              onClick={() => setViewMode("table")}
            >
              جدول كامل
            </Button>
            <Button
              variant={viewMode === "compact" ? "default" : "ghost"}
              size="sm"
              className="rounded-none border-0"
              onClick={() => setViewMode("compact")}
            >
              جدول مدمج
            </Button>
            <Button
              variant={viewMode === "cards" ? "default" : "ghost"}
              size="sm"
              className="rounded-none border-0"
              onClick={() => setViewMode("cards")}
            >
              بطاقات
            </Button>
          </div>

          <Button variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            تصدير
          </Button>
        </div>
      </div>

      {/* التنقل بين الصفحات */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-500">عرض 1-7 من 42 منتج</p>
          <div className="flex items-center gap-1">
            <Button variant="outline" size="icon" className="h-8 w-8">
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" className="h-8 w-8">
              1
            </Button>
            <Button variant="ghost" size="sm" className="h-8 w-8">
              2
            </Button>
            <Button variant="ghost" size="sm" className="h-8 w-8">
              3
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8">
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* عرض الجدول حسب حجم الشاشة */}
      <div className="hidden lg:block">
        <DesktopTable />
      </div>

      <div className="hidden md:block lg:hidden">
        <TabletCompactTable />
      </div>

      <div className="md:hidden">
        <MobileCards />
      </div>

      {/* شرح طرق العرض المختلفة */}
      <div className="mt-12 bg-blue-50 p-4 rounded-lg">
        <h2 className="text-lg font-bold mb-2">طرق عرض الجدول المختلفة</h2>
        <ul className="list-disc list-inside space-y-2 text-sm">
          <li>
            <span className="font-bold">الديسكتوب (شاشات كبيرة):</span> عرض جدول كامل مع جميع الأعمدة العشرة وإمكانية
            التمرير الأفقي عند الحاجة.
          </li>
          <li>
            <span className="font-bold">التابلت (شاشات متوسطة):</span> عرض جدول مدمج يحتوي على الأعمدة ذات الأولوية
            العالية فقط (7 أعمدة).
          </li>
          <li>
            <span className="font-bold">الموبايل (شاشات صغيرة):</span> عرض بطاقات عمودية مع إمكانية توسيع كل بطاقة لرؤية
            جميع البيانات.
          </li>
        </ul>
      </div>
    </div>
  )
}
