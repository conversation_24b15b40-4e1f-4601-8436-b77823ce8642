'use client';

import { useState, useCallback } from 'react';
import { paymobAPI, PaymobPaymentIntent, PaymobPaymentStatus, PaymobGatewayInfo } from '@/lib/api/paymob';
import { showToast } from '@/lib/toast';

interface UsePaymobOptions {
  onPaymentSuccess?: (data: any) => void;
  onPaymentError?: (error: any) => void;
  onPaymentCancel?: () => void;
}

export function usePaymob(options: UsePaymobOptions = {}) {
  const [loading, setLoading] = useState(false);
  const [paymentIntent, setPaymentIntent] = useState<PaymobPaymentIntent | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<PaymobPaymentStatus | null>(null);
  const [availableGateways, setAvailableGateways] = useState<PaymobGatewayInfo[]>([]);
  const [error, setError] = useState<string | null>(null);

  /**
   * Create payment intent for an invoice
   */
  const createPaymentIntent = useCallback(async (invoiceId: string | number) => {
    try {
      setLoading(true);
      setError(null);
      
      const intent = await paymobAPI.createPaymentIntent(invoiceId);
      setPaymentIntent(intent);
      
      showToast.success('تم إنشاء رابط الدفع بنجاح');
      return intent;
    } catch (err: any) {
      const errorMessage = err.message || 'فشل في إنشاء رابط الدفع';
      setError(errorMessage);
      showToast.error(errorMessage);
      options.onPaymentError?.(err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [options]);

  /**
   * Get payment status for a transaction
   */
  const getPaymentStatus = useCallback(async (transactionId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const status = await paymobAPI.getPaymentStatus(transactionId);
      setPaymentStatus(status);
      
      return status;
    } catch (err: any) {
      const errorMessage = err.message || 'فشل في الحصول على حالة الدفع';
      setError(errorMessage);
      showToast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Load available payment gateways
   */
  const loadAvailableGateways = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const gateways = await paymobAPI.getAvailableGateways();
      setAvailableGateways(gateways);
      
      return gateways;
    } catch (err: any) {
      const errorMessage = err.message || 'فشل في تحميل بوابات الدفع';
      setError(errorMessage);
      showToast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Process payment using iframe
   */
  const processPaymentIframe = useCallback((paymentUrl?: string) => {
    const url = paymentUrl || paymentIntent?.payment_url;
    if (!url) {
      showToast.error('رابط الدفع غير متوفر');
      return;
    }

    paymobAPI.openPaymentIframe(url, {
      onSuccess: (data) => {
        setPaymentStatus({ status: 'success', ...data });
        showToast.success('تم الدفع بنجاح!');
        options.onPaymentSuccess?.(data);
      },
      onError: (error) => {
        setPaymentStatus({ status: 'failed', ...error });
        showToast.error('فشل في عملية الدفع');
        options.onPaymentError?.(error);
      },
      onCancel: () => {
        setPaymentStatus({ status: 'cancelled' });
        showToast.info('تم إلغاء عملية الدفع');
        options.onPaymentCancel?.();
      }
    });
  }, [paymentIntent, options]);

  /**
   * Process payment using new window
   */
  const processPaymentWindow = useCallback((paymentUrl?: string) => {
    const url = paymentUrl || paymentIntent?.payment_url;
    if (!url) {
      showToast.error('رابط الدفع غير متوفر');
      return;
    }

    paymobAPI.openPaymentWindow(url, {
      onSuccess: (data) => {
        setPaymentStatus({ status: 'success', ...data });
        showToast.success('تم الدفع بنجاح!');
        options.onPaymentSuccess?.(data);
      },
      onError: (error) => {
        setPaymentStatus({ status: 'failed', ...error });
        showToast.error('فشل في عملية الدفع');
        options.onPaymentError?.(error);
      },
      onCancel: () => {
        setPaymentStatus({ status: 'cancelled' });
        showToast.info('تم إلغاء عملية الدفع');
        options.onPaymentCancel?.();
      }
    });
  }, [paymentIntent, options]);

  /**
   * Redirect to payment page
   */
  const redirectToPayment = useCallback((paymentUrl?: string) => {
    const url = paymentUrl || paymentIntent?.payment_url;
    if (!url) {
      showToast.error('رابط الدفع غير متوفر');
      return;
    }

    paymobAPI.redirectToPayment(url);
  }, [paymentIntent]);

  /**
   * Reset payment state
   */
  const resetPayment = useCallback(() => {
    setPaymentIntent(null);
    setPaymentStatus(null);
    setError(null);
  }, []);

  /**
   * Check if payment is in progress
   */
  const isPaymentInProgress = paymentStatus?.status === 'pending';

  /**
   * Check if payment was successful
   */
  const isPaymentSuccessful = paymentStatus?.status === 'success';

  /**
   * Check if payment failed
   */
  const isPaymentFailed = paymentStatus?.status === 'failed';

  /**
   * Check if payment was cancelled
   */
  const isPaymentCancelled = paymentStatus?.status === 'cancelled';

  /**
   * Get formatted payment amount
   */
  const getFormattedAmount = useCallback((amountCents?: number) => {
    if (!amountCents && !paymentIntent?.amount_cents) return null;
    const amount = amountCents || paymentIntent?.amount_cents || 0;
    return paymobAPI.parseAmount(amount);
  }, [paymentIntent]);

  /**
   * Get payment method display name
   */
  const getPaymentMethodName = useCallback((method: string) => {
    return paymobAPI.getPaymentMethodName(method);
  }, []);

  /**
   * Get payment status display name
   */
  const getPaymentStatusName = useCallback((status: string) => {
    return paymobAPI.getPaymentStatusName(status);
  }, []);

  return {
    // State
    loading,
    paymentIntent,
    paymentStatus,
    availableGateways,
    error,

    // Actions
    createPaymentIntent,
    getPaymentStatus,
    loadAvailableGateways,
    processPaymentIframe,
    processPaymentWindow,
    redirectToPayment,
    resetPayment,

    // Computed values
    isPaymentInProgress,
    isPaymentSuccessful,
    isPaymentFailed,
    isPaymentCancelled,

    // Utilities
    getFormattedAmount,
    getPaymentMethodName,
    getPaymentStatusName,
  };
}

export default usePaymob;
