<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم {{ invoice.invoice_number }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            line-height: 1.6;
            color: #333;
            background-color: #f8fafc;
            margin: 0;
            padding: 20px;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        .content {
            padding: 30px;
        }
        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
            color: #374151;
        }
        .invoice-summary {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        .invoice-summary h3 {
            margin: 0 0 15px 0;
            color: #6366f1;
            font-size: 18px;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
        }
        .summary-row.total {
            border-top: 2px solid #6366f1;
            padding-top: 10px;
            margin-top: 15px;
            font-weight: bold;
            font-size: 16px;
        }
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            margin: 10px 0;
        }
        .status-draft { background: #f3f4f6; color: #374151; }
        .status-sent { background: #dbeafe; color: #1d4ed8; }
        .status-paid { background: #d1fae5; color: #065f46; }
        .status-overdue { background: #fee2e2; color: #dc2626; }
        .payment-info {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        .payment-info h4 {
            margin: 0 0 10px 0;
            color: #92400e;
        }
        .cta-button {
            display: inline-block;
            background: #6366f1;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
        }
        .footer {
            background: #f8fafc;
            padding: 25px 30px;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
            border-top: 1px solid #e2e8f0;
        }
        .footer p {
            margin: 5px 0;
        }
        .contact-info {
            margin: 20px 0;
            padding: 15px;
            background: #f0f9ff;
            border-radius: 8px;
            border: 1px solid #0ea5e9;
        }
        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            .content {
                padding: 20px;
            }
            .header {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>{{ company_name }}</h1>
            <p>فاتورة رقم {{ invoice.invoice_number }}</p>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="greeting">
                مرحباً {{ client.name }}،
            </div>

            <p>نأمل أن تكون بخير. نرسل إليك فاتورة الخدمات المقدمة لك.</p>

            {% if custom_message %}
            <div style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 15px; margin: 20px 0;">
                <p style="margin: 0; color: #0c4a6e;">{{ custom_message }}</p>
            </div>
            {% endif %}

            <!-- Invoice Summary -->
            <div class="invoice-summary">
                <h3>ملخص الفاتورة</h3>
                <div class="summary-row">
                    <span>رقم الفاتورة:</span>
                    <span><strong>{{ invoice.invoice_number }}</strong></span>
                </div>
                <div class="summary-row">
                    <span>تاريخ الفاتورة:</span>
                    <span>{{ invoice.invoice_date|date:"d/m/Y" }}</span>
                </div>
                {% if invoice.due_date %}
                <div class="summary-row">
                    <span>تاريخ الاستحقاق:</span>
                    <span>{{ invoice.due_date|date:"d/m/Y" }}</span>
                </div>
                {% endif %}
                <div class="summary-row">
                    <span>الحالة:</span>
                    <span><span class="status-badge status-{{ invoice.status }}">{{ invoice.get_status_display }}</span></span>
                </div>
                {% if invoice.project %}
                <div class="summary-row">
                    <span>المشروع:</span>
                    <span>{{ invoice.project.name }}</span>
                </div>
                {% endif %}
                
                <div class="summary-row total">
                    <span>المبلغ الإجمالي:</span>
                    <span>{{ invoice.total_amount|floatformat:2 }} ج.م</span>
                </div>
                
                {% if invoice.paid_amount > 0 %}
                <div class="summary-row">
                    <span>المبلغ المدفوع:</span>
                    <span style="color: #059669;">{{ invoice.paid_amount|floatformat:2 }} ج.م</span>
                </div>
                <div class="summary-row">
                    <span>المبلغ المتبقي:</span>
                    <span>{{ invoice.remaining_amount|floatformat:2 }} ج.م</span>
                </div>
                {% endif %}
            </div>

            <!-- Payment Information -->
            {% if not is_fully_paid %}
            <div class="payment-info">
                <h4>معلومات الدفع</h4>
                <p>يمكنك الدفع عبر الطرق التالية:</p>
                <ul style="margin: 10px 0; padding-right: 20px;">
                    <li>تحويل بنكي</li>
                    <li>فودافون كاش</li>
                    <li>فوري</li>
                    <li>بطاقة ائتمان</li>
                </ul>
                {% if invoice.payment_instructions %}
                <p><strong>تعليمات الدفع:</strong> {{ invoice.payment_instructions }}</p>
                {% endif %}
            </div>
            {% endif %}

            <!-- Contact Information -->
            <div class="contact-info">
                <h4 style="margin: 0 0 10px 0; color: #0c4a6e;">للاستفسار أو المساعدة</h4>
                <p style="margin: 5px 0;">📧 {{ company_email }}</p>
                <p style="margin: 5px 0;">📞 {{ company_phone }}</p>
                <p style="margin: 5px 0;">🌐 {{ company_website }}</p>
            </div>

            <p style="margin-top: 25px;">
                شكراً لك على ثقتك في خدماتنا. نتطلع إلى استمرار التعاون معك.
            </p>

            <p style="margin-top: 20px; color: #6b7280; font-size: 14px;">
                <strong>ملاحظة:</strong> ستجد الفاتورة مرفقة بهذا البريد الإلكتروني بصيغة PDF.
            </p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>{{ company_name }}</strong></p>
            <p>{{ company_website }} | {{ company_email }}</p>
            <p style="margin-top: 15px; font-size: 12px; color: #9ca3af;">
                © {{ current_year }} جميع الحقوق محفوظة
            </p>
        </div>
    </div>
</body>
</html>
