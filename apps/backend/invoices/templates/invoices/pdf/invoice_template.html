<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم {{ invoice.invoice_number }}</title>
</head>
<body>
    <div class="invoice-container">
        <!-- Header Section -->
        <div class="header">
            <div class="company-info">
                <h1>{{ company.name_ar }}</h1>
                <p>{{ company.address }}</p>
                <p>هاتف: {{ company.phone }}</p>
                <p>بريد إلكتروني: {{ company.email }}</p>
                <p>موقع إلكتروني: {{ company.website }}</p>
                <p>الرقم الضريبي: {{ company.tax_number }}</p>
                <p>السجل التجاري: {{ company.commercial_register }}</p>
            </div>
            <div class="invoice-title">
                <h2>INVOICE</h2>
                <p><strong>{{ invoice.invoice_number }}</strong></p>
                <p>{{ invoice.invoice_date|date:"d/m/Y" }}</p>
                <span class="status-badge status-{{ invoice.status }}">{{ invoice.get_status_display }}</span>
            </div>
        </div>

        <!-- Invoice Meta Information -->
        <div class="invoice-meta">
            <!-- Client Information -->
            <div class="client-info">
                <h3 class="section-title">معلومات العميل</h3>
                <p><strong>{{ invoice.client.name }}</strong></p>
                {% if invoice.client.company %}
                <p>{{ invoice.client.company }}</p>
                {% endif %}
                <p>{{ invoice.client.email }}</p>
                <p>{{ invoice.client.phone }}</p>
                {% if invoice.client.address %}
                <p>{{ invoice.client.address }}</p>
                {% endif %}
                {% if invoice.client.governorate %}
                <p>{{ invoice.client.get_governorate_display }}</p>
                {% endif %}
            </div>

            <!-- Invoice Details -->
            <div class="invoice-details">
                <h3 class="section-title">تفاصيل الفاتورة</h3>
                <p><strong>رقم الفاتورة:</strong> {{ invoice.invoice_number }}</p>
                <p><strong>تاريخ الفاتورة:</strong> {{ invoice.invoice_date|date:"d/m/Y" }}</p>
                {% if invoice.due_date %}
                <p><strong>تاريخ الاستحقاق:</strong> {{ invoice.due_date|date:"d/m/Y" }}</p>
                {% endif %}
                <p><strong>شروط الدفع:</strong> {{ invoice.get_payment_terms_display }}</p>
                <p><strong>الأولوية:</strong> {{ invoice.get_priority_display }}</p>
                {% if invoice.sales_rep %}
                <p><strong>مندوب المبيعات:</strong> {{ invoice.sales_rep.get_full_name|default:invoice.sales_rep.username }}</p>
                {% endif %}
                {% if invoice.project %}
                <p><strong>المشروع:</strong> {{ invoice.project.name }}</p>
                {% endif %}
                {% if invoice.quotation %}
                <p><strong>العرض المرجعي:</strong> {{ invoice.quotation.quotation_number }}</p>
                {% endif %}
            </div>
        </div>

        <!-- Invoice Title and Description -->
        {% if invoice.title %}
        <div style="margin: 30px 0;">
            <h2 style="color: #374151; margin-bottom: 10px;">{{ invoice.title }}</h2>
            {% if invoice.description %}
            <p style="color: #666; line-height: 1.6;">{{ invoice.description }}</p>
            {% endif %}
        </div>
        {% endif %}

        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 35%;">الخدمة / المنتج</th>
                    <th style="width: 20%;">الوصف</th>
                    <th style="width: 10%;">الكمية</th>
                    <th style="width: 15%;">سعر الوحدة</th>
                    <th style="width: 15%;">الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                {% for item in items %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td style="text-align: right; padding-right: 15px;">
                        <strong>{{ item.name }}</strong>
                        {% if item.item_type != 'service' %}
                        <br><small style="color: #666;">{{ item.get_item_type_display }}</small>
                        {% endif %}
                    </td>
                    <td style="text-align: right; padding-right: 10px;">
                        {% if item.description %}
                        {{ item.description|truncatewords:10 }}
                        {% else %}
                        -
                        {% endif %}
                    </td>
                    <td>{{ item.quantity|floatformat:0 }}</td>
                    <td>{{ format_currency:item.unit_price }}</td>
                    <td><strong>{{ format_currency:item.total_price }}</strong></td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" style="text-align: center; color: #666; padding: 30px;">
                        لا توجد عناصر في هذه الفاتورة
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Totals Section -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td><strong>المجموع الفرعي:</strong></td>
                    <td style="text-align: left;"><strong>{{ format_currency:subtotal }}</strong></td>
                </tr>
                {% if discount_amount > 0 %}
                <tr>
                    <td>الخصم ({{ invoice.discount_percentage }}%):</td>
                    <td style="text-align: left; color: #dc2626;">-{{ format_currency:discount_amount }}</td>
                </tr>
                {% endif %}
                {% if tax_amount > 0 %}
                <tr>
                    <td>ضريبة القيمة المضافة ({{ invoice.tax_percentage }}%):</td>
                    <td style="text-align: left;">{{ format_currency:tax_amount }}</td>
                </tr>
                {% endif %}
                <tr class="total-row">
                    <td><strong>المجموع الكلي:</strong></td>
                    <td style="text-align: left;"><strong>{{ format_currency:total_amount }}</strong></td>
                </tr>
                {% if paid_amount > 0 %}
                <tr>
                    <td>المبلغ المدفوع:</td>
                    <td style="text-align: left; color: #059669;">{{ format_currency:paid_amount }}</td>
                </tr>
                <tr>
                    <td><strong>المبلغ المتبقي:</strong></td>
                    <td style="text-align: left;"><strong>{{ format_currency:remaining_amount }}</strong></td>
                </tr>
                {% endif %}
            </table>
        </div>

        <!-- Payment Information -->
        {% if invoice.payment_instructions or invoice.status != 'paid' %}
        <div class="payment-info">
            <h3 style="margin-bottom: 15px; color: #92400e;">معلومات الدفع</h3>
            {% if invoice.payment_instructions %}
            <p style="margin-bottom: 10px;">{{ invoice.payment_instructions }}</p>
            {% endif %}
            {% if invoice.status != 'paid' %}
            <p><strong>طرق الدفع المتاحة:</strong> تحويل بنكي، فودافون كاش، فوري، بطاقة ائتمان</p>
            <p><strong>للاستفسار:</strong> {{ company.phone }} | {{ company.email }}</p>
            {% endif %}
        </div>
        {% endif %}

        <!-- Terms and Conditions -->
        {% if invoice.terms_conditions %}
        <div style="margin: 30px 0; padding: 20px; background: #f8fafc; border-radius: 8px; border: 1px solid #e2e8f0;">
            <h3 style="margin-bottom: 15px; color: #374151;">الشروط والأحكام</h3>
            <p style="line-height: 1.8; color: #4b5563;">{{ invoice.terms_conditions }}</p>
        </div>
        {% endif %}

        <!-- Notes -->
        {% if invoice.notes %}
        <div style="margin: 20px 0; padding: 15px; background: #fef3c7; border-radius: 8px; border: 1px solid #f59e0b;">
            <h4 style="margin-bottom: 10px; color: #92400e;">ملاحظات</h4>
            <p style="color: #92400e;">{{ invoice.notes }}</p>
        </div>
        {% endif %}

        <!-- Footer -->
        <div class="footer">
            <p>شكراً لك على ثقتك في خدماتنا</p>
            <p>{{ company.name_ar }} | {{ company.website }} | {{ company.email }}</p>
            <p style="margin-top: 10px; font-size: 10px; color: #9ca3af;">
                تم إنشاء هذه الفاتورة في {{ generated_at|date:"d/m/Y H:i" }} بتوقيت القاهرة
            </p>
        </div>
    </div>
</body>
</html>
