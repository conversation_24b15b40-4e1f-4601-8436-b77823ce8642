"""
Invoice module views following established patterns from quotations and finance modules.
"""

from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Count, Sum, Q, F
from django.utils import timezone
from django.http import HttpResponse
from datetime import date

from .models import InvoiceTemplate, Invoice, InvoiceItem, InvoicePayment
from .serializers import (
    InvoiceTemplateSerializer,
    InvoiceSerializer,
    InvoiceListSerializer,
    InvoiceCreateSerializer,
    InvoiceUpdateSerializer,
    InvoiceItemSerializer,
    InvoiceItemCreateSerializer,
    InvoicePaymentSerializer,
    InvoicePaymentCreateSerializer,
    InvoiceStatsSerializer,
)


class InvoiceTemplateViewSet(viewsets.ModelViewSet):
    """Invoice template management viewset"""

    queryset = InvoiceTemplate.objects.all().order_by("-is_default", "name")
    serializer_class = InvoiceTemplateSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["template_type", "is_active", "is_default"]
    search_fields = ["name", "description"]
    ordering_fields = ["name", "created_at"]
    ordering = ["-is_default", "name"]

    @action(detail=False, methods=["get"])
    def default(self, request):
        """Get default template"""
        try:
            template = InvoiceTemplate.objects.get(is_default=True, is_active=True)
            serializer = self.get_serializer(template)
            return Response(serializer.data)
        except InvoiceTemplate.DoesNotExist:
            return Response(
                {"error": "لا يوجد قالب افتراضي نشط"}, status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=["post"])
    def set_default(self, request, pk=None):
        """Set template as default"""
        template = self.get_object()

        # Remove default from other templates
        InvoiceTemplate.objects.filter(is_default=True).update(is_default=False)

        # Set this template as default
        template.is_default = True
        template.save()

        return Response({"message": "تم تعيين القالب كافتراضي بنجاح"})


class InvoiceViewSet(viewsets.ModelViewSet):
    """Invoice management viewset"""

    queryset = (
        Invoice.objects.all()
        .select_related("client", "project", "quotation", "sales_rep", "template")
        .prefetch_related("items", "payments")
        .order_by("-created_at")
    )
    permission_classes = [IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = [
        "status",
        "priority",
        "client",
        "project",
        "sales_rep",
        "payment_terms",
    ]
    search_fields = ["invoice_number", "title", "client__name", "client__email"]
    ordering_fields = [
        "invoice_number",
        "invoice_date",
        "due_date",
        "total_amount",
        "created_at",
    ]
    ordering = ["-created_at"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "list":
            return InvoiceListSerializer
        elif self.action == "create":
            return InvoiceCreateSerializer
        elif self.action in ["update", "partial_update"]:
            return InvoiceUpdateSerializer
        return InvoiceSerializer

    def get_queryset(self):
        """Filter queryset based on user permissions"""
        queryset = super().get_queryset()

        # Add computed fields
        queryset = queryset.annotate(
            is_overdue=Q(
                due_date__lt=date.today(),
                status__in=["pending", "sent", "viewed", "partially_paid"],
            ),
            days_overdue=F("due_date") - date.today(),
            payment_percentage=F("paid_amount") / F("total_amount") * 100,
            is_fully_paid=Q(paid_amount__gte=F("total_amount")),
            is_partially_paid=Q(paid_amount__gt=0, paid_amount__lt=F("total_amount")),
        )

        return queryset

    @action(detail=False, methods=["get"])
    def stats(self, request):
        """Get invoice statistics"""
        queryset = self.get_queryset()

        # Basic counts and amounts
        total_invoices = queryset.count()
        total_amount = queryset.aggregate(Sum("total_amount"))["total_amount__sum"] or 0
        paid_amount = queryset.aggregate(Sum("paid_amount"))["paid_amount__sum"] or 0

        # Status counts
        status_counts = queryset.values("status").annotate(count=Count("id"))
        status_dict = {item["status"]: item["count"] for item in status_counts}

        # Overdue and pending amounts
        overdue_amount = (
            queryset.filter(
                due_date__lt=date.today(),
                status__in=["pending", "sent", "viewed", "partially_paid"],
            ).aggregate(Sum("remaining_amount"))["remaining_amount__sum"]
            or 0
        )

        pending_amount = (
            queryset.filter(
                status__in=["pending", "sent", "viewed", "partially_paid"]
            ).aggregate(Sum("remaining_amount"))["remaining_amount__sum"]
            or 0
        )

        # Calculate average payment time
        paid_invoices = queryset.filter(status="paid", paid_date__isnull=False)
        avg_payment_time = 0
        if paid_invoices.exists():
            payment_times = []
            for invoice in paid_invoices:
                if invoice.invoice_date and invoice.paid_date:
                    days = (invoice.paid_date.date() - invoice.invoice_date).days
                    payment_times.append(days)
            if payment_times:
                avg_payment_time = sum(payment_times) / len(payment_times)

        # Payment rate
        payment_rate = (paid_amount / total_amount * 100) if total_amount > 0 else 0

        stats_data = {
            "total_invoices": total_invoices,
            "total_amount": total_amount,
            "paid_amount": paid_amount,
            "pending_amount": pending_amount,
            "overdue_amount": overdue_amount,
            "draft_count": status_dict.get("draft", 0),
            "pending_count": status_dict.get("pending", 0),
            "sent_count": status_dict.get("sent", 0),
            "paid_count": status_dict.get("paid", 0),
            "overdue_count": queryset.filter(
                due_date__lt=date.today(),
                status__in=["pending", "sent", "viewed", "partially_paid"],
            ).count(),
            "avg_payment_time": avg_payment_time,
            "payment_rate": payment_rate,
        }

        serializer = InvoiceStatsSerializer(stats_data)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def send(self, request, pk=None):
        """Send invoice to client"""
        invoice = self.get_object()

        if invoice.status == Invoice.Status.DRAFT:
            try:
                # Import email service
                from .email_service import send_invoice_email

                # Get optional parameters from request
                recipient_email = request.data.get("recipient_email")
                cc_emails = request.data.get("cc_emails", [])
                custom_message = request.data.get("custom_message")

                # Send email
                send_invoice_email(
                    invoice=invoice,
                    recipient_email=recipient_email,
                    cc_emails=cc_emails,
                    custom_message=custom_message,
                )

                # Update invoice status
                invoice.status = Invoice.Status.SENT
                invoice.sent_date = timezone.now()
                invoice.save()

                return Response(
                    {
                        "message": "تم إرسال الفاتورة بنجاح",
                        "sent_to": recipient_email or invoice.client.email,
                    }
                )

            except Exception as e:
                return Response(
                    {"error": f"فشل في إرسال الفاتورة: {str(e)}"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
        else:
            return Response(
                {"error": "يمكن إرسال الفواتير في حالة المسودة فقط"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def mark_viewed(self, request, pk=None):
        """Mark invoice as viewed by client"""
        invoice = self.get_object()

        if invoice.status == Invoice.Status.SENT:
            invoice.status = Invoice.Status.VIEWED
            invoice.viewed_date = timezone.now()
            invoice.save()

            return Response({"message": "تم تسجيل عرض الفاتورة"})
        else:
            return Response(
                {"error": "يمكن تسجيل العرض للفواتير المرسلة فقط"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def cancel(self, request, pk=None):
        """Cancel invoice"""
        invoice = self.get_object()

        if invoice.status not in [Invoice.Status.PAID, Invoice.Status.CANCELLED]:
            invoice.status = Invoice.Status.CANCELLED
            invoice.save()

            return Response({"message": "تم إلغاء الفاتورة بنجاح"})
        else:
            return Response(
                {"error": "لا يمكن إلغاء فاتورة مدفوعة أو ملغية مسبقاً"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["get"])
    def generate_pdf(self, request, pk=None):
        """Generate PDF for invoice"""
        invoice = self.get_object()

        try:
            from .pdf_generator import InvoicePDFGenerator

            pdf_generator = InvoicePDFGenerator()
            pdf_content = pdf_generator.generate_pdf(invoice)

            # Save PDF file to invoice
            from django.core.files.base import ContentFile

            pdf_filename = f"invoice_{invoice.invoice_number}.pdf"
            invoice.pdf_file.save(pdf_filename, ContentFile(pdf_content), save=True)

            # Return PDF as response
            response = HttpResponse(pdf_content, content_type="application/pdf")
            response["Content-Disposition"] = f'attachment; filename="{pdf_filename}"'
            return response

        except Exception as e:
            return Response(
                {
                    "error": f"حدث خطأ في توليد PDF: {str(e)}",
                    "invoice_number": invoice.invoice_number,
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["post"])
    def duplicate(self, request, pk=None):
        """Duplicate invoice"""
        original_invoice = self.get_object()

        # Create new invoice with same data
        new_invoice = Invoice.objects.create(
            title=f"نسخة من {original_invoice.title}",
            description=original_invoice.description,
            client=original_invoice.client,
            project=original_invoice.project,
            sales_rep=request.user,
            template=original_invoice.template,
            priority=original_invoice.priority,
            discount_percentage=original_invoice.discount_percentage,
            tax_percentage=original_invoice.tax_percentage,
            payment_terms=original_invoice.payment_terms,
            custom_payment_days=original_invoice.custom_payment_days,
            invoice_date=date.today(),
            notes=original_invoice.notes,
            terms_conditions=original_invoice.terms_conditions,
            payment_instructions=original_invoice.payment_instructions,
        )

        # Copy invoice items
        for item in original_invoice.items.all():
            InvoiceItem.objects.create(
                invoice=new_invoice,
                item_type=item.item_type,
                name=item.name,
                description=item.description,
                quantity=item.quantity,
                unit=item.unit,
                unit_price=item.unit_price,
                is_taxable=item.is_taxable,
                tax_percentage=item.tax_percentage,
            )

        serializer = self.get_serializer(new_invoice)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=["post"])
    def create_payment_intent(self, request, pk=None):
        """Create payment intent for invoice using payment gateway"""
        invoice = self.get_object()

        if invoice.status == Invoice.Status.PAID:
            return Response(
                {"error": "الفاتورة مدفوعة بالفعل"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        gateway_type = request.data.get("gateway", "paymob")

        try:
            from .payment_gateways.paymob import PaymentGatewayFactory

            gateway = PaymentGatewayFactory.get_gateway(gateway_type)
            payment_intent = gateway.create_payment_intent(invoice)

            if not payment_intent:
                return Response(
                    {"error": "فشل في إنشاء نية الدفع"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            return Response(
                {
                    "payment_intent": payment_intent,
                    "gateway": gateway_type,
                    "invoice_id": invoice.id,
                    "amount": float(invoice.total_amount),
                    "currency": "EGP",
                }
            )

        except Exception as e:
            return Response(
                {"error": f"خطأ في إنشاء نية الدفع: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def payment_gateways(self, request):
        """Get available payment gateways"""
        try:
            from .payment_gateways.paymob import PaymentGatewayFactory

            gateways = PaymentGatewayFactory.get_available_gateways()
            return Response({"gateways": gateways})

        except Exception as e:
            return Response(
                {"error": f"خطأ في جلب بوابات الدفع: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["post"])
    def create_from_quotation(self, request):
        """Create invoice from quotation"""
        quotation_id = request.data.get("quotation_id")

        if not quotation_id:
            return Response(
                {"error": "معرف العرض مطلوب"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from quotations.models import Quotation

            quotation = Quotation.objects.get(id=quotation_id)

            if quotation.status != "approved":
                return Response(
                    {"error": "يمكن إنشاء فاتورة من العروض المعتمدة فقط"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Create invoice from quotation
            invoice = Invoice.objects.create(
                title=f"فاتورة للعرض {quotation.quotation_number}",
                description=quotation.description,
                client=quotation.client,
                quotation=quotation,
                sales_rep=request.user,
                priority=quotation.priority,
                discount_percentage=quotation.discount_percentage,
                tax_percentage=quotation.tax_percentage,
                invoice_date=date.today(),
                notes=quotation.notes,
                terms_conditions=quotation.terms_conditions,
            )

            # Copy quotation items to invoice items
            for quotation_item in quotation.items.all():
                InvoiceItem.objects.create(
                    invoice=invoice,
                    quotation_item=quotation_item,
                    item_type="service",
                    name=quotation_item.service.name_ar,
                    description=quotation_item.description,
                    quantity=quotation_item.quantity,
                    unit="خدمة",
                    unit_price=quotation_item.unit_price,
                    is_taxable=True,
                    tax_percentage=invoice.tax_percentage,
                )

            serializer = self.get_serializer(invoice)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Quotation.DoesNotExist:
            return Response(
                {"error": "العرض المحدد غير موجود"}, status=status.HTTP_404_NOT_FOUND
            )


class InvoiceItemViewSet(viewsets.ModelViewSet):
    """Invoice item management viewset"""

    queryset = (
        InvoiceItem.objects.all()
        .select_related("invoice", "quotation_item")
        .order_by("id")
    )
    permission_classes = [IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["invoice", "item_type", "is_taxable"]
    search_fields = ["name", "description"]
    ordering_fields = ["name", "unit_price", "total_price", "created_at"]
    ordering = ["id"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "create":
            return InvoiceItemCreateSerializer
        return InvoiceItemSerializer

    def perform_create(self, serializer):
        """Create invoice item and update invoice totals"""
        serializer.save()


class InvoicePaymentViewSet(viewsets.ModelViewSet):
    """Invoice payment management viewset"""

    queryset = (
        InvoicePayment.objects.all()
        .select_related("invoice", "processed_by")
        .order_by("-payment_date")
    )
    permission_classes = [IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["invoice", "payment_method", "status"]
    search_fields = ["transaction_id", "reference_number", "notes"]
    ordering_fields = ["payment_date", "amount", "created_at"]
    ordering = ["-payment_date"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "create":
            return InvoicePaymentCreateSerializer
        return InvoicePaymentSerializer

    def perform_create(self, serializer):
        """Create payment and set processed_by to current user"""
        serializer.save(processed_by=self.request.user)

    @action(detail=True, methods=["post"])
    def approve(self, request, pk=None):
        """Approve payment"""
        payment = self.get_object()

        if payment.status == InvoicePayment.PaymentStatus.PENDING:
            payment.status = InvoicePayment.PaymentStatus.COMPLETED
            payment.processed_date = timezone.now()
            payment.save()

            return Response({"message": "تم اعتماد الدفعة بنجاح"})
        else:
            return Response(
                {"error": "يمكن اعتماد الدفعات في حالة الانتظار فقط"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def reject(self, request, pk=None):
        """Reject payment"""
        payment = self.get_object()

        if payment.status == InvoicePayment.PaymentStatus.PENDING:
            payment.status = InvoicePayment.PaymentStatus.FAILED
            payment.processed_date = timezone.now()
            payment.notes = request.data.get("rejection_reason", payment.notes)
            payment.save()

            return Response({"message": "تم رفض الدفعة"})
        else:
            return Response(
                {"error": "يمكن رفض الدفعات في حالة الانتظار فقط"},
                status=status.HTTP_400_BAD_REQUEST,
            )
