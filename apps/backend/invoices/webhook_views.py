"""
Webhook handlers for payment gateway notifications.
"""

import json
import logging
from django.http import HttpResponse, HttpResponseBadRequest
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
from django.utils import timezone
from .models import Invoice, InvoicePayment
from .payment_gateways.paymob import PaymobGateway

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
class PaymobWebhookView(View):
    """Handle Paymob webhook notifications"""
    
    def post(self, request):
        """Process Paymob webhook"""
        try:
            # Get raw payload
            payload = request.body.decode('utf-8')
            
            # Parse JSON data
            try:
                webhook_data = json.loads(payload)
            except json.JSONDecodeError:
                logger.error("Invalid JSON in Paymob webhook")
                return HttpResponseBadRequest("Invalid JSON")
            
            # Verify webhook signature
            signature = request.META.get('HTTP_X_PAYMOB_SIGNATURE', '')
            gateway = PaymobGateway()
            
            if not gateway.verify_webhook_signature(payload, signature):
                logger.error("Invalid Paymob webhook signature")
                return HttpResponseBadRequest("Invalid signature")
            
            # Process webhook
            result = gateway.process_webhook(webhook_data)
            
            if result.get('status') == 'error':
                logger.error(f"Error processing Paymob webhook: {result.get('error')}")
                return HttpResponseBadRequest("Webhook processing error")
            
            # Find invoice
            invoice_number = result.get('invoice_number')
            if not invoice_number:
                logger.error("No invoice number in Paymob webhook")
                return HttpResponseBadRequest("No invoice number")
            
            try:
                invoice = Invoice.objects.get(invoice_number=invoice_number)
            except Invoice.DoesNotExist:
                logger.error(f"Invoice {invoice_number} not found for Paymob webhook")
                return HttpResponseBadRequest("Invoice not found")
            
            # Create or update payment record
            payment_data = {
                'invoice': invoice,
                'payment_method': result.get('payment_method', 'paymob'),
                'payment_gateway': 'paymob',
                'amount': result.get('amount', 0),
                'status': result.get('status', 'pending'),
                'transaction_id': result.get('transaction_id'),
                'gateway_order_id': result.get('order_id'),
                'gateway_payment_id': result.get('transaction_id'),
                'payment_date': timezone.now(),
                'gateway_response': result.get('raw_data', {}),
                'notes': f"Payment via Paymob - {result.get('payment_method', 'unknown')}"
            }
            
            # Check if payment already exists
            existing_payment = InvoicePayment.objects.filter(
                invoice=invoice,
                transaction_id=result.get('transaction_id')
            ).first()
            
            if existing_payment:
                # Update existing payment
                for key, value in payment_data.items():
                    if key != 'invoice':  # Don't update invoice reference
                        setattr(existing_payment, key, value)
                existing_payment.save()
                payment = existing_payment
            else:
                # Create new payment
                payment = InvoicePayment.objects.create(**payment_data)
            
            # Update invoice status if payment is completed
            if result.get('status') == 'completed':
                self._update_invoice_status(invoice, payment)
            
            logger.info(f"Successfully processed Paymob webhook for invoice {invoice_number}")
            return HttpResponse("OK")
            
        except Exception as e:
            logger.error(f"Error processing Paymob webhook: {str(e)}")
            return HttpResponseBadRequest("Internal error")
    
    def _update_invoice_status(self, invoice, payment):
        """Update invoice status based on payment"""
        try:
            # Recalculate total paid amount
            total_paid = invoice.payments.filter(
                status='completed'
            ).aggregate(
                total=models.Sum('amount')
            )['total'] or 0
            
            invoice.paid_amount = total_paid
            
            # Update status
            if total_paid >= invoice.total_amount:
                invoice.status = 'paid'
                if not invoice.paid_date:
                    invoice.paid_date = timezone.now()
            elif total_paid > 0:
                invoice.status = 'partially_paid'
            
            invoice.save()
            
            # Trigger automation workflows
            from .automation.workflows import invoice_automation
            invoice_automation.auto_update_payment_status(payment)
            
        except Exception as e:
            logger.error(f"Error updating invoice status: {str(e)}")


@method_decorator(csrf_exempt, name='dispatch')
class StripeWebhookView(View):
    """Handle Stripe webhook notifications"""
    
    def post(self, request):
        """Process Stripe webhook"""
        try:
            import stripe
            from django.conf import settings
            
            payload = request.body
            sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
            endpoint_secret = getattr(settings, 'STRIPE_WEBHOOK_SECRET', '')
            
            try:
                event = stripe.Webhook.construct_event(
                    payload, sig_header, endpoint_secret
                )
            except ValueError:
                logger.error("Invalid payload in Stripe webhook")
                return HttpResponseBadRequest("Invalid payload")
            except stripe.error.SignatureVerificationError:
                logger.error("Invalid signature in Stripe webhook")
                return HttpResponseBadRequest("Invalid signature")
            
            # Handle the event
            if event['type'] == 'payment_intent.succeeded':
                payment_intent = event['data']['object']
                self._handle_stripe_payment_success(payment_intent)
            elif event['type'] == 'payment_intent.payment_failed':
                payment_intent = event['data']['object']
                self._handle_stripe_payment_failed(payment_intent)
            
            return HttpResponse("OK")
            
        except Exception as e:
            logger.error(f"Error processing Stripe webhook: {str(e)}")
            return HttpResponseBadRequest("Internal error")
    
    def _handle_stripe_payment_success(self, payment_intent):
        """Handle successful Stripe payment"""
        try:
            invoice_id = payment_intent['metadata'].get('invoice_id')
            if not invoice_id:
                return
            
            invoice = Invoice.objects.get(id=invoice_id)
            
            # Create payment record
            payment = InvoicePayment.objects.create(
                invoice=invoice,
                payment_method='credit_card',
                payment_gateway='stripe',
                amount=payment_intent['amount'] / 100,  # Convert from cents
                status='completed',
                transaction_id=payment_intent['id'],
                gateway_payment_id=payment_intent['id'],
                payment_date=timezone.now(),
                gateway_response=payment_intent,
                notes="Payment via Stripe"
            )
            
            # Update invoice status
            self._update_invoice_status(invoice, payment)
            
        except Exception as e:
            logger.error(f"Error handling Stripe payment success: {str(e)}")
    
    def _handle_stripe_payment_failed(self, payment_intent):
        """Handle failed Stripe payment"""
        try:
            invoice_id = payment_intent['metadata'].get('invoice_id')
            if not invoice_id:
                return
            
            invoice = Invoice.objects.get(id=invoice_id)
            
            # Create failed payment record
            InvoicePayment.objects.create(
                invoice=invoice,
                payment_method='credit_card',
                payment_gateway='stripe',
                amount=payment_intent['amount'] / 100,
                status='failed',
                transaction_id=payment_intent['id'],
                gateway_payment_id=payment_intent['id'],
                payment_date=timezone.now(),
                gateway_response=payment_intent,
                notes=f"Failed payment via Stripe: {payment_intent.get('last_payment_error', {}).get('message', 'Unknown error')}"
            )
            
        except Exception as e:
            logger.error(f"Error handling Stripe payment failure: {str(e)}")
    
    def _update_invoice_status(self, invoice, payment):
        """Update invoice status based on payment"""
        try:
            from django.db import models
            
            # Recalculate total paid amount
            total_paid = invoice.payments.filter(
                status='completed'
            ).aggregate(
                total=models.Sum('amount')
            )['total'] or 0
            
            invoice.paid_amount = total_paid
            
            # Update status
            if total_paid >= invoice.total_amount:
                invoice.status = 'paid'
                if not invoice.paid_date:
                    invoice.paid_date = timezone.now()
            elif total_paid > 0:
                invoice.status = 'partially_paid'
            
            invoice.save()
            
        except Exception as e:
            logger.error(f"Error updating invoice status: {str(e)}")


@csrf_exempt
@require_http_methods(["POST"])
def paypal_webhook(request):
    """Handle PayPal webhook notifications"""
    try:
        payload = json.loads(request.body.decode('utf-8'))
        
        # Handle PayPal events
        event_type = payload.get('event_type')
        
        if event_type == 'PAYMENT.CAPTURE.COMPLETED':
            # Handle successful payment
            pass
        elif event_type == 'PAYMENT.CAPTURE.DENIED':
            # Handle failed payment
            pass
        
        return HttpResponse("OK")
        
    except Exception as e:
        logger.error(f"Error processing PayPal webhook: {str(e)}")
        return HttpResponseBadRequest("Internal error")
