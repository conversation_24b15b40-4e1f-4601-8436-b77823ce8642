"""
Invoice module admin configuration following established patterns.
"""

from django.contrib import admin
from .models import InvoiceTemplate, Invoice, InvoiceItem, InvoicePayment


class InvoiceItemInline(admin.TabularInline):
    """Inline admin for invoice items"""

    model = InvoiceItem
    extra = 1
    fields = [
        "item_type",
        "name",
        "description",
        "quantity",
        "unit",
        "unit_price",
        "total_price",
        "is_taxable",
    ]
    readonly_fields = ["total_price"]


class InvoicePaymentInline(admin.TabularInline):
    """Inline admin for invoice payments"""

    model = InvoicePayment
    extra = 0
    fields = ["payment_method", "amount", "status", "payment_date", "transaction_id"]
    readonly_fields = ["processed_date"]


@admin.register(InvoiceTemplate)
class InvoiceTemplateAdmin(admin.ModelAdmin):
    """Admin configuration for invoice templates"""

    list_display = ["name", "template_type", "is_active", "is_default", "created_at"]
    list_filter = ["template_type", "is_active", "is_default"]
    search_fields = ["name", "description"]
    ordering = ["-is_default", "name"]

    fieldsets = (
        (
            "معلومات أساسية",
            {
                "fields": (
                    "name",
                    "description",
                    "template_type",
                    "is_active",
                    "is_default",
                )
            },
        ),
        (
            "محتوى القالب",
            {
                "fields": (
                    "header_text",
                    "footer_text",
                    "terms_conditions",
                    "payment_instructions",
                )
            },
        ),
        ("التصميم", {"fields": ("primary_color", "secondary_color", "font_family")}),
    )


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    """Admin configuration for invoices"""

    list_display = [
        "invoice_number",
        "title",
        "client",
        "status",
        "priority",
        "total_amount",
        "paid_amount",
        "invoice_date",
        "due_date",
    ]
    list_filter = ["status", "priority", "payment_terms", "invoice_date", "due_date"]
    search_fields = ["invoice_number", "title", "client__name", "client__email"]
    ordering = ["-created_at"]
    readonly_fields = [
        "invoice_number",
        "subtotal",
        "discount_amount",
        "tax_amount",
        "total_amount",
        "paid_amount",
        "remaining_amount",
        "sent_date",
        "viewed_date",
        "paid_date",
        "created_at",
        "updated_at",
    ]

    inlines = [InvoiceItemInline, InvoicePaymentInline]

    fieldsets = (
        (
            "معلومات أساسية",
            {
                "fields": (
                    "invoice_number",
                    "title",
                    "description",
                    "client",
                    "project",
                    "quotation",
                    "sales_rep",
                    "template",
                )
            },
        ),
        ("الحالة والأولوية", {"fields": ("status", "priority")}),
        (
            "المبالغ المالية",
            {
                "fields": (
                    "subtotal",
                    "discount_percentage",
                    "discount_amount",
                    "tax_percentage",
                    "tax_amount",
                    "total_amount",
                    "paid_amount",
                    "remaining_amount",
                )
            },
        ),
        ("شروط الدفع", {"fields": ("payment_terms", "custom_payment_days")}),
        (
            "التواريخ",
            {
                "fields": (
                    "invoice_date",
                    "due_date",
                    "sent_date",
                    "viewed_date",
                    "paid_date",
                )
            },
        ),
        (
            "معلومات إضافية",
            {
                "fields": (
                    "notes",
                    "terms_conditions",
                    "payment_instructions",
                    "pdf_file",
                )
            },
        ),
        (
            "معلومات النظام",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )


@admin.register(InvoiceItem)
class InvoiceItemAdmin(admin.ModelAdmin):
    """Admin configuration for invoice items"""

    list_display = [
        "name",
        "invoice",
        "item_type",
        "quantity",
        "unit_price",
        "total_price",
        "is_taxable",
    ]
    list_filter = ["item_type", "is_taxable", "invoice__status"]
    search_fields = ["name", "description", "invoice__invoice_number"]
    ordering = ["invoice", "id"]
    readonly_fields = ["total_price"]

    fieldsets = (
        (
            "معلومات العنصر",
            {
                "fields": (
                    "invoice",
                    "quotation_item",
                    "item_type",
                    "name",
                    "description",
                )
            },
        ),
        (
            "الكمية والسعر",
            {"fields": ("quantity", "unit", "unit_price", "total_price")},
        ),
        ("الضريبة", {"fields": ("is_taxable", "tax_percentage")}),
    )


@admin.register(InvoicePayment)
class InvoicePaymentAdmin(admin.ModelAdmin):
    """Admin configuration for invoice payments"""

    list_display = [
        "invoice",
        "payment_method",
        "amount",
        "status",
        "payment_date",
        "processed_by",
        "transaction_id",
    ]
    list_filter = ["payment_method", "status", "payment_date"]
    search_fields = [
        "invoice__invoice_number",
        "transaction_id",
        "reference_number",
        "notes",
    ]
    ordering = ["-payment_date"]
    readonly_fields = ["processed_date"]

    fieldsets = (
        (
            "معلومات الدفع",
            {"fields": ("invoice", "payment_method", "amount", "status")},
        ),
        (
            "تفاصيل المعاملة",
            {
                "fields": (
                    "transaction_id",
                    "reference_number",
                    "payment_date",
                    "processed_date",
                    "processed_by",
                )
            },
        ),
        ("معلومات إضافية", {"fields": ("notes", "receipt_file")}),
    )
