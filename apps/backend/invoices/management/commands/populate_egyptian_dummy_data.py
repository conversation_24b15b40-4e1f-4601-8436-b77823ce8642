"""
Management command to populate comprehensive Egyptian business dummy data for all ERP modules.
"""

import random
from decimal import Decimal
from datetime import date, timedelta
import datetime
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db import transaction

User = get_user_model()


class Command(BaseCommand):
    help = "Populate comprehensive Egyptian business dummy data for all ERP modules"

    def add_arguments(self, parser):
        parser.add_argument(
            "--clear", action="store_true", help="Clear existing data before populating"
        )

        parser.add_argument(
            "--module",
            type=str,
            choices=[
                "all",
                "clients",
                "projects",
                "tasks",
                "team",
                "invoices",
                "quotations",
                "payments",
                "commissions",
                "support",
            ],
            default="all",
            help="Specific module to populate (default: all)",
        )

    def handle(self, *args, **options):
        clear_data = options["clear"]
        module = options["module"]

        self.stdout.write(
            self.style.SUCCESS("Starting Egyptian dummy data population...")
        )

        if clear_data:
            self._clear_existing_data()

        with transaction.atomic():
            if module in ["all", "team"]:
                self._create_team_members()

            if module in ["all", "clients"]:
                self._create_egyptian_clients()

            if module in ["all", "projects"]:
                self._create_digital_agency_projects()

            if module in ["all", "tasks"]:
                self._create_project_tasks()

            if module in ["all", "quotations"]:
                self._create_quotations()

            if module in ["all", "invoices"]:
                self._create_invoices()

            if module in ["all", "payments"]:
                self._create_payments()

            if module in ["all", "commissions"]:
                self._create_commissions()

            if module in ["all", "support"]:
                self._create_support_data()

        self.stdout.write(
            self.style.SUCCESS("Egyptian dummy data population completed successfully!")
        )

    def _clear_existing_data(self):
        """Clear existing dummy data"""
        self.stdout.write("Clearing existing data...")

        # Import models
        from clients.models import Client
        from projects.models import Project
        from tasks.models import Task
        from quotations.models import Quotation
        from invoices.models import Invoice, InvoicePayment
        from commissions.models import Commission

        # Clear data in reverse dependency order
        Commission.objects.all().delete()
        InvoicePayment.objects.all().delete()
        Invoice.objects.all().delete()
        Quotation.objects.all().delete()
        Task.objects.all().delete()
        Project.objects.all().delete()
        Client.objects.all().delete()

        self.stdout.write(self.style.WARNING("Existing data cleared"))

    def _create_team_members(self):
        """Create team members across all departments"""
        self.stdout.write("Creating team members...")

        team_members = [
            # Sales Team
            {
                "username": "ahmed_sales",
                "first_name": "أحمد",
                "last_name": "محمد",
                "email": "<EMAIL>",
                "department": "sales",
            },
            {
                "username": "fatma_sales",
                "first_name": "فاطمة",
                "last_name": "علي",
                "email": "<EMAIL>",
                "department": "sales",
            },
            {
                "username": "omar_sales",
                "first_name": "عمر",
                "last_name": "حسن",
                "email": "<EMAIL>",
                "department": "sales",
            },
            # Development Team
            {
                "username": "mohamed_dev",
                "first_name": "محمد",
                "last_name": "أحمد",
                "email": "<EMAIL>",
                "department": "development",
            },
            {
                "username": "sara_dev",
                "first_name": "سارة",
                "last_name": "محمود",
                "email": "<EMAIL>",
                "department": "development",
            },
            {
                "username": "youssef_dev",
                "first_name": "يوسف",
                "last_name": "عبدالله",
                "email": "<EMAIL>",
                "department": "development",
            },
            # Design Team
            {
                "username": "nour_design",
                "first_name": "نور",
                "last_name": "إبراهيم",
                "email": "<EMAIL>",
                "department": "design",
            },
            {
                "username": "menna_design",
                "first_name": "منة الله",
                "last_name": "خالد",
                "email": "<EMAIL>",
                "department": "design",
            },
            # Media Buying Team
            {
                "username": "hassan_media",
                "first_name": "حسن",
                "last_name": "عبدالرحمن",
                "email": "<EMAIL>",
                "department": "media_buying",
            },
            {
                "username": "aya_media",
                "first_name": "آية",
                "last_name": "صلاح",
                "email": "<EMAIL>",
                "department": "media_buying",
            },
            # Customer Service Team
            {
                "username": "mariam_cs",
                "first_name": "مريم",
                "last_name": "فؤاد",
                "email": "<EMAIL>",
                "department": "customer_service",
            },
            {
                "username": "karim_cs",
                "first_name": "كريم",
                "last_name": "طارق",
                "email": "<EMAIL>",
                "department": "customer_service",
            },
        ]

        created_users = []
        for member_data in team_members:
            user, created = User.objects.get_or_create(
                username=member_data["username"],
                defaults={
                    "first_name": member_data["first_name"],
                    "last_name": member_data["last_name"],
                    "email": member_data["email"],
                    "is_active": True,
                },
            )
            if created:
                user.set_password("demo123")
                user.save()
                created_users.append(user)

        self.stdout.write(f"Created {len(created_users)} team members")

    def _create_egyptian_clients(self):
        """Create realistic Egyptian business clients"""
        self.stdout.write("Creating Egyptian clients...")

        from clients.models import Client

        egyptian_companies = [
            {
                "name": "شركة النيل للتكنولوجيا",
                "company": "شركة النيل للتكنولوجيا المحدودة",
                "email": "<EMAIL>",
                "phone": "+20 2 2735 4821",
                "address": "شارع التحرير، وسط البلد، القاهرة",
                "governorate": "cairo",
            },
            {
                "name": "مجموعة الأهرام التجارية",
                "company": "مجموعة الأهرام التجارية ش.م.م",
                "email": "<EMAIL>",
                "phone": "+20 2 3574 9632",
                "address": "مدينة نصر، القاهرة الجديدة",
                "governorate": "cairo",
            },
            {
                "name": "شركة دلتا للصناعات الغذائية",
                "company": "شركة دلتا للصناعات الغذائية",
                "email": "<EMAIL>",
                "phone": "+20 50 234 5678",
                "address": "المنطقة الصناعية، المنصورة، الدقهلية",
                "governorate": "dakahlia",
            },
            {
                "name": "مؤسسة الإسكندرية للاستيراد والتصدير",
                "company": "مؤسسة الإسكندرية للاستيراد والتصدير",
                "email": "<EMAIL>",
                "phone": "+20 3 487 2156",
                "address": "منطقة الميناء، الإسكندرية",
                "governorate": "alexandria",
            },
            {
                "name": "شركة الصعيد للمقاولات",
                "company": "شركة الصعيد للمقاولات والإنشاءات",
                "email": "<EMAIL>",
                "phone": "+20 95 123 4567",
                "address": "شارع الكورنيش، أسوان",
                "governorate": "aswan",
            },
            {
                "name": "مركز الشرق الأوسط الطبي",
                "company": "مركز الشرق الأوسط الطبي",
                "email": "<EMAIL>",
                "phone": "+20 2 2456 7890",
                "address": "مصر الجديدة، القاهرة",
                "governorate": "cairo",
            },
            {
                "name": "أكاديمية المستقبل التعليمية",
                "company": "أكاديمية المستقبل للتعليم والتدريب",
                "email": "<EMAIL>",
                "phone": "+20 2 3456 7891",
                "address": "التجمع الخامس، القاهرة الجديدة",
                "governorate": "cairo",
            },
            {
                "name": "شركة البحر الأحمر للسياحة",
                "company": "شركة البحر الأحمر للسياحة والفنادق",
                "email": "<EMAIL>",
                "phone": "+20 65 344 5566",
                "address": "منطقة السقالة، الغردقة، البحر الأحمر",
                "governorate": "red_sea",
            },
            {
                "name": "مصنع القطن المصري",
                "company": "مصنع القطن المصري للنسيج",
                "email": "<EMAIL>",
                "phone": "+20 40 567 8901",
                "address": "المحلة الكبرى، الغربية",
                "governorate": "gharbia",
            },
            {
                "name": "شركة سيناء للبترول",
                "company": "شركة سيناء للبترول والغاز الطبيعي",
                "email": "<EMAIL>",
                "phone": "+20 69 234 5678",
                "address": "العريش، شمال سيناء",
                "governorate": "north_sinai",
            },
        ]

        # Add more clients to reach 20+
        additional_clients = [
            {
                "name": "مطاعم أبو طارق",
                "company": "سلسلة مطاعم أبو طارق",
                "email": "<EMAIL>",
                "phone": "+20 2 2567 8901",
                "address": "شارع فيصل، الجيزة",
                "governorate": "giza",
            },
            {
                "name": "صيدلية النور",
                "company": "صيدليات النور",
                "email": "<EMAIL>",
                "phone": "+20 88 345 6789",
                "address": "شارع الجمهورية، أسيوط",
                "governorate": "assiut",
            },
            {
                "name": "مركز الأزهر للدراسات الإسلامية",
                "company": "مركز الأزهر للدراسات الإسلامية",
                "email": "<EMAIL>",
                "phone": "+20 2 2678 9012",
                "address": "الأزهر الشريف، القاهرة",
                "governorate": "cairo",
            },
            {
                "name": "شركة الوادي الجديد للزراعة",
                "company": "شركة الوادي الجديد للزراعة والاستصلاح",
                "email": "<EMAIL>",
                "phone": "+20 92 456 7890",
                "address": "الخارجة، الوادي الجديد",
                "governorate": "new_valley",
            },
            {
                "name": "مجمع دمياط للأثاث",
                "company": "مجمع دمياط للأثاث والموبيليا",
                "email": "<EMAIL>",
                "phone": "+20 57 789 0123",
                "address": "دمياط الجديدة، دمياط",
                "governorate": "damietta",
            },
        ]

        all_clients = egyptian_companies + additional_clients
        created_clients = []

        for client_data in all_clients:
            client, created = Client.objects.get_or_create(
                email=client_data["email"], defaults=client_data
            )
            if created:
                created_clients.append(client)

        self.stdout.write(f"Created {len(created_clients)} Egyptian clients")
        return created_clients

    def _create_digital_agency_projects(self):
        """Create digital agency projects"""
        self.stdout.write("Creating digital agency projects...")

        from projects.models import Project
        from clients.models import Client

        clients = list(Client.objects.all())
        if not clients:
            self.stdout.write(
                self.style.WARNING("No clients found. Creating clients first...")
            )
            clients = self._create_egyptian_clients()

        project_templates = [
            {
                "name": "تطوير موقع إلكتروني متكامل",
                "description": "تصميم وتطوير موقع إلكتروني متجاوب مع لوحة تحكم إدارية",
                "type": "website",
                "budget_range": (15000, 35000),
                "duration_days": (30, 60),
            },
            {
                "name": "تطبيق جوال للتجارة الإلكترونية",
                "description": "تطوير تطبيق جوال لنظامي iOS و Android للتجارة الإلكترونية",
                "type": "mobile_app",
                "budget_range": (25000, 50000),
                "duration_days": (45, 90),
            },
            {
                "name": "حملة تسويق رقمي شاملة",
                "description": "حملة تسويقية متكاملة عبر وسائل التواصل الاجتماعي وجوجل",
                "type": "marketing",
                "budget_range": (8000, 20000),
                "duration_days": (30, 90),
            },
            {
                "name": "تصميم هوية بصرية متكاملة",
                "description": "تصميم شعار وهوية بصرية شاملة مع دليل الاستخدام",
                "type": "website",
                "budget_range": (5000, 15000),
                "duration_days": (14, 30),
            },
            {
                "name": "نظام إدارة محتوى مخصص",
                "description": "تطوير نظام إدارة محتوى مخصص حسب احتياجات العميل",
                "type": "web_app",
                "budget_range": (20000, 40000),
                "duration_days": (60, 120),
            },
            {
                "name": "متجر إلكتروني متكامل",
                "description": "تطوير متجر إلكتروني مع نظام دفع وإدارة المخزون",
                "type": "ecommerce",
                "budget_range": (18000, 35000),
                "duration_days": (45, 75),
            },
            {
                "name": "تطبيق ويب تفاعلي",
                "description": "تطوير تطبيق ويب تفاعلي باستخدام أحدث التقنيات",
                "type": "web_app",
                "budget_range": (22000, 45000),
                "duration_days": (60, 90),
            },
        ]

        created_projects = []

        for i in range(15):  # Create 15 projects
            template = random.choice(project_templates)
            client = random.choice(clients)

            # Generate project name with client context
            project_name = f"{template['name']} - {client.name}"

            # Random budget within range
            budget = random.randint(
                template["budget_range"][0], template["budget_range"][1]
            )

            # Random duration
            duration = random.randint(
                template["duration_days"][0], template["duration_days"][1]
            )

            # Random start date (last 6 months to next 3 months)
            start_date = date.today() + timedelta(days=random.randint(-180, 90))
            end_date = start_date + timedelta(days=duration)

            # Random status
            statuses = ["planning", "active", "completed", "on_hold"]
            status = random.choice(statuses)

            # Random priority
            priorities = ["low", "medium", "high", "urgent"]
            priority = random.choice(priorities)

            project_data = {
                "name": project_name,
                "description": template["description"],
                "client": client,
                "type": template["type"],
                "status": status,
                "priority": priority,
                "budget": budget,
                "start_date": start_date,
                "end_date": end_date,
                "progress": (random.randint(0, 100) if status != "planning" else 0),
            }

            project, created = Project.objects.get_or_create(
                name=project_name, client=client, defaults=project_data
            )

            if created:
                created_projects.append(project)

        self.stdout.write(f"Created {len(created_projects)} digital agency projects")
        return created_projects

    def _create_project_tasks(self):
        """Create realistic project tasks"""
        self.stdout.write("Creating project tasks...")

        from tasks.models import Task
        from projects.models import Project

        projects = list(Project.objects.all())
        if not projects:
            self.stdout.write(
                self.style.WARNING("No projects found. Creating projects first...")
            )
            projects = self._create_digital_agency_projects()

        team_members = list(User.objects.filter(is_superuser=False))

        # Get a user to assign as creator
        founder_user = User.objects.filter(username="founder").first()
        if not founder_user:
            founder_user = User.objects.first()
        if not founder_user:
            self.stdout.write(self.style.WARNING("No users found. Create users first."))
            return []

        task_templates = {
            "website": [
                {"name": "تحليل المتطلبات", "category": "light", "hours": (8, 16)},
                {
                    "name": "تصميم واجهة المستخدم",
                    "category": "medium",
                    "hours": (16, 32),
                },
                {
                    "name": "تطوير الواجهة الأمامية",
                    "category": "extreme",
                    "hours": (24, 48),
                },
                {
                    "name": "تطوير الواجهة الخلفية",
                    "category": "extreme",
                    "hours": (32, 64),
                },
                {"name": "اختبار الموقع", "category": "medium", "hours": (8, 16)},
                {"name": "نشر الموقع", "category": "light", "hours": (4, 8)},
            ],
            "mobile_app": [
                {
                    "name": "تصميم تجربة المستخدم",
                    "category": "medium",
                    "hours": (20, 40),
                },
                {
                    "name": "تطوير تطبيق iOS",
                    "category": "extreme",
                    "hours": (40, 80),
                },
                {
                    "name": "تطوير تطبيق Android",
                    "category": "extreme",
                    "hours": (40, 80),
                },
                {"name": "اختبار التطبيقات", "category": "medium", "hours": (16, 32)},
                {"name": "نشر في المتاجر", "category": "light", "hours": (8, 16)},
            ],
            "marketing": [
                {
                    "name": "إعداد استراتيجية التسويق",
                    "category": "medium",
                    "hours": (12, 24),
                },
                {"name": "إنشاء المحتوى", "category": "medium", "hours": (20, 40)},
                {
                    "name": "إدارة حملات فيسبوك",
                    "category": "medium",
                    "hours": (16, 32),
                },
                {
                    "name": "إدارة حملات جوجل",
                    "category": "medium",
                    "hours": (16, 32),
                },
                {"name": "تحليل النتائج", "category": "light", "hours": (8, 16)},
            ],
            "branding": [
                {
                    "name": "بحث السوق والمنافسين",
                    "category": "light",
                    "hours": (8, 16),
                },
                {"name": "تصميم الشعار", "category": "medium", "hours": (12, 24)},
                {
                    "name": "تطوير الهوية البصرية",
                    "category": "extreme",
                    "hours": (16, 32),
                },
                {
                    "name": "إعداد دليل الاستخدام",
                    "category": "light",
                    "hours": (8, 16),
                },
            ],
        }

        created_tasks = []

        for project in projects:
            project_type = getattr(project, "type", "website")
            templates = task_templates.get(project_type, task_templates["website"])

            # Create 3-6 tasks per project
            num_tasks = random.randint(3, 6)
            selected_templates = random.sample(
                templates, min(num_tasks, len(templates))
            )

            for template in selected_templates:
                # Random assignee
                assignee = random.choice(team_members) if team_members else None

                # Random hours
                estimated_hours = random.randint(
                    template["hours"][0], template["hours"][1]
                )
                actual_hours = (
                    random.randint(0, int(estimated_hours * 1.2))
                    if random.choice([True, False])
                    else 0
                )

                # Random dates (timezone-aware)
                if project.start_date:
                    # Convert date to datetime and make timezone-aware
                    base_datetime = timezone.make_aware(
                        datetime.datetime.combine(
                            project.start_date, datetime.datetime.min.time()
                        )
                    )
                    start_date = base_datetime + timedelta(days=random.randint(0, 30))
                else:
                    start_date = timezone.now()
                due_date = start_date + timedelta(days=random.randint(3, 14))

                # Random status
                statuses = ["pending", "in_progress", "completed", "on_hold"]
                task_status = random.choice(statuses)

                # Random priority
                priorities = ["low", "medium", "high", "urgent"]
                priority = random.choice(priorities)

                task_data = {
                    "title": template["name"],
                    "description": f"مهمة {template['name']} ضمن مشروع {project.name}",
                    "project": project,
                    "assigned_to": assignee,
                    "created_by": founder_user,
                    "category": template["category"],
                    "status": task_status,
                    "priority": priority,
                    "estimated_hours": estimated_hours,
                    "actual_hours": actual_hours,
                    "start_date": start_date,
                    "due_date": due_date,
                }

                task, created = Task.objects.get_or_create(
                    title=template["name"], project=project, defaults=task_data
                )

                if created:
                    created_tasks.append(task)

        self.stdout.write(f"Created {len(created_tasks)} project tasks")
        return created_tasks

    def _create_quotations(self):
        """Create quotations with Egyptian market pricing"""
        self.stdout.write("Creating quotations...")

        from quotations.models import Quotation, QuotationItem
        from clients.models import Client
        from projects.models import Project

        clients = list(Client.objects.all())
        projects = list(Project.objects.all())
        sales_team = list(User.objects.filter(username__contains="sales"))

        if not clients:
            clients = self._create_egyptian_clients()

        quotation_services = [
            {
                "name": "تصميم موقع إلكتروني",
                "unit_price": (8000, 25000),
                "description": "تصميم وتطوير موقع إلكتروني متجاوب",
            },
            {
                "name": "تطبيق جوال",
                "unit_price": (15000, 40000),
                "description": "تطوير تطبيق جوال لنظامي iOS و Android",
            },
            {
                "name": "حملة تسويقية",
                "unit_price": (5000, 15000),
                "description": "حملة تسويق رقمي شاملة",
            },
            {
                "name": "تصميم هوية بصرية",
                "unit_price": (3000, 10000),
                "description": "تصميم شعار وهوية بصرية متكاملة",
            },
            {
                "name": "إدارة وسائل التواصل",
                "unit_price": (2000, 8000),
                "description": "إدارة حسابات وسائل التواصل الاجتماعي",
            },
            {
                "name": "تحسين محركات البحث",
                "unit_price": (4000, 12000),
                "description": "تحسين الموقع لمحركات البحث SEO",
            },
            {
                "name": "متجر إلكتروني",
                "unit_price": (12000, 30000),
                "description": "تطوير متجر إلكتروني متكامل",
            },
            {
                "name": "نظام إدارة محتوى",
                "unit_price": (10000, 25000),
                "description": "تطوير نظام إدارة محتوى مخصص",
            },
        ]

        created_quotations = []

        for i in range(25):  # Create 25 quotations
            client = random.choice(clients)
            sales_rep = random.choice(sales_team) if sales_team else None

            # Random quotation data
            quotation_date = date.today() + timedelta(days=random.randint(-90, 30))
            valid_until = quotation_date + timedelta(days=random.randint(15, 45))

            statuses = ["draft", "sent", "viewed", "approved", "rejected", "expired"]
            status = random.choice(statuses)

            priorities = ["low", "medium", "high", "urgent"]
            priority = random.choice(priorities)

            quotation_data = {
                "title": f"عرض سعر للعميل {client.name}",
                "description": f"عرض سعر شامل للخدمات المطلوبة من العميل {client.name}",
                "client": client,
                "sales_rep": sales_rep,
                "valid_until": valid_until,
                "status": status,
                "priority": priority,
                "discount_percentage": (
                    random.choice([0, 5, 10, 15]) if random.choice([True, False]) else 0
                ),
                "tax_percentage": Decimal("14.00"),  # Egyptian VAT
                "notes": "عرض سعر وفقاً للمتطلبات المحددة من العميل",
                "terms_conditions": "الأسعار سارية لمدة 30 يوم من تاريخ العرض. جميع الأسعار شاملة ضريبة القيمة المضافة.",
            }

            quotation, created = Quotation.objects.get_or_create(
                title=quotation_data["title"], client=client, defaults=quotation_data
            )

            if created:
                # Add quotation items
                num_items = random.randint(1, 4)
                selected_services = random.sample(
                    quotation_services, min(num_items, len(quotation_services))
                )

                # Create service catalog entries first if they don't exist
                from quotations.models import ServiceCatalog

                for service in selected_services:
                    unit_price = random.randint(
                        service["unit_price"][0], service["unit_price"][1]
                    )
                    quantity = random.randint(1, 3)

                    # Create or get service catalog entry
                    service_catalog, _ = ServiceCatalog.objects.get_or_create(
                        name_ar=service["name"],
                        defaults={
                            "name_en": service["name"],
                            "category": "web_development",
                            "description_ar": service["description"],
                            "base_price": unit_price,
                            "unit": "project",
                        },
                    )

                    QuotationItem.objects.create(
                        quotation=quotation,
                        service=service_catalog,
                        description=service["description"],
                        quantity=quantity,
                        unit_price=unit_price,
                    )

                # Calculate totals
                quotation.calculate_totals()

                created_quotations.append(quotation)

        self.stdout.write(f"Created {len(created_quotations)} quotations")
        return created_quotations

    def _create_invoices(self):
        """Create invoices with various statuses"""
        self.stdout.write("Creating invoices...")

        from invoices.models import Invoice, InvoiceItem
        from quotations.models import Quotation
        from clients.models import Client
        from projects.models import Project

        clients = list(Client.objects.all())
        projects = list(Project.objects.all())
        quotations = list(Quotation.objects.filter(status="approved"))
        sales_team = list(User.objects.filter(username__contains="sales"))

        if not clients:
            clients = self._create_egyptian_clients()

        # Create invoices from approved quotations
        created_invoices = []

        for quotation in quotations[:15]:  # Convert 15 approved quotations to invoices
            invoice_date = quotation.created_at.date() + timedelta(
                days=random.randint(1, 10)
            )
            due_date = invoice_date + timedelta(days=random.randint(15, 45))

            statuses = ["draft", "sent", "viewed", "partially_paid", "paid", "overdue"]
            status = random.choice(statuses)

            invoice_data = {
                "title": f"فاتورة للعرض {quotation.quotation_number}",
                "description": quotation.description,
                "client": quotation.client,
                "quotation": quotation,
                "sales_rep": quotation.sales_rep,
                "invoice_date": invoice_date,
                "due_date": due_date,
                "status": status,
                "priority": quotation.priority,
                "discount_percentage": quotation.discount_percentage,
                "tax_percentage": quotation.tax_percentage,
                "notes": quotation.notes,
                "terms_conditions": quotation.terms_conditions,
                "payment_terms": random.choice(["net_15", "net_30", "net_45"]),
                "payment_instructions": "يمكن الدفع عبر التحويل البنكي أو فودافون كاش أو فوري",
            }

            invoice, created = Invoice.objects.get_or_create(
                title=invoice_data["title"],
                client=quotation.client,
                defaults=invoice_data,
            )

            if created:
                # Copy quotation items to invoice
                for quotation_item in quotation.items.all():
                    InvoiceItem.objects.create(
                        invoice=invoice,
                        name=quotation_item.name,
                        description=quotation_item.description,
                        quantity=quotation_item.quantity,
                        unit_price=quotation_item.unit_price,
                        is_taxable=quotation_item.is_taxable,
                        tax_percentage=quotation_item.tax_percentage,
                    )

                # Calculate amounts
                invoice.calculate_amounts()
                invoice.save()

                created_invoices.append(invoice)

        # Create additional standalone invoices
        invoice_services = [
            {"name": "استشارة تقنية", "unit_price": (1000, 5000)},
            {"name": "صيانة موقع إلكتروني", "unit_price": (500, 2000)},
            {"name": "تحديث تطبيق جوال", "unit_price": (2000, 8000)},
            {"name": "تدريب فريق العمل", "unit_price": (1500, 6000)},
            {"name": "دعم فني شهري", "unit_price": (800, 3000)},
        ]

        for i in range(15):  # Create 15 additional invoices
            client = random.choice(clients)
            project = (
                random.choice(projects)
                if projects and random.choice([True, False])
                else None
            )
            sales_rep = random.choice(sales_team) if sales_team else None

            invoice_date = date.today() + timedelta(days=random.randint(-60, 0))
            due_date = invoice_date + timedelta(days=random.randint(15, 45))

            statuses = ["draft", "sent", "viewed", "partially_paid", "paid", "overdue"]
            status = random.choice(statuses)

            invoice_data = {
                "title": f"فاتورة خدمات - {client.name}",
                "description": f"فاتورة خدمات متنوعة للعميل {client.name}",
                "client": client,
                "project": project,
                "sales_rep": sales_rep,
                "invoice_date": invoice_date,
                "due_date": due_date,
                "status": status,
                "priority": random.choice(["low", "medium", "high"]),
                "discount_percentage": (
                    random.choice([0, 5, 10]) if random.choice([True, False]) else 0
                ),
                "tax_percentage": Decimal("14.00"),
                "payment_terms": random.choice(["net_15", "net_30", "net_45"]),
                "payment_instructions": "يمكن الدفع عبر التحويل البنكي أو فودافون كاش أو فوري",
            }

            invoice, created = Invoice.objects.get_or_create(
                title=invoice_data["title"],
                client=client,
                invoice_date=invoice_date,
                defaults=invoice_data,
            )

            if created:
                # Add invoice items
                num_items = random.randint(1, 3)
                selected_services = random.sample(
                    invoice_services, min(num_items, len(invoice_services))
                )

                for service in selected_services:
                    unit_price = random.randint(
                        service["unit_price"][0], service["unit_price"][1]
                    )
                    quantity = random.randint(1, 2)

                    InvoiceItem.objects.create(
                        invoice=invoice,
                        name=service["name"],
                        description=f'خدمة {service["name"]} للعميل',
                        quantity=quantity,
                        unit_price=unit_price,
                        is_taxable=True,
                        tax_percentage=Decimal("14.00"),
                    )

                # Calculate amounts
                invoice.calculate_amounts()
                invoice.save()

                created_invoices.append(invoice)

        self.stdout.write(f"Created {len(created_invoices)} invoices")
        return created_invoices

    def _create_payments(self):
        """Create payment records with Egyptian payment methods"""
        self.stdout.write("Creating payment records...")

        from invoices.models import Invoice, InvoicePayment

        invoices = list(Invoice.objects.exclude(status__in=["draft", "cancelled"]))

        if not invoices:
            self.stdout.write(self.style.WARNING("No invoices found for payments"))
            return []

        egyptian_payment_methods = [
            "bank_transfer",
            "vodafone_cash",
            "fawry",
            "orange_money",
            "etisalat_cash",
            "credit_card",
            "cash",
        ]

        payment_gateways = ["manual", "paymob", "stripe", "paypal"]

        created_payments = []

        for invoice in invoices:
            # Determine if invoice should have payments based on status
            if invoice.status in ["paid", "partially_paid"]:
                # Create payment records
                if invoice.status == "paid":
                    # Full payment
                    payment_amount = invoice.total_amount
                    payment_status = "completed"
                else:
                    # Partial payment
                    payment_amount = invoice.total_amount * Decimal(
                        str(random.uniform(0.3, 0.8))
                    )
                    payment_status = "completed"

                payment_method = random.choice(egyptian_payment_methods)
                gateway = (
                    "paymob"
                    if payment_method in ["credit_card", "vodafone_cash", "fawry"]
                    else "manual"
                )

                payment_date = invoice.invoice_date + timedelta(
                    days=random.randint(1, 30)
                )

                payment_data = {
                    "invoice": invoice,
                    "payment_method": payment_method,
                    "payment_gateway": gateway,
                    "amount": payment_amount,
                    "status": payment_status,
                    "payment_date": payment_date,
                    "processed_date": payment_date,
                    "transaction_id": f"TXN{random.randint(100000, 999999)}",
                    "reference_number": f"REF{random.randint(10000, 99999)}",
                    "notes": f"دفعة عبر {payment_method}",
                }

                if gateway == "paymob":
                    payment_data.update(
                        {
                            "gateway_order_id": f"ORD{random.randint(100000, 999999)}",
                            "gateway_payment_id": f"PAY{random.randint(100000, 999999)}",
                            "gateway_fees": payment_amount
                            * Decimal("0.025"),  # 2.5% gateway fee
                            "gateway_response": {
                                "success": True,
                                "payment_method": payment_method,
                                "transaction_id": payment_data["transaction_id"],
                            },
                        }
                    )

                payment, created = InvoicePayment.objects.get_or_create(
                    invoice=invoice,
                    transaction_id=payment_data["transaction_id"],
                    defaults=payment_data,
                )

                if created:
                    created_payments.append(payment)

                    # Update invoice paid amount
                    invoice.paid_amount = payment_amount
                    invoice.save()

        self.stdout.write(f"Created {len(created_payments)} payment records")
        return created_payments

    def _create_commissions(self):
        """Create commission records for sales team"""
        self.stdout.write("Creating commission records...")

        try:
            from commissions.models import Commission
        except ImportError:
            self.stdout.write(
                self.style.WARNING("Commissions app not found, skipping...")
            )
            return []

        from invoices.models import Invoice

        paid_invoices = list(
            Invoice.objects.filter(status="paid", sales_rep__isnull=False)
        )

        if not paid_invoices:
            self.stdout.write(
                self.style.WARNING("No paid invoices with sales reps found")
            )
            return []

        created_commissions = []

        for invoice in paid_invoices:
            # 12.5% commission rate
            commission_rate = Decimal("12.5")
            commission_amount = (invoice.total_amount * commission_rate) / 100

            commission_data = {
                "sales_rep": invoice.sales_rep,
                "invoice": invoice,
                "client": invoice.client,
                "project": invoice.project,
                "commission_rate": commission_rate,
                "commission_amount": commission_amount,
                "base_amount": invoice.total_amount,
                "status": random.choice(["pending", "approved", "paid"]),
                "earned_date": invoice.invoice_date,
                "notes": f"عمولة {commission_rate}% من فاتورة {invoice.invoice_number}",
            }

            if commission_data["status"] in ["approved", "paid"]:
                commission_data["approved_date"] = invoice.paid_date or timezone.now()

            if commission_data["status"] == "paid":
                commission_data["paid_date"] = commission_data[
                    "approved_date"
                ] + timedelta(days=random.randint(1, 15))

            commission, created = Commission.objects.get_or_create(
                sales_rep=invoice.sales_rep, invoice=invoice, defaults=commission_data
            )

            if created:
                created_commissions.append(commission)

        self.stdout.write(f"Created {len(created_commissions)} commission records")
        return created_commissions

    def _create_support_data(self):
        """Create customer service and support data"""
        self.stdout.write("Creating support data...")

        # This would create support tickets, knowledge base articles, etc.
        # For now, we'll create a simple placeholder

        support_topics = [
            "مشكلة في تسجيل الدخول",
            "طلب تعديل على الموقع",
            "استفسار عن الفاتورة",
            "طلب دعم فني",
            "مشكلة في التطبيق الجوال",
            "طلب تدريب",
            "استفسار عن الخدمات",
            "مشكلة في الدفع",
            "طلب تحديث البيانات",
            "شكوى من الخدمة",
        ]

        # Create placeholder support data
        created_support_items = []

        # In a real implementation, this would create actual support tickets
        # For now, we'll just log that support data would be created

        for i, topic in enumerate(support_topics):
            # Placeholder for support ticket creation
            support_item = {
                "id": i + 1,
                "topic": topic,
                "status": random.choice(["open", "in_progress", "resolved", "closed"]),
                "priority": random.choice(["low", "medium", "high", "urgent"]),
                "created_date": date.today() + timedelta(days=random.randint(-30, 0)),
            }
            created_support_items.append(support_item)

        self.stdout.write(
            f"Created {len(created_support_items)} support items (placeholder)"
        )
        return created_support_items
