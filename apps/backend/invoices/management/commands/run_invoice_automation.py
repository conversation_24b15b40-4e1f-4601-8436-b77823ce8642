"""
Management command to run invoice automation tasks.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from invoices.tasks import (
    send_overdue_payment_reminders,
    update_invoice_statuses,
    generate_monthly_invoice_report
)


class Command(BaseCommand):
    help = 'Run automated invoice management tasks'

    def add_arguments(self, parser):
        parser.add_argument(
            '--task',
            type=str,
            choices=['reminders', 'statuses', 'report', 'all'],
            default='all',
            help='Specific task to run (default: all)'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without actually doing it'
        )

    def handle(self, *args, **options):
        task = options['task']
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No actual changes will be made')
            )
        
        self.stdout.write(
            self.style.SUCCESS(f'Starting invoice automation tasks at {timezone.now()}')
        )
        
        if task in ['reminders', 'all']:
            self._run_overdue_reminders(dry_run)
        
        if task in ['statuses', 'all']:
            self._run_status_updates(dry_run)
        
        if task in ['report', 'all']:
            self._run_monthly_report(dry_run)
        
        self.stdout.write(
            self.style.SUCCESS('Invoice automation tasks completed successfully')
        )

    def _run_overdue_reminders(self, dry_run):
        """Run overdue payment reminders"""
        self.stdout.write('Running overdue payment reminders...')
        
        if not dry_run:
            try:
                result = send_overdue_payment_reminders.delay()
                self.stdout.write(
                    self.style.SUCCESS(f'Overdue reminders task queued: {result.id}')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Failed to queue overdue reminders: {str(e)}')
                )
        else:
            # In dry run, just show what would be done
            from invoices.models import Invoice
            from datetime import date
            from django.db.models import Q
            
            today = date.today()
            overdue_invoices = Invoice.objects.filter(
                Q(status__in=['sent', 'viewed', 'partially_paid']) &
                Q(due_date__lt=today)
            ).select_related('client')
            
            self.stdout.write(f'Would send reminders for {overdue_invoices.count()} overdue invoices')
            
            for invoice in overdue_invoices[:5]:  # Show first 5
                days_overdue = (today - invoice.due_date).days
                self.stdout.write(f'  - {invoice.invoice_number}: {days_overdue} days overdue')

    def _run_status_updates(self, dry_run):
        """Run invoice status updates"""
        self.stdout.write('Running invoice status updates...')
        
        if not dry_run:
            try:
                result = update_invoice_statuses.delay()
                self.stdout.write(
                    self.style.SUCCESS(f'Status updates task queued: {result.id}')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Failed to queue status updates: {str(e)}')
                )
        else:
            # In dry run, show what would be updated
            from invoices.models import Invoice
            from datetime import date
            from django.db.models import Q, F
            
            today = date.today()
            
            # Count overdue invoices
            overdue_count = Invoice.objects.filter(
                Q(status__in=['sent', 'viewed', 'partially_paid']) &
                Q(due_date__lt=today)
            ).count()
            
            # Count fully paid invoices
            paid_count = Invoice.objects.filter(
                Q(status__in=['sent', 'viewed', 'partially_paid', 'overdue']) &
                Q(paid_amount__gte=F('total_amount'))
            ).count()
            
            self.stdout.write(f'Would mark {overdue_count} invoices as overdue')
            self.stdout.write(f'Would mark {paid_count} invoices as paid')

    def _run_monthly_report(self, dry_run):
        """Run monthly report generation"""
        self.stdout.write('Running monthly report generation...')
        
        if not dry_run:
            try:
                result = generate_monthly_invoice_report.delay()
                self.stdout.write(
                    self.style.SUCCESS(f'Monthly report task queued: {result.id}')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Failed to queue monthly report: {str(e)}')
                )
        else:
            # In dry run, show report summary
            from invoices.models import Invoice
            from datetime import date
            from django.db.models import Sum, Count
            
            today = date.today()
            first_day = today.replace(day=1)
            
            monthly_invoices = Invoice.objects.filter(
                invoice_date__gte=first_day,
                invoice_date__lt=today
            )
            
            stats = monthly_invoices.aggregate(
                total_count=Count('id'),
                total_amount=Sum('total_amount'),
                paid_amount=Sum('paid_amount')
            )
            
            self.stdout.write(f'Monthly report for {today.strftime("%Y-%m")}:')
            self.stdout.write(f'  - Total invoices: {stats["total_count"] or 0}')
            self.stdout.write(f'  - Total amount: {stats["total_amount"] or 0:.2f} EGP')
            self.stdout.write(f'  - Paid amount: {stats["paid_amount"] or 0:.2f} EGP')
