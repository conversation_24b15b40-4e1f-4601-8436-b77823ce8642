"""
Smart automated workflows for invoice management with Egyptian business compliance.
"""

import logging
from datetime import date, timedelta
from decimal import Decimal
from django.utils import timezone
from django.db.models import Q, F
from django.conf import settings

logger = logging.getLogger(__name__)


class InvoiceAutomationWorkflows:
    """Smart automation workflows for invoice management"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def auto_generate_from_quotation(self, quotation):
        """
        Automatically generate invoice when quotation is approved
        """
        try:
            from ..models import Invoice, InvoiceItem
            from ..tasks import process_sales_commission
            
            # Check if invoice already exists
            existing_invoice = Invoice.objects.filter(quotation=quotation).first()
            if existing_invoice:
                self.logger.info(f"Invoice already exists for quotation {quotation.quotation_number}")
                return existing_invoice
            
            # Create invoice with smart defaults
            invoice = Invoice.objects.create(
                title=f"فاتورة للعرض {quotation.quotation_number}",
                description=quotation.description,
                client=quotation.client,
                project=quotation.project,
                quotation=quotation,
                sales_rep=quotation.sales_rep,
                priority=quotation.priority,
                discount_percentage=quotation.discount_percentage,
                tax_percentage=quotation.tax_percentage or Decimal("14.00"),  # Egyptian VAT
                invoice_date=date.today(),
                due_date=self._calculate_smart_due_date(quotation.client),
                notes=quotation.notes,
                terms_conditions=quotation.terms_conditions or self._get_default_terms(),
                payment_terms=self._determine_payment_terms(quotation.client),
                payment_instructions=self._get_payment_instructions(),
            )
            
            # Copy quotation items
            for quotation_item in quotation.items.all():
                InvoiceItem.objects.create(
                    invoice=invoice,
                    name=quotation_item.name,
                    description=quotation_item.description,
                    item_type=quotation_item.item_type,
                    quantity=quotation_item.quantity,
                    unit_price=quotation_item.unit_price,
                    is_taxable=quotation_item.is_taxable,
                    tax_percentage=quotation_item.tax_percentage or Decimal("14.00"),
                )
            
            # Calculate amounts
            invoice.calculate_amounts()
            invoice.save()
            
            # Trigger commission processing
            if invoice.sales_rep:
                process_sales_commission.delay(invoice.id)
            
            self.logger.info(f"Auto-generated invoice {invoice.invoice_number} from quotation {quotation.quotation_number}")
            
            return invoice
            
        except Exception as e:
            self.logger.error(f"Failed to auto-generate invoice from quotation {quotation.id}: {str(e)}")
            raise
    
    def auto_update_payment_status(self, payment):
        """
        Automatically update invoice status when payment is processed
        """
        try:
            from ..email_service import send_payment_confirmation_email
            
            invoice = payment.invoice
            
            if payment.status == 'completed':
                # Recalculate paid amount
                total_paid = invoice.payments.filter(status='completed').aggregate(
                    total=models.Sum('amount')
                )['total'] or 0
                
                invoice.paid_amount = total_paid
                
                # Update invoice status
                if total_paid >= invoice.total_amount:
                    invoice.status = 'paid'
                    if not invoice.paid_date:
                        invoice.paid_date = timezone.now()
                elif total_paid > 0:
                    invoice.status = 'partially_paid'
                
                invoice.save()
                
                # Send payment confirmation email
                try:
                    send_payment_confirmation_email(invoice, payment)
                except Exception as e:
                    self.logger.warning(f"Failed to send payment confirmation email: {str(e)}")
                
                # Update commission status if fully paid
                if invoice.status == 'paid' and invoice.sales_rep:
                    self._update_commission_status(invoice)
                
                self.logger.info(f"Auto-updated payment status for invoice {invoice.invoice_number}")
            
        except Exception as e:
            self.logger.error(f"Failed to auto-update payment status: {str(e)}")
    
    def auto_currency_conversion(self, invoice):
        """
        Automatically convert currencies using real-time rates
        """
        try:
            from finance.services.currency_service import CurrencyService
            
            currency_service = CurrencyService()
            
            # Convert amounts if client has different preferred currency
            if hasattr(invoice.client, 'preferred_currency') and invoice.client.preferred_currency != 'EGP':
                target_currency = invoice.client.preferred_currency
                
                # Convert total amount for display
                converted_amount = currency_service.convert_amount(
                    amount=invoice.total_amount,
                    from_currency='EGP',
                    to_currency=target_currency
                )
                
                # Store conversion info in invoice notes
                conversion_note = f"\nالمبلغ بعملة {target_currency}: {converted_amount:,.2f}"
                if invoice.notes:
                    invoice.notes += conversion_note
                else:
                    invoice.notes = f"تحويل العملة:{conversion_note}"
                
                invoice.save()
                
                self.logger.info(f"Auto-converted currency for invoice {invoice.invoice_number}")
            
        except Exception as e:
            self.logger.warning(f"Failed to auto-convert currency: {str(e)}")
    
    def auto_tax_calculation(self, invoice):
        """
        Automatically calculate Egyptian taxes and compliance
        """
        try:
            # Egyptian VAT is 14%
            egyptian_vat_rate = Decimal("14.00")
            
            # Update tax percentage if not set
            if not invoice.tax_percentage:
                invoice.tax_percentage = egyptian_vat_rate
            
            # Update item tax rates
            for item in invoice.items.all():
                if item.is_taxable and not item.tax_percentage:
                    item.tax_percentage = egyptian_vat_rate
                    item.save()
            
            # Recalculate amounts
            invoice.calculate_amounts()
            invoice.save()
            
            self.logger.info(f"Auto-calculated Egyptian taxes for invoice {invoice.invoice_number}")
            
        except Exception as e:
            self.logger.error(f"Failed to auto-calculate taxes: {str(e)}")
    
    def schedule_payment_reminders(self, invoice):
        """
        Schedule automated payment reminders
        """
        try:
            from ..tasks import send_overdue_payment_reminders
            from celery import current_app
            
            if invoice.due_date and invoice.status not in ['paid', 'cancelled']:
                # Schedule reminders at 3, 7, 14, 30 days after due date
                reminder_days = [3, 7, 14, 30]
                
                for days in reminder_days:
                    reminder_date = invoice.due_date + timedelta(days=days)
                    
                    # Schedule task
                    current_app.send_task(
                        'invoices.tasks.send_overdue_payment_reminders',
                        eta=reminder_date
                    )
                
                self.logger.info(f"Scheduled payment reminders for invoice {invoice.invoice_number}")
            
        except Exception as e:
            self.logger.warning(f"Failed to schedule payment reminders: {str(e)}")
    
    def _calculate_smart_due_date(self, client):
        """Calculate smart due date based on client history"""
        try:
            # Default to 30 days
            default_days = 30
            
            # Check client's payment history
            if hasattr(client, 'invoices'):
                recent_invoices = client.invoices.filter(
                    status='paid',
                    paid_date__isnull=False
                ).order_by('-paid_date')[:5]
                
                if recent_invoices.exists():
                    # Calculate average payment time
                    total_days = 0
                    count = 0
                    
                    for inv in recent_invoices:
                        if inv.due_date and inv.paid_date:
                            days_to_pay = (inv.paid_date.date() - inv.due_date).days
                            if days_to_pay > 0:  # Only count late payments
                                total_days += days_to_pay
                                count += 1
                    
                    if count > 0:
                        avg_late_days = total_days / count
                        # Add buffer for historically late clients
                        default_days += int(avg_late_days)
            
            return date.today() + timedelta(days=min(default_days, 60))  # Max 60 days
            
        except Exception:
            return date.today() + timedelta(days=30)
    
    def _determine_payment_terms(self, client):
        """Determine payment terms based on client relationship"""
        try:
            # Check client's payment history and relationship
            if hasattr(client, 'invoices'):
                total_invoices = client.invoices.count()
                paid_invoices = client.invoices.filter(status='paid').count()
                
                if total_invoices > 0:
                    payment_rate = paid_invoices / total_invoices
                    
                    if payment_rate >= 0.9 and total_invoices >= 5:
                        return "net_45"  # Good payment history
                    elif payment_rate >= 0.7:
                        return "net_30"  # Average payment history
                    else:
                        return "net_15"  # Poor payment history
            
            return "net_30"  # Default for new clients
            
        except Exception:
            return "net_30"
    
    def _get_default_terms(self):
        """Get default terms and conditions in Arabic"""
        return """
الشروط والأحكام:
1. يجب سداد الفاتورة في الموعد المحدد
2. في حالة التأخير في السداد، سيتم تطبيق غرامة تأخير 2% شهرياً
3. جميع الأسعار شاملة ضريبة القيمة المضافة 14%
4. يحتفظ مقدم الخدمة بحقوق الملكية الفكرية حتى السداد الكامل
5. أي نزاع يخضع لقوانين جمهورية مصر العربية
        """.strip()
    
    def _get_payment_instructions(self):
        """Get payment instructions in Arabic"""
        return """
طرق الدفع المتاحة:
- تحويل بنكي: حساب رقم 123456789 - البنك الأهلي المصري
- فودافون كاش: 01234567890
- فوري: كود الدفع سيتم إرساله عند الطلب
- بطاقة ائتمان: عبر الموقع الإلكتروني
        """.strip()
    
    def _update_commission_status(self, invoice):
        """Update commission status when invoice is paid"""
        try:
            from commissions.models import Commission
            
            commissions = Commission.objects.filter(
                invoice=invoice,
                status='pending'
            )
            
            for commission in commissions:
                commission.status = 'approved'
                commission.approved_date = timezone.now()
                commission.save()
            
            self.logger.info(f"Updated commission status for invoice {invoice.invoice_number}")
            
        except Exception as e:
            self.logger.warning(f"Failed to update commission status: {str(e)}")


# Convenience instance
invoice_automation = InvoiceAutomationWorkflows()
