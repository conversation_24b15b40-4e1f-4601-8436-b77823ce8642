"""
Professional email service for invoice delivery with Arabic RTL templates.
"""

import os
import logging
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from .pdf_generator import InvoicePDFGenerator

logger = logging.getLogger(__name__)


class InvoiceEmailService:
    """Professional email service for invoice delivery"""
    
    def __init__(self):
        self.pdf_generator = InvoicePDFGenerator()
        
    def send_invoice_email(self, invoice, recipient_email=None, cc_emails=None, custom_message=None):
        """Send invoice email with PDF attachment"""
        try:
            # Determine recipient
            recipient = recipient_email or invoice.client.email
            if not recipient:
                raise ValueError("لا يوجد بريد إلكتروني للعميل")
            
            # Prepare email context
            context = self._prepare_email_context(invoice, custom_message)
            
            # Generate email content
            subject = f"فاتورة رقم {invoice.invoice_number} من {settings.COMPANY_NAME_AR}"
            html_content = render_to_string('invoices/email/invoice_email.html', context)
            text_content = render_to_string('invoices/email/invoice_email.txt', context)
            
            # Create email
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[recipient],
                cc=cc_emails or []
            )
            
            # Attach HTML version
            email.attach_alternative(html_content, "text/html")
            
            # Generate and attach PDF
            try:
                pdf_content = self.pdf_generator.generate_pdf(invoice)
                pdf_filename = f"invoice_{invoice.invoice_number}.pdf"
                email.attach(pdf_filename, pdf_content, 'application/pdf')
            except Exception as e:
                logger.warning(f"Failed to attach PDF for invoice {invoice.invoice_number}: {str(e)}")
            
            # Send email
            email.send()
            
            # Log successful send
            logger.info(f"Invoice email sent successfully for {invoice.invoice_number} to {recipient}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send invoice email for {invoice.invoice_number}: {str(e)}")
            raise Exception(f"فشل في إرسال البريد الإلكتروني: {str(e)}")
    
    def send_payment_confirmation_email(self, invoice, payment):
        """Send payment confirmation email"""
        try:
            recipient = invoice.client.email
            if not recipient:
                return False
            
            context = {
                'invoice': invoice,
                'payment': payment,
                'client': invoice.client,
                'company_name': getattr(settings, 'COMPANY_NAME_AR', 'MTBRMG'),
                'company_email': getattr(settings, 'COMPANY_EMAIL', '<EMAIL>'),
                'company_phone': getattr(settings, 'COMPANY_PHONE', '+20 ************'),
                'current_year': timezone.now().year,
            }
            
            subject = f"تأكيد استلام الدفعة - فاتورة رقم {invoice.invoice_number}"
            html_content = render_to_string('invoices/email/payment_confirmation.html', context)
            text_content = render_to_string('invoices/email/payment_confirmation.txt', context)
            
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[recipient]
            )
            
            email.attach_alternative(html_content, "text/html")
            email.send()
            
            logger.info(f"Payment confirmation email sent for invoice {invoice.invoice_number}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send payment confirmation email: {str(e)}")
            return False
    
    def send_overdue_reminder_email(self, invoice, days_overdue):
        """Send overdue payment reminder email"""
        try:
            recipient = invoice.client.email
            if not recipient:
                return False
            
            context = {
                'invoice': invoice,
                'client': invoice.client,
                'days_overdue': days_overdue,
                'company_name': getattr(settings, 'COMPANY_NAME_AR', 'MTBRMG'),
                'company_email': getattr(settings, 'COMPANY_EMAIL', '<EMAIL>'),
                'company_phone': getattr(settings, 'COMPANY_PHONE', '+20 ************'),
                'current_year': timezone.now().year,
            }
            
            subject = f"تذكير بالدفع - فاتورة رقم {invoice.invoice_number} متأخرة {days_overdue} يوم"
            html_content = render_to_string('invoices/email/overdue_reminder.html', context)
            text_content = render_to_string('invoices/email/overdue_reminder.txt', context)
            
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[recipient]
            )
            
            email.attach_alternative(html_content, "text/html")
            
            # Attach PDF invoice
            try:
                pdf_content = self.pdf_generator.generate_pdf(invoice)
                pdf_filename = f"invoice_{invoice.invoice_number}.pdf"
                email.attach(pdf_filename, pdf_content, 'application/pdf')
            except Exception as e:
                logger.warning(f"Failed to attach PDF to reminder email: {str(e)}")
            
            email.send()
            
            logger.info(f"Overdue reminder email sent for invoice {invoice.invoice_number}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send overdue reminder email: {str(e)}")
            return False
    
    def _prepare_email_context(self, invoice, custom_message=None):
        """Prepare context data for email templates"""
        
        # Calculate payment status
        payment_percentage = 0
        if invoice.total_amount > 0:
            payment_percentage = (invoice.paid_amount / invoice.total_amount) * 100
        
        context = {
            'invoice': invoice,
            'client': invoice.client,
            'items': invoice.items.all(),
            'payment_percentage': payment_percentage,
            'is_fully_paid': invoice.status == 'paid',
            'is_overdue': invoice.status == 'overdue',
            'custom_message': custom_message,
            'company_name': getattr(settings, 'COMPANY_NAME_AR', 'MTBRMG'),
            'company_email': getattr(settings, 'COMPANY_EMAIL', '<EMAIL>'),
            'company_phone': getattr(settings, 'COMPANY_PHONE', '+20 ************'),
            'company_website': getattr(settings, 'COMPANY_WEBSITE', 'www.mtbrmg.com'),
            'current_year': timezone.now().year,
            'support_email': getattr(settings, 'SUPPORT_EMAIL', '<EMAIL>'),
        }
        
        return context


# Convenience functions for easy usage
def send_invoice_email(invoice, recipient_email=None, cc_emails=None, custom_message=None):
    """Send invoice email - convenience function"""
    service = InvoiceEmailService()
    return service.send_invoice_email(invoice, recipient_email, cc_emails, custom_message)


def send_payment_confirmation_email(invoice, payment):
    """Send payment confirmation email - convenience function"""
    service = InvoiceEmailService()
    return service.send_payment_confirmation_email(invoice, payment)


def send_overdue_reminder_email(invoice, days_overdue):
    """Send overdue reminder email - convenience function"""
    service = InvoiceEmailService()
    return service.send_overdue_reminder_email(invoice, days_overdue)
