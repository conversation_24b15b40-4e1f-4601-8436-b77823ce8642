"""
Paymob payment gateway integration for Egyptian market.
Supports credit/debit cards, mobile wallets, and bank transfers.
"""

import logging
import requests
import hashlib
import hmac
from decimal import Decimal
from datetime import datetime, timezone
from django.conf import settings
from django.urls import reverse
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class PaymobGateway:
    """Paymob payment gateway integration"""
    
    def __init__(self):
        self.api_key = getattr(settings, 'PAYMOB_API_KEY', '')
        self.integration_id = getattr(settings, 'PAYMOB_INTEGRATION_ID', '')
        self.iframe_id = getattr(settings, 'PAYMOB_IFRAME_ID', '')
        self.hmac_secret = getattr(settings, 'PAYMOB_HMAC_SECRET', '')
        self.base_url = getattr(settings, 'PAYMOB_BASE_URL', 'https://accept.paymob.com/api')
        self.is_sandbox = getattr(settings, 'PAYMOB_SANDBOX', True)
        
        if self.is_sandbox:
            self.base_url = 'https://accept.paymobsolutions.com/api'
    
    def authenticate(self) -> Optional[str]:
        """Authenticate with Paymob and get auth token"""
        try:
            url = f"{self.base_url}/auth/tokens"
            payload = {"api_key": self.api_key}
            
            response = requests.post(url, json=payload, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            auth_token = data.get('token')
            
            if not auth_token:
                logger.error("Failed to get auth token from Paymob")
                return None
            
            logger.info("Successfully authenticated with Paymob")
            return auth_token
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Paymob authentication failed: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error during Paymob authentication: {str(e)}")
            return None
    
    def create_order(self, auth_token: str, invoice) -> Optional[Dict[str, Any]]:
        """Create order in Paymob"""
        try:
            url = f"{self.base_url}/ecommerce/orders"
            
            # Convert amount to cents (Paymob expects amount in cents)
            amount_cents = int(float(invoice.total_amount) * 100)
            
            payload = {
                "auth_token": auth_token,
                "delivery_needed": "false",
                "amount_cents": amount_cents,
                "currency": "EGP",
                "items": self._prepare_order_items(invoice),
                "merchant_order_id": invoice.invoice_number,
                "shipping_data": self._prepare_shipping_data(invoice),
                "shipping_details": {
                    "notes": f"Invoice payment for {invoice.invoice_number}",
                    "amount_cents": 0,
                    "description": "No shipping required"
                }
            }
            
            response = requests.post(url, json=payload, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"Successfully created Paymob order for invoice {invoice.invoice_number}")
            
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to create Paymob order: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error creating Paymob order: {str(e)}")
            return None
    
    def create_payment_key(self, auth_token: str, order_id: int, invoice) -> Optional[str]:
        """Create payment key for Paymob iframe"""
        try:
            url = f"{self.base_url}/acceptance/payment_keys"
            
            # Convert amount to cents
            amount_cents = int(float(invoice.total_amount) * 100)
            
            payload = {
                "auth_token": auth_token,
                "amount_cents": amount_cents,
                "expiration": 3600,  # 1 hour
                "order_id": order_id,
                "billing_data": self._prepare_billing_data(invoice),
                "currency": "EGP",
                "integration_id": self.integration_id,
                "lock_order_when_paid": "false"
            }
            
            response = requests.post(url, json=payload, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            payment_key = data.get('token')
            
            if not payment_key:
                logger.error("Failed to get payment key from Paymob")
                return None
            
            logger.info(f"Successfully created payment key for invoice {invoice.invoice_number}")
            return payment_key
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to create payment key: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error creating payment key: {str(e)}")
            return None
    
    def create_payment_intent(self, invoice) -> Optional[Dict[str, Any]]:
        """Create complete payment intent for invoice"""
        try:
            # Step 1: Authenticate
            auth_token = self.authenticate()
            if not auth_token:
                return None
            
            # Step 2: Create order
            order_data = self.create_order(auth_token, invoice)
            if not order_data:
                return None
            
            order_id = order_data.get('id')
            if not order_id:
                logger.error("No order ID returned from Paymob")
                return None
            
            # Step 3: Create payment key
            payment_key = self.create_payment_key(auth_token, order_id, invoice)
            if not payment_key:
                return None
            
            # Step 4: Prepare payment URLs
            payment_url = f"https://accept.paymob.com/api/acceptance/iframes/{self.iframe_id}?payment_token={payment_key}"
            
            if self.is_sandbox:
                payment_url = f"https://accept.paymobsolutions.com/api/acceptance/iframes/{self.iframe_id}?payment_token={payment_key}"
            
            return {
                'payment_url': payment_url,
                'payment_key': payment_key,
                'order_id': order_id,
                'amount_cents': int(float(invoice.total_amount) * 100),
                'currency': 'EGP'
            }
            
        except Exception as e:
            logger.error(f"Failed to create payment intent: {str(e)}")
            return None
    
    def verify_webhook_signature(self, payload: str, signature: str) -> bool:
        """Verify webhook signature from Paymob"""
        try:
            expected_signature = hmac.new(
                self.hmac_secret.encode('utf-8'),
                payload.encode('utf-8'),
                hashlib.sha512
            ).hexdigest()
            
            return hmac.compare_digest(expected_signature, signature)
            
        except Exception as e:
            logger.error(f"Failed to verify webhook signature: {str(e)}")
            return False
    
    def process_webhook(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process webhook from Paymob"""
        try:
            transaction_id = webhook_data.get('id')
            order_id = webhook_data.get('order', {}).get('id')
            merchant_order_id = webhook_data.get('order', {}).get('merchant_order_id')
            amount_cents = webhook_data.get('amount_cents')
            currency = webhook_data.get('currency')
            success = webhook_data.get('success', False)
            pending = webhook_data.get('pending', False)
            
            # Determine payment status
            if success and not pending:
                status = 'completed'
            elif pending:
                status = 'pending'
            else:
                status = 'failed'
            
            return {
                'transaction_id': str(transaction_id),
                'order_id': str(order_id),
                'invoice_number': merchant_order_id,
                'amount': Decimal(str(amount_cents)) / 100 if amount_cents else 0,
                'currency': currency,
                'status': status,
                'payment_method': self._determine_payment_method(webhook_data),
                'raw_data': webhook_data
            }
            
        except Exception as e:
            logger.error(f"Failed to process webhook: {str(e)}")
            return {'status': 'error', 'error': str(e)}
    
    def _prepare_order_items(self, invoice) -> list:
        """Prepare order items for Paymob"""
        items = []
        
        for item in invoice.items.all():
            items.append({
                "name": item.name,
                "amount_cents": int(float(item.total_price) * 100),
                "description": item.description or item.name,
                "quantity": int(item.quantity)
            })
        
        return items
    
    def _prepare_shipping_data(self, invoice) -> Dict[str, str]:
        """Prepare shipping data for Paymob"""
        client = invoice.client
        
        return {
            "apartment": "NA",
            "email": client.email or "<EMAIL>",
            "floor": "NA",
            "first_name": client.name.split()[0] if client.name else "Customer",
            "street": client.address or "No Address",
            "building": "NA",
            "phone_number": client.phone or "+201000000000",
            "postal_code": "00000",
            "extra_description": f"Invoice {invoice.invoice_number}",
            "city": client.get_governorate_display() if hasattr(client, 'governorate') else "Cairo",
            "country": "EG",
            "last_name": " ".join(client.name.split()[1:]) if client.name and len(client.name.split()) > 1 else "Customer",
            "state": client.get_governorate_display() if hasattr(client, 'governorate') else "Cairo"
        }
    
    def _prepare_billing_data(self, invoice) -> Dict[str, str]:
        """Prepare billing data for Paymob"""
        return self._prepare_shipping_data(invoice)
    
    def _determine_payment_method(self, webhook_data: Dict[str, Any]) -> str:
        """Determine payment method from webhook data"""
        source_data = webhook_data.get('source_data', {})
        type_info = source_data.get('type', '').lower()
        
        if 'card' in type_info:
            return 'credit_card'
        elif 'wallet' in type_info:
            sub_type = source_data.get('sub_type', '').lower()
            if 'vodafone' in sub_type:
                return 'vodafone_cash'
            elif 'orange' in sub_type:
                return 'orange_money'
            elif 'etisalat' in sub_type:
                return 'etisalat_cash'
            else:
                return 'fawry'
        elif 'bank' in type_info:
            return 'bank_transfer'
        else:
            return 'other'


# Additional payment gateways for international support
class StripeGateway:
    """Stripe payment gateway for international payments"""
    
    def __init__(self):
        self.api_key = getattr(settings, 'STRIPE_SECRET_KEY', '')
        self.publishable_key = getattr(settings, 'STRIPE_PUBLISHABLE_KEY', '')
        self.webhook_secret = getattr(settings, 'STRIPE_WEBHOOK_SECRET', '')
    
    def create_payment_intent(self, invoice) -> Optional[Dict[str, Any]]:
        """Create Stripe payment intent"""
        try:
            import stripe
            stripe.api_key = self.api_key
            
            # Convert EGP to USD for Stripe (approximate rate)
            amount_usd = float(invoice.total_amount) / 30  # Rough conversion
            amount_cents = int(amount_usd * 100)
            
            intent = stripe.PaymentIntent.create(
                amount=amount_cents,
                currency='usd',
                metadata={
                    'invoice_id': str(invoice.id),
                    'invoice_number': invoice.invoice_number,
                    'client_email': invoice.client.email
                }
            )
            
            return {
                'client_secret': intent.client_secret,
                'payment_intent_id': intent.id,
                'amount': amount_cents,
                'currency': 'usd'
            }
            
        except Exception as e:
            logger.error(f"Failed to create Stripe payment intent: {str(e)}")
            return None


class PayPalGateway:
    """PayPal payment gateway for international payments"""
    
    def __init__(self):
        self.client_id = getattr(settings, 'PAYPAL_CLIENT_ID', '')
        self.client_secret = getattr(settings, 'PAYPAL_CLIENT_SECRET', '')
        self.is_sandbox = getattr(settings, 'PAYPAL_SANDBOX', True)
        
        if self.is_sandbox:
            self.base_url = 'https://api.sandbox.paypal.com'
        else:
            self.base_url = 'https://api.paypal.com'
    
    def create_payment_intent(self, invoice) -> Optional[Dict[str, Any]]:
        """Create PayPal payment intent"""
        try:
            # Convert EGP to USD for PayPal
            amount_usd = float(invoice.total_amount) / 30  # Rough conversion
            
            return {
                'amount': f"{amount_usd:.2f}",
                'currency': 'USD',
                'invoice_id': str(invoice.id),
                'invoice_number': invoice.invoice_number
            }
            
        except Exception as e:
            logger.error(f"Failed to create PayPal payment intent: {str(e)}")
            return None


# Gateway factory
class PaymentGatewayFactory:
    """Factory for creating payment gateway instances"""
    
    @staticmethod
    def get_gateway(gateway_type: str):
        """Get payment gateway instance by type"""
        if gateway_type == 'paymob':
            return PaymobGateway()
        elif gateway_type == 'stripe':
            return StripeGateway()
        elif gateway_type == 'paypal':
            return PayPalGateway()
        else:
            raise ValueError(f"Unsupported gateway type: {gateway_type}")
    
    @staticmethod
    def get_available_gateways() -> list:
        """Get list of available payment gateways"""
        return [
            {
                'type': 'paymob',
                'name': 'Paymob',
                'description': 'Egyptian payment gateway supporting cards and mobile wallets',
                'currencies': ['EGP'],
                'methods': ['credit_card', 'debit_card', 'vodafone_cash', 'orange_money', 'etisalat_cash', 'fawry']
            },
            {
                'type': 'stripe',
                'name': 'Stripe',
                'description': 'International payment gateway',
                'currencies': ['USD', 'EUR'],
                'methods': ['credit_card', 'debit_card']
            },
            {
                'type': 'paypal',
                'name': 'PayPal',
                'description': 'International payment platform',
                'currencies': ['USD', 'EUR'],
                'methods': ['paypal']
            }
        ]
