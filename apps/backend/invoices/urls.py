"""
Invoice module URLs following established patterns from quotations and finance modules.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    InvoiceTemplateViewSet,
    InvoiceViewSet,
    InvoiceItemViewSet,
    InvoicePaymentViewSet,
)
from .webhook_views import PaymobWebhookView, StripeWebhookView, paypal_webhook

# Create router and register viewsets
router = DefaultRouter()
router.register(
    r"invoice-templates", InvoiceTemplateViewSet, basename="invoicetemplate"
)
router.register(r"invoices", InvoiceViewSet, basename="invoice")
router.register(r"invoice-items", InvoiceItemViewSet, basename="invoiceitem")
router.register(r"invoice-payments", InvoicePaymentViewSet, basename="invoicepayment")

urlpatterns = [
    path("", include(router.urls)),
    # Payment gateway webhooks
    path("webhooks/paymob/", PaymobWebhookView.as_view(), name="paymob-webhook"),
    path("webhooks/stripe/", StripeWebhookView.as_view(), name="stripe-webhook"),
    path("webhooks/paypal/", paypal_webhook, name="paypal-webhook"),
]
