# Generated by Django 4.2.9 on 2025-06-05 12:02

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import djmoney.models.fields
import simple_history.models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("quotations", "0001_initial"),
        ("projects", "0003_alter_historicalproject_actual_cost_currency_and_more"),
        ("clients", "0003_alter_client_total_revenue_currency_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="InvoiceTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="اسم القالب")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                (
                    "template_type",
                    models.CharField(
                        choices=[
                            ("standard", "قياسي"),
                            ("detailed", "مفصل"),
                            ("minimal", "مبسط"),
                            ("custom", "مخصص"),
                        ],
                        default="standard",
                        max_length=20,
                        verbose_name="نوع القالب",
                    ),
                ),
                (
                    "header_text",
                    models.TextField(blank=True, null=True, verbose_name="نص الرأس"),
                ),
                (
                    "footer_text",
                    models.TextField(blank=True, null=True, verbose_name="نص التذييل"),
                ),
                (
                    "terms_conditions",
                    models.TextField(
                        blank=True, null=True, verbose_name="الشروط والأحكام"
                    ),
                ),
                (
                    "payment_instructions",
                    models.TextField(
                        blank=True, null=True, verbose_name="تعليمات الدفع"
                    ),
                ),
                (
                    "primary_color",
                    models.CharField(
                        default="#7C3AED", max_length=7, verbose_name="اللون الأساسي"
                    ),
                ),
                (
                    "secondary_color",
                    models.CharField(
                        default="#F3F4F6", max_length=7, verbose_name="اللون الثانوي"
                    ),
                ),
                (
                    "font_family",
                    models.CharField(
                        default="IBM Plex Sans Arabic",
                        max_length=100,
                        verbose_name="نوع الخط",
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="نشط")),
                (
                    "is_default",
                    models.BooleanField(default=False, verbose_name="افتراضي"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "قالب الفاتورة",
                "verbose_name_plural": "قوالب الفواتير",
                "ordering": ["-is_default", "name"],
            },
        ),
        migrations.CreateModel(
            name="Invoice",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "invoice_number",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="رقم الفاتورة"
                    ),
                ),
                (
                    "title",
                    models.CharField(max_length=200, verbose_name="عنوان الفاتورة"),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "مسودة"),
                            ("pending", "في الانتظار"),
                            ("sent", "مرسلة"),
                            ("viewed", "تم عرضها"),
                            ("paid", "مدفوعة"),
                            ("partially_paid", "مدفوعة جزئياً"),
                            ("overdue", "متأخرة"),
                            ("cancelled", "ملغية"),
                            ("refunded", "مستردة"),
                        ],
                        default="draft",
                        max_length=20,
                        verbose_name="الحالة",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "منخفض"),
                            ("medium", "متوسط"),
                            ("high", "عالي"),
                            ("urgent", "عاجل"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="الأولوية",
                    ),
                ),
                (
                    "subtotal_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "subtotal",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="المجموع الفرعي",
                    ),
                ),
                (
                    "discount_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="نسبة الخصم",
                    ),
                ),
                (
                    "discount_amount_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "discount_amount",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="مبلغ الخصم",
                    ),
                ),
                (
                    "tax_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("14.00"),
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="نسبة الضريبة",
                    ),
                ),
                (
                    "tax_amount_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "tax_amount",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="مبلغ الضريبة",
                    ),
                ),
                (
                    "total_amount_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "total_amount",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="المبلغ الإجمالي",
                    ),
                ),
                (
                    "paid_amount_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "paid_amount",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="المبلغ المدفوع",
                    ),
                ),
                (
                    "remaining_amount_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "remaining_amount",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="المبلغ المتبقي",
                    ),
                ),
                (
                    "payment_terms",
                    models.CharField(
                        choices=[
                            ("net_15", "15 يوم"),
                            ("net_30", "30 يوم"),
                            ("net_45", "45 يوم"),
                            ("net_60", "60 يوم"),
                            ("due_on_receipt", "مستحق عند الاستلام"),
                            ("custom", "مخصص"),
                        ],
                        default="net_30",
                        max_length=20,
                        verbose_name="شروط الدفع",
                    ),
                ),
                (
                    "custom_payment_days",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="أيام الدفع المخصصة"
                    ),
                ),
                ("invoice_date", models.DateField(verbose_name="تاريخ الفاتورة")),
                (
                    "due_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="تاريخ الاستحقاق"
                    ),
                ),
                (
                    "sent_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ الإرسال"
                    ),
                ),
                (
                    "viewed_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ العرض"
                    ),
                ),
                (
                    "paid_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ الدفع"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="ملاحظات"),
                ),
                (
                    "terms_conditions",
                    models.TextField(
                        blank=True, null=True, verbose_name="الشروط والأحكام"
                    ),
                ),
                (
                    "payment_instructions",
                    models.TextField(
                        blank=True, null=True, verbose_name="تعليمات الدفع"
                    ),
                ),
                (
                    "pdf_file",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="invoices/pdfs/",
                        verbose_name="ملف PDF",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "client",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="invoices",
                        to="clients.client",
                        verbose_name="العميل",
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="invoices",
                        to="projects.project",
                        verbose_name="المشروع",
                    ),
                ),
                (
                    "quotation",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="invoices",
                        to="quotations.quotation",
                        verbose_name="العرض المرجعي",
                    ),
                ),
                (
                    "sales_rep",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="invoices",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="مندوب المبيعات",
                    ),
                ),
                (
                    "template",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="invoices.invoicetemplate",
                        verbose_name="قالب الفاتورة",
                    ),
                ),
            ],
            options={
                "verbose_name": "فاتورة",
                "verbose_name_plural": "الفواتير",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="HistoricalInvoice",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "invoice_number",
                    models.CharField(
                        db_index=True, max_length=20, verbose_name="رقم الفاتورة"
                    ),
                ),
                (
                    "title",
                    models.CharField(max_length=200, verbose_name="عنوان الفاتورة"),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "مسودة"),
                            ("pending", "في الانتظار"),
                            ("sent", "مرسلة"),
                            ("viewed", "تم عرضها"),
                            ("paid", "مدفوعة"),
                            ("partially_paid", "مدفوعة جزئياً"),
                            ("overdue", "متأخرة"),
                            ("cancelled", "ملغية"),
                            ("refunded", "مستردة"),
                        ],
                        default="draft",
                        max_length=20,
                        verbose_name="الحالة",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "منخفض"),
                            ("medium", "متوسط"),
                            ("high", "عالي"),
                            ("urgent", "عاجل"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="الأولوية",
                    ),
                ),
                (
                    "subtotal_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "subtotal",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="المجموع الفرعي",
                    ),
                ),
                (
                    "discount_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="نسبة الخصم",
                    ),
                ),
                (
                    "discount_amount_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "discount_amount",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="مبلغ الخصم",
                    ),
                ),
                (
                    "tax_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("14.00"),
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="نسبة الضريبة",
                    ),
                ),
                (
                    "tax_amount_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "tax_amount",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="مبلغ الضريبة",
                    ),
                ),
                (
                    "total_amount_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "total_amount",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="المبلغ الإجمالي",
                    ),
                ),
                (
                    "paid_amount_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "paid_amount",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="المبلغ المدفوع",
                    ),
                ),
                (
                    "remaining_amount_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "remaining_amount",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="المبلغ المتبقي",
                    ),
                ),
                (
                    "payment_terms",
                    models.CharField(
                        choices=[
                            ("net_15", "15 يوم"),
                            ("net_30", "30 يوم"),
                            ("net_45", "45 يوم"),
                            ("net_60", "60 يوم"),
                            ("due_on_receipt", "مستحق عند الاستلام"),
                            ("custom", "مخصص"),
                        ],
                        default="net_30",
                        max_length=20,
                        verbose_name="شروط الدفع",
                    ),
                ),
                (
                    "custom_payment_days",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="أيام الدفع المخصصة"
                    ),
                ),
                ("invoice_date", models.DateField(verbose_name="تاريخ الفاتورة")),
                (
                    "due_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="تاريخ الاستحقاق"
                    ),
                ),
                (
                    "sent_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ الإرسال"
                    ),
                ),
                (
                    "viewed_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ العرض"
                    ),
                ),
                (
                    "paid_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ الدفع"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="ملاحظات"),
                ),
                (
                    "terms_conditions",
                    models.TextField(
                        blank=True, null=True, verbose_name="الشروط والأحكام"
                    ),
                ),
                (
                    "payment_instructions",
                    models.TextField(
                        blank=True, null=True, verbose_name="تعليمات الدفع"
                    ),
                ),
                (
                    "pdf_file",
                    models.TextField(
                        blank=True, max_length=100, null=True, verbose_name="ملف PDF"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        verbose_name="تاريخ الإنشاء",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ التحديث"
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "client",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="clients.client",
                        verbose_name="العميل",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="projects.project",
                        verbose_name="المشروع",
                    ),
                ),
                (
                    "quotation",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="quotations.quotation",
                        verbose_name="العرض المرجعي",
                    ),
                ),
                (
                    "sales_rep",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="مندوب المبيعات",
                    ),
                ),
                (
                    "template",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="invoices.invoicetemplate",
                        verbose_name="قالب الفاتورة",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical فاتورة",
                "verbose_name_plural": "historical الفواتير",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="InvoicePayment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "payment_method",
                    models.CharField(
                        choices=[
                            ("cash", "نقدي"),
                            ("bank_transfer", "تحويل بنكي"),
                            ("credit_card", "بطاقة ائتمان"),
                            ("debit_card", "بطاقة خصم"),
                            ("check", "شيك"),
                            ("paypal", "باي بال"),
                            ("stripe", "سترايب"),
                            ("fawry", "فوري"),
                            ("vodafone_cash", "فودافون كاش"),
                            ("orange_money", "أورانج موني"),
                            ("etisalat_cash", "اتصالات كاش"),
                            ("other", "أخرى"),
                        ],
                        max_length=20,
                        verbose_name="طريقة الدفع",
                    ),
                ),
                (
                    "amount_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "amount",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2, max_digits=14, verbose_name="المبلغ"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "في الانتظار"),
                            ("completed", "مكتمل"),
                            ("failed", "فاشل"),
                            ("cancelled", "ملغي"),
                            ("refunded", "مسترد"),
                        ],
                        default="pending",
                        max_length=10,
                        verbose_name="الحالة",
                    ),
                ),
                (
                    "transaction_id",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="رقم المعاملة",
                    ),
                ),
                (
                    "reference_number",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="الرقم المرجعي",
                    ),
                ),
                ("payment_date", models.DateTimeField(verbose_name="تاريخ الدفع")),
                (
                    "processed_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ المعالجة"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="ملاحظات"),
                ),
                (
                    "receipt_file",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="invoices/receipts/",
                        verbose_name="ملف الإيصال",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "invoice",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payments",
                        to="invoices.invoice",
                        verbose_name="الفاتورة",
                    ),
                ),
                (
                    "processed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="تم المعالجة بواسطة",
                    ),
                ),
            ],
            options={
                "verbose_name": "دفعة الفاتورة",
                "verbose_name_plural": "دفعات الفواتير",
                "ordering": ["-payment_date"],
                "indexes": [
                    models.Index(
                        fields=["invoice", "status"], name="payment_invoice_status_idx"
                    ),
                    models.Index(fields=["payment_method"], name="payment_method_idx"),
                    models.Index(fields=["payment_date"], name="payment_date_idx"),
                    models.Index(
                        fields=["transaction_id"], name="payment_transaction_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="InvoiceItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "item_type",
                    models.CharField(
                        choices=[
                            ("service", "خدمة"),
                            ("product", "منتج"),
                            ("discount", "خصم"),
                            ("shipping", "شحن"),
                            ("other", "أخرى"),
                        ],
                        default="service",
                        max_length=10,
                        verbose_name="نوع العنصر",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="اسم العنصر")),
                ("description", models.TextField(verbose_name="الوصف")),
                (
                    "quantity",
                    models.PositiveIntegerField(default=1, verbose_name="الكمية"),
                ),
                (
                    "unit",
                    models.CharField(
                        default="خدمة", max_length=50, verbose_name="الوحدة"
                    ),
                ),
                (
                    "unit_price_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "unit_price",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2, max_digits=14, verbose_name="سعر الوحدة"
                    ),
                ),
                (
                    "total_price_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "total_price",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2, max_digits=14, verbose_name="السعر الإجمالي"
                    ),
                ),
                (
                    "is_taxable",
                    models.BooleanField(default=True, verbose_name="خاضع للضريبة"),
                ),
                (
                    "tax_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("14.00"),
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="نسبة الضريبة",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "invoice",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="items",
                        to="invoices.invoice",
                        verbose_name="الفاتورة",
                    ),
                ),
                (
                    "quotation_item",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="quotations.quotationitem",
                        verbose_name="عنصر العرض المرجعي",
                    ),
                ),
            ],
            options={
                "verbose_name": "عنصر الفاتورة",
                "verbose_name_plural": "عناصر الفاتورة",
                "ordering": ["id"],
                "indexes": [
                    models.Index(fields=["invoice"], name="invoice_item_invoice_idx"),
                    models.Index(fields=["item_type"], name="invoice_item_type_idx"),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="invoice",
            index=models.Index(
                fields=["status", "priority"], name="invoice_status_priority_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="invoice",
            index=models.Index(
                fields=["client", "status"], name="invoice_client_status_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="invoice",
            index=models.Index(
                fields=["sales_rep", "status"], name="invoice_sales_status_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="invoice",
            index=models.Index(fields=["invoice_date"], name="invoice_date_idx"),
        ),
        migrations.AddIndex(
            model_name="invoice",
            index=models.Index(fields=["due_date"], name="invoice_due_date_idx"),
        ),
        migrations.AddIndex(
            model_name="invoice",
            index=models.Index(fields=["quotation"], name="invoice_quotation_idx"),
        ),
        migrations.AddIndex(
            model_name="invoice",
            index=models.Index(
                condition=models.Q(
                    (
                        "status__in",
                        ["pending", "sent", "viewed", "partially_paid", "overdue"],
                    )
                ),
                fields=["client"],
                name="invoice_unpaid_client_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="invoice",
            unique_together={("invoice_number",)},
        ),
    ]
