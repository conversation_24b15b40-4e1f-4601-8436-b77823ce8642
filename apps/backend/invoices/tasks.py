"""
Celery tasks for automated invoice workflows and background processing.
"""

import logging
from datetime import date, timedelta
from decimal import Decimal
from celery import shared_task
from django.utils import timezone
from django.db.models import Q
from django.conf import settings

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def generate_invoice_from_quotation(self, quotation_id):
    """
    Automatically generate invoice from approved quotation
    """
    try:
        from quotations.models import Quotation
        from .models import Invoice, InvoiceItem
        
        quotation = Quotation.objects.get(id=quotation_id)
        
        if quotation.status != "approved":
            logger.warning(f"Quotation {quotation.quotation_number} is not approved")
            return {"status": "error", "message": "Quotation not approved"}
        
        # Check if invoice already exists for this quotation
        existing_invoice = Invoice.objects.filter(quotation=quotation).first()
        if existing_invoice:
            logger.info(f"Invoice already exists for quotation {quotation.quotation_number}")
            return {"status": "exists", "invoice_id": existing_invoice.id}
        
        # Create invoice
        invoice = Invoice.objects.create(
            title=f"فاتورة للعرض {quotation.quotation_number}",
            description=quotation.description,
            client=quotation.client,
            project=quotation.project,
            quotation=quotation,
            sales_rep=quotation.sales_rep,
            priority=quotation.priority,
            discount_percentage=quotation.discount_percentage,
            tax_percentage=quotation.tax_percentage,
            invoice_date=date.today(),
            notes=quotation.notes,
            terms_conditions=quotation.terms_conditions,
            payment_terms="net_30",  # Default payment terms
        )
        
        # Copy quotation items to invoice items
        for quotation_item in quotation.items.all():
            InvoiceItem.objects.create(
                invoice=invoice,
                name=quotation_item.name,
                description=quotation_item.description,
                item_type=quotation_item.item_type,
                quantity=quotation_item.quantity,
                unit_price=quotation_item.unit_price,
                is_taxable=quotation_item.is_taxable,
                tax_percentage=quotation_item.tax_percentage,
            )
        
        # Calculate amounts
        invoice.calculate_amounts()
        invoice.save()
        
        # Process commission if sales rep exists
        if invoice.sales_rep:
            process_sales_commission.delay(invoice.id)
        
        logger.info(f"Invoice {invoice.invoice_number} created from quotation {quotation.quotation_number}")
        
        return {
            "status": "success",
            "invoice_id": invoice.id,
            "invoice_number": invoice.invoice_number
        }
        
    except Exception as e:
        logger.error(f"Failed to generate invoice from quotation {quotation_id}: {str(e)}")
        self.retry(countdown=60, exc=e)


@shared_task(bind=True, max_retries=3)
def process_sales_commission(self, invoice_id):
    """
    Process 12.5% sales commission for invoice
    """
    try:
        from .models import Invoice
        from commissions.models import Commission
        
        invoice = Invoice.objects.get(id=invoice_id)
        
        if not invoice.sales_rep:
            return {"status": "no_sales_rep"}
        
        # Check if commission already exists
        existing_commission = Commission.objects.filter(
            invoice=invoice,
            sales_rep=invoice.sales_rep
        ).first()
        
        if existing_commission:
            return {"status": "exists", "commission_id": existing_commission.id}
        
        # Calculate 12.5% commission
        commission_rate = Decimal("12.5")
        commission_amount = (invoice.total_amount * commission_rate) / 100
        
        # Create commission record
        commission = Commission.objects.create(
            sales_rep=invoice.sales_rep,
            invoice=invoice,
            client=invoice.client,
            project=invoice.project,
            commission_rate=commission_rate,
            commission_amount=commission_amount,
            base_amount=invoice.total_amount,
            status="pending",
            earned_date=invoice.invoice_date,
        )
        
        logger.info(f"Commission {commission.id} created for invoice {invoice.invoice_number}")
        
        return {
            "status": "success",
            "commission_id": commission.id,
            "commission_amount": float(commission_amount)
        }
        
    except Exception as e:
        logger.error(f"Failed to process commission for invoice {invoice_id}: {str(e)}")
        self.retry(countdown=60, exc=e)


@shared_task
def send_overdue_payment_reminders():
    """
    Send automated reminders for overdue invoices
    """
    try:
        from .models import Invoice
        from .email_service import send_overdue_reminder_email
        
        today = date.today()
        
        # Find overdue invoices
        overdue_invoices = Invoice.objects.filter(
            Q(status__in=['sent', 'viewed', 'partially_paid']) &
            Q(due_date__lt=today)
        ).select_related('client')
        
        reminder_counts = {
            "sent": 0,
            "failed": 0,
            "no_email": 0
        }
        
        for invoice in overdue_invoices:
            if not invoice.client.email:
                reminder_counts["no_email"] += 1
                continue
            
            days_overdue = (today - invoice.due_date).days
            
            # Send reminders at 3, 7, 14, 30 days overdue
            if days_overdue in [3, 7, 14, 30]:
                try:
                    send_overdue_reminder_email(invoice, days_overdue)
                    reminder_counts["sent"] += 1
                    
                    # Update invoice status to overdue if not already
                    if invoice.status != 'overdue':
                        invoice.status = 'overdue'
                        invoice.save()
                        
                except Exception as e:
                    logger.error(f"Failed to send reminder for invoice {invoice.invoice_number}: {str(e)}")
                    reminder_counts["failed"] += 1
        
        logger.info(f"Overdue reminders: {reminder_counts['sent']} sent, {reminder_counts['failed']} failed, {reminder_counts['no_email']} no email")
        
        return reminder_counts
        
    except Exception as e:
        logger.error(f"Failed to send overdue reminders: {str(e)}")
        return {"error": str(e)}


@shared_task
def update_invoice_statuses():
    """
    Update invoice statuses based on due dates and payment status
    """
    try:
        from .models import Invoice
        
        today = date.today()
        updated_count = 0
        
        # Mark overdue invoices
        overdue_invoices = Invoice.objects.filter(
            Q(status__in=['sent', 'viewed', 'partially_paid']) &
            Q(due_date__lt=today)
        )
        
        for invoice in overdue_invoices:
            invoice.status = 'overdue'
            invoice.save()
            updated_count += 1
        
        # Mark fully paid invoices
        paid_invoices = Invoice.objects.filter(
            Q(status__in=['sent', 'viewed', 'partially_paid', 'overdue']) &
            Q(paid_amount__gte=F('total_amount'))
        )
        
        for invoice in paid_invoices:
            invoice.status = 'paid'
            if not invoice.paid_date:
                invoice.paid_date = timezone.now()
            invoice.save()
            updated_count += 1
        
        logger.info(f"Updated {updated_count} invoice statuses")
        
        return {"updated_count": updated_count}
        
    except Exception as e:
        logger.error(f"Failed to update invoice statuses: {str(e)}")
        return {"error": str(e)}


@shared_task(bind=True, max_retries=3)
def send_invoice_email_task(self, invoice_id, recipient_email=None, cc_emails=None, custom_message=None):
    """
    Send invoice email as background task
    """
    try:
        from .models import Invoice
        from .email_service import send_invoice_email
        
        invoice = Invoice.objects.get(id=invoice_id)
        
        send_invoice_email(
            invoice=invoice,
            recipient_email=recipient_email,
            cc_emails=cc_emails,
            custom_message=custom_message
        )
        
        logger.info(f"Invoice email sent for {invoice.invoice_number}")
        
        return {"status": "success", "invoice_number": invoice.invoice_number}
        
    except Exception as e:
        logger.error(f"Failed to send invoice email for {invoice_id}: {str(e)}")
        self.retry(countdown=60, exc=e)


@shared_task
def generate_monthly_invoice_report():
    """
    Generate monthly invoice statistics and reports
    """
    try:
        from .models import Invoice
        from django.db.models import Sum, Count, Avg
        
        today = date.today()
        first_day = today.replace(day=1)
        
        # Get monthly statistics
        monthly_invoices = Invoice.objects.filter(
            invoice_date__gte=first_day,
            invoice_date__lt=today
        )
        
        stats = monthly_invoices.aggregate(
            total_count=Count('id'),
            total_amount=Sum('total_amount'),
            paid_amount=Sum('paid_amount'),
            average_amount=Avg('total_amount')
        )
        
        # Status breakdown
        status_breakdown = {}
        for status_choice in Invoice.Status.choices:
            status_code = status_choice[0]
            count = monthly_invoices.filter(status=status_code).count()
            status_breakdown[status_code] = count
        
        report_data = {
            "month": today.strftime("%Y-%m"),
            "statistics": stats,
            "status_breakdown": status_breakdown,
            "generated_at": timezone.now().isoformat()
        }
        
        logger.info(f"Monthly invoice report generated for {today.strftime('%Y-%m')}")
        
        return report_data
        
    except Exception as e:
        logger.error(f"Failed to generate monthly report: {str(e)}")
        return {"error": str(e)}
