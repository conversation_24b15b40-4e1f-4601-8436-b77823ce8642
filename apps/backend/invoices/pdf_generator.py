"""
Professional Arabic RTL PDF generator for invoices with Egyptian business compliance.
"""

import os
import io
from decimal import Decimal
from datetime import datetime
from django.conf import settings
from django.template.loader import render_to_string
from django.utils import timezone
from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration


class InvoicePDFGenerator:
    """Professional PDF generator for invoices with Arabic RTL support"""
    
    def __init__(self):
        self.font_config = FontConfiguration()
        
    def generate_pdf(self, invoice):
        """Generate PDF for invoice with professional Arabic RTL template"""
        try:
            # Prepare context data
            context = self._prepare_context(invoice)
            
            # Render HTML template
            html_content = render_to_string('invoices/pdf/invoice_template.html', context)
            
            # Generate PDF
            pdf_content = self._html_to_pdf(html_content)
            
            return pdf_content
            
        except Exception as e:
            raise Exception(f"خطأ في توليد PDF: {str(e)}")
    
    def _prepare_context(self, invoice):
        """Prepare context data for PDF template"""
        
        # Calculate totals
        items = invoice.items.all()
        subtotal = sum(item.total_price for item in items)
        
        # Company information (can be moved to settings)
        company_info = {
            'name': 'MTBRMG Digital Agency',
            'name_ar': 'وكالة MTBRMG الرقمية',
            'address': 'القاهرة، مصر',
            'phone': '+20 ************',
            'email': '<EMAIL>',
            'website': 'www.mtbrmg.com',
            'tax_number': '***********',
            'commercial_register': 'CR-*********'
        }
        
        # Format currency amounts
        def format_currency(amount, currency='EGP'):
            if currency == 'EGP':
                return f"{amount:,.2f} ج.م"
            elif currency == 'USD':
                return f"${amount:,.2f}"
            else:
                return f"{amount:,.2f} {currency}"
        
        context = {
            'invoice': invoice,
            'items': items,
            'company': company_info,
            'subtotal': subtotal,
            'discount_amount': invoice.discount_amount or 0,
            'tax_amount': invoice.tax_amount or 0,
            'total_amount': invoice.total_amount,
            'paid_amount': invoice.paid_amount or 0,
            'remaining_amount': invoice.remaining_amount or invoice.total_amount,
            'format_currency': format_currency,
            'generated_at': timezone.now(),
            'qr_code_data': self._generate_qr_data(invoice),
        }
        
        return context
    
    def _generate_qr_data(self, invoice):
        """Generate QR code data for payment verification"""
        qr_data = {
            'invoice_number': invoice.invoice_number,
            'amount': str(invoice.total_amount),
            'currency': 'EGP',
            'due_date': invoice.due_date.isoformat() if invoice.due_date else None,
            'client': invoice.client.name,
        }
        return str(qr_data)
    
    def _html_to_pdf(self, html_content):
        """Convert HTML to PDF with Arabic font support"""
        
        # CSS for Arabic RTL support and professional styling
        css_content = """
        @import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'IBM Plex Sans Arabic', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background: white;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            background: white;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #6366f1;
            padding-bottom: 20px;
        }
        
        .company-info h1 {
            color: #6366f1;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .company-info p {
            color: #666;
            font-size: 12px;
            margin: 2px 0;
        }
        
        .invoice-title {
            text-align: left;
            direction: ltr;
        }
        
        .invoice-title h2 {
            font-size: 24px;
            color: #1f2937;
            margin-bottom: 10px;
        }
        
        .invoice-meta {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .client-info, .invoice-details {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .section-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 15px;
            font-size: 16px;
            border-bottom: 2px solid #6366f1;
            padding-bottom: 5px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .items-table th {
            background: #6366f1;
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: 600;
        }
        
        .items-table td {
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .items-table tr:nth-child(even) {
            background: #f8fafc;
        }
        
        .totals-section {
            margin-top: 30px;
            text-align: left;
            direction: ltr;
        }
        
        .totals-table {
            margin-left: auto;
            width: 300px;
        }
        
        .totals-table td {
            padding: 8px 15px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .totals-table .total-row {
            background: #6366f1;
            color: white;
            font-weight: 600;
            font-size: 16px;
        }
        
        .footer {
            margin-top: 50px;
            padding-top: 30px;
            border-top: 2px solid #e2e8f0;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
        
        .payment-info {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .status-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-draft { background: #f3f4f6; color: #374151; }
        .status-sent { background: #dbeafe; color: #1d4ed8; }
        .status-paid { background: #d1fae5; color: #065f46; }
        .status-overdue { background: #fee2e2; color: #dc2626; }
        
        @page {
            size: A4;
            margin: 2cm;
        }
        """
        
        # Create CSS object
        css = CSS(string=css_content, font_config=self.font_config)
        
        # Generate PDF
        html_doc = HTML(string=html_content)
        pdf_bytes = html_doc.write_pdf(stylesheets=[css], font_config=self.font_config)
        
        return pdf_bytes
