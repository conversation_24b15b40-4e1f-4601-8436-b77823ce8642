"""
Invoice module serializers following established patterns from quotations and finance modules.
"""

from rest_framework import serializers
from .models import InvoiceTemplate, Invoice, InvoiceItem, InvoicePayment
from authentication.serializers import UserListSerializer
from clients.serializers import ClientListSerializer
from projects.serializers import ProjectListSerializer
from quotations.serializers import QuotationListSerializer


class InvoiceTemplateSerializer(serializers.ModelSerializer):
    """Invoice template serializer"""
    
    template_type_display = serializers.CharField(source='get_template_type_display', read_only=True)
    
    class Meta:
        model = InvoiceTemplate
        fields = [
            'id', 'name', 'description', 'template_type', 'template_type_display',
            'header_text', 'footer_text', 'terms_conditions', 'payment_instructions',
            'primary_color', 'secondary_color', 'font_family',
            'is_active', 'is_default', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class InvoiceItemSerializer(serializers.ModelSerializer):
    """Invoice item serializer"""
    
    item_type_display = serializers.CharField(source='get_item_type_display', read_only=True)
    
    class Meta:
        model = InvoiceItem
        fields = [
            'id', 'item_type', 'item_type_display', 'name', 'description',
            'quantity', 'unit', 'unit_price', 'total_price',
            'is_taxable', 'tax_percentage', 'quotation_item', 'created_at'
        ]
        read_only_fields = ['id', 'total_price', 'created_at']


class InvoiceItemCreateSerializer(serializers.ModelSerializer):
    """Invoice item creation serializer"""
    
    class Meta:
        model = InvoiceItem
        fields = [
            'item_type', 'name', 'description', 'quantity', 'unit',
            'unit_price', 'is_taxable', 'tax_percentage', 'quotation_item'
        ]
    
    def validate_quantity(self, value):
        """Validate quantity is positive"""
        if value <= 0:
            raise serializers.ValidationError('الكمية يجب أن تكون أكبر من صفر')
        return value
    
    def validate_unit_price(self, value):
        """Validate unit price is positive"""
        if value <= 0:
            raise serializers.ValidationError('سعر الوحدة يجب أن يكون أكبر من صفر')
        return value


class InvoicePaymentSerializer(serializers.ModelSerializer):
    """Invoice payment serializer"""
    
    payment_method_display = serializers.CharField(source='get_payment_method_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    processed_by = UserListSerializer(read_only=True)
    
    class Meta:
        model = InvoicePayment
        fields = [
            'id', 'payment_method', 'payment_method_display', 'amount',
            'status', 'status_display', 'transaction_id', 'reference_number',
            'payment_date', 'processed_date', 'processed_by', 'notes',
            'receipt_file', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'processed_date', 'created_at', 'updated_at']


class InvoicePaymentCreateSerializer(serializers.ModelSerializer):
    """Invoice payment creation serializer"""
    
    class Meta:
        model = InvoicePayment
        fields = [
            'payment_method', 'amount', 'transaction_id', 'reference_number',
            'payment_date', 'notes', 'receipt_file'
        ]
    
    def validate_amount(self, value):
        """Validate payment amount is positive"""
        if value <= 0:
            raise serializers.ValidationError('مبلغ الدفع يجب أن يكون أكبر من صفر')
        return value


class InvoiceListSerializer(serializers.ModelSerializer):
    """Invoice list serializer for table views"""
    
    client = ClientListSerializer(read_only=True)
    sales_rep = UserListSerializer(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    payment_terms_display = serializers.CharField(source='get_payment_terms_display', read_only=True)
    
    # Computed fields
    is_overdue = serializers.BooleanField(read_only=True)
    days_overdue = serializers.IntegerField(read_only=True)
    payment_percentage = serializers.FloatField(read_only=True)
    items_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Invoice
        fields = [
            'id', 'invoice_number', 'title', 'client', 'sales_rep',
            'status', 'status_display', 'priority', 'priority_display',
            'total_amount', 'paid_amount', 'remaining_amount',
            'payment_terms', 'payment_terms_display', 'invoice_date', 'due_date',
            'is_overdue', 'days_overdue', 'payment_percentage', 'items_count',
            'created_at', 'updated_at'
        ]
    
    def get_items_count(self, obj):
        """Get number of items in invoice"""
        return obj.items.count()


class InvoiceSerializer(serializers.ModelSerializer):
    """Complete invoice serializer"""
    
    client = ClientListSerializer(read_only=True)
    project = ProjectListSerializer(read_only=True)
    quotation = QuotationListSerializer(read_only=True)
    sales_rep = UserListSerializer(read_only=True)
    template = InvoiceTemplateSerializer(read_only=True)
    items = InvoiceItemSerializer(many=True, read_only=True)
    payments = InvoicePaymentSerializer(many=True, read_only=True)
    
    # Display fields
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    payment_terms_display = serializers.CharField(source='get_payment_terms_display', read_only=True)
    
    # Computed fields
    is_overdue = serializers.BooleanField(read_only=True)
    days_overdue = serializers.IntegerField(read_only=True)
    payment_percentage = serializers.FloatField(read_only=True)
    is_fully_paid = serializers.BooleanField(read_only=True)
    is_partially_paid = serializers.BooleanField(read_only=True)
    items_count = serializers.SerializerMethodField()
    payments_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Invoice
        fields = [
            'id', 'invoice_number', 'title', 'description',
            'client', 'project', 'quotation', 'sales_rep', 'template',
            'status', 'status_display', 'priority', 'priority_display',
            'subtotal', 'discount_percentage', 'discount_amount',
            'tax_percentage', 'tax_amount', 'total_amount',
            'paid_amount', 'remaining_amount',
            'payment_terms', 'payment_terms_display', 'custom_payment_days',
            'invoice_date', 'due_date', 'sent_date', 'viewed_date', 'paid_date',
            'notes', 'terms_conditions', 'payment_instructions', 'pdf_file',
            'is_overdue', 'days_overdue', 'payment_percentage',
            'is_fully_paid', 'is_partially_paid',
            'items', 'payments', 'items_count', 'payments_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'invoice_number', 'subtotal', 'discount_amount',
            'tax_amount', 'total_amount', 'paid_amount', 'remaining_amount',
            'sent_date', 'viewed_date', 'paid_date', 'created_at', 'updated_at'
        ]
    
    def get_items_count(self, obj):
        """Get number of items in invoice"""
        return obj.items.count()
    
    def get_payments_count(self, obj):
        """Get number of payments for invoice"""
        return obj.payments.count()


class InvoiceCreateSerializer(serializers.ModelSerializer):
    """Invoice creation serializer"""
    
    client_id = serializers.IntegerField(write_only=True)
    project_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    quotation_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    template_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    items_data = InvoiceItemCreateSerializer(many=True, write_only=True, required=False)
    
    class Meta:
        model = Invoice
        fields = [
            'title', 'description', 'client_id', 'project_id', 'quotation_id',
            'template_id', 'priority', 'discount_percentage', 'tax_percentage',
            'payment_terms', 'custom_payment_days', 'invoice_date', 'due_date',
            'notes', 'terms_conditions', 'payment_instructions', 'items_data'
        ]
    
    def validate_client_id(self, value):
        """Validate client exists"""
        from clients.models import Client
        try:
            Client.objects.get(id=value)
            return value
        except Client.DoesNotExist:
            raise serializers.ValidationError('العميل المحدد غير موجود')
    
    def validate_project_id(self, value):
        """Validate project exists if provided"""
        if value is not None:
            from projects.models import Project
            try:
                Project.objects.get(id=value)
                return value
            except Project.DoesNotExist:
                raise serializers.ValidationError('المشروع المحدد غير موجود')
        return value
    
    def validate_quotation_id(self, value):
        """Validate quotation exists if provided"""
        if value is not None:
            from quotations.models import Quotation
            try:
                Quotation.objects.get(id=value)
                return value
            except Quotation.DoesNotExist:
                raise serializers.ValidationError('العرض المحدد غير موجود')
        return value
    
    def validate_template_id(self, value):
        """Validate template exists if provided"""
        if value is not None:
            try:
                InvoiceTemplate.objects.get(id=value)
                return value
            except InvoiceTemplate.DoesNotExist:
                raise serializers.ValidationError('قالب الفاتورة المحدد غير موجود')
        return value
    
    def create(self, validated_data):
        """Create invoice with items"""
        items_data = validated_data.pop('items_data', [])
        
        # Set foreign key relationships
        validated_data['client_id'] = validated_data.pop('client_id')
        if 'project_id' in validated_data:
            validated_data['project_id'] = validated_data.pop('project_id')
        if 'quotation_id' in validated_data:
            validated_data['quotation_id'] = validated_data.pop('quotation_id')
        if 'template_id' in validated_data:
            validated_data['template_id'] = validated_data.pop('template_id')
        
        # Set sales rep to current user
        validated_data['sales_rep'] = self.context['request'].user
        
        # Create invoice
        invoice = Invoice.objects.create(**validated_data)
        
        # Create invoice items
        for item_data in items_data:
            InvoiceItem.objects.create(invoice=invoice, **item_data)
        
        return invoice


class InvoiceUpdateSerializer(serializers.ModelSerializer):
    """Invoice update serializer"""
    
    class Meta:
        model = Invoice
        fields = [
            'title', 'description', 'priority', 'discount_percentage',
            'tax_percentage', 'payment_terms', 'custom_payment_days',
            'invoice_date', 'due_date', 'notes', 'terms_conditions',
            'payment_instructions'
        ]
    
    def validate(self, data):
        """Validate invoice can be updated"""
        if self.instance.status in [Invoice.Status.PAID, Invoice.Status.CANCELLED]:
            raise serializers.ValidationError('لا يمكن تعديل فاتورة مدفوعة أو ملغية')
        return data


class InvoiceStatsSerializer(serializers.Serializer):
    """Invoice statistics serializer"""
    
    total_invoices = serializers.IntegerField()
    total_amount = serializers.DecimalField(max_digits=14, decimal_places=2)
    paid_amount = serializers.DecimalField(max_digits=14, decimal_places=2)
    pending_amount = serializers.DecimalField(max_digits=14, decimal_places=2)
    overdue_amount = serializers.DecimalField(max_digits=14, decimal_places=2)
    
    draft_count = serializers.IntegerField()
    pending_count = serializers.IntegerField()
    sent_count = serializers.IntegerField()
    paid_count = serializers.IntegerField()
    overdue_count = serializers.IntegerField()
    
    avg_payment_time = serializers.FloatField()
    payment_rate = serializers.FloatField()
