from django.db import models
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from djmoney.models.fields import MoneyField
from simple_history.models import HistoricalRecords
from decimal import Decimal
import uuid


class InvoiceTemplate(models.Model):
    """Invoice template model for customizable invoice layouts"""

    class TemplateType(models.TextChoices):
        STANDARD = "standard", "قياسي"
        DETAILED = "detailed", "مفصل"
        MINIMAL = "minimal", "مبسط"
        CUSTOM = "custom", "مخصص"

    # Basic Information
    name = models.CharField(max_length=200, verbose_name="اسم القالب")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    template_type = models.CharField(
        max_length=20,
        choices=TemplateType.choices,
        default=TemplateType.STANDARD,
        verbose_name="نوع القالب",
    )

    # Template Configuration
    header_text = models.TextField(blank=True, null=True, verbose_name="نص الرأس")
    footer_text = models.TextField(blank=True, null=True, verbose_name="نص التذييل")
    terms_conditions = models.TextField(
        blank=True, null=True, verbose_name="الشروط والأحكام"
    )
    payment_instructions = models.TextField(
        blank=True, null=True, verbose_name="تعليمات الدفع"
    )

    # Styling Options
    primary_color = models.CharField(
        max_length=7, default="#7C3AED", verbose_name="اللون الأساسي"
    )
    secondary_color = models.CharField(
        max_length=7, default="#F3F4F6", verbose_name="اللون الثانوي"
    )
    font_family = models.CharField(
        max_length=100, default="IBM Plex Sans Arabic", verbose_name="نوع الخط"
    )

    # Status
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    is_default = models.BooleanField(default=False, verbose_name="افتراضي")

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "قالب الفاتورة"
        verbose_name_plural = "قوالب الفواتير"
        ordering = ["-is_default", "name"]

    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"

    def save(self, *args, **kwargs):
        # Ensure only one default template
        if self.is_default:
            InvoiceTemplate.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)


class Invoice(models.Model):
    """Invoice model for managing client invoices"""

    class Status(models.TextChoices):
        DRAFT = "draft", "مسودة"
        PENDING = "pending", "في الانتظار"
        SENT = "sent", "مرسلة"
        VIEWED = "viewed", "تم عرضها"
        PAID = "paid", "مدفوعة"
        PARTIALLY_PAID = "partially_paid", "مدفوعة جزئياً"
        OVERDUE = "overdue", "متأخرة"
        CANCELLED = "cancelled", "ملغية"
        REFUNDED = "refunded", "مستردة"

    class Priority(models.TextChoices):
        LOW = "low", "منخفض"
        MEDIUM = "medium", "متوسط"
        HIGH = "high", "عالي"
        URGENT = "urgent", "عاجل"

    class PaymentTerms(models.TextChoices):
        NET_15 = "net_15", "15 يوم"
        NET_30 = "net_30", "30 يوم"
        NET_45 = "net_45", "45 يوم"
        NET_60 = "net_60", "60 يوم"
        DUE_ON_RECEIPT = "due_on_receipt", "مستحق عند الاستلام"
        CUSTOM = "custom", "مخصص"

    # Basic Information
    invoice_number = models.CharField(
        max_length=20, unique=True, verbose_name="رقم الفاتورة"
    )
    title = models.CharField(max_length=200, verbose_name="عنوان الفاتورة")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")

    # Relationships
    client = models.ForeignKey(
        "clients.Client",
        on_delete=models.CASCADE,
        related_name="invoices",
        verbose_name="العميل",
    )
    project = models.ForeignKey(
        "projects.Project",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="invoices",
        verbose_name="المشروع",
    )
    quotation = models.ForeignKey(
        "quotations.Quotation",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="invoices",
        verbose_name="العرض المرجعي",
    )
    sales_rep = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="invoices",
        verbose_name="مندوب المبيعات",
    )
    template = models.ForeignKey(
        InvoiceTemplate,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="قالب الفاتورة",
    )

    # Status and Priority
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.DRAFT,
        verbose_name="الحالة",
    )
    priority = models.CharField(
        max_length=10,
        choices=Priority.choices,
        default=Priority.MEDIUM,
        verbose_name="الأولوية",
    )

    # Financial Details
    subtotal = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency="EGP",
        default=0,
        verbose_name="المجموع الفرعي",
    )
    discount_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name="نسبة الخصم",
    )
    discount_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency="EGP",
        default=0,
        verbose_name="مبلغ الخصم",
    )
    tax_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal("14.00"),
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name="نسبة الضريبة",
    )
    tax_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency="EGP",
        default=0,
        verbose_name="مبلغ الضريبة",
    )
    total_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency="EGP",
        default=0,
        verbose_name="المبلغ الإجمالي",
    )
    paid_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency="EGP",
        default=0,
        verbose_name="المبلغ المدفوع",
    )
    remaining_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency="EGP",
        default=0,
        verbose_name="المبلغ المتبقي",
    )

    # Payment Terms
    payment_terms = models.CharField(
        max_length=20,
        choices=PaymentTerms.choices,
        default=PaymentTerms.NET_30,
        verbose_name="شروط الدفع",
    )
    custom_payment_days = models.PositiveIntegerField(
        blank=True, null=True, verbose_name="أيام الدفع المخصصة"
    )

    # Dates
    invoice_date = models.DateField(verbose_name="تاريخ الفاتورة")
    due_date = models.DateField(blank=True, null=True, verbose_name="تاريخ الاستحقاق")
    sent_date = models.DateTimeField(
        blank=True, null=True, verbose_name="تاريخ الإرسال"
    )
    viewed_date = models.DateTimeField(
        blank=True, null=True, verbose_name="تاريخ العرض"
    )
    paid_date = models.DateTimeField(blank=True, null=True, verbose_name="تاريخ الدفع")

    # Additional Information
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    terms_conditions = models.TextField(
        blank=True, null=True, verbose_name="الشروط والأحكام"
    )
    payment_instructions = models.TextField(
        blank=True, null=True, verbose_name="تعليمات الدفع"
    )

    # File Management
    pdf_file = models.FileField(
        upload_to="invoices/pdfs/", blank=True, null=True, verbose_name="ملف PDF"
    )

    # Tracking
    created_at = models.DateTimeField(
        auto_now_add=True, db_index=True, verbose_name="تاريخ الإنشاء"
    )
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    # History tracking
    history = HistoricalRecords()

    class Meta:
        verbose_name = "فاتورة"
        verbose_name_plural = "الفواتير"
        ordering = ["-created_at"]
        unique_together = ["invoice_number"]
        indexes = [
            models.Index(
                fields=["status", "priority"], name="invoice_status_priority_idx"
            ),
            models.Index(fields=["client", "status"], name="invoice_client_status_idx"),
            models.Index(
                fields=["sales_rep", "status"], name="invoice_sales_status_idx"
            ),
            models.Index(fields=["invoice_date"], name="invoice_date_idx"),
            models.Index(fields=["due_date"], name="invoice_due_date_idx"),
            models.Index(fields=["quotation"], name="invoice_quotation_idx"),
            # Partial index for unpaid invoices
            models.Index(
                fields=["client"],
                name="invoice_unpaid_client_idx",
                condition=models.Q(
                    status__in=[
                        "pending",
                        "sent",
                        "viewed",
                        "partially_paid",
                        "overdue",
                    ]
                ),
            ),
        ]

    def __str__(self):
        return f"{self.invoice_number} - {self.client.name}"

    def save(self, *args, **kwargs):
        # Generate invoice number if not provided
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()

        # Set due date based on payment terms
        if not self.due_date and self.invoice_date:
            self.due_date = self.calculate_due_date()

        # Save first to get primary key
        super().save(*args, **kwargs)

        # Calculate amounts after save (when pk exists)
        if self.pk:
            self.calculate_amounts()
            # Save again if amounts were calculated
            if hasattr(self, "_amounts_calculated"):
                super().save(*args, **kwargs)

    def generate_invoice_number(self):
        """Generate unique invoice number"""
        from datetime import datetime

        year = datetime.now().year
        month = datetime.now().month

        # Get last invoice number for this month
        last_invoice = (
            Invoice.objects.filter(invoice_number__startswith=f"INV-{year}{month:02d}")
            .order_by("-invoice_number")
            .first()
        )

        if last_invoice:
            last_number = int(last_invoice.invoice_number.split("-")[-1])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"INV-{year}{month:02d}-{new_number:04d}"

    def calculate_amounts(self):
        """Calculate invoice amounts based on items"""
        # Calculate subtotal from items
        self.subtotal = sum(item.total_price for item in self.items.all())

        # Calculate discount amount
        if self.discount_percentage > 0:
            self.discount_amount = (self.subtotal * self.discount_percentage) / 100

        # Calculate tax amount
        taxable_amount = self.subtotal - self.discount_amount
        if self.tax_percentage > 0:
            self.tax_amount = (taxable_amount * self.tax_percentage) / 100

        # Calculate total amount
        self.total_amount = taxable_amount + self.tax_amount

        # Calculate remaining amount
        self.remaining_amount = self.total_amount - self.paid_amount

        # Set flag to indicate amounts were calculated
        self._amounts_calculated = True

    def calculate_due_date(self):
        """Calculate due date based on payment terms"""
        from datetime import timedelta

        if self.payment_terms == self.PaymentTerms.DUE_ON_RECEIPT:
            return self.invoice_date
        elif self.payment_terms == self.PaymentTerms.NET_15:
            return self.invoice_date + timedelta(days=15)
        elif self.payment_terms == self.PaymentTerms.NET_30:
            return self.invoice_date + timedelta(days=30)
        elif self.payment_terms == self.PaymentTerms.NET_45:
            return self.invoice_date + timedelta(days=45)
        elif self.payment_terms == self.PaymentTerms.NET_60:
            return self.invoice_date + timedelta(days=60)
        elif (
            self.payment_terms == self.PaymentTerms.CUSTOM and self.custom_payment_days
        ):
            return self.invoice_date + timedelta(days=self.custom_payment_days)
        else:
            return self.invoice_date + timedelta(days=30)  # Default to 30 days

    @property
    def is_overdue(self):
        """Check if invoice is overdue"""
        from datetime import date

        return (
            self.due_date
            and self.due_date < date.today()
            and self.status
            not in [self.Status.PAID, self.Status.CANCELLED, self.Status.REFUNDED]
        )

    @property
    def days_overdue(self):
        """Get number of days overdue"""
        from datetime import date

        if self.is_overdue:
            return (date.today() - self.due_date).days
        return 0

    @property
    def payment_percentage(self):
        """Get payment percentage"""
        if self.total_amount > 0:
            return (self.paid_amount / self.total_amount) * 100
        return 0

    @property
    def is_fully_paid(self):
        """Check if invoice is fully paid"""
        return self.paid_amount >= self.total_amount

    @property
    def is_partially_paid(self):
        """Check if invoice is partially paid"""
        return 0 < self.paid_amount < self.total_amount


class InvoiceItem(models.Model):
    """Individual items in an invoice"""

    class ItemType(models.TextChoices):
        SERVICE = "service", "خدمة"
        PRODUCT = "product", "منتج"
        DISCOUNT = "discount", "خصم"
        SHIPPING = "shipping", "شحن"
        OTHER = "other", "أخرى"

    # Relationships
    invoice = models.ForeignKey(
        Invoice, on_delete=models.CASCADE, related_name="items", verbose_name="الفاتورة"
    )
    quotation_item = models.ForeignKey(
        "quotations.QuotationItem",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="عنصر العرض المرجعي",
    )

    # Item Details
    item_type = models.CharField(
        max_length=10,
        choices=ItemType.choices,
        default=ItemType.SERVICE,
        verbose_name="نوع العنصر",
    )
    name = models.CharField(max_length=200, verbose_name="اسم العنصر")
    description = models.TextField(verbose_name="الوصف")
    quantity = models.PositiveIntegerField(default=1, verbose_name="الكمية")
    unit = models.CharField(max_length=50, default="خدمة", verbose_name="الوحدة")

    # Pricing
    unit_price = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency="EGP",
        verbose_name="سعر الوحدة",
    )
    total_price = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency="EGP",
        verbose_name="السعر الإجمالي",
    )

    # Tax Information
    is_taxable = models.BooleanField(default=True, verbose_name="خاضع للضريبة")
    tax_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal("14.00"),
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name="نسبة الضريبة",
    )

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "عنصر الفاتورة"
        verbose_name_plural = "عناصر الفاتورة"
        ordering = ["id"]
        indexes = [
            models.Index(fields=["invoice"], name="invoice_item_invoice_idx"),
            models.Index(fields=["item_type"], name="invoice_item_type_idx"),
        ]

    def __str__(self):
        return f"{self.name} - {self.invoice.invoice_number}"

    def save(self, *args, **kwargs):
        # Calculate total price
        self.total_price = self.unit_price * self.quantity
        super().save(*args, **kwargs)

        # Update invoice totals
        self.invoice.calculate_amounts()
        self.invoice.save()


class InvoicePayment(models.Model):
    """Payment records for invoices"""

    class PaymentMethod(models.TextChoices):
        CASH = "cash", "نقدي"
        BANK_TRANSFER = "bank_transfer", "تحويل بنكي"
        CREDIT_CARD = "credit_card", "بطاقة ائتمان"
        DEBIT_CARD = "debit_card", "بطاقة خصم"
        CHECK = "check", "شيك"
        PAYPAL = "paypal", "باي بال"
        STRIPE = "stripe", "سترايب"
        PAYMOB = "paymob", "بايموب"
        FAWRY = "fawry", "فوري"
        VODAFONE_CASH = "vodafone_cash", "فودافون كاش"
        ORANGE_MONEY = "orange_money", "أورانج موني"
        ETISALAT_CASH = "etisalat_cash", "اتصالات كاش"
        OTHER = "other", "أخرى"

    class PaymentGateway(models.TextChoices):
        MANUAL = "manual", "يدوي"
        PAYMOB = "paymob", "بايموب"
        STRIPE = "stripe", "سترايب"
        PAYPAL = "paypal", "باي بال"

    class PaymentStatus(models.TextChoices):
        PENDING = "pending", "في الانتظار"
        COMPLETED = "completed", "مكتمل"
        FAILED = "failed", "فاشل"
        CANCELLED = "cancelled", "ملغي"
        REFUNDED = "refunded", "مسترد"

    # Relationships
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        related_name="payments",
        verbose_name="الفاتورة",
    )
    processed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="تم المعالجة بواسطة",
    )

    # Payment Details
    payment_method = models.CharField(
        max_length=20, choices=PaymentMethod.choices, verbose_name="طريقة الدفع"
    )
    payment_gateway = models.CharField(
        max_length=20,
        choices=PaymentGateway.choices,
        default=PaymentGateway.MANUAL,
        verbose_name="بوابة الدفع",
    )
    amount = MoneyField(
        max_digits=14, decimal_places=2, default_currency="EGP", verbose_name="المبلغ"
    )
    status = models.CharField(
        max_length=10,
        choices=PaymentStatus.choices,
        default=PaymentStatus.PENDING,
        verbose_name="الحالة",
    )

    # Transaction Information
    transaction_id = models.CharField(
        max_length=100, blank=True, null=True, verbose_name="رقم المعاملة"
    )
    reference_number = models.CharField(
        max_length=100, blank=True, null=True, verbose_name="الرقم المرجعي"
    )
    gateway_order_id = models.CharField(
        max_length=100, blank=True, null=True, verbose_name="رقم الطلب في البوابة"
    )
    gateway_payment_id = models.CharField(
        max_length=100, blank=True, null=True, verbose_name="رقم الدفع في البوابة"
    )

    # Dates
    payment_date = models.DateTimeField(verbose_name="تاريخ الدفع")
    processed_date = models.DateTimeField(
        blank=True, null=True, verbose_name="تاريخ المعالجة"
    )

    # Gateway Response Data
    gateway_response = models.JSONField(
        blank=True, null=True, verbose_name="استجابة البوابة"
    )
    gateway_fees = MoneyField(
        max_digits=10,
        decimal_places=2,
        default_currency="EGP",
        default=0,
        verbose_name="رسوم البوابة",
    )

    # Additional Information
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    receipt_file = models.FileField(
        upload_to="invoices/receipts/",
        blank=True,
        null=True,
        verbose_name="ملف الإيصال",
    )

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "دفعة الفاتورة"
        verbose_name_plural = "دفعات الفواتير"
        ordering = ["-payment_date"]
        indexes = [
            models.Index(
                fields=["invoice", "status"], name="payment_invoice_status_idx"
            ),
            models.Index(fields=["payment_method"], name="payment_method_idx"),
            models.Index(fields=["payment_date"], name="payment_date_idx"),
            models.Index(fields=["transaction_id"], name="payment_transaction_idx"),
        ]

    def __str__(self):
        return f"{self.invoice.invoice_number} - {self.amount} - {self.get_payment_method_display()}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        # Update invoice paid amount and status
        if self.status == self.PaymentStatus.COMPLETED:
            self.update_invoice_payment_status()

    def update_invoice_payment_status(self):
        """Update invoice payment status based on payments"""
        invoice = self.invoice

        # Calculate total paid amount
        total_paid = (
            invoice.payments.filter(status=self.PaymentStatus.COMPLETED).aggregate(
                total=models.Sum("amount")
            )["total"]
            or 0
        )

        invoice.paid_amount = total_paid

        # Update invoice status
        if total_paid >= invoice.total_amount:
            invoice.status = Invoice.Status.PAID
            if not invoice.paid_date:
                invoice.paid_date = self.payment_date
        elif total_paid > 0:
            invoice.status = Invoice.Status.PARTIALLY_PAID

        invoice.save()
