"""
Serializers for settings models.
"""

from rest_framework import serializers
from .models import PaymentGatewaySettings, SystemSettings


class PaymentGatewaySettingsSerializer(serializers.ModelSerializer):
    """Serializer for payment gateway settings"""
    
    gateway_type_display = serializers.CharField(source='get_gateway_type_display', read_only=True)
    masked_configuration = serializers.SerializerMethodField()
    
    class Meta:
        model = PaymentGatewaySettings
        fields = [
            'id', 'gateway_type', 'gateway_type_display', 'enabled', 
            'sandbox_mode', 'configuration', 'masked_configuration',
            'last_test_date', 'last_test_status', 'last_test_message',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'last_test_date', 'last_test_status', 'last_test_message',
            'created_at', 'updated_at'
        ]

    def get_masked_configuration(self, obj):
        """Return masked configuration for security"""
        return obj.get_masked_configuration()

    def validate(self, data):
        """Validate configuration based on gateway type"""
        gateway_type = data.get('gateway_type')
        configuration = data.get('configuration', {})
        enabled = data.get('enabled', False)
        
        if enabled and gateway_type:
            if gateway_type == 'paymob':
                required_fields = ['api_key', 'integration_id', 'iframe_id', 'hmac_secret']
            elif gateway_type == 'stripe':
                required_fields = ['publishable_key', 'secret_key', 'webhook_secret']
            elif gateway_type == 'paypal':
                required_fields = ['client_id', 'client_secret']
            else:
                required_fields = []

            for field in required_fields:
                if not configuration.get(field):
                    raise serializers.ValidationError(
                        f"حقل {field} مطلوب عند تفعيل بوابة {gateway_type}"
                    )
        
        return data

    def create(self, validated_data):
        """Create payment gateway settings"""
        request = self.context.get('request')
        if request and request.user:
            validated_data['updated_by'] = request.user
        return super().create(validated_data)

    def update(self, instance, validated_data):
        """Update payment gateway settings"""
        request = self.context.get('request')
        if request and request.user:
            validated_data['updated_by'] = request.user
        return super().update(instance, validated_data)


class PaymentGatewayTestSerializer(serializers.Serializer):
    """Serializer for testing payment gateway connection"""
    
    gateway_type = serializers.ChoiceField(
        choices=PaymentGatewaySettings.GATEWAY_CHOICES
    )
    
    def validate_gateway_type(self, value):
        """Validate that the gateway exists and is enabled"""
        try:
            gateway = PaymentGatewaySettings.objects.get(gateway_type=value)
            if not gateway.enabled:
                raise serializers.ValidationError("بوابة الدفع غير مفعلة")
        except PaymentGatewaySettings.DoesNotExist:
            raise serializers.ValidationError("بوابة الدفع غير موجودة")
        
        return value


class SystemSettingsSerializer(serializers.ModelSerializer):
    """Serializer for system settings"""
    
    class Meta:
        model = SystemSettings
        fields = [
            'id', 'company_name_ar', 'company_name_en', 'company_email',
            'company_phone', 'company_website', 'default_language',
            'default_timezone', 'default_currency', 'default_tax_rate',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def update(self, instance, validated_data):
        """Update system settings"""
        request = self.context.get('request')
        if request and request.user:
            validated_data['updated_by'] = request.user
        return super().update(instance, validated_data)


class PaymentGatewayStatusSerializer(serializers.ModelSerializer):
    """Serializer for payment gateway status information"""
    
    gateway_type_display = serializers.CharField(source='get_gateway_type_display', read_only=True)
    
    class Meta:
        model = PaymentGatewaySettings
        fields = [
            'gateway_type', 'gateway_type_display', 'enabled', 'sandbox_mode',
            'last_test_date', 'last_test_status', 'last_test_message'
        ]
        read_only_fields = fields


class PaymentGatewayConfigurationSerializer(serializers.Serializer):
    """Serializer for payment gateway configuration without sensitive data"""
    
    gateway_type = serializers.CharField()
    enabled = serializers.BooleanField()
    sandbox_mode = serializers.BooleanField()
    
    # Paymob fields
    api_key_masked = serializers.CharField(required=False, allow_blank=True)
    integration_id = serializers.CharField(required=False, allow_blank=True)
    iframe_id = serializers.CharField(required=False, allow_blank=True)
    hmac_secret_masked = serializers.CharField(required=False, allow_blank=True)
    
    # Stripe fields
    publishable_key = serializers.CharField(required=False, allow_blank=True)
    secret_key_masked = serializers.CharField(required=False, allow_blank=True)
    webhook_secret_masked = serializers.CharField(required=False, allow_blank=True)
    
    # PayPal fields
    client_id = serializers.CharField(required=False, allow_blank=True)
    client_secret_masked = serializers.CharField(required=False, allow_blank=True)
    
    # Status fields
    last_test_status = serializers.CharField(required=False, allow_null=True)
    last_test_date = serializers.DateTimeField(required=False, allow_null=True)
    last_test_message = serializers.CharField(required=False, allow_blank=True)


class BulkPaymentGatewaySettingsSerializer(serializers.Serializer):
    """Serializer for bulk updating payment gateway settings"""
    
    paymob = PaymentGatewayConfigurationSerializer(required=False)
    stripe = PaymentGatewayConfigurationSerializer(required=False)
    paypal = PaymentGatewayConfigurationSerializer(required=False)
    
    def validate(self, data):
        """Validate bulk payment gateway settings"""
        for gateway_type, config in data.items():
            if config.get('enabled'):
                # Validate required fields for each gateway type
                if gateway_type == 'paymob':
                    required_fields = ['integration_id', 'iframe_id']
                    # API key and HMAC secret are required but may be masked
                    if not config.get('api_key_masked') and not config.get('api_key'):
                        raise serializers.ValidationError(f"API Key مطلوب لبوابة Paymob")
                    if not config.get('hmac_secret_masked') and not config.get('hmac_secret'):
                        raise serializers.ValidationError(f"HMAC Secret مطلوب لبوابة Paymob")
                        
                elif gateway_type == 'stripe':
                    required_fields = ['publishable_key']
                    if not config.get('secret_key_masked') and not config.get('secret_key'):
                        raise serializers.ValidationError(f"Secret Key مطلوب لبوابة Stripe")
                    if not config.get('webhook_secret_masked') and not config.get('webhook_secret'):
                        raise serializers.ValidationError(f"Webhook Secret مطلوب لبوابة Stripe")
                        
                elif gateway_type == 'paypal':
                    required_fields = ['client_id']
                    if not config.get('client_secret_masked') and not config.get('client_secret'):
                        raise serializers.ValidationError(f"Client Secret مطلوب لبوابة PayPal")
                else:
                    required_fields = []
                
                for field in required_fields:
                    if not config.get(field):
                        raise serializers.ValidationError(
                            f"حقل {field} مطلوب عند تفعيل بوابة {gateway_type}"
                        )
        
        return data
