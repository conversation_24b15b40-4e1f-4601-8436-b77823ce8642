"""
Admin configuration for settings models.
"""

from django.contrib import admin
from django.utils.html import format_html
from .models import PaymentGatewaySettings, SystemSettings


@admin.register(PaymentGatewaySettings)
class PaymentGatewaySettingsAdmin(admin.ModelAdmin):
    """Admin configuration for payment gateway settings"""
    
    list_display = [
        'gateway_type', 'enabled', 'sandbox_mode', 
        'last_test_status_display', 'last_test_date', 'updated_at'
    ]
    list_filter = ['gateway_type', 'enabled', 'sandbox_mode', 'last_test_status']
    search_fields = ['gateway_type']
    readonly_fields = [
        'created_at', 'updated_at', 'last_test_date', 
        'last_test_status', 'last_test_message', 'masked_configuration_display'
    ]
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('gateway_type', 'enabled', 'sandbox_mode')
        }),
        ('إعدادات البوابة', {
            'fields': ('configuration', 'masked_configuration_display'),
            'description': 'إعدادات البوابة (البيانات الحساسة مخفية في العرض المقنع)'
        }),
        ('حالة الاختبار', {
            'fields': ('last_test_date', 'last_test_status', 'last_test_message'),
            'classes': ('collapse',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at', 'updated_by'),
            'classes': ('collapse',)
        }),
    )
    
    def last_test_status_display(self, obj):
        """Display last test status with color coding"""
        if obj.last_test_status == 'success':
            return format_html(
                '<span style="color: green; font-weight: bold;">✓ نجح</span>'
            )
        elif obj.last_test_status == 'failed':
            return format_html(
                '<span style="color: red; font-weight: bold;">✗ فشل</span>'
            )
        elif obj.last_test_status == 'pending':
            return format_html(
                '<span style="color: orange; font-weight: bold;">⏳ في الانتظار</span>'
            )
        else:
            return format_html(
                '<span style="color: gray;">لم يتم الاختبار</span>'
            )
    last_test_status_display.short_description = 'حالة آخر اختبار'
    
    def masked_configuration_display(self, obj):
        """Display masked configuration"""
        masked_config = obj.get_masked_configuration()
        if not masked_config:
            return "لا توجد إعدادات"
        
        html = "<ul>"
        for key, value in masked_config.items():
            html += f"<li><strong>{key}:</strong> {value}</li>"
        html += "</ul>"
        return format_html(html)
    masked_configuration_display.short_description = 'الإعدادات المقنعة'
    
    def save_model(self, request, obj, form, change):
        """Save model with current user"""
        if not change or not obj.updated_by:
            obj.updated_by = request.user
        super().save_model(request, obj, form, change)
    
    actions = ['test_connection', 'enable_gateways', 'disable_gateways']
    
    def test_connection(self, request, queryset):
        """Test connection for selected gateways"""
        tested = 0
        successful = 0
        
        for gateway in queryset:
            if gateway.enabled:
                success = gateway.test_connection()
                tested += 1
                if success:
                    successful += 1
        
        self.message_user(
            request,
            f"تم اختبار {tested} بوابة، نجح منها {successful}"
        )
    test_connection.short_description = "اختبار الاتصال للبوابات المحددة"
    
    def enable_gateways(self, request, queryset):
        """Enable selected gateways"""
        updated = queryset.update(enabled=True)
        self.message_user(request, f"تم تفعيل {updated} بوابة")
    enable_gateways.short_description = "تفعيل البوابات المحددة"
    
    def disable_gateways(self, request, queryset):
        """Disable selected gateways"""
        updated = queryset.update(enabled=False)
        self.message_user(request, f"تم إلغاء تفعيل {updated} بوابة")
    disable_gateways.short_description = "إلغاء تفعيل البوابات المحددة"


@admin.register(SystemSettings)
class SystemSettingsAdmin(admin.ModelAdmin):
    """Admin configuration for system settings"""
    
    list_display = ['company_name_ar', 'default_language', 'default_currency', 'updated_at']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('معلومات الشركة', {
            'fields': (
                'company_name_ar', 'company_name_en', 'company_email',
                'company_phone', 'company_website'
            )
        }),
        ('إعدادات النظام', {
            'fields': (
                'default_language', 'default_timezone', 'default_currency',
                'default_tax_rate'
            )
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at', 'updated_by'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        """Save model with current user"""
        if not change or not obj.updated_by:
            obj.updated_by = request.user
        super().save_model(request, obj, form, change)
    
    def has_add_permission(self, request):
        """Only allow one system settings instance"""
        return not SystemSettings.objects.exists()
    
    def has_delete_permission(self, request, obj=None):
        """Don't allow deletion of system settings"""
        return False
