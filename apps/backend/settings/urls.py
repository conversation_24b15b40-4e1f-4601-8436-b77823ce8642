"""
URLs for settings app.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import PaymentGatewaySettingsViewSet, SystemSettingsViewSet

# Create router and register viewsets
router = DefaultRouter()
router.register(r'payment-gateways', PaymentGatewaySettingsViewSet, basename='paymentgatewaysettings')
router.register(r'system', SystemSettingsViewSet, basename='systemsettings')

urlpatterns = [
    path('', include(router.urls)),
]
