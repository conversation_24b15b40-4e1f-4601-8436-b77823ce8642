# Generated migration for settings models

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentGatewaySettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('gateway_type', models.CharField(choices=[('paymob', 'Paymob'), ('stripe', 'Stripe'), ('paypal', 'PayPal')], max_length=20, unique=True, verbose_name='نوع البوابة')),
                ('enabled', models.BooleanField(default=False, verbose_name='مفعل')),
                ('sandbox_mode', models.BooleanField(default=True, verbose_name='وضع الاختبار')),
                ('configuration', models.JSONField(default=dict, verbose_name='إعدادات البوابة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('last_test_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ آخر اختبار')),
                ('last_test_status', models.CharField(blank=True, choices=[('success', 'نجح'), ('failed', 'فشل'), ('pending', 'في الانتظار')], max_length=20, null=True, verbose_name='حالة آخر اختبار')),
                ('last_test_message', models.TextField(blank=True, null=True, verbose_name='رسالة آخر اختبار')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة')),
            ],
            options={
                'verbose_name': 'إعدادات بوابة الدفع',
                'verbose_name_plural': 'إعدادات بوابات الدفع',
                'ordering': ['gateway_type'],
            },
        ),
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_name_ar', models.CharField(default='وكالة MTBRMG الرقمية', max_length=200, verbose_name='اسم الشركة بالعربية')),
                ('company_name_en', models.CharField(default='MTBRMG Digital Agency', max_length=200, verbose_name='اسم الشركة بالإنجليزية')),
                ('company_email', models.EmailField(default='<EMAIL>', max_length=254, verbose_name='البريد الإلكتروني للشركة')),
                ('company_phone', models.CharField(default='+20 ************', max_length=20, verbose_name='هاتف الشركة')),
                ('company_website', models.URLField(default='https://www.mtbrmg.com', verbose_name='موقع الشركة')),
                ('default_language', models.CharField(choices=[('ar', 'العربية'), ('en', 'English')], default='ar', max_length=5, verbose_name='اللغة الافتراضية')),
                ('default_timezone', models.CharField(default='Africa/Cairo', max_length=50, verbose_name='المنطقة الزمنية الافتراضية')),
                ('default_currency', models.CharField(choices=[('EGP', 'جنيه مصري'), ('USD', 'دولار أمريكي'), ('SAR', 'ريال سعودي'), ('AED', 'درهم إماراتي')], default='EGP', max_length=3, verbose_name='العملة الافتراضية')),
                ('default_tax_rate', models.DecimalField(decimal_places=2, default=14.0, max_digits=5, verbose_name='معدل الضريبة الافتراضي (%)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة')),
            ],
            options={
                'verbose_name': 'إعدادات النظام',
                'verbose_name_plural': 'إعدادات النظام',
            },
        ),
    ]
