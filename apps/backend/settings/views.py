"""
Views for settings management.
"""

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.utils import timezone
import logging

from .models import PaymentGatewaySettings, SystemSettings
from .serializers import (
    PaymentGatewaySettingsSerializer,
    PaymentGatewayTestSerializer,
    SystemSettingsSerializer,
    PaymentGatewayStatusSerializer,
    BulkPaymentGatewaySettingsSerializer
)

logger = logging.getLogger(__name__)


class PaymentGatewaySettingsViewSet(viewsets.ModelViewSet):
    """ViewSet for managing payment gateway settings"""
    
    queryset = PaymentGatewaySettings.objects.all()
    serializer_class = PaymentGatewaySettingsSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = 'gateway_type'

    def get_queryset(self):
        """Return payment gateway settings"""
        return PaymentGatewaySettings.objects.all().order_by('gateway_type')

    @action(detail=True, methods=['post'])
    def test_connection(self, request, gateway_type=None):
        """Test connection to a specific payment gateway"""
        try:
            gateway_settings = get_object_or_404(
                PaymentGatewaySettings, 
                gateway_type=gateway_type
            )
            
            if not gateway_settings.enabled:
                return Response(
                    {"error": "بوابة الدفع غير مفعلة"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Test the connection
            success = gateway_settings.test_connection()
            
            if success:
                return Response({
                    "success": True,
                    "message": gateway_settings.last_test_message,
                    "test_date": gateway_settings.last_test_date
                })
            else:
                return Response({
                    "success": False,
                    "message": gateway_settings.last_test_message,
                    "test_date": gateway_settings.last_test_date
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"Error testing gateway {gateway_type}: {str(e)}")
            return Response(
                {"error": f"خطأ في اختبار البوابة: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def status(self, request):
        """Get status of all payment gateways"""
        try:
            gateways = PaymentGatewaySettings.objects.all()
            serializer = PaymentGatewayStatusSerializer(gateways, many=True)
            return Response({"gateways": serializer.data})
        except Exception as e:
            logger.error(f"Error getting gateway status: {str(e)}")
            return Response(
                {"error": f"خطأ في جلب حالة البوابات: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def bulk_update(self, request):
        """Bulk update payment gateway settings"""
        try:
            serializer = BulkPaymentGatewaySettingsSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(
                    {"errors": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            validated_data = serializer.validated_data
            updated_gateways = []
            
            for gateway_type, config in validated_data.items():
                # Get or create gateway settings
                gateway_settings, created = PaymentGatewaySettings.objects.get_or_create(
                    gateway_type=gateway_type,
                    defaults={
                        'enabled': config.get('enabled', False),
                        'sandbox_mode': config.get('sandbox_mode', True),
                        'configuration': {},
                        'updated_by': request.user
                    }
                )
                
                # Update basic settings
                gateway_settings.enabled = config.get('enabled', gateway_settings.enabled)
                gateway_settings.sandbox_mode = config.get('sandbox_mode', gateway_settings.sandbox_mode)
                gateway_settings.updated_by = request.user
                
                # Update configuration
                current_config = gateway_settings.configuration or {}
                
                if gateway_type == 'paymob':
                    if config.get('api_key') and not config.get('api_key').startswith('***'):
                        current_config['api_key'] = config['api_key']
                    if config.get('integration_id'):
                        current_config['integration_id'] = config['integration_id']
                    if config.get('iframe_id'):
                        current_config['iframe_id'] = config['iframe_id']
                    if config.get('hmac_secret') and not config.get('hmac_secret').startswith('***'):
                        current_config['hmac_secret'] = config['hmac_secret']
                        
                elif gateway_type == 'stripe':
                    if config.get('publishable_key'):
                        current_config['publishable_key'] = config['publishable_key']
                    if config.get('secret_key') and not config.get('secret_key').startswith('***'):
                        current_config['secret_key'] = config['secret_key']
                    if config.get('webhook_secret') and not config.get('webhook_secret').startswith('***'):
                        current_config['webhook_secret'] = config['webhook_secret']
                        
                elif gateway_type == 'paypal':
                    if config.get('client_id'):
                        current_config['client_id'] = config['client_id']
                    if config.get('client_secret') and not config.get('client_secret').startswith('***'):
                        current_config['client_secret'] = config['client_secret']
                
                gateway_settings.configuration = current_config
                gateway_settings.save()
                
                updated_gateways.append({
                    'gateway_type': gateway_type,
                    'enabled': gateway_settings.enabled,
                    'sandbox_mode': gateway_settings.sandbox_mode
                })
            
            return Response({
                "success": True,
                "message": "تم تحديث إعدادات بوابات الدفع بنجاح",
                "updated_gateways": updated_gateways
            })
            
        except Exception as e:
            logger.error(f"Error bulk updating gateways: {str(e)}")
            return Response(
                {"error": f"خطأ في تحديث إعدادات البوابات: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def test_all(self, request):
        """Test connection to all enabled payment gateways"""
        try:
            enabled_gateways = PaymentGatewaySettings.objects.filter(enabled=True)
            results = []
            
            for gateway in enabled_gateways:
                success = gateway.test_connection()
                results.append({
                    'gateway_type': gateway.gateway_type,
                    'gateway_name': gateway.get_gateway_type_display(),
                    'success': success,
                    'message': gateway.last_test_message,
                    'test_date': gateway.last_test_date
                })
            
            return Response({
                "results": results,
                "total_tested": len(results),
                "successful": len([r for r in results if r['success']])
            })
            
        except Exception as e:
            logger.error(f"Error testing all gateways: {str(e)}")
            return Response(
                {"error": f"خطأ في اختبار البوابات: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SystemSettingsViewSet(viewsets.ModelViewSet):
    """ViewSet for managing system settings"""
    
    queryset = SystemSettings.objects.all()
    serializer_class = SystemSettingsSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        """Get or create system settings instance"""
        return SystemSettings.get_settings()

    def list(self, request, *args, **kwargs):
        """Return system settings"""
        settings = self.get_object()
        serializer = self.get_serializer(settings)
        return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        """Update system settings"""
        settings = self.get_object()
        serializer = self.get_serializer(
            settings, 
            data=request.data, 
            partial=kwargs.pop('partial', False)
        )
        
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        
        return Response(
            serializer.errors, 
            status=status.HTTP_400_BAD_REQUEST
        )

    def partial_update(self, request, *args, **kwargs):
        """Partially update system settings"""
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs)

    @action(detail=False, methods=['post'])
    def reset_to_defaults(self, request):
        """Reset system settings to default values"""
        try:
            settings = self.get_object()
            
            # Reset to default values
            settings.company_name_ar = "وكالة MTBRMG الرقمية"
            settings.company_name_en = "MTBRMG Digital Agency"
            settings.company_email = "<EMAIL>"
            settings.company_phone = "+20 ************"
            settings.company_website = "https://www.mtbrmg.com"
            settings.default_language = "ar"
            settings.default_timezone = "Africa/Cairo"
            settings.default_currency = "EGP"
            settings.default_tax_rate = 14.00
            settings.updated_by = request.user
            
            settings.save()
            
            serializer = self.get_serializer(settings)
            return Response({
                "success": True,
                "message": "تم إعادة تعيين الإعدادات للقيم الافتراضية",
                "settings": serializer.data
            })
            
        except Exception as e:
            logger.error(f"Error resetting settings: {str(e)}")
            return Response(
                {"error": f"خطأ في إعادة تعيين الإعدادات: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
