"""
Settings models for MTBRMG ERP system.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
import json

User = get_user_model()


class PaymentGatewaySettings(models.Model):
    """Model for storing payment gateway configurations"""
    
    GATEWAY_CHOICES = [
        ('paymob', 'Paymob'),
        ('stripe', 'Stripe'),
        ('paypal', 'PayPal'),
    ]
    
    # Basic Information
    gateway_type = models.CharField(
        max_length=20,
        choices=GATEWAY_CHOICES,
        unique=True,
        verbose_name="نوع البوابة"
    )
    enabled = models.BooleanField(
        default=False,
        verbose_name="مفعل"
    )
    sandbox_mode = models.BooleanField(
        default=True,
        verbose_name="وضع الاختبار"
    )
    
    # Configuration Data (stored as JSON)
    configuration = models.JSONField(
        default=dict,
        verbose_name="إعدادات البوابة"
    )
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="تم التحديث بواسطة"
    )
    
    # Connection Status
    last_test_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="تاريخ آخر اختبار"
    )
    last_test_status = models.CharField(
        max_length=20,
        choices=[
            ('success', 'نجح'),
            ('failed', 'فشل'),
            ('pending', 'في الانتظار'),
        ],
        null=True,
        blank=True,
        verbose_name="حالة آخر اختبار"
    )
    last_test_message = models.TextField(
        null=True,
        blank=True,
        verbose_name="رسالة آخر اختبار"
    )

    class Meta:
        verbose_name = "إعدادات بوابة الدفع"
        verbose_name_plural = "إعدادات بوابات الدفع"
        ordering = ['gateway_type']

    def __str__(self):
        return f"{self.get_gateway_type_display()} - {'مفعل' if self.enabled else 'معطل'}"

    def clean(self):
        """Validate configuration based on gateway type"""
        if self.gateway_type == 'paymob':
            required_fields = ['api_key', 'integration_id', 'iframe_id', 'hmac_secret']
        elif self.gateway_type == 'stripe':
            required_fields = ['publishable_key', 'secret_key', 'webhook_secret']
        elif self.gateway_type == 'paypal':
            required_fields = ['client_id', 'client_secret']
        else:
            return

        if self.enabled:
            for field in required_fields:
                if not self.configuration.get(field):
                    raise ValidationError(f"حقل {field} مطلوب عند تفعيل البوابة")

    def get_masked_configuration(self):
        """Return configuration with sensitive data masked"""
        if not self.configuration:
            return {}
        
        masked_config = self.configuration.copy()
        sensitive_fields = [
            'api_key', 'secret_key', 'webhook_secret', 
            'hmac_secret', 'client_secret'
        ]
        
        for field in sensitive_fields:
            if field in masked_config and masked_config[field]:
                # Show only first 4 and last 4 characters
                value = str(masked_config[field])
                if len(value) > 8:
                    masked_config[field] = f"{value[:4]}...{value[-4:]}"
                else:
                    masked_config[field] = "***"
        
        return masked_config

    def test_connection(self):
        """Test connection to the payment gateway"""
        from django.utils import timezone
        
        self.last_test_date = timezone.now()
        
        try:
            if self.gateway_type == 'paymob':
                from invoices.payment_gateways.paymob import PaymobGateway
                gateway = PaymobGateway()
                # Test authentication
                auth_token = gateway.authenticate()
                if auth_token:
                    self.last_test_status = 'success'
                    self.last_test_message = 'تم الاتصال بنجاح'
                else:
                    self.last_test_status = 'failed'
                    self.last_test_message = 'فشل في المصادقة'
                    
            elif self.gateway_type == 'stripe':
                # TODO: Implement Stripe connection test
                self.last_test_status = 'success'
                self.last_test_message = 'اختبار Stripe - تم بنجاح'
                
            elif self.gateway_type == 'paypal':
                # TODO: Implement PayPal connection test
                self.last_test_status = 'success'
                self.last_test_message = 'اختبار PayPal - تم بنجاح'
                
        except Exception as e:
            self.last_test_status = 'failed'
            self.last_test_message = f'خطأ في الاتصال: {str(e)}'
        
        self.save()
        return self.last_test_status == 'success'


class SystemSettings(models.Model):
    """Model for storing general system settings"""
    
    # Company Information
    company_name_ar = models.CharField(
        max_length=200,
        default="وكالة MTBRMG الرقمية",
        verbose_name="اسم الشركة بالعربية"
    )
    company_name_en = models.CharField(
        max_length=200,
        default="MTBRMG Digital Agency",
        verbose_name="اسم الشركة بالإنجليزية"
    )
    company_email = models.EmailField(
        default="<EMAIL>",
        verbose_name="البريد الإلكتروني للشركة"
    )
    company_phone = models.CharField(
        max_length=20,
        default="+20 ************",
        verbose_name="هاتف الشركة"
    )
    company_website = models.URLField(
        default="https://www.mtbrmg.com",
        verbose_name="موقع الشركة"
    )
    
    # System Configuration
    default_language = models.CharField(
        max_length=5,
        choices=[('ar', 'العربية'), ('en', 'English')],
        default='ar',
        verbose_name="اللغة الافتراضية"
    )
    default_timezone = models.CharField(
        max_length=50,
        default='Africa/Cairo',
        verbose_name="المنطقة الزمنية الافتراضية"
    )
    default_currency = models.CharField(
        max_length=3,
        choices=[
            ('EGP', 'جنيه مصري'),
            ('USD', 'دولار أمريكي'),
            ('SAR', 'ريال سعودي'),
            ('AED', 'درهم إماراتي'),
        ],
        default='EGP',
        verbose_name="العملة الافتراضية"
    )
    
    # Tax Settings
    default_tax_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=14.00,
        verbose_name="معدل الضريبة الافتراضي (%)"
    )
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="تم التحديث بواسطة"
    )

    class Meta:
        verbose_name = "إعدادات النظام"
        verbose_name_plural = "إعدادات النظام"

    def __str__(self):
        return f"إعدادات النظام - {self.company_name_ar}"

    @classmethod
    def get_settings(cls):
        """Get or create system settings instance"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings
