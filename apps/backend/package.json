{"name": "@mtbrmg/backend", "version": "1.0.0", "private": true, "scripts": {"dev": "source venv/bin/activate && python manage.py runserver 8000", "migrate": "python manage.py migrate", "makemigrations": "python manage.py makemigrations", "collectstatic": "python manage.py collectstatic --noinput", "createsuperuser": "python manage.py <PERSON><PERSON><PERSON>er", "shell": "python manage.py shell", "test": "pytest", "lint": "flake8 .", "format": "black . && isort .", "celery": "celery -A mtbrmg_erp worker -l info", "celery-beat": "celery -A mtbrmg_erp beat -l info"}}