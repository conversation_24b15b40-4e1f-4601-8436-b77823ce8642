[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  migrations
  | venv
  | __pycache__
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
skip = ["migrations", "venv"]
known_django = "django"
known_first_party = ["mtbrmg_erp"]
sections = ["FUTURE", "STDLIB", "DJANGO", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

[tool.pylint.messages_control]
disable = [
  "C0114",  # missing-module-docstring
  "C0115",  # missing-class-docstring
  "C0116",  # missing-function-docstring
  "R0903",  # too-few-public-methods
  "R0801"   # duplicate-code
]

[tool.pylint.format]
max-line-length = 88
