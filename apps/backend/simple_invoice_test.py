#!/usr/bin/env python
"""
Simple test script to verify the invoices module is working correctly.
"""

import os
import sys
import django

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mtbrmg_erp.settings')
django.setup()

def test_invoice_models():
    """Test invoice models can be imported and basic functionality"""
    print("🧪 Testing Invoice Models Import...")
    
    try:
        from invoices.models import InvoiceTemplate, Invoice, InvoiceItem, InvoicePayment
        print("✅ Invoice models imported successfully")
        
        # Check existing data
        template_count = InvoiceTemplate.objects.count()
        invoice_count = Invoice.objects.count()
        item_count = InvoiceItem.objects.count()
        payment_count = InvoicePayment.objects.count()
        
        print(f"   Templates: {template_count}")
        print(f"   Invoices: {invoice_count}")
        print(f"   Items: {item_count}")
        print(f"   Payments: {payment_count}")
        
        # Test model methods if we have data
        if invoice_count > 0:
            invoice = Invoice.objects.first()
            print(f"✅ Sample invoice: {invoice.invoice_number}")
            print(f"   Status: {invoice.status}")
            print(f"   Total: {invoice.total_amount}")
            print(f"   Is Overdue: {invoice.is_overdue}")
            print(f"   Payment %: {invoice.payment_percentage:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Model test failed: {e}")
        return False


def test_invoice_urls():
    """Test invoice URL patterns"""
    print("\n🌐 Testing Invoice URLs...")
    
    try:
        from django.urls import reverse
        from invoices.urls import urlpatterns
        
        print(f"✅ Invoice URLs loaded: {len(urlpatterns)} patterns")
        
        # Test URL reversing
        url_patterns = [
            'invoicetemplate-list',
            'invoice-list', 
            'invoiceitem-list',
            'invoicepayment-list'
        ]
        
        for pattern in url_patterns:
            try:
                url = reverse(pattern)
                print(f"   ✅ {pattern}: {url}")
            except Exception as e:
                print(f"   ❌ {pattern}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ URL test failed: {e}")
        return False


def test_invoice_serializers():
    """Test invoice serializers"""
    print("\n📝 Testing Invoice Serializers...")
    
    try:
        from invoices.serializers import (
            InvoiceTemplateSerializer,
            InvoiceSerializer,
            InvoiceListSerializer,
            InvoiceItemSerializer,
            InvoicePaymentSerializer
        )
        
        print("✅ Invoice serializers imported successfully")
        
        # Test serializer instantiation
        serializers = [
            ('InvoiceTemplateSerializer', InvoiceTemplateSerializer),
            ('InvoiceSerializer', InvoiceSerializer),
            ('InvoiceListSerializer', InvoiceListSerializer),
            ('InvoiceItemSerializer', InvoiceItemSerializer),
            ('InvoicePaymentSerializer', InvoicePaymentSerializer),
        ]
        
        for name, serializer_class in serializers:
            try:
                serializer = serializer_class()
                print(f"   ✅ {name}: {len(serializer.fields)} fields")
            except Exception as e:
                print(f"   ❌ {name}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Serializer test failed: {e}")
        return False


def test_invoice_views():
    """Test invoice views"""
    print("\n👁️ Testing Invoice Views...")
    
    try:
        from invoices.views import (
            InvoiceTemplateViewSet,
            InvoiceViewSet,
            InvoiceItemViewSet,
            InvoicePaymentViewSet
        )
        
        print("✅ Invoice views imported successfully")
        
        # Test viewset instantiation
        viewsets = [
            ('InvoiceTemplateViewSet', InvoiceTemplateViewSet),
            ('InvoiceViewSet', InvoiceViewSet),
            ('InvoiceItemViewSet', InvoiceItemViewSet),
            ('InvoicePaymentViewSet', InvoicePaymentViewSet),
        ]
        
        for name, viewset_class in viewsets:
            try:
                viewset = viewset_class()
                print(f"   ✅ {name}: {viewset.queryset.model.__name__}")
            except Exception as e:
                print(f"   ❌ {name}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ View test failed: {e}")
        return False


def test_invoice_admin():
    """Test invoice admin"""
    print("\n⚙️ Testing Invoice Admin...")
    
    try:
        from invoices.admin import (
            InvoiceTemplateAdmin,
            InvoiceAdmin,
            InvoiceItemAdmin,
            InvoicePaymentAdmin
        )
        
        print("✅ Invoice admin classes imported successfully")
        
        # Test admin instantiation
        admin_classes = [
            ('InvoiceTemplateAdmin', InvoiceTemplateAdmin),
            ('InvoiceAdmin', InvoiceAdmin),
            ('InvoiceItemAdmin', InvoiceItemAdmin),
            ('InvoicePaymentAdmin', InvoicePaymentAdmin),
        ]
        
        for name, admin_class in admin_classes:
            try:
                admin = admin_class(admin_class.model, None)
                print(f"   ✅ {name}: {len(admin.list_display)} display fields")
            except Exception as e:
                print(f"   ❌ {name}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Admin test failed: {e}")
        return False


def test_database_tables():
    """Test database tables exist"""
    print("\n🗄️ Testing Database Tables...")
    
    try:
        from django.db import connection
        
        cursor = connection.cursor()
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name LIKE 'invoices_%'
            ORDER BY table_name;
        """)
        
        tables = cursor.fetchall()
        print(f"✅ Found {len(tables)} invoice tables:")
        
        for table in tables:
            print(f"   - {table[0]}")
        
        return len(tables) > 0
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False


def main():
    """Main test function"""
    print("🚀 Starting Invoice Module Simple Tests...\n")
    
    tests = [
        test_invoice_models,
        test_invoice_urls,
        test_invoice_serializers,
        test_invoice_views,
        test_invoice_admin,
        test_database_tables
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with error: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! Invoice module is working correctly.")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
