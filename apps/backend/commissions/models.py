from django.db import models
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from djmoney.models.fields import MoneyField
from decimal import Decimal


class CommissionRule(models.Model):
    """Commission rules for different scenarios"""

    class RuleType(models.TextChoices):
        PERCENTAGE = 'percentage', 'نسبة مئوية'
        FIXED_AMOUNT = 'fixed_amount', 'مبلغ ثابت'
        TIERED = 'tiered', 'متدرج'

    name = models.CharField(max_length=200, verbose_name='اسم القاعدة')
    rule_type = models.CharField(
        max_length=20,
        choices=RuleType.choices,
        default=RuleType.PERCENTAGE,
        verbose_name='نوع القاعدة'
    )
    percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('12.50'),
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name='النسبة المئوية'
    )
    fixed_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        default=0,
        verbose_name='المبلغ الثابت'
    )
    min_project_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        blank=True,
        null=True,
        verbose_name='الحد الأدنى لمبلغ المشروع'
    )
    max_project_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        blank=True,
        null=True,
        verbose_name='الحد الأقصى لمبلغ المشروع'
    )
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'قاعدة العمولة'
        verbose_name_plural = 'قواعد العمولة'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} - {self.percentage}%"

    def calculate_commission(self, project_amount):
        """Calculate commission based on rule type"""
        if not self.is_active:
            return Decimal('0')

        # Check amount limits
        if self.min_project_amount and project_amount < self.min_project_amount:
            return Decimal('0')
        if self.max_project_amount and project_amount > self.max_project_amount:
            return Decimal('0')

        if self.rule_type == self.RuleType.PERCENTAGE:
            return (project_amount * self.percentage) / 100
        elif self.rule_type == self.RuleType.FIXED_AMOUNT:
            return self.fixed_amount
        else:
            # For tiered, we'll implement later if needed
            return (project_amount * self.percentage) / 100


class Commission(models.Model):
    """Commission tracking for sales representatives"""

    class Status(models.TextChoices):
        PENDING = 'pending', 'في الانتظار'
        APPROVED = 'approved', 'موافق عليها'
        PAID = 'paid', 'مدفوعة'
        CANCELLED = 'cancelled', 'ملغية'

    # Relationships
    sales_rep = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='commissions',
        verbose_name='مندوب المبيعات'
    )
    client = models.ForeignKey(
        'clients.Client',
        on_delete=models.CASCADE,
        related_name='commissions',
        verbose_name='العميل'
    )
    project = models.ForeignKey(
        'projects.Project',
        on_delete=models.CASCADE,
        related_name='commissions',
        verbose_name='المشروع'
    )
    commission_rule = models.ForeignKey(
        CommissionRule,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='قاعدة العمولة'
    )

    # Financial Details
    project_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        verbose_name='مبلغ المشروع'
    )
    commission_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('12.50'),
        verbose_name='نسبة العمولة'
    )
    commission_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        verbose_name='مبلغ العمولة'
    )

    # Status and Tracking
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING,
        verbose_name='الحالة'
    )
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    # Dates
    earned_date = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الاستحقاق')
    approved_date = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')
    paid_date = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الدفع')

    # Audit
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_commissions',
        verbose_name='أنشأ بواسطة'
    )

    class Meta:
        verbose_name = 'عمولة'
        verbose_name_plural = 'العمولات'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['sales_rep', 'status'], name='comm_sales_rep_status_idx'),
            models.Index(fields=['status', 'earned_date'], name='comm_status_date_idx'),
            models.Index(fields=['project'], name='comm_project_idx'),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['project', 'sales_rep'],
                name='unique_commission_per_project_sales_rep'
            )
        ]

    def __str__(self):
        return f"عمولة {self.sales_rep.get_full_name()} - {self.project.name}"

    @property
    def is_overdue(self):
        """Check if commission payment is overdue (more than 30 days)"""
        from django.utils import timezone
        from datetime import timedelta

        if self.status == self.Status.PAID:
            return False

        overdue_date = self.earned_date + timedelta(days=30)
        return timezone.now() > overdue_date

    def approve(self, approved_by=None):
        """Approve the commission"""
        from django.utils import timezone

        self.status = self.Status.APPROVED
        self.approved_date = timezone.now()
        if approved_by:
            self.notes = f"تم الموافقة بواسطة {approved_by.get_full_name()}"
        self.save(update_fields=['status', 'approved_date', 'notes', 'updated_at'])

    def mark_as_paid(self, paid_by=None):
        """Mark commission as paid"""
        from django.utils import timezone

        self.status = self.Status.PAID
        self.paid_date = timezone.now()
        if paid_by:
            self.notes = f"تم الدفع بواسطة {paid_by.get_full_name()}"
        self.save(update_fields=['status', 'paid_date', 'notes', 'updated_at'])


class CommissionPayment(models.Model):
    """Commission payment tracking"""

    class PaymentMethod(models.TextChoices):
        BANK_TRANSFER = 'bank_transfer', 'تحويل بنكي'
        CASH = 'cash', 'نقداً'
        CHECK = 'check', 'شيك'
        DIGITAL_WALLET = 'digital_wallet', 'محفظة رقمية'

    class PaymentStatus(models.TextChoices):
        PENDING = 'pending', 'في الانتظار'
        PROCESSING = 'processing', 'قيد المعالجة'
        COMPLETED = 'completed', 'مكتمل'
        FAILED = 'failed', 'فشل'
        CANCELLED = 'cancelled', 'ملغي'

    # Relationships
    commission = models.OneToOneField(
        Commission,
        on_delete=models.CASCADE,
        related_name='payment',
        verbose_name='العمولة'
    )
    paid_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='commission_payments_made',
        verbose_name='دفع بواسطة'
    )

    # Payment Details
    payment_method = models.CharField(
        max_length=20,
        choices=PaymentMethod.choices,
        default=PaymentMethod.BANK_TRANSFER,
        verbose_name='طريقة الدفع'
    )
    payment_status = models.CharField(
        max_length=20,
        choices=PaymentStatus.choices,
        default=PaymentStatus.PENDING,
        verbose_name='حالة الدفع'
    )
    payment_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        verbose_name='مبلغ الدفع'
    )
    transaction_reference = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name='مرجع المعاملة'
    )

    # Banking Details
    bank_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name='اسم البنك'
    )
    account_number = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='رقم الحساب'
    )

    # Dates
    payment_date = models.DateTimeField(verbose_name='تاريخ الدفع')
    processed_date = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ المعالجة')

    # Additional Info
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    receipt_url = models.URLField(blank=True, null=True, verbose_name='رابط الإيصال')

    # Audit
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'دفعة عمولة'
        verbose_name_plural = 'دفعات العمولات'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['payment_status', 'payment_date'], name='comm_pay_status_date_idx'),
            models.Index(fields=['commission'], name='comm_pay_commission_idx'),
        ]

    def __str__(self):
        return f"دفعة عمولة {self.commission.sales_rep.get_full_name()} - {self.payment_amount}"

    def mark_as_completed(self):
        """Mark payment as completed"""
        from django.utils import timezone

        self.payment_status = self.PaymentStatus.COMPLETED
        self.processed_date = timezone.now()
        self.save(update_fields=['payment_status', 'processed_date', 'updated_at'])

        # Update the related commission status
        self.commission.mark_as_paid(self.paid_by)

    def mark_as_failed(self, reason=None):
        """Mark payment as failed"""
        self.payment_status = self.PaymentStatus.FAILED
        if reason:
            self.notes = f"فشل الدفع: {reason}"
        self.save(update_fields=['payment_status', 'notes', 'updated_at'])
