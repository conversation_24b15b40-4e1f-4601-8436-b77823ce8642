from django.contrib import admin
from .models import Commission, CommissionRule, CommissionPayment


@admin.register(CommissionRule)
class CommissionRuleAdmin(admin.ModelAdmin):
    list_display = ['name', 'rule_type', 'percentage', 'is_active', 'created_at']
    list_filter = ['rule_type', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'rule_type', 'description', 'is_active')
        }),
        ('إعدادات العمولة', {
            'fields': ('percentage', 'fixed_amount', 'min_project_amount', 'max_project_amount')
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Commission)
class CommissionAdmin(admin.ModelAdmin):
    list_display = [
        'sales_rep', 'client', 'project', 'commission_amount',
        'status', 'earned_date', 'is_overdue'
    ]
    list_filter = ['status', 'earned_date', 'approved_date', 'paid_date']
    search_fields = [
        'sales_rep__first_name', 'sales_rep__last_name',
        'client__name', 'project__name'
    ]
    readonly_fields = [
        'commission_amount', 'earned_date', 'approved_date',
        'paid_date', 'is_overdue', 'created_at', 'updated_at'
    ]
    raw_id_fields = ['sales_rep', 'client', 'project', 'commission_rule', 'created_by']

    fieldsets = (
        ('معلومات العمولة', {
            'fields': ('sales_rep', 'client', 'project', 'commission_rule')
        }),
        ('التفاصيل المالية', {
            'fields': ('project_amount', 'commission_percentage', 'commission_amount')
        }),
        ('الحالة والتتبع', {
            'fields': ('status', 'notes')
        }),
        ('التواريخ', {
            'fields': ('earned_date', 'approved_date', 'paid_date'),
            'classes': ('collapse',)
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'sales_rep', 'client', 'project', 'commission_rule', 'created_by'
        )


@admin.register(CommissionPayment)
class CommissionPaymentAdmin(admin.ModelAdmin):
    list_display = [
        'commission', 'payment_amount', 'payment_method',
        'payment_status', 'payment_date'
    ]
    list_filter = ['payment_method', 'payment_status', 'payment_date']
    search_fields = [
        'commission__sales_rep__first_name', 'commission__sales_rep__last_name',
        'transaction_reference'
    ]
    readonly_fields = ['processed_date', 'created_at', 'updated_at']
    raw_id_fields = ['commission', 'paid_by']

    fieldsets = (
        ('معلومات الدفع', {
            'fields': ('commission', 'paid_by', 'payment_amount')
        }),
        ('تفاصيل الدفع', {
            'fields': ('payment_method', 'payment_status', 'transaction_reference')
        }),
        ('معلومات البنك', {
            'fields': ('bank_name', 'account_number'),
            'classes': ('collapse',)
        }),
        ('التواريخ', {
            'fields': ('payment_date', 'processed_date')
        }),
        ('معلومات إضافية', {
            'fields': ('notes', 'receipt_url'),
            'classes': ('collapse',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'commission', 'paid_by'
        )
