from rest_framework import serializers
from .models import Commission, CommissionRule, CommissionPayment
from authentication.serializers import UserListSerializer
from clients.serializers import ClientListSerializer
from projects.serializers import ProjectListSerializer


class CommissionRuleSerializer(serializers.ModelSerializer):
    """Commission rule serializer"""
    
    class Meta:
        model = CommissionRule
        fields = [
            'id', 'name', 'rule_type', 'percentage', 'fixed_amount',
            'min_project_amount', 'max_project_amount', 'is_active',
            'description', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class CommissionRuleListSerializer(serializers.ModelSerializer):
    """Commission rule list serializer"""
    
    class Meta:
        model = CommissionRule
        fields = [
            'id', 'name', 'rule_type', 'percentage', 'is_active'
        ]


class CommissionPaymentSerializer(serializers.ModelSerializer):
    """Commission payment serializer"""
    paid_by = UserListSerializer(read_only=True)
    paid_by_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    
    class Meta:
        model = CommissionPayment
        fields = [
            'id', 'commission', 'paid_by', 'paid_by_id',
            'payment_method', 'payment_status', 'payment_amount',
            'transaction_reference', 'bank_name', 'account_number',
            'payment_date', 'processed_date', 'notes', 'receipt_url',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'processed_date', 'created_at', 'updated_at']


class CommissionSerializer(serializers.ModelSerializer):
    """Commission serializer with all fields"""
    sales_rep = UserListSerializer(read_only=True)
    sales_rep_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    client = ClientListSerializer(read_only=True)
    client_id = serializers.IntegerField(write_only=True)
    project = ProjectListSerializer(read_only=True)
    project_id = serializers.IntegerField(write_only=True)
    commission_rule = CommissionRuleListSerializer(read_only=True)
    commission_rule_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    payment = CommissionPaymentSerializer(read_only=True)
    created_by = UserListSerializer(read_only=True)
    
    # Computed fields
    is_overdue = serializers.ReadOnlyField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Commission
        fields = [
            'id', 'sales_rep', 'sales_rep_id', 'client', 'client_id',
            'project', 'project_id', 'commission_rule', 'commission_rule_id',
            'project_amount', 'commission_percentage', 'commission_amount',
            'status', 'status_display', 'notes', 'payment',
            'earned_date', 'approved_date', 'paid_date',
            'is_overdue', 'created_by', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'commission_amount', 'earned_date', 'approved_date', 
            'paid_date', 'is_overdue', 'created_by', 'created_at', 'updated_at'
        ]

    def create(self, validated_data):
        """Create commission with automatic calculation"""
        # Calculate commission amount based on project amount and percentage
        project_amount = validated_data['project_amount']
        commission_percentage = validated_data.get('commission_percentage', 12.50)
        commission_amount = (project_amount * commission_percentage) / 100
        validated_data['commission_amount'] = commission_amount
        
        # Set created_by to current user
        request = self.context.get('request')
        if request and request.user:
            validated_data['created_by'] = request.user
            
        return super().create(validated_data)


class CommissionListSerializer(serializers.ModelSerializer):
    """Commission list serializer"""
    sales_rep = UserListSerializer(read_only=True)
    client = ClientListSerializer(read_only=True)
    project = ProjectListSerializer(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    is_overdue = serializers.ReadOnlyField()
    
    class Meta:
        model = Commission
        fields = [
            'id', 'sales_rep', 'client', 'project',
            'project_amount', 'commission_percentage', 'commission_amount',
            'status', 'status_display', 'is_overdue',
            'earned_date', 'approved_date', 'paid_date'
        ]


class CommissionCreateSerializer(serializers.ModelSerializer):
    """Commission creation serializer"""
    
    class Meta:
        model = Commission
        fields = [
            'sales_rep_id', 'client_id', 'project_id', 'commission_rule_id',
            'project_amount', 'commission_percentage', 'notes'
        ]

    def create(self, validated_data):
        """Create commission with automatic calculation"""
        # Calculate commission amount
        project_amount = validated_data['project_amount']
        commission_percentage = validated_data.get('commission_percentage', 12.50)
        commission_amount = (project_amount * commission_percentage) / 100
        validated_data['commission_amount'] = commission_amount
        
        # Set created_by to current user
        request = self.context.get('request')
        if request and request.user:
            validated_data['created_by'] = request.user
            
        return super().create(validated_data)


class CommissionStatsSerializer(serializers.Serializer):
    """Commission statistics serializer"""
    total_commissions = serializers.IntegerField()
    pending_commissions = serializers.IntegerField()
    approved_commissions = serializers.IntegerField()
    paid_commissions = serializers.IntegerField()
    total_commission_amount = serializers.DecimalField(max_digits=14, decimal_places=2)
    pending_commission_amount = serializers.DecimalField(max_digits=14, decimal_places=2)
    paid_commission_amount = serializers.DecimalField(max_digits=14, decimal_places=2)
    overdue_commissions = serializers.IntegerField()
    top_sales_reps = serializers.ListField()


class CommissionApprovalSerializer(serializers.Serializer):
    """Commission approval serializer"""
    commission_ids = serializers.ListField(
        child=serializers.IntegerField(),
        allow_empty=False
    )
    notes = serializers.CharField(required=False, allow_blank=True)


class CommissionPaymentCreateSerializer(serializers.ModelSerializer):
    """Commission payment creation serializer"""
    
    class Meta:
        model = CommissionPayment
        fields = [
            'commission_id', 'payment_method', 'payment_amount',
            'transaction_reference', 'bank_name', 'account_number',
            'payment_date', 'notes'
        ]

    def create(self, validated_data):
        """Create commission payment"""
        # Set paid_by to current user
        request = self.context.get('request')
        if request and request.user:
            validated_data['paid_by'] = request.user
            
        return super().create(validated_data)
