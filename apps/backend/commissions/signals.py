from django.db.models.signals import post_save
from django.dispatch import receiver
from projects.models import Project
from .models import Commission, CommissionRule
from decimal import Decimal


@receiver(post_save, sender=Project)
def create_commission_for_project(sender, instance, created, **kwargs):
    """
    Automatically create commission when a new project is created
    """
    if created and instance.budget and instance.client and instance.client.sales_rep:
        # Get the default commission rule or create one if it doesn't exist
        commission_rule, created_rule = CommissionRule.objects.get_or_create(
            name='Default Sales Commission',
            defaults={
                'rule_type': CommissionRule.RuleType.PERCENTAGE,
                'percentage': Decimal('12.50'),
                'description': 'Default 12.5% commission for new client projects',
                'is_active': True
            }
        )
        
        # Calculate commission amount
        commission_percentage = commission_rule.percentage
        commission_amount = (instance.budget * commission_percentage) / 100
        
        # Create commission record
        try:
            commission = Commission.objects.create(
                sales_rep=instance.client.sales_rep,
                client=instance.client,
                project=instance,
                commission_rule=commission_rule,
                project_amount=instance.budget,
                commission_percentage=commission_percentage,
                commission_amount=commission_amount,
                status=Commission.Status.PENDING,
                notes=f'عمولة تلقائية للمشروع الجديد: {instance.name}'
            )
            
            # Log the commission creation
            print(f"Commission created: {commission.commission_amount} for {commission.sales_rep.get_full_name()}")
            
        except Exception as e:
            # Log error but don't fail project creation
            print(f"Error creating commission for project {instance.name}: {str(e)}")


@receiver(post_save, sender=Project)
def update_commission_on_budget_change(sender, instance, created, **kwargs):
    """
    Update commission amount when project budget changes
    """
    if not created and instance.budget:
        # Find existing commission for this project
        try:
            commission = Commission.objects.get(project=instance)
            
            # Recalculate commission if budget changed
            old_amount = commission.commission_amount
            new_amount = (instance.budget * commission.commission_percentage) / 100
            
            if old_amount != new_amount:
                commission.project_amount = instance.budget
                commission.commission_amount = new_amount
                commission.notes = f"{commission.notes}\nتم تحديث العمولة بسبب تغيير الميزانية من {old_amount} إلى {new_amount}"
                commission.save(update_fields=['project_amount', 'commission_amount', 'notes'])
                
                print(f"Commission updated: {old_amount} -> {new_amount} for project {instance.name}")
                
        except Commission.DoesNotExist:
            # No commission exists, create one if conditions are met
            if instance.client and instance.client.sales_rep:
                create_commission_for_project(sender, instance, True, **kwargs)
        except Commission.MultipleObjectsReturned:
            # Multiple commissions exist (shouldn't happen due to unique constraint)
            print(f"Multiple commissions found for project {instance.name}")
