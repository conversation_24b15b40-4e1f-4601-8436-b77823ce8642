from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Count, Sum, Avg, Q
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>
from .models import Commission, CommissionRule, CommissionPayment
from .serializers import (
    CommissionSerializer,
    CommissionListSerializer,
    CommissionCreateSerializer,
    CommissionRuleSerializer,
    CommissionRuleListSerializer,
    CommissionPaymentSerializer,
    CommissionPaymentCreateSerializer,
    CommissionStatsSerializer,
    CommissionApprovalSerializer
)


class CommissionRuleViewSet(viewsets.ModelViewSet):
    """Commission rule management viewset"""
    queryset = CommissionRule.objects.all().order_by('-created_at')
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['rule_type', 'is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'percentage', 'created_at']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return CommissionRuleListSerializer
        return CommissionRuleSerializer

    def get_queryset(self):
        """Filter queryset based on user role"""
        queryset = super().get_queryset()
        user = self.request.user

        # Only admins and sales managers can manage commission rules
        if user.role not in ['admin', 'sales_manager']:
            return queryset.filter(is_active=True)

        return queryset


class CommissionViewSet(viewsets.ModelViewSet):
    """Commission management viewset"""
    queryset = Commission.objects.all().select_related(
        'sales_rep', 'client', 'project', 'commission_rule', 'created_by'
    ).prefetch_related('payment').order_by('-created_at')
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'sales_rep', 'client', 'project']
    search_fields = ['sales_rep__first_name', 'sales_rep__last_name', 'client__name', 'project__name']
    ordering_fields = ['earned_date', 'commission_amount', 'status']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return CommissionListSerializer
        elif self.action == 'create':
            return CommissionCreateSerializer
        return CommissionSerializer

    def get_queryset(self):
        """Filter queryset based on user role"""
        queryset = super().get_queryset()
        user = self.request.user

        # Sales reps can only see their own commissions
        if user.role not in ['admin', 'sales_manager']:
            return queryset.filter(sales_rep=user)

        return queryset

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get commission statistics"""
        queryset = self.get_queryset()

        # Basic counts
        total_commissions = queryset.count()
        pending_commissions = queryset.filter(status='pending').count()
        approved_commissions = queryset.filter(status='approved').count()
        paid_commissions = queryset.filter(status='paid').count()

        # Amount calculations
        total_amount = queryset.aggregate(
            total=Sum('commission_amount')
        )['total'] or 0

        pending_amount = queryset.filter(status='pending').aggregate(
            total=Sum('commission_amount')
        )['total'] or 0

        paid_amount = queryset.filter(status='paid').aggregate(
            total=Sum('commission_amount')
        )['total'] or 0

        # Overdue commissions (more than 30 days old and not paid)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        overdue_commissions = queryset.filter(
            earned_date__lt=thirty_days_ago,
            status__in=['pending', 'approved']
        ).count()

        # Top sales reps by commission amount
        top_sales_reps = queryset.values(
            'sales_rep__id',
            'sales_rep__first_name',
            'sales_rep__last_name'
        ).annotate(
            total_commission=Sum('commission_amount'),
            commission_count=Count('id')
        ).order_by('-total_commission')[:5]

        stats = {
            'total_commissions': total_commissions,
            'pending_commissions': pending_commissions,
            'approved_commissions': approved_commissions,
            'paid_commissions': paid_commissions,
            'total_commission_amount': total_amount,
            'pending_commission_amount': pending_amount,
            'paid_commission_amount': paid_amount,
            'overdue_commissions': overdue_commissions,
            'top_sales_reps': list(top_sales_reps)
        }

        serializer = CommissionStatsSerializer(stats)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def bulk_approve(self, request):
        """Bulk approve commissions"""
        serializer = CommissionApprovalSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        commission_ids = serializer.validated_data['commission_ids']
        notes = serializer.validated_data.get('notes', '')

        # Check permissions
        if request.user.role not in ['admin', 'sales_manager']:
            return Response(
                {'error': 'ليس لديك صلاحية للموافقة على العمولات'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get commissions to approve
        commissions = self.get_queryset().filter(
            id__in=commission_ids,
            status='pending'
        )

        if not commissions.exists():
            return Response(
                {'error': 'لم يتم العثور على عمولات صالحة للموافقة'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Approve commissions
        approved_count = 0
        for commission in commissions:
            commission.approve(request.user)
            approved_count += 1

        return Response({
            'message': f'تم الموافقة على {approved_count} عمولة بنجاح',
            'approved_count': approved_count
        })

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve a single commission"""
        commission = self.get_object()

        # Check permissions
        if request.user.role not in ['admin', 'sales_manager']:
            return Response(
                {'error': 'ليس لديك صلاحية للموافقة على العمولات'},
                status=status.HTTP_403_FORBIDDEN
            )

        if commission.status != 'pending':
            return Response(
                {'error': 'يمكن الموافقة على العمولات المعلقة فقط'},
                status=status.HTTP_400_BAD_REQUEST
            )

        commission.approve(request.user)

        return Response({
            'message': 'تم الموافقة على العمولة بنجاح',
            'commission': CommissionSerializer(commission).data
        })

    @action(detail=True, methods=['post'])
    def mark_paid(self, request, pk=None):
        """Mark commission as paid"""
        commission = self.get_object()

        # Check permissions
        if request.user.role not in ['admin', 'sales_manager']:
            return Response(
                {'error': 'ليس لديك صلاحية لتسجيل دفع العمولات'},
                status=status.HTTP_403_FORBIDDEN
            )

        if commission.status not in ['approved', 'pending']:
            return Response(
                {'error': 'يمكن دفع العمولات الموافق عليها أو المعلقة فقط'},
                status=status.HTTP_400_BAD_REQUEST
            )

        commission.mark_as_paid(request.user)

        return Response({
            'message': 'تم تسجيل دفع العمولة بنجاح',
            'commission': CommissionSerializer(commission).data
        })

    @action(detail=False, methods=['get'])
    def overdue(self, request):
        """Get overdue commissions"""
        thirty_days_ago = timezone.now() - timedelta(days=30)
        overdue_commissions = self.get_queryset().filter(
            earned_date__lt=thirty_days_ago,
            status__in=['pending', 'approved']
        )

        serializer = self.get_serializer(overdue_commissions, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def my_commissions(self, request):
        """Get current user's commissions"""
        user_commissions = self.get_queryset().filter(sales_rep=request.user)
        serializer = self.get_serializer(user_commissions, many=True)
        return Response(serializer.data)


class CommissionPaymentViewSet(viewsets.ModelViewSet):
    """Commission payment management viewset"""
    queryset = CommissionPayment.objects.all().select_related(
        'commission', 'paid_by'
    ).order_by('-created_at')
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['payment_status', 'payment_method']
    search_fields = ['commission__sales_rep__first_name', 'commission__sales_rep__last_name']
    ordering_fields = ['payment_date', 'payment_amount']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'create':
            return CommissionPaymentCreateSerializer
        return CommissionPaymentSerializer

    def get_queryset(self):
        """Filter queryset based on user role"""
        queryset = super().get_queryset()
        user = self.request.user

        # Sales reps can only see their own commission payments
        if user.role not in ['admin', 'sales_manager']:
            return queryset.filter(commission__sales_rep=user)

        return queryset
