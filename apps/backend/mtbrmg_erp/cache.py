"""
Redis caching utilities for MTBRMG ERP system
"""
from django.core.cache import cache
from django.conf import settings
import json
import hashlib
from typing import Any, Optional, Dict, List
from functools import wraps


class CacheService:
    """Service for managing Redis cache operations"""
    
    # Cache timeout constants (in seconds)
    TIMEOUT_SHORT = 300      # 5 minutes
    TIMEOUT_MEDIUM = 1800    # 30 minutes
    TIMEOUT_LONG = 3600      # 1 hour
    TIMEOUT_VERY_LONG = 86400  # 24 hours
    
    @staticmethod
    def generate_cache_key(prefix: str, *args, **kwargs) -> str:
        """Generate a consistent cache key"""
        key_data = {
            'args': args,
            'kwargs': kwargs
        }
        key_string = json.dumps(key_data, sort_keys=True)
        key_hash = hashlib.md5(key_string.encode()).hexdigest()
        return f"{prefix}:{key_hash}"
    
    @staticmethod
    def get(key: str) -> Any:
        """Get value from cache"""
        return cache.get(key)
    
    @staticmethod
    def set(key: str, value: Any, timeout: int = TIMEOUT_MEDIUM) -> None:
        """Set value in cache"""
        cache.set(key, value, timeout)
    
    @staticmethod
    def delete(key: str) -> None:
        """Delete key from cache"""
        cache.delete(key)
    
    @staticmethod
    def delete_pattern(pattern: str) -> None:
        """Delete all keys matching pattern"""
        # This requires django-redis backend
        try:
            cache.delete_pattern(pattern)
        except AttributeError:
            # Fallback for other cache backends
            pass
    
    @staticmethod
    def invalidate_user_cache(user_id: int) -> None:
        """Invalidate all cache entries for a specific user"""
        patterns = [
            f"clients_list_{user_id}_*",
            f"projects_list_{user_id}_*",
            f"tasks_list_{user_id}_*",
            f"user_profile_{user_id}",
            f"user_permissions_{user_id}",
        ]
        for pattern in patterns:
            CacheService.delete_pattern(pattern)
    
    @staticmethod
    def invalidate_client_cache(client_id: int) -> None:
        """Invalidate cache entries related to a specific client"""
        patterns = [
            f"client_{client_id}",
            f"client_projects_{client_id}",
            "clients_list_*",
            "dashboard_metrics_*",
        ]
        for pattern in patterns:
            CacheService.delete_pattern(pattern)
    
    @staticmethod
    def invalidate_project_cache(project_id: int) -> None:
        """Invalidate cache entries related to a specific project"""
        patterns = [
            f"project_{project_id}",
            f"project_tasks_{project_id}",
            "projects_list_*",
            "dashboard_metrics_*",
        ]
        for pattern in patterns:
            CacheService.delete_pattern(pattern)


def cache_result(timeout: int = CacheService.TIMEOUT_MEDIUM, key_prefix: str = None):
    """
    Decorator to cache function results
    
    Usage:
    @cache_result(timeout=3600, key_prefix='user_data')
    def get_user_data(user_id):
        return expensive_operation(user_id)
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            prefix = key_prefix or f"{func.__module__}.{func.__name__}"
            cache_key = CacheService.generate_cache_key(prefix, *args, **kwargs)
            
            # Try to get from cache
            result = CacheService.get(cache_key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            CacheService.set(cache_key, result, timeout)
            return result
        
        return wrapper
    return decorator


class DashboardCache:
    """Specialized caching for dashboard metrics"""
    
    @staticmethod
    def get_metrics_cache_key(user_id: int, filters: Dict = None) -> str:
        """Generate cache key for dashboard metrics"""
        return CacheService.generate_cache_key(
            "dashboard_metrics", 
            user_id, 
            filters or {}
        )
    
    @staticmethod
    def get_cached_metrics(user_id: int, filters: Dict = None) -> Optional[Dict]:
        """Get cached dashboard metrics"""
        cache_key = DashboardCache.get_metrics_cache_key(user_id, filters)
        return CacheService.get(cache_key)
    
    @staticmethod
    def cache_metrics(user_id: int, metrics: Dict, filters: Dict = None) -> None:
        """Cache dashboard metrics"""
        cache_key = DashboardCache.get_metrics_cache_key(user_id, filters)
        CacheService.set(cache_key, metrics, CacheService.TIMEOUT_SHORT)
    
    @staticmethod
    def invalidate_metrics_cache(user_id: int = None) -> None:
        """Invalidate dashboard metrics cache"""
        if user_id:
            pattern = f"dashboard_metrics_{user_id}_*"
        else:
            pattern = "dashboard_metrics_*"
        CacheService.delete_pattern(pattern)


class ListCache:
    """Specialized caching for list views"""
    
    @staticmethod
    def get_list_cache_key(model_name: str, user_id: int, params: Dict = None) -> str:
        """Generate cache key for list views"""
        return CacheService.generate_cache_key(
            f"{model_name}_list",
            user_id,
            params or {}
        )
    
    @staticmethod
    def get_cached_list(model_name: str, user_id: int, params: Dict = None) -> Optional[List]:
        """Get cached list data"""
        cache_key = ListCache.get_list_cache_key(model_name, user_id, params)
        return CacheService.get(cache_key)
    
    @staticmethod
    def cache_list(model_name: str, user_id: int, data: List, params: Dict = None) -> None:
        """Cache list data"""
        cache_key = ListCache.get_list_cache_key(model_name, user_id, params)
        CacheService.set(cache_key, data, CacheService.TIMEOUT_MEDIUM)
    
    @staticmethod
    def invalidate_list_cache(model_name: str, user_id: int = None) -> None:
        """Invalidate list cache"""
        if user_id:
            pattern = f"{model_name}_list_{user_id}_*"
        else:
            pattern = f"{model_name}_list_*"
        CacheService.delete_pattern(pattern)


# Cache invalidation signals
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver


@receiver(post_save, sender='clients.Client')
@receiver(post_delete, sender='clients.Client')
def invalidate_client_cache_on_change(sender, instance, **kwargs):
    """Invalidate client-related cache when client changes"""
    CacheService.invalidate_client_cache(instance.id)
    DashboardCache.invalidate_metrics_cache()


@receiver(post_save, sender='projects.Project')
@receiver(post_delete, sender='projects.Project')
def invalidate_project_cache_on_change(sender, instance, **kwargs):
    """Invalidate project-related cache when project changes"""
    CacheService.invalidate_project_cache(instance.id)
    if hasattr(instance, 'client_id'):
        CacheService.invalidate_client_cache(instance.client_id)
    DashboardCache.invalidate_metrics_cache()


@receiver(post_save, sender='tasks.Task')
@receiver(post_delete, sender='tasks.Task')
def invalidate_task_cache_on_change(sender, instance, **kwargs):
    """Invalidate task-related cache when task changes"""
    patterns = [
        f"task_{instance.id}",
        "tasks_list_*",
        "dashboard_metrics_*",
    ]
    if hasattr(instance, 'project_id') and instance.project_id:
        patterns.append(f"project_tasks_{instance.project_id}")
        CacheService.invalidate_project_cache(instance.project_id)
    
    for pattern in patterns:
        CacheService.delete_pattern(pattern)
