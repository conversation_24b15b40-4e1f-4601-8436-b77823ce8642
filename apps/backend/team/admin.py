from django.contrib import admin
from .models import TeamMember, TeamPerformanceMetric


@admin.register(TeamMember)
class TeamMemberAdmin(admin.ModelAdmin):
    list_display = ['full_name', 'employee_id', 'department', 'position', 'status', 'hire_date', 'performance_score']
    list_filter = ['department', 'status', 'hire_date']
    search_fields = ['user__first_name', 'user__last_name', 'employee_id', 'position']
    readonly_fields = ['created_at', 'updated_at', 'productivity_score']
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('user', 'employee_id', 'department', 'position', 'status')
        }),
        ('معلومات مهنية', {
            'fields': ('hire_date', 'salary', 'skills', 'certifications')
        }),
        ('مقاييس الأداء', {
            'fields': ('performance_score', 'completed_projects', 'total_tasks_completed', 'productivity_score')
        }),
        ('معلومات الاتصال الطارئ', {
            'fields': ('emergency_contact_name', 'emergency_contact_phone')
        }),
        ('معلومات التتبع', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(TeamPerformanceMetric)
class TeamPerformanceMetricAdmin(admin.ModelAdmin):
    list_display = ['team_member', 'metric_type', 'period_start', 'period_end', 'tasks_completed', 'projects_completed']
    list_filter = ['metric_type', 'period_start', 'team_member__department']
    search_fields = ['team_member__user__first_name', 'team_member__user__last_name']
    readonly_fields = ['created_at', 'updated_at']
    fieldsets = (
        ('معلومات الفترة', {
            'fields': ('team_member', 'metric_type', 'period_start', 'period_end')
        }),
        ('بيانات الأداء', {
            'fields': ('tasks_completed', 'projects_completed', 'hours_worked', 'client_satisfaction', 'revenue_generated')
        }),
        ('معلومات التتبع', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
