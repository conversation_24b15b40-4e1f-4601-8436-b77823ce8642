from rest_framework import serializers
from .models import Team<PERSON>ember, TeamPerformanceMetric
from authentication.serializers import UserListSerializer


class TeamMemberSerializer(serializers.ModelSerializer):
    """Team member serializer with all fields"""
    user = UserListSerializer(read_only=True)
    user_id = serializers.IntegerField(write_only=True)
    full_name = serializers.CharField(read_only=True)
    productivity_score = serializers.IntegerField(read_only=True)
    department_display = serializers.CharField(source='get_department_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = TeamMember
        fields = [
            'id', 'user', 'user_id', 'employee_id', 'department', 'department_display',
            'position', 'status', 'status_display', 'hire_date', 'salary',
            'skills', 'certifications', 'performance_score', 'completed_projects',
            'total_tasks_completed', 'emergency_contact_name', 'emergency_contact_phone',
            'full_name', 'productivity_score', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class TeamMemberListSerializer(serializers.ModelSerializer):
    """Simplified team member serializer for list views"""
    user = UserListSerializer(read_only=True)
    full_name = serializers.CharField(read_only=True)
    productivity_score = serializers.IntegerField(read_only=True)
    department_display = serializers.CharField(source='get_department_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = TeamMember
        fields = [
            'id', 'user', 'employee_id', 'department', 'department_display',
            'position', 'status', 'status_display', 'hire_date',
            'completed_projects', 'total_tasks_completed', 'performance_score',
            'full_name', 'productivity_score'
        ]


class TeamMemberCreateSerializer(serializers.ModelSerializer):
    """Team member creation serializer"""
    
    class Meta:
        model = TeamMember
        fields = [
            'user', 'employee_id', 'department', 'position', 'status',
            'hire_date', 'salary', 'skills', 'certifications',
            'emergency_contact_name', 'emergency_contact_phone'
        ]
    
    def validate_employee_id(self, value):
        """Validate employee ID is unique"""
        if TeamMember.objects.filter(employee_id=value).exists():
            raise serializers.ValidationError("رقم الموظف موجود بالفعل")
        return value
    
    def validate_user(self, value):
        """Validate user doesn't already have a team member profile"""
        if TeamMember.objects.filter(user=value).exists():
            raise serializers.ValidationError("هذا المستخدم لديه ملف شخصي في الفريق بالفعل")
        return value


class TeamMemberDetailSerializer(serializers.ModelSerializer):
    """Detailed team member serializer with performance metrics"""
    user = UserListSerializer(read_only=True)
    full_name = serializers.CharField(read_only=True)
    productivity_score = serializers.IntegerField(read_only=True)
    department_display = serializers.CharField(source='get_department_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    performance_metrics = serializers.SerializerMethodField()
    
    class Meta:
        model = TeamMember
        fields = [
            'id', 'user', 'employee_id', 'department', 'department_display',
            'position', 'status', 'status_display', 'hire_date', 'salary',
            'skills', 'certifications', 'performance_score', 'completed_projects',
            'total_tasks_completed', 'emergency_contact_name', 'emergency_contact_phone',
            'full_name', 'productivity_score', 'performance_metrics',
            'created_at', 'updated_at'
        ]
    
    def get_performance_metrics(self, obj):
        """Get recent performance metrics"""
        recent_metrics = obj.performance_metrics.all()[:3]
        return TeamPerformanceMetricSerializer(recent_metrics, many=True).data


class TeamPerformanceMetricSerializer(serializers.ModelSerializer):
    """Performance metric serializer"""
    team_member_name = serializers.CharField(source='team_member.full_name', read_only=True)
    metric_type_display = serializers.CharField(source='get_metric_type_display', read_only=True)
    
    class Meta:
        model = TeamPerformanceMetric
        fields = [
            'id', 'team_member', 'team_member_name', 'metric_type', 'metric_type_display',
            'period_start', 'period_end', 'tasks_completed', 'projects_completed',
            'hours_worked', 'client_satisfaction', 'revenue_generated',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class TeamPerformanceMetricCreateSerializer(serializers.ModelSerializer):
    """Performance metric creation serializer"""
    
    class Meta:
        model = TeamPerformanceMetric
        fields = [
            'team_member', 'metric_type', 'period_start', 'period_end',
            'tasks_completed', 'projects_completed', 'hours_worked',
            'client_satisfaction', 'revenue_generated'
        ]
    
    def validate(self, data):
        """Validate period dates and uniqueness"""
        if data['period_start'] >= data['period_end']:
            raise serializers.ValidationError("تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
        
        # Check for overlapping periods
        existing = TeamPerformanceMetric.objects.filter(
            team_member=data['team_member'],
            metric_type=data['metric_type'],
            period_start=data['period_start']
        )
        if existing.exists():
            raise serializers.ValidationError("يوجد مقياس أداء لنفس الفترة والنوع")
        
        return data


class TeamStatsSerializer(serializers.Serializer):
    """Team statistics serializer"""
    total_members = serializers.IntegerField()
    active_members = serializers.IntegerField()
    inactive_members = serializers.IntegerField()
    on_leave_members = serializers.IntegerField()
    department_distribution = serializers.DictField()
    average_performance_score = serializers.DecimalField(max_digits=3, decimal_places=1)
    total_completed_projects = serializers.IntegerField()
    total_completed_tasks = serializers.IntegerField()
    top_performers = serializers.ListField()


class TeamMemberUpdateSerializer(serializers.ModelSerializer):
    """Team member update serializer"""
    
    class Meta:
        model = TeamMember
        fields = [
            'department', 'position', 'status', 'salary', 'skills',
            'certifications', 'performance_score', 'completed_projects',
            'total_tasks_completed', 'emergency_contact_name', 'emergency_contact_phone'
        ]
    
    def validate_employee_id(self, value):
        """Validate employee ID is unique (excluding current instance)"""
        instance = getattr(self, 'instance', None)
        if instance and TeamMember.objects.filter(employee_id=value).exclude(pk=instance.pk).exists():
            raise serializers.ValidationError("رقم الموظف موجود بالفعل")
        return value
