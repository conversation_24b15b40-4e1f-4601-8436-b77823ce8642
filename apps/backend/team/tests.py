from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from .models import TeamMember, TeamPerformanceMetric
from datetime import date, timedelta

User = get_user_model()


class TeamMemberModelTest(TestCase):
    """Test cases for TeamMember model"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            first_name='Test',
            last_name='User',
            password='testpass123'
        )

    def test_team_member_creation(self):
        """Test creating a team member"""
        team_member = TeamMember.objects.create(
            user=self.user,
            employee_id='EMP001',
            department='development',
            position='مطور ويب',
            hire_date=date.today(),
            skills=['Python', 'Django', 'React']
        )

        self.assertEqual(team_member.user, self.user)
        self.assertEqual(team_member.employee_id, 'EMP001')
        self.assertEqual(team_member.department, 'development')
        self.assertEqual(team_member.full_name, 'Test User')
        self.assertEqual(team_member.productivity_score, 0)  # No projects/tasks yet

    def test_team_member_str_representation(self):
        """Test string representation of team member"""
        team_member = TeamMember.objects.create(
            user=self.user,
            employee_id='EMP001',
            department='development',
            position='مطور ويب',
            hire_date=date.today()
        )

        expected_str = f"{self.user.get_full_name()} - التطوير"
        self.assertEqual(str(team_member), expected_str)

    def test_productivity_score_calculation(self):
        """Test productivity score calculation"""
        team_member = TeamMember.objects.create(
            user=self.user,
            employee_id='EMP001',
            department='development',
            position='مطور ويب',
            hire_date=date.today(),
            completed_projects=5,
            total_tasks_completed=20
        )

        # Expected: (5 * 10) + (20 * 2) = 90
        self.assertEqual(team_member.productivity_score, 90)

    def test_productivity_score_max_limit(self):
        """Test productivity score doesn't exceed 100"""
        team_member = TeamMember.objects.create(
            user=self.user,
            employee_id='EMP001',
            department='development',
            position='مطور ويب',
            hire_date=date.today(),
            completed_projects=20,
            total_tasks_completed=100
        )

        # Should be capped at 100
        self.assertEqual(team_member.productivity_score, 100)
