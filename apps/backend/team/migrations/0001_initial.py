# Generated by Django 4.2.9 on 2025-06-02 18:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import simple_history.models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="TeamMember",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "employee_id",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="رقم الموظف"
                    ),
                ),
                (
                    "department",
                    models.CharField(
                        choices=[
                            ("sales", "المبيعات"),
                            ("media_buying", "شراء الإعلانات"),
                            ("development", "التطوير"),
                            ("design", "التصميم"),
                            ("wordpress", "ووردبريس"),
                            ("management", "الإدارة"),
                        ],
                        max_length=20,
                        verbose_name="القسم",
                    ),
                ),
                ("position", models.CharField(max_length=100, verbose_name="المنصب")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "نشط"),
                            ("inactive", "غير نشط"),
                            ("on_leave", "في إجازة"),
                        ],
                        default="active",
                        max_length=10,
                        verbose_name="الحالة",
                    ),
                ),
                ("hire_date", models.DateField(verbose_name="تاريخ التوظيف")),
                (
                    "salary",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="الراتب",
                    ),
                ),
                (
                    "skills",
                    models.JSONField(blank=True, default=list, verbose_name="المهارات"),
                ),
                (
                    "certifications",
                    models.JSONField(blank=True, default=list, verbose_name="الشهادات"),
                ),
                (
                    "performance_score",
                    models.DecimalField(
                        blank=True,
                        decimal_places=1,
                        max_digits=3,
                        null=True,
                        verbose_name="درجة الأداء",
                    ),
                ),
                (
                    "completed_projects",
                    models.PositiveIntegerField(
                        default=0, verbose_name="المشاريع المكتملة"
                    ),
                ),
                (
                    "total_tasks_completed",
                    models.PositiveIntegerField(
                        default=0, verbose_name="إجمالي المهام المكتملة"
                    ),
                ),
                (
                    "emergency_contact_name",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="اسم جهة الاتصال الطارئ",
                    ),
                ),
                (
                    "emergency_contact_phone",
                    models.CharField(
                        blank=True,
                        max_length=20,
                        null=True,
                        verbose_name="هاتف جهة الاتصال الطارئ",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="team_member",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="المستخدم",
                    ),
                ),
            ],
            options={
                "verbose_name": "عضو فريق",
                "verbose_name_plural": "أعضاء الفريق",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="HistoricalTeamMember",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "employee_id",
                    models.CharField(
                        db_index=True, max_length=20, verbose_name="رقم الموظف"
                    ),
                ),
                (
                    "department",
                    models.CharField(
                        choices=[
                            ("sales", "المبيعات"),
                            ("media_buying", "شراء الإعلانات"),
                            ("development", "التطوير"),
                            ("design", "التصميم"),
                            ("wordpress", "ووردبريس"),
                            ("management", "الإدارة"),
                        ],
                        max_length=20,
                        verbose_name="القسم",
                    ),
                ),
                ("position", models.CharField(max_length=100, verbose_name="المنصب")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "نشط"),
                            ("inactive", "غير نشط"),
                            ("on_leave", "في إجازة"),
                        ],
                        default="active",
                        max_length=10,
                        verbose_name="الحالة",
                    ),
                ),
                ("hire_date", models.DateField(verbose_name="تاريخ التوظيف")),
                (
                    "salary",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=10,
                        null=True,
                        verbose_name="الراتب",
                    ),
                ),
                (
                    "skills",
                    models.JSONField(blank=True, default=list, verbose_name="المهارات"),
                ),
                (
                    "certifications",
                    models.JSONField(blank=True, default=list, verbose_name="الشهادات"),
                ),
                (
                    "performance_score",
                    models.DecimalField(
                        blank=True,
                        decimal_places=1,
                        max_digits=3,
                        null=True,
                        verbose_name="درجة الأداء",
                    ),
                ),
                (
                    "completed_projects",
                    models.PositiveIntegerField(
                        default=0, verbose_name="المشاريع المكتملة"
                    ),
                ),
                (
                    "total_tasks_completed",
                    models.PositiveIntegerField(
                        default=0, verbose_name="إجمالي المهام المكتملة"
                    ),
                ),
                (
                    "emergency_contact_name",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="اسم جهة الاتصال الطارئ",
                    ),
                ),
                (
                    "emergency_contact_phone",
                    models.CharField(
                        blank=True,
                        max_length=20,
                        null=True,
                        verbose_name="هاتف جهة الاتصال الطارئ",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ التحديث"
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="المستخدم",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical عضو فريق",
                "verbose_name_plural": "historical أعضاء الفريق",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="TeamPerformanceMetric",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "metric_type",
                    models.CharField(
                        choices=[
                            ("monthly", "شهري"),
                            ("quarterly", "ربع سنوي"),
                            ("yearly", "سنوي"),
                        ],
                        max_length=10,
                        verbose_name="نوع المقياس",
                    ),
                ),
                ("period_start", models.DateField(verbose_name="بداية الفترة")),
                ("period_end", models.DateField(verbose_name="نهاية الفترة")),
                (
                    "tasks_completed",
                    models.PositiveIntegerField(
                        default=0, verbose_name="المهام المكتملة"
                    ),
                ),
                (
                    "projects_completed",
                    models.PositiveIntegerField(
                        default=0, verbose_name="المشاريع المكتملة"
                    ),
                ),
                (
                    "hours_worked",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=6,
                        verbose_name="ساعات العمل",
                    ),
                ),
                (
                    "client_satisfaction",
                    models.DecimalField(
                        blank=True,
                        decimal_places=1,
                        max_digits=3,
                        null=True,
                        verbose_name="رضا العملاء",
                    ),
                ),
                (
                    "revenue_generated",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=12,
                        verbose_name="الإيرادات المحققة",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "team_member",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="performance_metrics",
                        to="team.teammember",
                        verbose_name="عضو الفريق",
                    ),
                ),
            ],
            options={
                "verbose_name": "مقياس أداء الفريق",
                "verbose_name_plural": "مقاييس أداء الفريق",
                "ordering": ["-period_start"],
                "unique_together": {("team_member", "metric_type", "period_start")},
            },
        ),
    ]
