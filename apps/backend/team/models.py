from django.db import models
from django.conf import settings
from simple_history.models import HistoricalRecords


class TeamMember(models.Model):
    """Team member model for managing agency team members"""

    class Department(models.TextChoices):
        SALES = 'sales', 'المبيعات'
        MEDIA_BUYING = 'media_buying', 'شراء الإعلانات'
        DEVELOPMENT = 'development', 'التطوير'
        DESIGN = 'design', 'التصميم'
        WORDPRESS = 'wordpress', 'ووردبريس'
        MANAGEMENT = 'management', 'الإدارة'

    class Status(models.TextChoices):
        ACTIVE = 'active', 'نشط'
        INACTIVE = 'inactive', 'غير نشط'
        ON_LEAVE = 'on_leave', 'في إجازة'

    # Basic Information
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='team_member',
        verbose_name='المستخدم'
    )
    employee_id = models.Char<PERSON>ield(
        max_length=20,
        unique=True,
        verbose_name='رقم الموظف'
    )
    department = models.CharField(
        max_length=20,
        choices=Department.choices,
        verbose_name='القسم'
    )
    position = models.CharField(
        max_length=100,
        verbose_name='المنصب'
    )
    status = models.CharField(
        max_length=10,
        choices=Status.choices,
        default=Status.ACTIVE,
        verbose_name='الحالة'
    )

    # Professional Information
    hire_date = models.DateField(verbose_name='تاريخ التوظيف')
    salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name='الراتب'
    )
    skills = models.JSONField(
        default=list,
        blank=True,
        verbose_name='المهارات'
    )
    certifications = models.JSONField(
        default=list,
        blank=True,
        verbose_name='الشهادات'
    )

    # Performance Metrics
    performance_score = models.DecimalField(
        max_digits=3,
        decimal_places=1,
        blank=True,
        null=True,
        verbose_name='درجة الأداء'
    )
    completed_projects = models.PositiveIntegerField(
        default=0,
        verbose_name='المشاريع المكتملة'
    )
    total_tasks_completed = models.PositiveIntegerField(
        default=0,
        verbose_name='إجمالي المهام المكتملة'
    )

    # Contact Information
    emergency_contact_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name='اسم جهة الاتصال الطارئ'
    )
    emergency_contact_phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name='هاتف جهة الاتصال الطارئ'
    )

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    # History tracking
    history = HistoricalRecords()

    class Meta:
        verbose_name = 'عضو فريق'
        verbose_name_plural = 'أعضاء الفريق'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.get_department_display()}"

    @property
    def full_name(self):
        return self.user.get_full_name() or self.user.username

    @property
    def productivity_score(self):
        """Calculate productivity score based on completed tasks and projects"""
        if self.completed_projects == 0 and self.total_tasks_completed == 0:
            return 0
        return min(100, (self.completed_projects * 10) + (self.total_tasks_completed * 2))


class TeamPerformanceMetric(models.Model):
    """Performance metrics tracking for team members"""

    class MetricType(models.TextChoices):
        MONTHLY = 'monthly', 'شهري'
        QUARTERLY = 'quarterly', 'ربع سنوي'
        YEARLY = 'yearly', 'سنوي'

    team_member = models.ForeignKey(
        TeamMember,
        on_delete=models.CASCADE,
        related_name='performance_metrics',
        verbose_name='عضو الفريق'
    )
    metric_type = models.CharField(
        max_length=10,
        choices=MetricType.choices,
        verbose_name='نوع المقياس'
    )
    period_start = models.DateField(verbose_name='بداية الفترة')
    period_end = models.DateField(verbose_name='نهاية الفترة')

    # Performance Data
    tasks_completed = models.PositiveIntegerField(default=0, verbose_name='المهام المكتملة')
    projects_completed = models.PositiveIntegerField(default=0, verbose_name='المشاريع المكتملة')
    hours_worked = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=0,
        verbose_name='ساعات العمل'
    )
    client_satisfaction = models.DecimalField(
        max_digits=3,
        decimal_places=1,
        blank=True,
        null=True,
        verbose_name='رضا العملاء'
    )
    revenue_generated = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        verbose_name='الإيرادات المحققة'
    )

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'مقياس أداء الفريق'
        verbose_name_plural = 'مقاييس أداء الفريق'
        unique_together = ['team_member', 'metric_type', 'period_start']
        ordering = ['-period_start']

    def __str__(self):
        return f"{self.team_member.full_name} - {self.get_metric_type_display()} ({self.period_start})"
