# Generated by Django 4.2.9 on 2025-06-04 21:17

from django.db import migrations
import djmoney.models.fields


class Migration(migrations.Migration):
    dependencies = [
        ("finance", "0002_financialreport"),
    ]

    operations = [
        migrations.AlterField(
            model_name="budget",
            name="allocated_amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="budget",
            name="remaining_amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="budget",
            name="spent_amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="budget",
            name="total_budget_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="budgetallocation",
            name="allocated_amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="budgetallocation",
            name="spent_amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="cashflowprojection",
            name="actual_expenses_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="cashflowprojection",
            name="actual_profit_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="cashflowprojection",
            name="actual_revenue_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="cashflowprojection",
            name="projected_expenses_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="cashflowprojection",
            name="projected_profit_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="cashflowprojection",
            name="projected_revenue_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="expense",
            name="amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="expense",
            name="net_amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="expense",
            name="tax_amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="historicalbudget",
            name="allocated_amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="historicalbudget",
            name="remaining_amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="historicalbudget",
            name="spent_amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="historicalbudget",
            name="total_budget_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="historicalexpense",
            name="amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="historicalexpense",
            name="net_amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="historicalexpense",
            name="tax_amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="historicalrevenuestream",
            name="amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="historicalrevenuestream",
            name="net_amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="historicalrevenuestream",
            name="tax_amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="revenuestream",
            name="amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="revenuestream",
            name="net_amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="revenuestream",
            name="tax_amount_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
    ]
