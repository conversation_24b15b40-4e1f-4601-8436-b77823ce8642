from django.core.management.base import BaseCommand
from quotations.models import ServiceCatalog


class Command(BaseCommand):
    help = 'Populate service catalog with Egyptian digital agency services'

    def handle(self, *args, **options):
        services_data = [
            # Web Development Services
            {
                'name_ar': 'تطوير موقع ووردبريس أساسي',
                'name_en': 'Basic WordPress Website',
                'category': 'web_development',
                'base_price': 3000,
                'unit': 'project',
                'description_ar': 'تطوير موقع ووردبريس أساسي مع تصميم جاهز وإعداد المحتوى الأساسي',
                'description_en': 'Basic WordPress website with ready-made design and basic content setup',
                'estimated_hours_min': 20,
                'estimated_hours_max': 40
            },
            {
                'name_ar': 'تطوير موقع ووردبريس متقدم',
                'name_en': 'Advanced WordPress Website',
                'category': 'web_development',
                'base_price': 8000,
                'unit': 'project',
                'description_ar': 'تطوير موقع ووردبريس متقدم مع تصميم مخصص وميزات إضافية',
                'description_en': 'Advanced WordPress website with custom design and additional features',
                'estimated_hours_min': 60,
                'estimated_hours_max': 120
            },
            {
                'name_ar': 'تطوير متجر إلكتروني WooCommerce',
                'name_en': 'WooCommerce E-commerce Store',
                'category': 'web_development',
                'base_price': 12000,
                'unit': 'project',
                'description_ar': 'تطوير متجر إلكتروني متكامل باستخدام WooCommerce مع بوابات الدفع المصرية',
                'description_en': 'Complete e-commerce store using WooCommerce with Egyptian payment gateways',
                'estimated_hours_min': 80,
                'estimated_hours_max': 160
            },
            {
                'name_ar': 'تطوير موقع React.js',
                'name_en': 'React.js Website Development',
                'category': 'web_development',
                'base_price': 15000,
                'unit': 'project',
                'description_ar': 'تطوير موقع ديناميكي باستخدام React.js مع واجهة مستخدم حديثة',
                'description_en': 'Dynamic website development using React.js with modern UI',
                'estimated_hours_min': 100,
                'estimated_hours_max': 200
            },
            
            # Mobile Development Services
            {
                'name_ar': 'تطوير تطبيق أندرويد أساسي',
                'name_en': 'Basic Android App Development',
                'category': 'mobile_development',
                'base_price': 10000,
                'unit': 'project',
                'description_ar': 'تطوير تطبيق أندرويد أساسي مع الميزات الأساسية',
                'description_en': 'Basic Android app development with essential features',
                'estimated_hours_min': 80,
                'estimated_hours_max': 150
            },
            {
                'name_ar': 'تطوير تطبيق iOS أساسي',
                'name_en': 'Basic iOS App Development',
                'category': 'mobile_development',
                'base_price': 12000,
                'unit': 'project',
                'description_ar': 'تطوير تطبيق iOS أساسي مع الميزات الأساسية',
                'description_en': 'Basic iOS app development with essential features',
                'estimated_hours_min': 80,
                'estimated_hours_max': 150
            },
            {
                'name_ar': 'تطوير تطبيق متعدد المنصات React Native',
                'name_en': 'Cross-platform React Native App',
                'category': 'mobile_development',
                'base_price': 18000,
                'unit': 'project',
                'description_ar': 'تطوير تطبيق متعدد المنصات باستخدام React Native',
                'description_en': 'Cross-platform app development using React Native',
                'estimated_hours_min': 120,
                'estimated_hours_max': 250
            },
            
            # Digital Marketing Services
            {
                'name_ar': 'إدارة حسابات التواصل الاجتماعي',
                'name_en': 'Social Media Management',
                'category': 'digital_marketing',
                'base_price': 2000,
                'unit': 'month',
                'description_ar': 'إدارة شاملة لحسابات التواصل الاجتماعي مع إنشاء المحتوى',
                'description_en': 'Comprehensive social media management with content creation',
                'estimated_hours_min': 20,
                'estimated_hours_max': 40
            },
            {
                'name_ar': 'حملة إعلانية على فيسبوك وإنستجرام',
                'name_en': 'Facebook & Instagram Ad Campaign',
                'category': 'digital_marketing',
                'base_price': 1500,
                'unit': 'month',
                'description_ar': 'إنشاء وإدارة حملات إعلانية مدفوعة على فيسبوك وإنستجرام',
                'description_en': 'Create and manage paid advertising campaigns on Facebook and Instagram',
                'estimated_hours_min': 15,
                'estimated_hours_max': 30
            },
            {
                'name_ar': 'حملة إعلانية على جوجل',
                'name_en': 'Google Ads Campaign',
                'category': 'digital_marketing',
                'base_price': 2500,
                'unit': 'month',
                'description_ar': 'إنشاء وإدارة حملات إعلانية على جوجل أدز',
                'description_en': 'Create and manage Google Ads campaigns',
                'estimated_hours_min': 20,
                'estimated_hours_max': 35
            },
            
            # SEO Services
            {
                'name_ar': 'تحسين محركات البحث الأساسي',
                'name_en': 'Basic SEO Optimization',
                'category': 'seo',
                'base_price': 1200,
                'unit': 'month',
                'description_ar': 'تحسين أساسي لمحركات البحث مع تحليل الكلمات المفتاحية',
                'description_en': 'Basic SEO optimization with keyword analysis',
                'estimated_hours_min': 10,
                'estimated_hours_max': 20
            },
            {
                'name_ar': 'تحسين محركات البحث المتقدم',
                'name_en': 'Advanced SEO Optimization',
                'category': 'seo',
                'base_price': 2500,
                'unit': 'month',
                'description_ar': 'تحسين متقدم لمحركات البحث مع بناء الروابط والتحليل التفصيلي',
                'description_en': 'Advanced SEO optimization with link building and detailed analysis',
                'estimated_hours_min': 25,
                'estimated_hours_max': 45
            },
            
            # Branding Services
            {
                'name_ar': 'تصميم شعار احترافي',
                'name_en': 'Professional Logo Design',
                'category': 'branding',
                'base_price': 800,
                'unit': 'project',
                'description_ar': 'تصميم شعار احترافي مع عدة مفاهيم وتعديلات',
                'description_en': 'Professional logo design with multiple concepts and revisions',
                'estimated_hours_min': 8,
                'estimated_hours_max': 16
            },
            {
                'name_ar': 'تصميم هوية بصرية متكاملة',
                'name_en': 'Complete Brand Identity Design',
                'category': 'branding',
                'base_price': 3500,
                'unit': 'project',
                'description_ar': 'تصميم هوية بصرية متكاملة تشمل الشعار والألوان والخطوط',
                'description_en': 'Complete brand identity design including logo, colors, and fonts',
                'estimated_hours_min': 30,
                'estimated_hours_max': 60
            },
            
            # Design Services
            {
                'name_ar': 'تصميم واجهة مستخدم UI',
                'name_en': 'UI Design',
                'category': 'design',
                'base_price': 150,
                'unit': 'page',
                'description_ar': 'تصميم واجهة مستخدم احترافية للمواقع والتطبيقات',
                'description_en': 'Professional UI design for websites and applications',
                'estimated_hours_min': 4,
                'estimated_hours_max': 8
            },
            {
                'name_ar': 'تصميم تجربة مستخدم UX',
                'name_en': 'UX Design',
                'category': 'design',
                'base_price': 200,
                'unit': 'page',
                'description_ar': 'تصميم تجربة مستخدم شاملة مع دراسة سلوك المستخدمين',
                'description_en': 'Comprehensive UX design with user behavior analysis',
                'estimated_hours_min': 6,
                'estimated_hours_max': 12
            },
            
            # Maintenance Services
            {
                'name_ar': 'صيانة موقع شهرية',
                'name_en': 'Monthly Website Maintenance',
                'category': 'maintenance',
                'base_price': 500,
                'unit': 'month',
                'description_ar': 'صيانة شهرية للموقع تشمل التحديثات والنسخ الاحتياطية',
                'description_en': 'Monthly website maintenance including updates and backups',
                'estimated_hours_min': 4,
                'estimated_hours_max': 8
            },
            {
                'name_ar': 'دعم فني متقدم',
                'name_en': 'Advanced Technical Support',
                'category': 'maintenance',
                'base_price': 100,
                'unit': 'hour',
                'description_ar': 'دعم فني متقدم لحل المشاكل التقنية',
                'description_en': 'Advanced technical support for solving technical issues',
                'estimated_hours_min': 1,
                'estimated_hours_max': 1
            },
            
            # Hosting Services
            {
                'name_ar': 'استضافة مشتركة',
                'name_en': 'Shared Hosting',
                'category': 'hosting',
                'base_price': 300,
                'unit': 'year',
                'description_ar': 'استضافة مشتركة مناسبة للمواقع الصغيرة والمتوسطة',
                'description_en': 'Shared hosting suitable for small to medium websites',
                'estimated_hours_min': 2,
                'estimated_hours_max': 4
            },
            {
                'name_ar': 'استضافة VPS',
                'name_en': 'VPS Hosting',
                'category': 'hosting',
                'base_price': 1200,
                'unit': 'year',
                'description_ar': 'استضافة VPS للمواقع عالية الأداء',
                'description_en': 'VPS hosting for high-performance websites',
                'estimated_hours_min': 4,
                'estimated_hours_max': 8
            }
        ]

        created_count = 0
        for service_data in services_data:
            service, created = ServiceCatalog.objects.get_or_create(
                name_ar=service_data['name_ar'],
                defaults=service_data
            )
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'تم إنشاء الخدمة: {service.name_ar}')
                )

        self.stdout.write(
            self.style.SUCCESS(f'تم إنشاء {created_count} خدمة جديدة بنجاح!')
        )
