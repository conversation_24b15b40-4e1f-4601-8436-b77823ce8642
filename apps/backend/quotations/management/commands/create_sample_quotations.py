from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from quotations.models import Quotation, QuotationItem, ServiceCatalog
from clients.models import Client
from datetime import datetime, timedelta
import random

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample quotations for testing'

    def handle(self, *args, **options):
        # Get or create a user for sales rep
        user, created = User.objects.get_or_create(
            username='sales_rep',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'أحمد',
                'last_name': 'محمد',
                'role': 'admin'
            }
        )
        if created:
            user.set_password('password123')
            user.save()
            self.stdout.write(f'تم إنشاء مستخدم: {user.username}')

        # Get or create sample clients
        clients_data = [
            {
                'name': 'شركة التقنية المتقدمة',
                'email': '<EMAIL>',
                'phone': '01234567890',
                'company': 'شركة التقنية المتقدمة',
                'governorate': 'cairo'
            },
            {
                'name': 'محمد أحمد',
                'email': '<EMAIL>',
                'phone': '01098765432',
                'company': 'مؤسسة النجاح',
                'governorate': 'giza'
            },
            {
                'name': 'فاطمة علي',
                'email': '<EMAIL>',
                'phone': '01555666777',
                'company': 'ستارت أب الإبداع',
                'governorate': 'alexandria'
            }
        ]

        clients = []
        for client_data in clients_data:
            client, created = Client.objects.get_or_create(
                email=client_data['email'],
                defaults=client_data
            )
            clients.append(client)
            if created:
                self.stdout.write(f'تم إنشاء عميل: {client.name}')

        # Get services
        services = list(ServiceCatalog.objects.filter(is_active=True))
        if not services:
            self.stdout.write(self.style.ERROR('لا توجد خدمات متاحة. يرجى تشغيل populate_services أولاً'))
            return

        # Sample quotations data
        quotations_data = [
            {
                'title': 'تطوير موقع إلكتروني متكامل',
                'description': 'تطوير موقع إلكتروني احترافي للشركة مع نظام إدارة المحتوى',
                'priority': 'high',
                'status': 'pending',
                'services': ['تطوير موقع ووردبريس متقدم', 'تصميم هوية بصرية متكاملة', 'تحسين محركات البحث الأساسي']
            },
            {
                'title': 'حملة تسويقية شاملة',
                'description': 'حملة تسويقية متكاملة على منصات التواصل الاجتماعي',
                'priority': 'medium',
                'status': 'draft',
                'services': ['إدارة حسابات التواصل الاجتماعي', 'حملة إعلانية على فيسبوك وإنستجرام', 'تصميم شعار احترافي']
            },
            {
                'title': 'تطوير تطبيق جوال',
                'description': 'تطوير تطبيق جوال متعدد المنصات للتجارة الإلكترونية',
                'priority': 'urgent',
                'status': 'approved',
                'services': ['تطوير تطبيق متعدد المنصات React Native', 'تصميم واجهة مستخدم UI', 'تصميم تجربة مستخدم UX']
            },
            {
                'title': 'متجر إلكتروني متكامل',
                'description': 'إنشاء متجر إلكتروني متكامل مع بوابات الدفع المصرية',
                'priority': 'high',
                'status': 'converted',
                'services': ['تطوير متجر إلكتروني WooCommerce', 'استضافة VPS', 'صيانة موقع شهرية']
            },
            {
                'title': 'خدمات الاستضافة والصيانة',
                'description': 'باقة استضافة وصيانة سنوية للموقع الإلكتروني',
                'priority': 'low',
                'status': 'rejected',
                'services': ['استضافة مشتركة', 'صيانة موقع شهرية', 'دعم فني متقدم']
            }
        ]

        created_count = 0
        for i, quotation_data in enumerate(quotations_data):
            # Create quotation
            quotation = Quotation.objects.create(
                title=quotation_data['title'],
                description=quotation_data['description'],
                client=clients[i % len(clients)],
                sales_rep=user,
                priority=quotation_data['priority'],
                status=quotation_data['status'],
                tax_percentage=14,
                valid_until=datetime.now().date() + timedelta(days=30),
                notes=f'عرض تجريبي رقم {i + 1}',
                terms_conditions='الشروط والأحكام العامة للشركة تطبق على هذا العرض.'
            )

            # Add items to quotation
            for service_name in quotation_data['services']:
                service = services[0]  # Default service
                for s in services:
                    if service_name in s.name_ar:
                        service = s
                        break

                # Random quantity and complexity
                quantity = random.randint(1, 3)
                complexity_levels = ['light', 'medium', 'complex']
                complexity = random.choice(complexity_levels)
                
                # Adjust price based on complexity
                base_price = float(service.base_price.amount)
                complexity_multiplier = {'light': 1.0, 'medium': 1.5, 'complex': 2.0}
                unit_price = base_price * complexity_multiplier[complexity]

                QuotationItem.objects.create(
                    quotation=quotation,
                    service=service,
                    description=f'{service.description_ar} - مستوى {complexity}',
                    quantity=quantity,
                    unit_price=unit_price,
                    complexity_level=complexity,
                    estimated_hours=service.estimated_hours_min * complexity_multiplier[complexity]
                )

            # Calculate totals
            quotation.calculate_totals()
            
            # Set approval for approved quotations
            if quotation.status == 'approved':
                quotation.approved_at = datetime.now()
                quotation.approved_by = user
                quotation.save()

            created_count += 1
            self.stdout.write(
                self.style.SUCCESS(f'تم إنشاء العرض: {quotation.title} - {quotation.quotation_number}')
            )

        self.stdout.write(
            self.style.SUCCESS(f'تم إنشاء {created_count} عرض تجريبي بنجاح!')
        )
