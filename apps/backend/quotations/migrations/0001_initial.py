# Generated by Django 4.2.9 on 2025-06-05 02:05

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import djmoney.models.fields
import simple_history.models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("projects", "0003_alter_historicalproject_actual_cost_currency_and_more"),
        ("clients", "0003_alter_client_total_revenue_currency_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Quotation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "quotation_number",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="رقم العرض"
                    ),
                ),
                ("title", models.Char<PERSON>ield(max_length=200, verbose_name="عنوان العرض")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "مسودة"),
                            ("pending", "في الانتظار"),
                            ("approved", "موافق عليه"),
                            ("rejected", "مرفوض"),
                            ("expired", "منتهي الصلاحية"),
                            ("converted", "تم تحويله لمشروع"),
                        ],
                        default="draft",
                        max_length=10,
                        verbose_name="الحالة",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "منخفض"),
                            ("medium", "متوسط"),
                            ("high", "عالي"),
                            ("urgent", "عاجل"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="الأولوية",
                    ),
                ),
                (
                    "subtotal_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "subtotal",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="المجموع الفرعي",
                    ),
                ),
                (
                    "discount_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="نسبة الخصم",
                    ),
                ),
                (
                    "discount_amount_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "discount_amount",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="مبلغ الخصم",
                    ),
                ),
                (
                    "tax_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=14,
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="نسبة الضريبة",
                    ),
                ),
                (
                    "tax_amount_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "tax_amount",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="مبلغ الضريبة",
                    ),
                ),
                (
                    "total_amount_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "total_amount",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="المبلغ الإجمالي",
                    ),
                ),
                ("valid_until", models.DateField(verbose_name="صالح حتى")),
                (
                    "approved_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ الموافقة"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="ملاحظات"),
                ),
                (
                    "terms_conditions",
                    models.TextField(
                        blank=True, null=True, verbose_name="الشروط والأحكام"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_quotations",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="تمت الموافقة بواسطة",
                    ),
                ),
                (
                    "client",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="quotations",
                        to="clients.client",
                        verbose_name="العميل",
                    ),
                ),
                (
                    "converted_to_project",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="source_quotation",
                        to="projects.project",
                        verbose_name="تم تحويله إلى مشروع",
                    ),
                ),
                (
                    "sales_rep",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="quotations",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="مندوب المبيعات",
                    ),
                ),
            ],
            options={
                "verbose_name": "عرض سعر",
                "verbose_name_plural": "عروض الأسعار",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ServiceCatalog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name_ar",
                    models.CharField(max_length=200, verbose_name="الاسم بالعربية"),
                ),
                (
                    "name_en",
                    models.CharField(max_length=200, verbose_name="الاسم بالإنجليزية"),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("web_development", "تطوير المواقع"),
                            ("mobile_development", "تطوير التطبيقات"),
                            ("digital_marketing", "التسويق الرقمي"),
                            ("branding", "العلامة التجارية"),
                            ("seo", "تحسين محركات البحث"),
                            ("maintenance", "الصيانة والدعم"),
                            ("hosting", "الاستضافة"),
                            ("design", "التصميم"),
                        ],
                        max_length=20,
                        verbose_name="الفئة",
                    ),
                ),
                (
                    "base_price_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "base_price",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2, max_digits=14, verbose_name="السعر الأساسي"
                    ),
                ),
                (
                    "unit",
                    models.CharField(
                        choices=[
                            ("project", "مشروع"),
                            ("hour", "ساعة"),
                            ("month", "شهر"),
                            ("year", "سنة"),
                            ("page", "صفحة"),
                            ("feature", "ميزة"),
                        ],
                        default="project",
                        max_length=10,
                        verbose_name="الوحدة",
                    ),
                ),
                ("description_ar", models.TextField(verbose_name="الوصف بالعربية")),
                (
                    "description_en",
                    models.TextField(
                        blank=True, null=True, verbose_name="الوصف بالإنجليزية"
                    ),
                ),
                (
                    "estimated_hours_min",
                    models.PositiveIntegerField(
                        default=1, verbose_name="الحد الأدنى للساعات المقدرة"
                    ),
                ),
                (
                    "estimated_hours_max",
                    models.PositiveIntegerField(
                        default=8, verbose_name="الحد الأقصى للساعات المقدرة"
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="نشط")),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "خدمة",
                "verbose_name_plural": "كتالوج الخدمات",
                "ordering": ["category", "name_ar"],
                "indexes": [
                    models.Index(
                        fields=["category", "is_active"],
                        name="service_category_active_idx",
                    ),
                    models.Index(fields=["is_active"], name="service_active_idx"),
                ],
            },
        ),
        migrations.CreateModel(
            name="HistoricalQuotation",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "quotation_number",
                    models.CharField(
                        db_index=True, max_length=20, verbose_name="رقم العرض"
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="عنوان العرض")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "مسودة"),
                            ("pending", "في الانتظار"),
                            ("approved", "موافق عليه"),
                            ("rejected", "مرفوض"),
                            ("expired", "منتهي الصلاحية"),
                            ("converted", "تم تحويله لمشروع"),
                        ],
                        default="draft",
                        max_length=10,
                        verbose_name="الحالة",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "منخفض"),
                            ("medium", "متوسط"),
                            ("high", "عالي"),
                            ("urgent", "عاجل"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="الأولوية",
                    ),
                ),
                (
                    "subtotal_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "subtotal",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="المجموع الفرعي",
                    ),
                ),
                (
                    "discount_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="نسبة الخصم",
                    ),
                ),
                (
                    "discount_amount_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "discount_amount",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="مبلغ الخصم",
                    ),
                ),
                (
                    "tax_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=14,
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="نسبة الضريبة",
                    ),
                ),
                (
                    "tax_amount_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "tax_amount",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="مبلغ الضريبة",
                    ),
                ),
                (
                    "total_amount_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "total_amount",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        default=Decimal("0"),
                        max_digits=14,
                        verbose_name="المبلغ الإجمالي",
                    ),
                ),
                ("valid_until", models.DateField(verbose_name="صالح حتى")),
                (
                    "approved_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ الموافقة"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="ملاحظات"),
                ),
                (
                    "terms_conditions",
                    models.TextField(
                        blank=True, null=True, verbose_name="الشروط والأحكام"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ التحديث"
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="تمت الموافقة بواسطة",
                    ),
                ),
                (
                    "client",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="clients.client",
                        verbose_name="العميل",
                    ),
                ),
                (
                    "converted_to_project",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="projects.project",
                        verbose_name="تم تحويله إلى مشروع",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "sales_rep",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="مندوب المبيعات",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical عرض سعر",
                "verbose_name_plural": "historical عروض الأسعار",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="QuotationItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("description", models.TextField(verbose_name="الوصف")),
                (
                    "quantity",
                    models.PositiveIntegerField(default=1, verbose_name="الكمية"),
                ),
                (
                    "unit_price_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "unit_price",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2, max_digits=14, verbose_name="سعر الوحدة"
                    ),
                ),
                (
                    "total_price_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    "total_price",
                    djmoney.models.fields.MoneyField(
                        decimal_places=2, max_digits=14, verbose_name="السعر الإجمالي"
                    ),
                ),
                (
                    "complexity_level",
                    models.CharField(
                        choices=[
                            ("light", "بسيط"),
                            ("medium", "متوسط"),
                            ("complex", "معقد"),
                            ("enterprise", "مؤسسي"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="مستوى التعقيد",
                    ),
                ),
                (
                    "estimated_hours",
                    models.PositiveIntegerField(
                        default=8, verbose_name="الساعات المقدرة"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "quotation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="items",
                        to="quotations.quotation",
                        verbose_name="العرض",
                    ),
                ),
                (
                    "service",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="quotation_items",
                        to="quotations.servicecatalog",
                        verbose_name="الخدمة",
                    ),
                ),
            ],
            options={
                "verbose_name": "عنصر العرض",
                "verbose_name_plural": "عناصر العرض",
                "ordering": ["id"],
                "indexes": [
                    models.Index(
                        fields=["quotation"], name="quotation_item_quotation_idx"
                    ),
                    models.Index(fields=["service"], name="quotation_item_service_idx"),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="quotation",
            index=models.Index(
                fields=["status", "priority"], name="quotation_status_priority_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="quotation",
            index=models.Index(
                fields=["client", "status"], name="quotation_client_status_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="quotation",
            index=models.Index(
                fields=["sales_rep", "status"], name="quotation_sales_status_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="quotation",
            index=models.Index(fields=["valid_until"], name="quotation_validity_idx"),
        ),
        migrations.AddIndex(
            model_name="quotation",
            index=models.Index(
                fields=["quotation_number"], name="quotation_number_idx"
            ),
        ),
    ]
