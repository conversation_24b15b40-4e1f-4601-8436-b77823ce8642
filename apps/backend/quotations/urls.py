from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON>ult<PERSON><PERSON><PERSON>
from .views import ServiceCatalogViewSet, QuotationViewSet, QuotationItemViewSet

# Create router and register viewsets
router = DefaultRouter()
router.register(r'service-catalog', ServiceCatalogViewSet, basename='servicecatalog')
router.register(r'quotations', QuotationViewSet, basename='quotation')
router.register(r'quotation-items', QuotationItemViewSet, basename='quotationitem')

urlpatterns = [
    path('', include(router.urls)),
]
