from django.db import models
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from djmoney.models.fields import MoneyField
from simple_history.models import HistoricalRecords
import uuid
from datetime import datetime, timedelta


class ServiceCatalog(models.Model):
    """Service catalog for quotation items"""
    
    class Category(models.TextChoices):
        WEB_DEVELOPMENT = 'web_development', 'تطوير المواقع'
        MOBILE_DEVELOPMENT = 'mobile_development', 'تطوير التطبيقات'
        DIGITAL_MARKETING = 'digital_marketing', 'التسويق الرقمي'
        BRANDING = 'branding', 'العلامة التجارية'
        SEO = 'seo', 'تحسين محركات البحث'
        MAINTENANCE = 'maintenance', 'الصيانة والدعم'
        HOSTING = 'hosting', 'الاستضافة'
        DESIGN = 'design', 'التصميم'
    
    class Unit(models.TextChoices):
        PROJECT = 'project', 'مشروع'
        HOUR = 'hour', 'ساعة'
        MONTH = 'month', 'شهر'
        YEAR = 'year', 'سنة'
        PAGE = 'page', 'صفحة'
        FEATURE = 'feature', 'ميزة'
    
    # Basic Information
    name_ar = models.CharField(max_length=200, verbose_name='الاسم بالعربية')
    name_en = models.CharField(max_length=200, verbose_name='الاسم بالإنجليزية')
    category = models.CharField(
        max_length=20,
        choices=Category.choices,
        verbose_name='الفئة'
    )
    
    # Pricing
    base_price = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        verbose_name='السعر الأساسي'
    )
    unit = models.CharField(
        max_length=10,
        choices=Unit.choices,
        default=Unit.PROJECT,
        verbose_name='الوحدة'
    )
    
    # Description
    description_ar = models.TextField(verbose_name='الوصف بالعربية')
    description_en = models.TextField(blank=True, null=True, verbose_name='الوصف بالإنجليزية')
    
    # Time Estimation
    estimated_hours_min = models.PositiveIntegerField(
        default=1,
        verbose_name='الحد الأدنى للساعات المقدرة'
    )
    estimated_hours_max = models.PositiveIntegerField(
        default=8,
        verbose_name='الحد الأقصى للساعات المقدرة'
    )
    
    # Status
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    
    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    
    class Meta:
        verbose_name = 'خدمة'
        verbose_name_plural = 'كتالوج الخدمات'
        ordering = ['category', 'name_ar']
        indexes = [
            models.Index(fields=['category', 'is_active'], name='service_category_active_idx'),
            models.Index(fields=['is_active'], name='service_active_idx'),
        ]
    
    def __str__(self):
        return f"{self.name_ar} - {self.get_category_display()}"


class Quotation(models.Model):
    """Quotation model for managing client quotations"""
    
    class Status(models.TextChoices):
        DRAFT = 'draft', 'مسودة'
        PENDING = 'pending', 'في الانتظار'
        APPROVED = 'approved', 'موافق عليه'
        REJECTED = 'rejected', 'مرفوض'
        EXPIRED = 'expired', 'منتهي الصلاحية'
        CONVERTED = 'converted', 'تم تحويله لمشروع'
    
    class Priority(models.TextChoices):
        LOW = 'low', 'منخفض'
        MEDIUM = 'medium', 'متوسط'
        HIGH = 'high', 'عالي'
        URGENT = 'urgent', 'عاجل'
    
    # Basic Information
    quotation_number = models.CharField(
        max_length=20,
        unique=True,
        verbose_name='رقم العرض'
    )
    title = models.CharField(max_length=200, verbose_name='عنوان العرض')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    
    # Relationships
    client = models.ForeignKey(
        'clients.Client',
        on_delete=models.CASCADE,
        related_name='quotations',
        verbose_name='العميل'
    )
    sales_rep = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='quotations',
        verbose_name='مندوب المبيعات'
    )
    
    # Status and Priority
    status = models.CharField(
        max_length=10,
        choices=Status.choices,
        default=Status.DRAFT,
        verbose_name='الحالة'
    )
    priority = models.CharField(
        max_length=10,
        choices=Priority.choices,
        default=Priority.MEDIUM,
        verbose_name='الأولوية'
    )
    
    # Financial Information
    subtotal = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        default=0,
        verbose_name='المجموع الفرعي'
    )
    discount_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name='نسبة الخصم'
    )
    discount_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        default=0,
        verbose_name='مبلغ الخصم'
    )
    tax_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=14,  # Egyptian VAT
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name='نسبة الضريبة'
    )
    tax_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        default=0,
        verbose_name='مبلغ الضريبة'
    )
    total_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        default=0,
        verbose_name='المبلغ الإجمالي'
    )
    
    # Validity
    valid_until = models.DateField(verbose_name='صالح حتى')
    
    # Approval
    approved_at = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_quotations',
        verbose_name='تمت الموافقة بواسطة'
    )
    
    # Project Conversion
    converted_to_project = models.ForeignKey(
        'projects.Project',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='source_quotation',
        verbose_name='تم تحويله إلى مشروع'
    )
    
    # Additional Information
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    terms_conditions = models.TextField(blank=True, null=True, verbose_name='الشروط والأحكام')
    
    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    
    # History tracking
    history = HistoricalRecords()
    
    class Meta:
        verbose_name = 'عرض سعر'
        verbose_name_plural = 'عروض الأسعار'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'priority'], name='quotation_status_priority_idx'),
            models.Index(fields=['client', 'status'], name='quotation_client_status_idx'),
            models.Index(fields=['sales_rep', 'status'], name='quotation_sales_status_idx'),
            models.Index(fields=['valid_until'], name='quotation_validity_idx'),
            models.Index(fields=['quotation_number'], name='quotation_number_idx'),
        ]
    
    def __str__(self):
        return f"{self.quotation_number} - {self.client.name}"
    
    def save(self, *args, **kwargs):
        # Generate quotation number if not exists
        if not self.quotation_number:
            self.quotation_number = self.generate_quotation_number()
        
        # Set default validity (30 days from creation)
        if not self.valid_until:
            self.valid_until = (datetime.now().date() + timedelta(days=30))
        
        super().save(*args, **kwargs)
    
    def generate_quotation_number(self):
        """Generate unique quotation number"""
        year = datetime.now().year
        month = datetime.now().month
        
        # Get last quotation number for this month
        last_quotation = Quotation.objects.filter(
            created_at__year=year,
            created_at__month=month
        ).order_by('-id').first()
        
        if last_quotation and last_quotation.quotation_number:
            try:
                last_number = int(last_quotation.quotation_number.split('-')[-1])
                next_number = last_number + 1
            except (ValueError, IndexError):
                next_number = 1
        else:
            next_number = 1
        
        return f"QUO-{year}{month:02d}-{next_number:04d}"
    
    def calculate_totals(self):
        """Calculate quotation totals"""
        # Calculate subtotal from items
        self.subtotal = sum(item.total_price for item in self.items.all())
        
        # Calculate discount amount
        if self.discount_percentage > 0:
            self.discount_amount = self.subtotal * (self.discount_percentage / 100)
        else:
            self.discount_amount = 0
        
        # Calculate amount after discount
        amount_after_discount = self.subtotal - self.discount_amount
        
        # Calculate tax amount
        if self.tax_percentage > 0:
            self.tax_amount = amount_after_discount * (self.tax_percentage / 100)
        else:
            self.tax_amount = 0
        
        # Calculate total amount
        self.total_amount = amount_after_discount + self.tax_amount
        
        self.save(update_fields=['subtotal', 'discount_amount', 'tax_amount', 'total_amount'])
    
    @property
    def is_expired(self):
        """Check if quotation is expired"""
        return self.valid_until < datetime.now().date()
    
    @property
    def days_until_expiry(self):
        """Get days until expiry"""
        if self.is_expired:
            return 0
        return (self.valid_until - datetime.now().date()).days


class QuotationItem(models.Model):
    """Individual items in a quotation"""
    
    class ComplexityLevel(models.TextChoices):
        LIGHT = 'light', 'بسيط'
        MEDIUM = 'medium', 'متوسط'
        COMPLEX = 'complex', 'معقد'
        ENTERPRISE = 'enterprise', 'مؤسسي'
    
    # Relationships
    quotation = models.ForeignKey(
        Quotation,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name='العرض'
    )
    service = models.ForeignKey(
        ServiceCatalog,
        on_delete=models.CASCADE,
        related_name='quotation_items',
        verbose_name='الخدمة'
    )
    
    # Item Details
    description = models.TextField(verbose_name='الوصف')
    quantity = models.PositiveIntegerField(default=1, verbose_name='الكمية')
    unit_price = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        verbose_name='سعر الوحدة'
    )
    total_price = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        verbose_name='السعر الإجمالي'
    )
    
    # Complexity and Estimation
    complexity_level = models.CharField(
        max_length=10,
        choices=ComplexityLevel.choices,
        default=ComplexityLevel.MEDIUM,
        verbose_name='مستوى التعقيد'
    )
    estimated_hours = models.PositiveIntegerField(
        default=8,
        verbose_name='الساعات المقدرة'
    )
    
    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    class Meta:
        verbose_name = 'عنصر العرض'
        verbose_name_plural = 'عناصر العرض'
        ordering = ['id']
        indexes = [
            models.Index(fields=['quotation'], name='quotation_item_quotation_idx'),
            models.Index(fields=['service'], name='quotation_item_service_idx'),
        ]
    
    def __str__(self):
        return f"{self.quotation.quotation_number} - {self.service.name_ar}"
    
    def save(self, *args, **kwargs):
        # Calculate total price
        self.total_price = self.unit_price * self.quantity
        super().save(*args, **kwargs)
        
        # Update quotation totals
        self.quotation.calculate_totals()
