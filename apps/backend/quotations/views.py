from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Count, Sum, Avg, Q
from django.utils import timezone
from datetime import datetime, timedelta
from .models import ServiceCatalog, Quotation, QuotationItem
from .serializers import (
    ServiceCatalogSerializer,
    ServiceCatalogListSerializer,
    QuotationSerializer,
    QuotationListSerializer,
    QuotationCreateSerializer,
    QuotationUpdateSerializer,
    QuotationApprovalSerializer,
    QuotationStatsSerializer,
    QuotationItemSerializer,
    QuotationItemCreateSerializer
)


class ServiceCatalogViewSet(viewsets.ModelViewSet):
    """Service catalog management viewset"""
    queryset = ServiceCatalog.objects.all().order_by('category', 'name_ar')
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category', 'unit', 'is_active']
    search_fields = ['name_ar', 'name_en', 'description_ar']
    ordering_fields = ['name_ar', 'category', 'base_price', 'created_at']
    ordering = ['category', 'name_ar']
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ServiceCatalogListSerializer
        return ServiceCatalogSerializer
    
    def get_queryset(self):
        """Filter active services for non-admin users"""
        queryset = super().get_queryset()
        if self.action == 'list' and not self.request.user.is_staff:
            queryset = queryset.filter(is_active=True)
        return queryset
    
    @action(detail=False, methods=['get'])
    def categories(self, request):
        """Get available service categories"""
        categories = ServiceCatalog.Category.choices
        return Response([
            {'value': choice[0], 'label': choice[1]}
            for choice in categories
        ])
    
    @action(detail=False, methods=['get'])
    def by_category(self, request):
        """Get services grouped by category"""
        category = request.query_params.get('category')
        if not category:
            return Response({'error': 'يجب تحديد الفئة'}, status=status.HTTP_400_BAD_REQUEST)
        
        services = self.get_queryset().filter(category=category, is_active=True)
        serializer = ServiceCatalogListSerializer(services, many=True)
        return Response(serializer.data)


class QuotationViewSet(viewsets.ModelViewSet):
    """Quotation management viewset"""
    queryset = Quotation.objects.all().select_related(
        'client', 'sales_rep', 'approved_by', 'converted_to_project'
    ).prefetch_related('items__service').order_by('-created_at')
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'priority', 'client', 'sales_rep']
    search_fields = ['quotation_number', 'title', 'client__name', 'client__email']
    ordering_fields = ['quotation_number', 'created_at', 'valid_until', 'total_amount']
    ordering = ['-created_at']
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return QuotationListSerializer
        elif self.action == 'create':
            return QuotationCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return QuotationUpdateSerializer
        return QuotationSerializer
    
    def get_queryset(self):
        """Filter quotations based on user role"""
        queryset = super().get_queryset()
        
        # Non-admin users see only their quotations
        if not self.request.user.is_staff:
            queryset = queryset.filter(sales_rep=self.request.user)
        
        return queryset
    
    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve or reject quotation"""
        quotation = self.get_object()
        serializer = QuotationApprovalSerializer(
            data=request.data,
            context={'quotation': quotation}
        )
        
        if serializer.is_valid():
            action = serializer.validated_data['action']
            notes = serializer.validated_data.get('notes', '')
            
            if action == 'approve':
                quotation.status = 'approved'
                quotation.approved_at = timezone.now()
                quotation.approved_by = request.user
                message = 'تم الموافقة على العرض بنجاح'
            else:
                quotation.status = 'rejected'
                message = 'تم رفض العرض'
            
            if notes:
                quotation.notes = f"{quotation.notes or ''}\n\nملاحظات الموافقة: {notes}"
            
            quotation.save()
            
            return Response({
                'message': message,
                'quotation': QuotationSerializer(quotation).data
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def convert_to_project(self, request, pk=None):
        """Convert approved quotation to project"""
        quotation = self.get_object()
        
        if quotation.status != 'approved':
            return Response(
                {'error': 'يمكن تحويل العروض الموافق عليها فقط إلى مشاريع'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if quotation.converted_to_project:
            return Response(
                {'error': 'تم تحويل هذا العرض إلى مشروع بالفعل'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Create project from quotation
        from projects.models import Project
        from projects.serializers import ProjectCreateSerializer
        
        project_data = {
            'name': quotation.title,
            'description': quotation.description or f"مشروع تم إنشاؤه من العرض {quotation.quotation_number}",
            'client': quotation.client.id,
            'project_manager': request.user.id,
            'budget': float(quotation.total_amount.amount),
            'start_date': timezone.now().date(),
            'status': 'planning'
        }
        
        project_serializer = ProjectCreateSerializer(data=project_data)
        if project_serializer.is_valid():
            project = project_serializer.save()
            
            # Update quotation
            quotation.status = 'converted'
            quotation.converted_to_project = project
            quotation.save()
            
            # Update client metrics
            quotation.client.update_metrics()
            
            return Response({
                'message': 'تم تحويل العرض إلى مشروع بنجاح',
                'project_id': project.id,
                'quotation': QuotationSerializer(quotation).data
            })
        
        return Response(project_serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get quotation statistics"""
        queryset = self.get_queryset()
        
        # Basic counts
        total_quotations = queryset.count()
        pending_quotations = queryset.filter(status='pending').count()
        approved_quotations = queryset.filter(status='approved').count()
        rejected_quotations = queryset.filter(status='rejected').count()
        expired_quotations = queryset.filter(
            valid_until__lt=timezone.now().date()
        ).count()
        converted_quotations = queryset.filter(status='converted').count()
        
        # Financial stats
        total_value = queryset.aggregate(
            total=Sum('total_amount')
        )['total'] or 0
        
        approved_value = queryset.filter(status='approved').aggregate(
            total=Sum('total_amount')
        )['total'] or 0
        
        # Conversion rate
        conversion_rate = 0
        if total_quotations > 0:
            conversion_rate = (converted_quotations / total_quotations) * 100
        
        # Average quotation value
        average_quotation_value = 0
        if total_quotations > 0:
            average_quotation_value = total_value / total_quotations
        
        stats_data = {
            'total_quotations': total_quotations,
            'pending_quotations': pending_quotations,
            'approved_quotations': approved_quotations,
            'rejected_quotations': rejected_quotations,
            'expired_quotations': expired_quotations,
            'converted_quotations': converted_quotations,
            'total_value': total_value,
            'approved_value': approved_value,
            'conversion_rate': round(conversion_rate, 2),
            'average_quotation_value': round(average_quotation_value, 2)
        }
        
        serializer = QuotationStatsSerializer(stats_data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def expiring_soon(self, request):
        """Get quotations expiring in the next 7 days"""
        days = int(request.query_params.get('days', 7))
        expiry_date = timezone.now().date() + timedelta(days=days)
        
        expiring_quotations = self.get_queryset().filter(
            status__in=['draft', 'pending'],
            valid_until__lte=expiry_date,
            valid_until__gte=timezone.now().date()
        )
        
        serializer = QuotationListSerializer(expiring_quotations, many=True)
        return Response(serializer.data)


class QuotationItemViewSet(viewsets.ModelViewSet):
    """Quotation item management viewset"""
    queryset = QuotationItem.objects.all().select_related(
        'quotation', 'service'
    ).order_by('-created_at')
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['quotation', 'service', 'complexity_level']
    search_fields = ['description', 'service__name_ar']
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'create':
            return QuotationItemCreateSerializer
        return QuotationItemSerializer
    
    def get_queryset(self):
        """Filter items based on quotation access"""
        queryset = super().get_queryset()
        
        # Non-admin users see only items from their quotations
        if not self.request.user.is_staff:
            queryset = queryset.filter(quotation__sales_rep=self.request.user)
        
        return queryset
    
    def perform_create(self, serializer):
        """Create quotation item and update totals"""
        item = serializer.save()
        item.quotation.calculate_totals()
    
    def perform_update(self, serializer):
        """Update quotation item and recalculate totals"""
        item = serializer.save()
        item.quotation.calculate_totals()
    
    def perform_destroy(self, instance):
        """Delete quotation item and recalculate totals"""
        quotation = instance.quotation
        instance.delete()
        quotation.calculate_totals()
