from django.contrib import admin
from .models import ServiceCatalog, Quotation, QuotationItem


class QuotationItemInline(admin.TabularInline):
    """Inline admin for quotation items"""
    model = QuotationItem
    extra = 1
    fields = ['service', 'description', 'quantity', 'unit_price', 'complexity_level', 'estimated_hours']
    readonly_fields = ['total_price']


@admin.register(ServiceCatalog)
class ServiceCatalogAdmin(admin.ModelAdmin):
    """Admin interface for Service Catalog"""
    list_display = ['name_ar', 'category', 'base_price', 'unit', 'is_active', 'created_at']
    list_filter = ['category', 'unit', 'is_active', 'created_at']
    search_fields = ['name_ar', 'name_en', 'description_ar']
    list_editable = ['is_active']
    ordering = ['category', 'name_ar']
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name_ar', 'name_en', 'category')
        }),
        ('التسعير', {
            'fields': ('base_price', 'unit')
        }),
        ('الوصف', {
            'fields': ('description_ar', 'description_en')
        }),
        ('تقدير الوقت', {
            'fields': ('estimated_hours_min', 'estimated_hours_max')
        }),
        ('الحالة', {
            'fields': ('is_active',)
        }),
    )


@admin.register(Quotation)
class QuotationAdmin(admin.ModelAdmin):
    """Admin interface for Quotations"""
    list_display = [
        'quotation_number', 'client', 'title', 'status', 'priority', 
        'total_amount', 'valid_until', 'created_at'
    ]
    list_filter = ['status', 'priority', 'created_at', 'valid_until']
    search_fields = ['quotation_number', 'title', 'client__name', 'client__email']
    list_editable = ['status', 'priority']
    ordering = ['-created_at']
    inlines = [QuotationItemInline]
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('quotation_number', 'title', 'description')
        }),
        ('العلاقات', {
            'fields': ('client', 'sales_rep')
        }),
        ('الحالة والأولوية', {
            'fields': ('status', 'priority')
        }),
        ('المعلومات المالية', {
            'fields': (
                'subtotal', 'discount_percentage', 'discount_amount',
                'tax_percentage', 'tax_amount', 'total_amount'
            )
        }),
        ('الصلاحية', {
            'fields': ('valid_until',)
        }),
        ('الموافقة', {
            'fields': ('approved_at', 'approved_by')
        }),
        ('تحويل المشروع', {
            'fields': ('converted_to_project',)
        }),
        ('معلومات إضافية', {
            'fields': ('notes', 'terms_conditions')
        }),
    )
    
    readonly_fields = ['quotation_number', 'subtotal', 'discount_amount', 'tax_amount', 'total_amount']
    
    def save_model(self, request, obj, form, change):
        """Override save to set sales rep if not set"""
        if not obj.sales_rep:
            obj.sales_rep = request.user
        super().save_model(request, obj, form, change)


@admin.register(QuotationItem)
class QuotationItemAdmin(admin.ModelAdmin):
    """Admin interface for Quotation Items"""
    list_display = [
        'quotation', 'service', 'quantity', 'unit_price', 
        'total_price', 'complexity_level', 'estimated_hours'
    ]
    list_filter = ['complexity_level', 'service__category', 'created_at']
    search_fields = ['quotation__quotation_number', 'service__name_ar', 'description']
    ordering = ['-created_at']
    
    fieldsets = (
        ('العلاقات', {
            'fields': ('quotation', 'service')
        }),
        ('تفاصيل العنصر', {
            'fields': ('description', 'quantity', 'unit_price', 'total_price')
        }),
        ('التعقيد والتقدير', {
            'fields': ('complexity_level', 'estimated_hours')
        }),
    )
    
    readonly_fields = ['total_price']
