from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from .models import ServiceCatalog, Quotation, QuotationItem
from clients.models import Client

User = get_user_model()


class ServiceCatalogTestCase(TestCase):
    """Test cases for ServiceCatalog model"""
    
    def setUp(self):
        self.service = ServiceCatalog.objects.create(
            name_ar='تطوير موقع ووردبريس',
            name_en='WordPress Website Development',
            category='web_development',
            base_price=5000,
            description_ar='تطوير موقع ووردبريس احترافي',
            estimated_hours_min=40,
            estimated_hours_max=80
        )
    
    def test_service_creation(self):
        """Test service catalog creation"""
        self.assertEqual(self.service.name_ar, 'تطوير موقع ووردبريس')
        self.assertEqual(self.service.category, 'web_development')
        self.assertTrue(self.service.is_active)
    
    def test_service_str_representation(self):
        """Test service string representation"""
        expected = f"{self.service.name_ar} - {self.service.get_category_display()}"
        self.assertEqual(str(self.service), expected)


class QuotationTestCase(TestCase):
    """Test cases for Quotation model"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.client = Client.objects.create(
            name='Test Client',
            email='<EMAIL>',
            phone='01234567890'
        )
        
        self.service = ServiceCatalog.objects.create(
            name_ar='تطوير موقع',
            category='web_development',
            base_price=5000,
            description_ar='تطوير موقع احترافي'
        )
        
        self.quotation = Quotation.objects.create(
            title='عرض تطوير موقع',
            client=self.client,
            sales_rep=self.user,
            tax_percentage=14
        )
    
    def test_quotation_creation(self):
        """Test quotation creation"""
        self.assertIsNotNone(self.quotation.quotation_number)
        self.assertEqual(self.quotation.status, 'draft')
        self.assertEqual(self.quotation.tax_percentage, 14)
    
    def test_quotation_number_generation(self):
        """Test quotation number generation"""
        quotation_number = self.quotation.quotation_number
        self.assertTrue(quotation_number.startswith('QUO-'))
        self.assertEqual(len(quotation_number), 12)  # QUO-YYYYMM-NNNN
    
    def test_quotation_expiry(self):
        """Test quotation expiry logic"""
        # Set quotation to expire yesterday
        self.quotation.valid_until = timezone.now().date() - timedelta(days=1)
        self.quotation.save()
        
        self.assertTrue(self.quotation.is_expired)
        self.assertEqual(self.quotation.days_until_expiry, 0)
    
    def test_quotation_calculate_totals(self):
        """Test quotation totals calculation"""
        # Add quotation item
        item = QuotationItem.objects.create(
            quotation=self.quotation,
            service=self.service,
            description='تطوير موقع ووردبريس',
            quantity=1,
            unit_price=5000,
            estimated_hours=40
        )
        
        self.quotation.calculate_totals()
        
        # Check calculations
        self.assertEqual(self.quotation.subtotal, 5000)
        self.assertEqual(self.quotation.tax_amount, 700)  # 14% of 5000
        self.assertEqual(self.quotation.total_amount, 5700)


class QuotationItemTestCase(TestCase):
    """Test cases for QuotationItem model"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.client = Client.objects.create(
            name='Test Client',
            email='<EMAIL>',
            phone='01234567890'
        )
        
        self.service = ServiceCatalog.objects.create(
            name_ar='تطوير موقع',
            category='web_development',
            base_price=5000,
            description_ar='تطوير موقع احترافي'
        )
        
        self.quotation = Quotation.objects.create(
            title='عرض تطوير موقع',
            client=self.client,
            sales_rep=self.user
        )
        
        self.item = QuotationItem.objects.create(
            quotation=self.quotation,
            service=self.service,
            description='تطوير موقع ووردبريس',
            quantity=2,
            unit_price=5000,
            estimated_hours=40
        )
    
    def test_quotation_item_creation(self):
        """Test quotation item creation"""
        self.assertEqual(self.item.quantity, 2)
        self.assertEqual(self.item.unit_price, 5000)
        self.assertEqual(self.item.total_price, 10000)  # 2 * 5000
    
    def test_quotation_item_total_calculation(self):
        """Test quotation item total price calculation"""
        self.item.quantity = 3
        self.item.unit_price = 4000
        self.item.save()
        
        self.assertEqual(self.item.total_price, 12000)  # 3 * 4000
    
    def test_quotation_item_str_representation(self):
        """Test quotation item string representation"""
        expected = f"{self.quotation.quotation_number} - {self.service.name_ar}"
        self.assertEqual(str(self.item), expected)
