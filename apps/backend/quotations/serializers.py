from rest_framework import serializers
from .models import ServiceCatalog, Quotation, QuotationItem
from authentication.serializers import UserListSerializer
from clients.serializers import ClientListSerializer


class ServiceCatalogSerializer(serializers.ModelSerializer):
    """Service catalog serializer"""
    category_display = serializers.CharField(source='get_category_display', read_only=True)
    unit_display = serializers.CharField(source='get_unit_display', read_only=True)
    
    class Meta:
        model = ServiceCatalog
        fields = [
            'id', 'name_ar', 'name_en', 'category', 'category_display',
            'base_price', 'unit', 'unit_display', 'description_ar', 'description_en',
            'estimated_hours_min', 'estimated_hours_max', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class ServiceCatalogListSerializer(serializers.ModelSerializer):
    """Simplified service catalog serializer for lists"""
    category_display = serializers.CharField(source='get_category_display', read_only=True)
    unit_display = serializers.CharField(source='get_unit_display', read_only=True)
    
    class Meta:
        model = ServiceCatalog
        fields = [
            'id', 'name_ar', 'category', 'category_display',
            'base_price', 'unit', 'unit_display', 'is_active'
        ]


class QuotationItemSerializer(serializers.ModelSerializer):
    """Quotation item serializer"""
    service = ServiceCatalogListSerializer(read_only=True)
    service_id = serializers.IntegerField(write_only=True)
    complexity_level_display = serializers.CharField(source='get_complexity_level_display', read_only=True)
    
    class Meta:
        model = QuotationItem
        fields = [
            'id', 'service', 'service_id', 'description', 'quantity',
            'unit_price', 'total_price', 'complexity_level', 'complexity_level_display',
            'estimated_hours', 'created_at'
        ]
        read_only_fields = ['id', 'total_price', 'created_at']
    
    def validate_service_id(self, value):
        """Validate service exists and is active"""
        try:
            service = ServiceCatalog.objects.get(id=value, is_active=True)
            return value
        except ServiceCatalog.DoesNotExist:
            raise serializers.ValidationError('الخدمة المحددة غير موجودة أو غير نشطة')


class QuotationItemCreateSerializer(serializers.ModelSerializer):
    """Quotation item creation serializer"""
    
    class Meta:
        model = QuotationItem
        fields = [
            'service', 'description', 'quantity', 'unit_price',
            'complexity_level', 'estimated_hours'
        ]
    
    def validate_service(self, value):
        """Validate service is active"""
        if not value.is_active:
            raise serializers.ValidationError('الخدمة المحددة غير نشطة')
        return value


class QuotationSerializer(serializers.ModelSerializer):
    """Complete quotation serializer"""
    client = ClientListSerializer(read_only=True)
    sales_rep = UserListSerializer(read_only=True)
    approved_by = UserListSerializer(read_only=True)
    items = QuotationItemSerializer(many=True, read_only=True)
    
    # Display fields
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    
    # Computed fields
    is_expired = serializers.BooleanField(read_only=True)
    days_until_expiry = serializers.IntegerField(read_only=True)
    items_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Quotation
        fields = [
            'id', 'quotation_number', 'title', 'description',
            'client', 'sales_rep', 'status', 'status_display',
            'priority', 'priority_display', 'subtotal', 'discount_percentage',
            'discount_amount', 'tax_percentage', 'tax_amount', 'total_amount',
            'valid_until', 'approved_at', 'approved_by', 'converted_to_project',
            'notes', 'terms_conditions', 'created_at', 'updated_at',
            'is_expired', 'days_until_expiry', 'items', 'items_count'
        ]
        read_only_fields = [
            'id', 'quotation_number', 'subtotal', 'discount_amount',
            'tax_amount', 'total_amount', 'approved_at', 'approved_by',
            'created_at', 'updated_at'
        ]
    
    def get_items_count(self, obj):
        """Get number of items in quotation"""
        return obj.items.count()


class QuotationListSerializer(serializers.ModelSerializer):
    """Simplified quotation serializer for lists"""
    client = ClientListSerializer(read_only=True)
    sales_rep = UserListSerializer(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    is_expired = serializers.BooleanField(read_only=True)
    days_until_expiry = serializers.IntegerField(read_only=True)
    items_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Quotation
        fields = [
            'id', 'quotation_number', 'title', 'client', 'sales_rep',
            'status', 'status_display', 'priority', 'priority_display',
            'total_amount', 'valid_until', 'created_at',
            'is_expired', 'days_until_expiry', 'items_count'
        ]
    
    def get_items_count(self, obj):
        """Get number of items in quotation"""
        return obj.items.count()


class QuotationCreateSerializer(serializers.ModelSerializer):
    """Quotation creation serializer"""
    client_id = serializers.IntegerField(write_only=True)
    items_data = QuotationItemCreateSerializer(many=True, write_only=True, required=False)
    
    class Meta:
        model = Quotation
        fields = [
            'title', 'description', 'client_id', 'priority',
            'discount_percentage', 'tax_percentage', 'valid_until',
            'notes', 'terms_conditions', 'items_data'
        ]
    
    def validate_client_id(self, value):
        """Validate client exists"""
        from clients.models import Client
        try:
            Client.objects.get(id=value)
            return value
        except Client.DoesNotExist:
            raise serializers.ValidationError('العميل المحدد غير موجود')
    
    def create(self, validated_data):
        """Create quotation with items"""
        items_data = validated_data.pop('items_data', [])
        client_id = validated_data.pop('client_id')
        
        # Set client and sales rep
        validated_data['client_id'] = client_id
        validated_data['sales_rep'] = self.context['request'].user
        
        # Create quotation
        quotation = Quotation.objects.create(**validated_data)
        
        # Create items
        for item_data in items_data:
            QuotationItem.objects.create(quotation=quotation, **item_data)
        
        # Calculate totals
        quotation.calculate_totals()
        
        return quotation


class QuotationUpdateSerializer(serializers.ModelSerializer):
    """Quotation update serializer"""
    
    class Meta:
        model = Quotation
        fields = [
            'title', 'description', 'priority', 'discount_percentage',
            'tax_percentage', 'valid_until', 'notes', 'terms_conditions'
        ]
    
    def validate(self, data):
        """Validate quotation can be updated"""
        if self.instance.status in ['approved', 'converted']:
            raise serializers.ValidationError('لا يمكن تعديل العرض بعد الموافقة عليه أو تحويله')
        return data


class QuotationApprovalSerializer(serializers.Serializer):
    """Quotation approval serializer"""
    action = serializers.ChoiceField(choices=['approve', 'reject'])
    notes = serializers.CharField(required=False, allow_blank=True)
    
    def validate(self, data):
        """Validate approval action"""
        quotation = self.context['quotation']
        
        if quotation.status != 'pending':
            raise serializers.ValidationError('يمكن الموافقة فقط على العروض في حالة الانتظار')
        
        if quotation.is_expired:
            raise serializers.ValidationError('لا يمكن الموافقة على عرض منتهي الصلاحية')
        
        return data


class QuotationStatsSerializer(serializers.Serializer):
    """Quotation statistics serializer"""
    total_quotations = serializers.IntegerField()
    pending_quotations = serializers.IntegerField()
    approved_quotations = serializers.IntegerField()
    rejected_quotations = serializers.IntegerField()
    expired_quotations = serializers.IntegerField()
    converted_quotations = serializers.IntegerField()
    total_value = serializers.DecimalField(max_digits=14, decimal_places=2)
    approved_value = serializers.DecimalField(max_digits=14, decimal_places=2)
    conversion_rate = serializers.DecimalField(max_digits=5, decimal_places=2)
    average_quotation_value = serializers.DecimalField(max_digits=14, decimal_places=2)
