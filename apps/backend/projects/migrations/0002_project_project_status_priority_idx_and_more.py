# Generated by Django 4.2.9 on 2025-06-03 15:09

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("projects", "0001_initial"),
    ]

    operations = [
        migrations.AddIndex(
            model_name="project",
            index=models.Index(
                fields=["status", "priority"], name="project_status_priority_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="project",
            index=models.Index(
                fields=["client", "status"], name="project_client_status_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="project",
            index=models.Index(
                fields=["project_manager", "status"], name="project_manager_status_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="project",
            index=models.Index(fields=["start_date"], name="project_start_date_idx"),
        ),
        migrations.AddIndex(
            model_name="project",
            index=models.Index(fields=["deadline"], name="project_deadline_idx"),
        ),
        migrations.AddIndex(
            model_name="project",
            index=models.Index(
                condition=models.Q(
                    ("status__in", ["planning", "development", "testing", "deployment"])
                ),
                fields=["client"],
                name="project_active_client_idx",
            ),
        ),
    ]
