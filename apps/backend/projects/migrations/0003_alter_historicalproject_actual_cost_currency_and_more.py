# Generated by Django 4.2.9 on 2025-06-04 21:17

from django.db import migrations
import djmoney.models.fields


class Migration(migrations.Migration):
    dependencies = [
        ("projects", "0002_project_project_status_priority_idx_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="historicalproject",
            name="actual_cost_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="historicalproject",
            name="budget_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="project",
            name="actual_cost_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="project",
            name="budget_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
                null=True,
            ),
        ),
    ]
