from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Count, Sum, Avg, Q
from django.utils import timezone
from .models import Project
from .serializers import (
    ProjectSerializer,
    ProjectListSerializer,
    ProjectCreateSerializer,
    ProjectDetailSerializer,
    ProjectStatsSerializer,
    ProjectUpdateProgressSerializer,
    UnifiedProjectCreationSerializer
)


class ProjectViewSet(viewsets.ModelViewSet):
    """Project management viewset"""
    queryset = Project.objects.all().select_related('client', 'project_manager').prefetch_related('assigned_team').order_by('-created_at')
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'priority', 'type', 'client', 'project_manager']
    search_fields = ['name', 'description', 'client__name']
    ordering_fields = ['name', 'created_at', 'start_date', 'deadline', 'progress', 'budget']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ProjectListSerializer
        elif self.action == 'create':
            return ProjectCreateSerializer
        elif self.action == 'retrieve':
            return ProjectDetailSerializer
        return ProjectSerializer

    def get_queryset(self):
        """Filter queryset based on user role"""
        queryset = super().get_queryset()
        user = self.request.user

        # Admins and sales managers can see all projects
        if user.role in ['admin', 'sales_manager']:
            return queryset

        # Project managers can see projects they manage
        if user.role in ['developer', 'designer', 'wordpress_developer']:
            return queryset.filter(
                Q(project_manager=user) | Q(assigned_team=user)
            ).distinct()

        # Other users can only see projects they're assigned to
        return queryset.filter(assigned_team=user)

    def perform_create(self, serializer):
        """Set project manager to current user if not specified"""
        if not serializer.validated_data.get('project_manager_id'):
            if self.request.user.role in ['admin', 'sales_manager', 'developer']:
                serializer.save(project_manager=self.request.user)
            else:
                serializer.save()
        else:
            serializer.save()

    @action(detail=True, methods=['post'])
    def update_progress(self, request, pk=None):
        """Update project progress"""
        project = self.get_object()
        serializer = ProjectUpdateProgressSerializer(data=request.data)

        if serializer.is_valid():
            progress = serializer.validated_data['progress']
            notes = serializer.validated_data.get('notes', '')

            project.progress = progress
            project.save(update_fields=['progress'])

            # Auto-update status based on progress
            if progress == 100 and project.status != 'completed':
                project.status = 'completed'
                project.save(update_fields=['status'])
            elif progress > 0 and project.status == 'planning':
                project.status = 'development'
                project.save(update_fields=['status'])

            return Response({
                'project': ProjectDetailSerializer(project).data,
                'message': f'تم تحديث نسبة الإنجاز إلى {progress}%'
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get project statistics"""
        queryset = self.get_queryset()

        # Basic counts
        total_projects = queryset.count()
        active_projects = queryset.exclude(status__in=['completed', 'cancelled']).count()
        completed_projects = queryset.filter(status='completed').count()

        # Overdue projects
        today = timezone.now().date()
        overdue_projects = queryset.filter(
            deadline__lt=today,
            status__in=['planning', 'development', 'testing', 'deployment']
        ).count()

        # Financial stats
        budget_stats = queryset.aggregate(
            total_budget=Sum('budget'),
            total_actual_cost=Sum('actual_cost')
        )

        # Progress stats
        avg_progress = queryset.aggregate(avg=Avg('progress'))['avg'] or 0

        # Distribution stats
        status_distribution = dict(
            queryset.values('status').annotate(count=Count('id')).values_list('status', 'count')
        )

        type_distribution = dict(
            queryset.values('type').annotate(count=Count('id')).values_list('type', 'count')
        )

        priority_distribution = dict(
            queryset.values('priority').annotate(count=Count('id')).values_list('priority', 'count')
        )

        stats_data = {
            'total_projects': total_projects,
            'active_projects': active_projects,
            'completed_projects': completed_projects,
            'overdue_projects': overdue_projects,
            'total_budget': budget_stats['total_budget'] or 0,
            'total_actual_cost': budget_stats['total_actual_cost'] or 0,
            'avg_progress': round(avg_progress, 2),
            'status_distribution': status_distribution,
            'type_distribution': type_distribution,
            'priority_distribution': priority_distribution
        }

        serializer = ProjectStatsSerializer(stats_data)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def create_with_client(self, request):
        """Create project with new client in atomic transaction"""
        from django.db import transaction
        from clients.serializers import ClientCreateSerializer, ClientSerializer

        # Validate the unified request data
        unified_serializer = UnifiedProjectCreationSerializer(data=request.data)
        if not unified_serializer.is_valid():
            return Response(
                unified_serializer.errors,
                status=status.HTTP_400_BAD_REQUEST
            )

        validated_data = unified_serializer.validated_data
        creation_mode = validated_data['creation_mode']

        try:
            with transaction.atomic():
                if creation_mode == 'existing':
                    # Use existing client
                    client_id = validated_data['client_id']
                    project_data = validated_data['project_data']
                    project_data['client'] = client_id

                    # Create project with existing client
                    project_serializer = ProjectCreateSerializer(data=project_data)
                    project_serializer.is_valid(raise_exception=True)
                    project = project_serializer.save()

                    # Get client data for response
                    from clients.models import Client
                    client = Client.objects.get(id=client_id)

                    return Response({
                        'client': ClientSerializer(client).data,
                        'project': ProjectSerializer(project).data,
                        'message': 'تم إنشاء المشروع بنجاح للعميل الموجود'
                    }, status=status.HTTP_201_CREATED)

                else:  # creation_mode == 'new'
                    # Create new client first
                    client_data = validated_data['client_data']
                    client_serializer = ClientCreateSerializer(data=client_data)
                    client_serializer.is_valid(raise_exception=True)
                    client = client_serializer.save()

                    # Create project with new client
                    project_data = validated_data['project_data']
                    project_data['client'] = client.id
                    project_serializer = ProjectCreateSerializer(data=project_data)
                    project_serializer.is_valid(raise_exception=True)
                    project = project_serializer.save()

                    # Update client metrics
                    client.update_metrics()

                    return Response({
                        'client': ClientSerializer(client).data,
                        'project': ProjectSerializer(project).data,
                        'message': 'تم إنشاء العميل والمشروع بنجاح'
                    }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                'error': 'فشل في إنشاء العميل والمشروع',
                'details': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
