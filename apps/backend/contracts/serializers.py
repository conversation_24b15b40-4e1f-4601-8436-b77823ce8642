"""
Contracts Module Serializers
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from clients.models import Client
from projects.models import Project
from .models import (
    ContractTemplate,
    Contract,
    ContractClause,
    ContractSignature,
    ContractRenewal,
)

User = get_user_model()


# Shared serializers for DRY principle
class UserListSerializer(serializers.ModelSerializer):
    """Lightweight user serializer for lists"""

    full_name = serializers.CharField(read_only=True)

    class Meta:
        model = User
        fields = ["id", "username", "first_name", "last_name", "full_name", "role"]


class ClientListSerializer(serializers.ModelSerializer):
    """Lightweight client serializer for lists"""

    class Meta:
        model = Client
        fields = ["id", "name", "email", "company"]


class ProjectListSerializer(serializers.ModelSerializer):
    """Lightweight project serializer for lists"""

    class Meta:
        model = Project
        fields = ["id", "name", "status"]


# Contract Template Serializers
class ContractTemplateSerializer(serializers.ModelSerializer):
    """Contract template serializer"""

    created_by_name = serializers.CharField(source="created_by.get_full_name", read_only=True)
    template_type_display = serializers.CharField(source="get_template_type_display", read_only=True)

    class Meta:
        model = ContractTemplate
        fields = [
            "id",
            "name",
            "description",
            "template_type",
            "template_type_display",
            "content",
            "is_active",
            "created_by",
            "created_by_name",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at", "created_by"]

    def create(self, validated_data):
        """Set created_by to current user"""
        validated_data["created_by"] = self.context["request"].user
        return super().create(validated_data)


class ContractTemplateListSerializer(serializers.ModelSerializer):
    """Contract template list serializer"""

    template_type_display = serializers.CharField(source="get_template_type_display", read_only=True)
    created_by_name = serializers.CharField(source="created_by.get_full_name", read_only=True)

    class Meta:
        model = ContractTemplate
        fields = [
            "id",
            "name",
            "template_type",
            "template_type_display",
            "is_active",
            "created_by_name",
            "created_at",
        ]


# Contract Clause Serializers
class ContractClauseSerializer(serializers.ModelSerializer):
    """Contract clause serializer"""

    clause_type_display = serializers.CharField(source="get_clause_type_display", read_only=True)
    created_by_name = serializers.CharField(source="created_by.get_full_name", read_only=True)

    class Meta:
        model = ContractClause
        fields = [
            "id",
            "title",
            "content",
            "clause_type",
            "clause_type_display",
            "is_mandatory",
            "is_active",
            "created_by",
            "created_by_name",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at", "created_by"]

    def create(self, validated_data):
        """Set created_by to current user"""
        validated_data["created_by"] = self.context["request"].user
        return super().create(validated_data)


# Contract Serializers
class ContractSerializer(serializers.ModelSerializer):
    """Main contract serializer"""

    # Related object details
    client_details = ClientListSerializer(source="client", read_only=True)
    project_details = ProjectListSerializer(source="project", read_only=True)
    template_details = ContractTemplateListSerializer(source="template", read_only=True)
    assigned_to_details = UserListSerializer(source="assigned_to", read_only=True)
    created_by_details = UserListSerializer(source="created_by", read_only=True)

    # Display fields
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    priority_display = serializers.CharField(source="get_priority_display", read_only=True)

    # Computed properties
    is_active = serializers.ReadOnlyField()
    days_until_expiry = serializers.ReadOnlyField()
    needs_renewal_notice = serializers.ReadOnlyField()

    class Meta:
        model = Contract
        fields = [
            "id",
            "contract_number",
            "title",
            "description",
            "status",
            "status_display",
            "priority",
            "priority_display",
            "client",
            "client_details",
            "project",
            "project_details",
            "template",
            "template_details",
            "assigned_to",
            "assigned_to_details",
            "content",
            "terms_conditions",
            "contract_value",
            "start_date",
            "end_date",
            "signed_date",
            "pdf_file",
            "signed_pdf_file",
            "auto_renewal",
            "renewal_period_months",
            "renewal_notice_days",
            "notes",
            "created_at",
            "updated_at",
            "created_by",
            "created_by_details",
            "is_active",
            "days_until_expiry",
            "needs_renewal_notice",
        ]
        read_only_fields = [
            "id",
            "contract_number",
            "created_at",
            "updated_at",
            "created_by",
            "pdf_file",
            "signed_pdf_file",
        ]

    def create(self, validated_data):
        """Set created_by to current user"""
        validated_data["created_by"] = self.context["request"].user
        return super().create(validated_data)

    def validate(self, data):
        """Validate contract data"""
        if data.get("start_date") and data.get("end_date"):
            if data["start_date"] >= data["end_date"]:
                raise serializers.ValidationError(
                    "تاريخ النهاية يجب أن يكون بعد تاريخ البداية"
                )
        return data


class ContractListSerializer(serializers.ModelSerializer):
    """Contract list serializer for performance"""

    client_name = serializers.CharField(source="client.name", read_only=True)
    project_name = serializers.CharField(source="project.name", read_only=True)
    assigned_to_name = serializers.CharField(source="assigned_to.get_full_name", read_only=True)
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    priority_display = serializers.CharField(source="get_priority_display", read_only=True)
    is_active = serializers.ReadOnlyField()
    days_until_expiry = serializers.ReadOnlyField()

    class Meta:
        model = Contract
        fields = [
            "id",
            "contract_number",
            "title",
            "status",
            "status_display",
            "priority",
            "priority_display",
            "client_name",
            "project_name",
            "assigned_to_name",
            "contract_value",
            "start_date",
            "end_date",
            "is_active",
            "days_until_expiry",
            "created_at",
        ]


class ContractCreateSerializer(serializers.ModelSerializer):
    """Contract creation serializer"""

    client_id = serializers.IntegerField(write_only=True)
    project_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    template_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    assigned_to_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)

    class Meta:
        model = Contract
        fields = [
            "title",
            "description",
            "priority",
            "client_id",
            "project_id",
            "template_id",
            "assigned_to_id",
            "content",
            "terms_conditions",
            "contract_value",
            "start_date",
            "end_date",
            "auto_renewal",
            "renewal_period_months",
            "renewal_notice_days",
            "notes",
        ]

    def validate_client_id(self, value):
        """Validate client exists"""
        try:
            Client.objects.get(id=value)
            return value
        except Client.DoesNotExist:
            raise serializers.ValidationError("العميل المحدد غير موجود")

    def validate_project_id(self, value):
        """Validate project exists if provided"""
        if value is not None:
            try:
                Project.objects.get(id=value)
                return value
            except Project.DoesNotExist:
                raise serializers.ValidationError("المشروع المحدد غير موجود")
        return value

    def validate_template_id(self, value):
        """Validate template exists if provided"""
        if value is not None:
            try:
                ContractTemplate.objects.get(id=value, is_active=True)
                return value
            except ContractTemplate.DoesNotExist:
                raise serializers.ValidationError("القالب المحدد غير موجود أو غير نشط")
        return value

    def validate_assigned_to_id(self, value):
        """Validate user exists if provided"""
        if value is not None:
            try:
                User.objects.get(id=value)
                return value
            except User.DoesNotExist:
                raise serializers.ValidationError("المستخدم المحدد غير موجود")
        return value

    def create(self, validated_data):
        """Create contract with proper relationships"""
        client_id = validated_data.pop("client_id")
        project_id = validated_data.pop("project_id", None)
        template_id = validated_data.pop("template_id", None)
        assigned_to_id = validated_data.pop("assigned_to_id", None)

        validated_data["client_id"] = client_id
        if project_id:
            validated_data["project_id"] = project_id
        if template_id:
            validated_data["template_id"] = template_id
        if assigned_to_id:
            validated_data["assigned_to_id"] = assigned_to_id

        validated_data["created_by"] = self.context["request"].user
        return Contract.objects.create(**validated_data)


# Contract Signature Serializers
class ContractSignatureSerializer(serializers.ModelSerializer):
    """Contract signature serializer"""

    contract_number = serializers.CharField(source="contract.contract_number", read_only=True)
    signature_type_display = serializers.CharField(source="get_signature_type_display", read_only=True)

    class Meta:
        model = ContractSignature
        fields = [
            "id",
            "contract",
            "contract_number",
            "signer_name",
            "signer_email",
            "signature_type",
            "signature_type_display",
            "signed_at",
            "ip_address",
            "signature_data",
            "is_signed",
            "created_at",
        ]
        read_only_fields = ["id", "created_at", "signed_at", "ip_address"]


# Contract Renewal Serializers
class ContractRenewalSerializer(serializers.ModelSerializer):
    """Contract renewal serializer"""

    original_contract_number = serializers.CharField(source="original_contract.contract_number", read_only=True)
    new_contract_number = serializers.CharField(source="new_contract.contract_number", read_only=True)
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    created_by_name = serializers.CharField(source="created_by.get_full_name", read_only=True)

    class Meta:
        model = ContractRenewal
        fields = [
            "id",
            "original_contract",
            "original_contract_number",
            "new_contract",
            "new_contract_number",
            "renewal_date",
            "new_end_date",
            "new_value",
            "status",
            "status_display",
            "notes",
            "created_at",
            "created_by",
            "created_by_name",
        ]
        read_only_fields = ["id", "created_at", "created_by"]

    def create(self, validated_data):
        """Set created_by to current user"""
        validated_data["created_by"] = self.context["request"].user
        return super().create(validated_data)
