"""
Contracts Module Admin Configuration
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

from django.contrib import admin
from django.utils.html import format_html
from simple_history.admin import SimpleHistoryAdmin
from .models import (
    ContractTemplate,
    Contract,
    ContractClause,
    ContractSignature,
    ContractRenewal,
)


@admin.register(ContractTemplate)
class ContractTemplateAdmin(SimpleHistoryAdmin):
    """Contract template admin"""

    list_display = [
        "name",
        "template_type",
        "is_active",
        "created_by",
        "created_at",
    ]
    list_filter = [
        "template_type",
        "is_active",
        "created_at",
        "created_by",
    ]
    search_fields = [
        "name",
        "description",
        "content",
    ]
    readonly_fields = [
        "created_at",
        "updated_at",
        "created_by",
    ]
    fieldsets = (
        (
            "معلومات أساسية",
            {
                "fields": (
                    "name",
                    "description",
                    "template_type",
                    "is_active",
                )
            },
        ),
        ("محتوى القالب", {"fields": ("content",)}),
        (
            "معلومات النظام",
            {
                "fields": (
                    "created_by",
                    "created_at",
                    "updated_at",
                ),
                "classes": ("collapse",),
            },
        ),
    )

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Contract)
class ContractAdmin(SimpleHistoryAdmin):
    """Contract admin"""

    list_display = [
        "contract_number",
        "title",
        "client",
        "status_badge",
        "priority_badge",
        "contract_value",
        "start_date",
        "end_date",
        "is_active_badge",
        "created_at",
    ]
    list_filter = [
        "status",
        "priority",
        "auto_renewal",
        "start_date",
        "end_date",
        "created_at",
        "client",
        "assigned_to",
    ]
    search_fields = [
        "contract_number",
        "title",
        "description",
        "client__name",
        "content",
    ]
    readonly_fields = [
        "contract_number",
        "created_at",
        "updated_at",
        "created_by",
        "is_active",
        "days_until_expiry",
        "needs_renewal_notice",
    ]
    raw_id_fields = [
        "client",
        "project",
        "template",
        "assigned_to",
    ]
    fieldsets = (
        (
            "معلومات أساسية",
            {
                "fields": (
                    "contract_number",
                    "title",
                    "description",
                    "status",
                    "priority",
                )
            },
        ),
        (
            "العلاقات",
            {
                "fields": (
                    "client",
                    "project",
                    "template",
                    "assigned_to",
                )
            },
        ),
        (
            "محتوى العقد",
            {
                "fields": (
                    "content",
                    "terms_conditions",
                )
            },
        ),
        ("المعلومات المالية", {"fields": ("contract_value",)}),
        (
            "الجدول الزمني",
            {
                "fields": (
                    "start_date",
                    "end_date",
                    "signed_date",
                )
            },
        ),
        (
            "إدارة الملفات",
            {
                "fields": (
                    "pdf_file",
                    "signed_pdf_file",
                )
            },
        ),
        (
            "التجديد",
            {
                "fields": (
                    "auto_renewal",
                    "renewal_period_months",
                    "renewal_notice_days",
                )
            },
        ),
        ("ملاحظات", {"fields": ("notes",)}),
        (
            "معلومات النظام",
            {
                "fields": (
                    "created_by",
                    "created_at",
                    "updated_at",
                    "is_active",
                    "days_until_expiry",
                    "needs_renewal_notice",
                ),
                "classes": ("collapse",),
            },
        ),
    )

    def status_badge(self, obj):
        """Display status with color badge"""
        colors = {
            "draft": "gray",
            "pending_review": "orange",
            "pending_signature": "blue",
            "active": "green",
            "completed": "purple",
            "cancelled": "red",
            "expired": "darkred",
        }
        color = colors.get(obj.status, "gray")
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display(),
        )

    status_badge.short_description = "الحالة"

    def priority_badge(self, obj):
        """Display priority with color badge"""
        colors = {
            "low": "green",
            "medium": "orange",
            "high": "red",
            "urgent": "darkred",
        }
        color = colors.get(obj.priority, "gray")
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_priority_display(),
        )

    priority_badge.short_description = "الأولوية"

    def is_active_badge(self, obj):
        """Display active status with badge"""
        if obj.is_active:
            return format_html(
                '<span style="color: green; font-weight: bold;">✓ نشط</span>'
            )
        return format_html(
            '<span style="color: red; font-weight: bold;">✗ غير نشط</span>'
        )

    is_active_badge.short_description = "نشط"

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
