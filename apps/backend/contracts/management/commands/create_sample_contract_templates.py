"""
Management command to create sample contract templates for MTBRMG ERP system.
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from contracts.models import ContractTemplate

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample contract templates for digital agency services'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample contract templates...')

        # Get or create admin user
        admin_user = User.objects.filter(is_staff=True).first()
        if not admin_user:
            admin_user = User.objects.create_user(
                email='<EMAIL>',
                first_name='محمد',
                last_name='عبد الفتاح',
                is_staff=True,
                is_active=True
            )
            admin_user.set_password('demo123')
            admin_user.save()

        # Create contract templates
        templates = [
            {
                'name': 'عقد تطوير موقع إلكتروني',
                'description': 'قالب عقد شامل لخدمات تطوير المواقع الإلكترونية مع التصميم والبرمجة',
                'template_type': 'website',
                'content': self.get_website_template(),
            },
            {
                'name': 'عقد تطوير تطبيق جوال',
                'description': 'قالب عقد لتطوير تطبيقات الهواتف الذكية لنظامي iOS و Android',
                'template_type': 'mobile_app',
                'content': self.get_mobile_app_template(),
            },
            {
                'name': 'عقد خدمات التسويق الرقمي',
                'description': 'قالب عقد شامل لخدمات التسويق الرقمي وإدارة الحملات الإعلانية',
                'template_type': 'marketing',
                'content': self.get_marketing_template(),
            },
            {
                'name': 'عقد تصميم الهوية التجارية',
                'description': 'قالب عقد لخدمات تصميم الهوية التجارية والمواد التسويقية',
                'template_type': 'branding',
                'content': self.get_branding_template(),
            },
            {
                'name': 'عقد خدمات تحسين محركات البحث',
                'description': 'قالب عقد لخدمات SEO وتحسين ظهور المواقع في نتائج البحث',
                'template_type': 'seo',
                'content': self.get_seo_template(),
            },
            {
                'name': 'عقد إدارة وسائل التواصل الاجتماعي',
                'description': 'قالب عقد لخدمات إدارة حسابات وسائل التواصل الاجتماعي',
                'template_type': 'social_media',
                'content': self.get_social_media_template(),
            },
        ]

        for template_data in templates:
            template, created = ContractTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults={
                    **template_data,
                    'created_by': admin_user,
                    'is_active': True,
                }
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Created template: {template.name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Template already exists: {template.name}')
                )

        self.stdout.write(
            self.style.SUCCESS('Successfully created sample contract templates')
        )

    def get_website_template(self):
        return """
عقد تطوير موقع إلكتروني

بين:
الطرف الأول: شركة MTBRMG للحلول الرقمية
العنوان: {company_address}
الهاتف: {company_phone}
البريد الإلكتروني: {company_email}

الطرف الثاني: {client_name}
العنوان: {client_address}
الهاتف: {client_phone}
البريد الإلكتروني: {client_email}

تم الاتفاق على ما يلي:

المادة الأولى: موضوع العقد
يتعهد الطرف الأول بتطوير موقع إلكتروني للطرف الثاني وفقاً للمواصفات التالية:
- اسم المشروع: {project_name}
- وصف المشروع: {project_description}
- نوع الموقع: موقع إلكتروني تفاعلي
- اللغات المطلوبة: العربية والإنجليزية
- التوافق مع الأجهزة المحمولة: نعم

المادة الثانية: نطاق العمل
يشمل العمل المطلوب:
1. تصميم واجهة المستخدم (UI/UX Design)
2. برمجة الموقع باستخدام أحدث التقنيات
3. إنشاء لوحة تحكم إدارية
4. تحسين الموقع لمحركات البحث (SEO)
5. اختبار الموقع وضمان الجودة
6. رفع الموقع على الخادم
7. تدريب العميل على استخدام الموقع

المادة الثالثة: المدة الزمنية
- تاريخ البدء: {start_date}
- تاريخ التسليم المتوقع: {end_date}
- مدة العقد: من تاريخ التوقيع حتى تاريخ التسليم النهائي

المادة الرابعة: القيمة المالية
- إجمالي قيمة العقد: {contract_value} جنيه مصري
- طريقة الدفع: على ثلاث دفعات
  * 40% عند التوقيع على العقد
  * 40% عند تسليم النسخة التجريبية
  * 20% عند التسليم النهائي والموافقة

المادة الخامسة: الضمان والصيانة
- فترة ضمان مجانية: 6 أشهر من تاريخ التسليم
- صيانة تشمل: إصلاح الأخطاء البرمجية وتحديثات الأمان
- الدعم الفني: متاح خلال ساعات العمل الرسمية

المادة السادسة: حقوق الملكية الفكرية
- جميع حقوق الملكية الفكرية للموقع تؤول للعميل بعد سداد كامل المبلغ
- الشركة تحتفظ بحق استخدام المشروع كنموذج في أعمالها التسويقية

المادة السابعة: التزامات الطرفين
التزامات الطرف الأول:
- تسليم العمل في الموعد المحدد
- ضمان جودة العمل المنجز
- تقديم الدعم الفني المطلوب

التزامات الطرف الثاني:
- توفير المحتوى والصور المطلوبة
- سداد المبالغ في المواعيد المحددة
- التعاون في مراحل التطوير والاختبار

تاريخ العقد: {current_date}
رقم العقد: {contract_number}

توقيع الطرف الأول: ________________
توقيع الطرف الثاني: ________________
"""

    def get_mobile_app_template(self):
        return """
عقد تطوير تطبيق جوال

بين:
الطرف الأول: شركة MTBRMG للحلول الرقمية
الطرف الثاني: {client_name}

موضوع العقد: تطوير تطبيق جوال
اسم المشروع: {project_name}
وصف المشروع: {project_description}

المنصات المطلوبة:
- نظام iOS (iPhone/iPad)
- نظام Android

الخصائص الأساسية:
- واجهة مستخدم عصرية وسهلة الاستخدام
- نظام تسجيل دخول وإدارة المستخدمين
- إشعارات فورية (Push Notifications)
- تكامل مع وسائل التواصل الاجتماعي
- نظام دفع إلكتروني آمن
- لوحة تحكم إدارية

القيمة المالية: {contract_value} جنيه مصري
مدة التطوير: من {start_date} إلى {end_date}

شروط الدفع:
- 50% عند التوقيع
- 30% عند تسليم النسخة التجريبية
- 20% عند النشر في المتاجر الرسمية

الضمان: سنة كاملة من تاريخ النشر
الصيانة: تشمل إصلاح الأخطاء والتحديثات الأمنية

رقم العقد: {contract_number}
تاريخ العقد: {current_date}
"""

    def get_marketing_template(self):
        return """
عقد خدمات التسويق الرقمي

بين شركة MTBRMG و {client_name}

نطاق الخدمات:
1. إدارة حملات إعلانية على:
   - فيسبوك وإنستغرام
   - جوجل أدز
   - يوتيوب
   - لينكد إن

2. تحليل وتقارير شهرية
3. إنشاء محتوى إبداعي
4. استراتيجية تسويقية شاملة

الهدف: زيادة المبيعات والوصول للعملاء المستهدفين

المدة: من {start_date} إلى {end_date}
القيمة: {contract_value} جنيه مصري شهرياً

شروط الدفع: شهرياً مقدماً
الضمان: تحقيق النتائج المتفق عليها أو استرداد 50% من القيمة

رقم العقد: {contract_number}
"""

    def get_branding_template(self):
        return """
عقد تصميم الهوية التجارية

العميل: {client_name}
المشروع: {project_name}

الخدمات المشمولة:
1. تصميم الشعار (Logo)
2. دليل الهوية البصرية
3. تصميم الكروت الشخصية
4. تصميم الأوراق الرسمية
5. تصميم اللافتات والإعلانات
6. قوالب وسائل التواصل الاجتماعي

المواصفات:
- تصميمات عصرية ومبتكرة
- ملفات بجودة عالية (AI, PSD, PNG, JPG)
- حقوق ملكية كاملة للعميل

القيمة: {contract_value} جنيه مصري
مدة التنفيذ: {start_date} إلى {end_date}

التسليم: على مراحل مع إمكانية التعديل
الضمان: 3 أشهر لأي تعديلات مطلوبة

رقم العقد: {contract_number}
"""

    def get_seo_template(self):
        return """
عقد خدمات تحسين محركات البحث (SEO)

العميل: {client_name}
الموقع: {project_name}

الخدمات:
1. تحليل الموقع الحالي
2. بحث الكلمات المفتاحية
3. تحسين المحتوى
4. بناء الروابط الخارجية
5. تحسين السرعة والأداء
6. تقارير شهرية مفصلة

الأهداف:
- تحسين ترتيب الموقع في نتائج البحث
- زيادة الزيارات العضوية
- تحسين معدل التحويل

المدة: 6 أشهر قابلة للتجديد
القيمة: {contract_value} جنيه مصري شهرياً

الضمان: تحسن ملحوظ في الترتيب خلال 3 أشهر

رقم العقد: {contract_number}
"""

    def get_social_media_template(self):
        return """
عقد إدارة وسائل التواصل الاجتماعي

العميل: {client_name}
المشروع: {project_name}

المنصات المشمولة:
- فيسبوك
- إنستغرام  
- تويتر
- لينكد إن
- يوتيوب

الخدمات:
1. إنشاء وجدولة المنشورات
2. تصميم المحتوى البصري
3. الرد على التعليقات والرسائل
4. إدارة الحملات الإعلانية
5. تحليل الأداء وتقارير شهرية
6. زيادة المتابعين والتفاعل

المحتوى: 20 منشور شهرياً لكل منصة
التصميم: قوالب احترافية متنوعة

المدة: من {start_date} إلى {end_date}
القيمة: {contract_value} جنيه مصري شهرياً

رقم العقد: {contract_number}
"""
