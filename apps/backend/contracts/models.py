"""
Contracts Module Models
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

from django.db import models
from django.conf import settings
from simple_history.models import HistoricalRecords
from djmoney.models.fields import MoneyField
from django.core.validators import MinValueValidator, MaxValueValidator


class ContractTemplate(models.Model):
    """Reusable contract templates"""

    class TemplateType(models.TextChoices):
        WEBSITE = "website", "تطوير موقع إلكتروني"
        MOBILE_APP = "mobile_app", "تطبيق جوال"
        WEB_APP = "web_app", "تطبيق ويب"
        ECOMMERCE = "ecommerce", "متجر إلكتروني"
        MAINTENANCE = "maintenance", "صيانة وتطوير"
        MARKETING = "marketing", "تسويق رقمي"
        BRANDING = "branding", "هوية تجارية وتصميم"
        SEO = "seo", "خدمات تحسين محركات البحث"
        SOCIAL_MEDIA = "social_media", "إدارة وسائل التواصل الاجتماعي"
        CUSTOM = "custom", "خدمة مخصصة"

    name = models.CharField(max_length=200, verbose_name="اسم القالب")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    template_type = models.CharField(
        max_length=20,
        choices=TemplateType.choices,
        default=TemplateType.WEBSITE,
        verbose_name="نوع القالب",
    )
    content = models.TextField(verbose_name="محتوى القالب")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="created_contract_templates",
        verbose_name="منشئ القالب",
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    # History tracking
    history = HistoricalRecords()

    class Meta:
        verbose_name = "قالب عقد"
        verbose_name_plural = "قوالب العقود"
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["template_type", "is_active"]),
            models.Index(fields=["created_by"]),
        ]

    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"


class Contract(models.Model):
    """Main contract entity"""

    class Status(models.TextChoices):
        DRAFT = "draft", "مسودة"
        PENDING_REVIEW = "pending_review", "في انتظار المراجعة"
        PENDING_SIGNATURE = "pending_signature", "في انتظار التوقيع"
        ACTIVE = "active", "نشط"
        COMPLETED = "completed", "مكتمل"
        CANCELLED = "cancelled", "ملغي"
        EXPIRED = "expired", "منتهي الصلاحية"

    class Priority(models.TextChoices):
        LOW = "low", "منخفض"
        MEDIUM = "medium", "متوسط"
        HIGH = "high", "عالي"
        URGENT = "urgent", "عاجل"

    # Basic Information
    contract_number = models.CharField(
        max_length=50, unique=True, verbose_name="رقم العقد"
    )
    title = models.CharField(max_length=200, verbose_name="عنوان العقد")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")

    # Status & Priority
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.DRAFT,
        verbose_name="الحالة",
    )
    priority = models.CharField(
        max_length=10,
        choices=Priority.choices,
        default=Priority.MEDIUM,
        verbose_name="الأولوية",
    )

    # Relationships
    client = models.ForeignKey(
        "clients.Client",
        on_delete=models.CASCADE,
        related_name="contracts",
        verbose_name="العميل",
    )
    project = models.ForeignKey(
        "projects.Project",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="contracts",
        verbose_name="المشروع",
    )
    template = models.ForeignKey(
        ContractTemplate,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="contracts",
        verbose_name="القالب المستخدم",
    )
    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="assigned_contracts",
        verbose_name="المكلف بالعقد",
    )

    # Contract Content
    content = models.TextField(verbose_name="محتوى العقد")
    terms_conditions = models.TextField(
        blank=True, null=True, verbose_name="الشروط والأحكام"
    )

    # Financial Information
    contract_value = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency="EGP",
        blank=True,
        null=True,
        verbose_name="قيمة العقد",
    )

    # Timeline
    start_date = models.DateField(verbose_name="تاريخ البداية")
    end_date = models.DateField(verbose_name="تاريخ النهاية")
    signed_date = models.DateTimeField(
        blank=True, null=True, verbose_name="تاريخ التوقيع"
    )

    # File Management
    pdf_file = models.FileField(
        upload_to="contracts/pdfs/", blank=True, null=True, verbose_name="ملف PDF"
    )
    signed_pdf_file = models.FileField(
        upload_to="contracts/signed/",
        blank=True,
        null=True,
        verbose_name="ملف PDF موقع",
    )

    # Renewal Information
    auto_renewal = models.BooleanField(default=False, verbose_name="تجديد تلقائي")
    renewal_period_months = models.PositiveIntegerField(
        blank=True, null=True, verbose_name="فترة التجديد (شهور)"
    )
    renewal_notice_days = models.PositiveIntegerField(
        default=30, verbose_name="أيام الإشعار قبل التجديد"
    )

    # Additional Information
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")

    # Tracking
    created_at = models.DateTimeField(
        auto_now_add=True, db_index=True, verbose_name="تاريخ الإنشاء"
    )
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="created_contracts",
        verbose_name="منشئ العقد",
    )

    # History tracking
    history = HistoricalRecords()

    class Meta:
        verbose_name = "عقد"
        verbose_name_plural = "العقود"
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["status", "priority"]),
            models.Index(fields=["client", "status"]),
            models.Index(fields=["assigned_to", "status"]),
            models.Index(fields=["start_date"]),
            models.Index(fields=["end_date"]),
            models.Index(fields=["contract_number"]),
        ]

    def __str__(self):
        return f"{self.contract_number} - {self.title}"

    def save(self, *args, **kwargs):
        if not self.contract_number:
            self.contract_number = self.generate_contract_number()
        super().save(*args, **kwargs)

    def generate_contract_number(self):
        """Generate unique contract number"""
        from django.utils import timezone

        year = timezone.now().year
        month = timezone.now().month

        # Get last contract number for this month
        last_contract = (
            Contract.objects.filter(created_at__year=year, created_at__month=month)
            .order_by("-id")
            .first()
        )

        if last_contract and last_contract.contract_number:
            try:
                last_number = int(last_contract.contract_number.split("-")[-1])
                new_number = last_number + 1
            except (ValueError, IndexError):
                new_number = 1
        else:
            new_number = 1

        return f"CON-{year}{month:02d}-{new_number:04d}"

    @property
    def is_active(self):
        """Check if contract is currently active"""
        from django.utils import timezone

        today = timezone.now().date()
        return (
            self.status == self.Status.ACTIVE
            and self.start_date <= today <= self.end_date
        )

    @property
    def days_until_expiry(self):
        """Calculate days until contract expires"""
        from django.utils import timezone

        today = timezone.now().date()
        if self.end_date > today:
            return (self.end_date - today).days
        return 0

    @property
    def needs_renewal_notice(self):
        """Check if renewal notice should be sent"""
        return (
            self.auto_renewal
            and self.days_until_expiry <= self.renewal_notice_days
            and self.status == self.Status.ACTIVE
        )


class ContractClause(models.Model):
    """Standard contract clauses library"""

    class ClauseType(models.TextChoices):
        PAYMENT = "payment", "الدفع"
        DELIVERY = "delivery", "التسليم"
        WARRANTY = "warranty", "الضمان"
        CANCELLATION = "cancellation", "الإلغاء"
        INTELLECTUAL_PROPERTY = "ip", "الملكية الفكرية"
        CONFIDENTIALITY = "confidentiality", "السرية"
        LIMITATION = "limitation", "تحديد المسؤولية"
        GENERAL = "general", "عام"

    title = models.CharField(max_length=200, verbose_name="عنوان البند")
    content = models.TextField(verbose_name="محتوى البند")
    clause_type = models.CharField(
        max_length=20,
        choices=ClauseType.choices,
        default=ClauseType.GENERAL,
        verbose_name="نوع البند",
    )
    is_mandatory = models.BooleanField(default=False, verbose_name="إجباري")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="created_contract_clauses",
        verbose_name="منشئ البند",
    )

    class Meta:
        verbose_name = "بند عقد"
        verbose_name_plural = "بنود العقود"
        ordering = ["clause_type", "title"]
        indexes = [
            models.Index(fields=["clause_type", "is_active"]),
        ]

    def __str__(self):
        return f"{self.title} ({self.get_clause_type_display()})"


class ContractSignature(models.Model):
    """Digital signature tracking"""

    class SignatureType(models.TextChoices):
        CLIENT = "client", "العميل"
        COMPANY = "company", "الشركة"
        WITNESS = "witness", "شاهد"

    contract = models.ForeignKey(
        Contract,
        on_delete=models.CASCADE,
        related_name="signatures",
        verbose_name="العقد",
    )
    signer_name = models.CharField(max_length=200, verbose_name="اسم الموقع")
    signer_email = models.EmailField(verbose_name="بريد الموقع الإلكتروني")
    signature_type = models.CharField(
        max_length=10, choices=SignatureType.choices, verbose_name="نوع التوقيع"
    )
    signed_at = models.DateTimeField(
        blank=True, null=True, verbose_name="تاريخ التوقيع"
    )
    ip_address = models.GenericIPAddressField(
        blank=True, null=True, verbose_name="عنوان IP"
    )
    signature_data = models.TextField(
        blank=True, null=True, verbose_name="بيانات التوقيع"
    )
    is_signed = models.BooleanField(default=False, verbose_name="موقع")

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "توقيع عقد"
        verbose_name_plural = "توقيعات العقود"
        ordering = ["-created_at"]
        unique_together = ["contract", "signature_type"]

    def __str__(self):
        return f"{self.contract.contract_number} - {self.signer_name}"


class ContractRenewal(models.Model):
    """Contract renewal tracking"""

    class RenewalStatus(models.TextChoices):
        PENDING = "pending", "في الانتظار"
        APPROVED = "approved", "موافق عليه"
        REJECTED = "rejected", "مرفوض"
        COMPLETED = "completed", "مكتمل"

    original_contract = models.ForeignKey(
        Contract,
        on_delete=models.CASCADE,
        related_name="renewals",
        verbose_name="العقد الأصلي",
    )
    new_contract = models.ForeignKey(
        Contract,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="renewal_source",
        verbose_name="العقد الجديد",
    )
    renewal_date = models.DateField(verbose_name="تاريخ التجديد")
    new_end_date = models.DateField(verbose_name="تاريخ النهاية الجديد")
    new_value = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency="EGP",
        blank=True,
        null=True,
        verbose_name="القيمة الجديدة",
    )
    status = models.CharField(
        max_length=10,
        choices=RenewalStatus.choices,
        default=RenewalStatus.PENDING,
        verbose_name="حالة التجديد",
    )
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="created_renewals",
        verbose_name="منشئ التجديد",
    )

    class Meta:
        verbose_name = "تجديد عقد"
        verbose_name_plural = "تجديدات العقود"
        ordering = ["-created_at"]

    def __str__(self):
        return f"تجديد {self.original_contract.contract_number}"
