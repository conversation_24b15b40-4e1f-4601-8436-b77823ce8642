"""
Contracts module URLs following established patterns from quotations and finance modules.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    ContractTemplateViewSet,
    ContractViewSet,
    ContractClauseViewSet,
    ContractSignatureViewSet,
    ContractRenewalViewSet,
)

# Create router and register viewsets
router = DefaultRouter()
router.register(r"contract-templates", ContractTemplateViewSet, basename="contracttemplate")
router.register(r"contracts", ContractViewSet, basename="contract")
router.register(r"contract-clauses", ContractClauseViewSet, basename="contractclause")
router.register(r"contract-signatures", ContractSignatureViewSet, basename="contractsignature")
router.register(r"contract-renewals", ContractRenewalViewSet, basename="contractrenewal")

urlpatterns = [
    path("", include(router.urls)),
]
