"""
Contracts Module Views
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Count, Sum, Q
from django.utils import timezone
from django.http import HttpResponse

from .models import (
    ContractTemplate,
    Contract,
    ContractClause,
    ContractSignature,
    ContractRenewal,
)
from .serializers import (
    ContractTemplateSerializer,
    ContractTemplateListSerializer,
    ContractSerializer,
    ContractListSerializer,
    ContractCreateSerializer,
    ContractClauseSerializer,
    ContractSignatureSerializer,
    ContractRenewalSerializer,
)


class ContractTemplateViewSet(viewsets.ModelViewSet):
    """Contract template management viewset"""

    queryset = (
        ContractTemplate.objects.all()
        .select_related("created_by")
        .order_by("-created_at")
    )
    permission_classes = [IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["template_type", "is_active", "created_by"]
    search_fields = ["name", "description"]
    ordering_fields = ["name", "created_at", "template_type"]
    ordering = ["-created_at"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "list":
            return ContractTemplateListSerializer
        return ContractTemplateSerializer

    def get_queryset(self):
        """Filter queryset based on user role"""
        queryset = super().get_queryset()
        user = self.request.user

        # Admins and sales managers can see all templates
        if user.role in ["admin", "sales_manager"]:
            return queryset

        # Other users can only see active templates
        return queryset.filter(is_active=True)

    @action(detail=False, methods=["get"])
    def active(self, request):
        """Get only active templates"""
        templates = self.get_queryset().filter(is_active=True)
        serializer = ContractTemplateListSerializer(templates, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def by_type(self, request):
        """Get templates grouped by type"""
        template_type = request.query_params.get("type")
        if not template_type:
            return Response(
                {"error": "نوع القالب مطلوب"}, status=status.HTTP_400_BAD_REQUEST
            )

        templates = self.get_queryset().filter(
            template_type=template_type, is_active=True
        )
        serializer = ContractTemplateListSerializer(templates, many=True)
        return Response(serializer.data)


class ContractViewSet(viewsets.ModelViewSet):
    """Contract management viewset"""

    queryset = (
        Contract.objects.all()
        .select_related("client", "project", "template", "assigned_to", "created_by")
        .prefetch_related("signatures", "renewals")
        .order_by("-created_at")
    )
    permission_classes = [IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = [
        "status",
        "priority",
        "client",
        "project",
        "assigned_to",
        "auto_renewal",
    ]
    search_fields = ["contract_number", "title", "description", "client__name"]
    ordering_fields = [
        "title",
        "created_at",
        "start_date",
        "end_date",
        "contract_value",
    ]
    ordering = ["-created_at"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "list":
            return ContractListSerializer
        elif self.action == "create":
            return ContractCreateSerializer
        return ContractSerializer

    def get_queryset(self):
        """Filter queryset based on user role"""
        queryset = super().get_queryset()
        user = self.request.user

        # Admins and sales managers can see all contracts
        if user.role in ["admin", "sales_manager"]:
            return queryset

        # Other users can only see contracts they're assigned to or created
        return queryset.filter(Q(assigned_to=user) | Q(created_by=user)).distinct()

    @action(detail=False, methods=["get"])
    def stats(self, request):
        """Get contract statistics"""
        queryset = self.get_queryset()

        stats = {
            "total_contracts": queryset.count(),
            "active_contracts": queryset.filter(status=Contract.Status.ACTIVE).count(),
            "pending_signature": queryset.filter(
                status=Contract.Status.PENDING_SIGNATURE
            ).count(),
            "expiring_soon": queryset.filter(
                status=Contract.Status.ACTIVE,
                end_date__lte=timezone.now().date() + timezone.timedelta(days=30),
            ).count(),
            "total_value": queryset.aggregate(total=Sum("contract_value"))["total"]
            or 0,
            "by_status": {},
            "by_priority": {},
        }

        # Group by status
        for status_choice in Contract.Status.choices:
            status_key = status_choice[0]
            status_count = queryset.filter(status=status_key).count()
            stats["by_status"][status_key] = {
                "count": status_count,
                "label": status_choice[1],
            }

        # Group by priority
        for priority_choice in Contract.Priority.choices:
            priority_key = priority_choice[0]
            priority_count = queryset.filter(priority=priority_key).count()
            stats["by_priority"][priority_key] = {
                "count": priority_count,
                "label": priority_choice[1],
            }

        return Response(stats)

    @action(detail=False, methods=["get"])
    def expiring_soon(self, request):
        """Get contracts expiring within specified days"""
        days = int(request.query_params.get("days", 30))

        contracts = self.get_queryset().filter(
            status=Contract.Status.ACTIVE,
            end_date__lte=timezone.now().date() + timezone.timedelta(days=days),
        )

        serializer = ContractListSerializer(contracts, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def renewal_candidates(self, request):
        """Get contracts that need renewal notices"""
        contracts = self.get_queryset().filter(
            auto_renewal=True, status=Contract.Status.ACTIVE
        )

        # Filter contracts that need renewal notice
        renewal_candidates = []
        for contract in contracts:
            if contract.needs_renewal_notice:
                renewal_candidates.append(contract)

        serializer = ContractListSerializer(renewal_candidates, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=["get"])
    def generate_pdf(self, request, pk=None):
        """Generate PDF for contract"""
        contract = self.get_object()

        try:
            from .pdf_generator import ContractPDFGenerator

            pdf_generator = ContractPDFGenerator()
            pdf_content = pdf_generator.generate_pdf(contract)

            # Save PDF file to contract
            from django.core.files.base import ContentFile

            pdf_filename = f"contract_{contract.contract_number}.pdf"
            contract.pdf_file.save(pdf_filename, ContentFile(pdf_content), save=True)

            # Return PDF as response
            response = HttpResponse(pdf_content, content_type="application/pdf")
            response["Content-Disposition"] = f'attachment; filename="{pdf_filename}"'
            return response

        except Exception as e:
            return Response(
                {"error": f"خطأ في توليد PDF: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["post"])
    def activate(self, request, pk=None):
        """Activate contract"""
        contract = self.get_object()
        contract.status = Contract.Status.ACTIVE
        contract.save()

        serializer = self.get_serializer(contract)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def complete(self, request, pk=None):
        """Complete contract"""
        contract = self.get_object()
        contract.status = Contract.Status.COMPLETED
        contract.save()

        serializer = self.get_serializer(contract)
        return Response(serializer.data)


class ContractClauseViewSet(viewsets.ModelViewSet):
    """Contract clause management viewset"""

    queryset = (
        ContractClause.objects.all()
        .select_related("created_by")
        .order_by("clause_type", "title")
    )
    permission_classes = [IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["clause_type", "is_mandatory", "is_active", "created_by"]
    search_fields = ["title", "content"]
    ordering_fields = ["title", "created_at", "clause_type"]
    ordering = ["clause_type", "title"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        return ContractClauseSerializer

    def get_queryset(self):
        """Filter queryset based on user role"""
        queryset = super().get_queryset()
        user = self.request.user

        # Admins and sales managers can see all clauses
        if user.role in ["admin", "sales_manager"]:
            return queryset

        # Other users can only see active clauses
        return queryset.filter(is_active=True)

    @action(detail=False, methods=["get"])
    def by_type(self, request):
        """Get clauses by type"""
        clause_type = request.query_params.get("type")
        if not clause_type:
            return Response(
                {"error": "نوع البند مطلوب"}, status=status.HTTP_400_BAD_REQUEST
            )

        clauses = self.get_queryset().filter(clause_type=clause_type, is_active=True)
        serializer = self.get_serializer(clauses, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def mandatory(self, request):
        """Get mandatory clauses"""
        clauses = self.get_queryset().filter(is_mandatory=True, is_active=True)
        serializer = self.get_serializer(clauses, many=True)
        return Response(serializer.data)


class ContractSignatureViewSet(viewsets.ModelViewSet):
    """Contract signature management viewset"""

    queryset = (
        ContractSignature.objects.all()
        .select_related("contract")
        .order_by("-created_at")
    )
    permission_classes = [IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["contract", "signature_type", "is_signed"]
    search_fields = ["signer_name", "signer_email", "contract__contract_number"]
    ordering_fields = ["signer_name", "created_at", "signed_at"]
    ordering = ["-created_at"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        return ContractSignatureSerializer

    def get_queryset(self):
        """Filter queryset based on user role"""
        queryset = super().get_queryset()
        user = self.request.user

        # Admins and sales managers can see all signatures
        if user.role in ["admin", "sales_manager"]:
            return queryset

        # Other users can only see signatures for contracts they're involved with
        return queryset.filter(
            Q(contract__assigned_to=user) | Q(contract__created_by=user)
        ).distinct()

    @action(detail=True, methods=["post"])
    def sign(self, request, pk=None):
        """Sign the contract"""
        signature = self.get_object()

        if signature.is_signed:
            return Response(
                {"error": "العقد موقع بالفعل"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get client IP
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")

        # Update signature
        signature.is_signed = True
        signature.signed_at = timezone.now()
        signature.ip_address = ip
        signature.signature_data = request.data.get("signature_data", "")
        signature.save()

        # Check if all required signatures are complete
        contract = signature.contract
        required_signatures = contract.signatures.filter(
            signature_type__in=[
                ContractSignature.SignatureType.CLIENT,
                ContractSignature.SignatureType.COMPANY,
            ]
        )

        if all(sig.is_signed for sig in required_signatures):
            contract.status = Contract.Status.ACTIVE
            contract.signed_date = timezone.now()
            contract.save()

        serializer = self.get_serializer(signature)
        return Response(serializer.data)


class ContractRenewalViewSet(viewsets.ModelViewSet):
    """Contract renewal management viewset"""

    queryset = (
        ContractRenewal.objects.all()
        .select_related("original_contract", "new_contract", "created_by")
        .order_by("-created_at")
    )
    permission_classes = [IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["original_contract", "new_contract", "status", "created_by"]
    search_fields = [
        "original_contract__contract_number",
        "new_contract__contract_number",
        "notes",
    ]
    ordering_fields = ["renewal_date", "created_at", "status"]
    ordering = ["-created_at"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        return ContractRenewalSerializer

    def get_queryset(self):
        """Filter queryset based on user role"""
        queryset = super().get_queryset()
        user = self.request.user

        # Admins and sales managers can see all renewals
        if user.role in ["admin", "sales_manager"]:
            return queryset

        # Other users can only see renewals for contracts they're involved with
        return queryset.filter(
            Q(original_contract__assigned_to=user)
            | Q(original_contract__created_by=user)
        ).distinct()

    @action(detail=True, methods=["post"])
    def approve(self, request, pk=None):
        """Approve renewal"""
        renewal = self.get_object()
        renewal.status = ContractRenewal.RenewalStatus.APPROVED
        renewal.save()

        serializer = self.get_serializer(renewal)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def reject(self, request, pk=None):
        """Reject renewal"""
        renewal = self.get_object()
        renewal.status = ContractRenewal.RenewalStatus.REJECTED
        renewal.save()

        serializer = self.get_serializer(renewal)
        return Response(serializer.data)
