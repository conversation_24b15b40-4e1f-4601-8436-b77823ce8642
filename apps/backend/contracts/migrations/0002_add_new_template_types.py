# Generated by Django 4.2.9 on 2025-06-05 19:25

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("contracts", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="contracttemplate",
            name="template_type",
            field=models.CharField(
                choices=[
                    ("website", "تطوير موقع إلكتروني"),
                    ("mobile_app", "تطبيق جوال"),
                    ("web_app", "تطبيق ويب"),
                    ("ecommerce", "متجر إلكتروني"),
                    ("maintenance", "صيانة وتطوير"),
                    ("marketing", "تسويق رقمي"),
                    ("branding", "هوية تجارية وتصميم"),
                    ("seo", "خدمات تحسين محركات البحث"),
                    ("social_media", "إدارة وسائل التواصل الاجتماعي"),
                    ("custom", "خدمة مخصصة"),
                ],
                default="website",
                max_length=20,
                verbose_name="نوع القالب",
            ),
        ),
        migrations.AlterField(
            model_name="historicalcontracttemplate",
            name="template_type",
            field=models.CharField(
                choices=[
                    ("website", "تطوير موقع إلكتروني"),
                    ("mobile_app", "تطبيق جوال"),
                    ("web_app", "تطبيق ويب"),
                    ("ecommerce", "متجر إلكتروني"),
                    ("maintenance", "صيانة وتطوير"),
                    ("marketing", "تسويق رقمي"),
                    ("branding", "هوية تجارية وتصميم"),
                    ("seo", "خدمات تحسين محركات البحث"),
                    ("social_media", "إدارة وسائل التواصل الاجتماعي"),
                    ("custom", "خدمة مخصصة"),
                ],
                default="website",
                max_length=20,
                verbose_name="نوع القالب",
            ),
        ),
    ]
