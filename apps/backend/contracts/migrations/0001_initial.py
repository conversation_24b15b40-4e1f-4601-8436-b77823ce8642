# Generated by Django 4.2.9 on 2025-06-05 18:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import djmoney.models.fields
import simple_history.models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("clients", "0003_alter_client_total_revenue_currency_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("projects", "0003_alter_historicalproject_actual_cost_currency_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Contract",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "contract_number",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="رقم العقد"
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="عنوان العقد")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "مسودة"),
                            ("pending_review", "في انتظار المراجعة"),
                            ("pending_signature", "في انتظار التوقيع"),
                            ("active", "نشط"),
                            ("completed", "مكتمل"),
                            ("cancelled", "ملغي"),
                            ("expired", "منتهي الصلاحية"),
                        ],
                        default="draft",
                        max_length=20,
                        verbose_name="الحالة",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "منخفض"),
                            ("medium", "متوسط"),
                            ("high", "عالي"),
                            ("urgent", "عاجل"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="الأولوية",
                    ),
                ),
                ("content", models.TextField(verbose_name="محتوى العقد")),
                (
                    "terms_conditions",
                    models.TextField(
                        blank=True, null=True, verbose_name="الشروط والأحكام"
                    ),
                ),
                (
                    "contract_value_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                        null=True,
                    ),
                ),
                (
                    "contract_value",
                    djmoney.models.fields.MoneyField(
                        blank=True,
                        decimal_places=2,
                        max_digits=14,
                        null=True,
                        verbose_name="قيمة العقد",
                    ),
                ),
                ("start_date", models.DateField(verbose_name="تاريخ البداية")),
                ("end_date", models.DateField(verbose_name="تاريخ النهاية")),
                (
                    "signed_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ التوقيع"
                    ),
                ),
                (
                    "pdf_file",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="contracts/pdfs/",
                        verbose_name="ملف PDF",
                    ),
                ),
                (
                    "signed_pdf_file",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="contracts/signed/",
                        verbose_name="ملف PDF موقع",
                    ),
                ),
                (
                    "auto_renewal",
                    models.BooleanField(default=False, verbose_name="تجديد تلقائي"),
                ),
                (
                    "renewal_period_months",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="فترة التجديد (شهور)"
                    ),
                ),
                (
                    "renewal_notice_days",
                    models.PositiveIntegerField(
                        default=30, verbose_name="أيام الإشعار قبل التجديد"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="ملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "assigned_to",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="assigned_contracts",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="المكلف بالعقد",
                    ),
                ),
                (
                    "client",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="contracts",
                        to="clients.client",
                        verbose_name="العميل",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_contracts",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="منشئ العقد",
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="contracts",
                        to="projects.project",
                        verbose_name="المشروع",
                    ),
                ),
            ],
            options={
                "verbose_name": "عقد",
                "verbose_name_plural": "العقود",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ContractTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="اسم القالب")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                (
                    "template_type",
                    models.CharField(
                        choices=[
                            ("website", "تطوير موقع إلكتروني"),
                            ("mobile_app", "تطبيق جوال"),
                            ("web_app", "تطبيق ويب"),
                            ("ecommerce", "متجر إلكتروني"),
                            ("maintenance", "صيانة"),
                            ("marketing", "تسويق رقمي"),
                            ("custom", "مخصص"),
                        ],
                        default="website",
                        max_length=20,
                        verbose_name="نوع القالب",
                    ),
                ),
                ("content", models.TextField(verbose_name="محتوى القالب")),
                ("is_active", models.BooleanField(default=True, verbose_name="نشط")),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_contract_templates",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="منشئ القالب",
                    ),
                ),
            ],
            options={
                "verbose_name": "قالب عقد",
                "verbose_name_plural": "قوالب العقود",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="HistoricalContractTemplate",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                ("name", models.CharField(max_length=200, verbose_name="اسم القالب")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                (
                    "template_type",
                    models.CharField(
                        choices=[
                            ("website", "تطوير موقع إلكتروني"),
                            ("mobile_app", "تطبيق جوال"),
                            ("web_app", "تطبيق ويب"),
                            ("ecommerce", "متجر إلكتروني"),
                            ("maintenance", "صيانة"),
                            ("marketing", "تسويق رقمي"),
                            ("custom", "مخصص"),
                        ],
                        default="website",
                        max_length=20,
                        verbose_name="نوع القالب",
                    ),
                ),
                ("content", models.TextField(verbose_name="محتوى القالب")),
                ("is_active", models.BooleanField(default=True, verbose_name="نشط")),
                (
                    "created_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ التحديث"
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="منشئ القالب",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "historical قالب عقد",
                "verbose_name_plural": "historical قوالب العقود",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="HistoricalContract",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "contract_number",
                    models.CharField(
                        db_index=True, max_length=50, verbose_name="رقم العقد"
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="عنوان العقد")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "مسودة"),
                            ("pending_review", "في انتظار المراجعة"),
                            ("pending_signature", "في انتظار التوقيع"),
                            ("active", "نشط"),
                            ("completed", "مكتمل"),
                            ("cancelled", "ملغي"),
                            ("expired", "منتهي الصلاحية"),
                        ],
                        default="draft",
                        max_length=20,
                        verbose_name="الحالة",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "منخفض"),
                            ("medium", "متوسط"),
                            ("high", "عالي"),
                            ("urgent", "عاجل"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="الأولوية",
                    ),
                ),
                ("content", models.TextField(verbose_name="محتوى العقد")),
                (
                    "terms_conditions",
                    models.TextField(
                        blank=True, null=True, verbose_name="الشروط والأحكام"
                    ),
                ),
                (
                    "contract_value_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                        null=True,
                    ),
                ),
                (
                    "contract_value",
                    djmoney.models.fields.MoneyField(
                        blank=True,
                        decimal_places=2,
                        max_digits=14,
                        null=True,
                        verbose_name="قيمة العقد",
                    ),
                ),
                ("start_date", models.DateField(verbose_name="تاريخ البداية")),
                ("end_date", models.DateField(verbose_name="تاريخ النهاية")),
                (
                    "signed_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ التوقيع"
                    ),
                ),
                (
                    "pdf_file",
                    models.TextField(
                        blank=True, max_length=100, null=True, verbose_name="ملف PDF"
                    ),
                ),
                (
                    "signed_pdf_file",
                    models.TextField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="ملف PDF موقع",
                    ),
                ),
                (
                    "auto_renewal",
                    models.BooleanField(default=False, verbose_name="تجديد تلقائي"),
                ),
                (
                    "renewal_period_months",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="فترة التجديد (شهور)"
                    ),
                ),
                (
                    "renewal_notice_days",
                    models.PositiveIntegerField(
                        default=30, verbose_name="أيام الإشعار قبل التجديد"
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="ملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        blank=True,
                        db_index=True,
                        editable=False,
                        verbose_name="تاريخ الإنشاء",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ التحديث"
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "assigned_to",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="المكلف بالعقد",
                    ),
                ),
                (
                    "client",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="clients.client",
                        verbose_name="العميل",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="منشئ العقد",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="projects.project",
                        verbose_name="المشروع",
                    ),
                ),
                (
                    "template",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="contracts.contracttemplate",
                        verbose_name="القالب المستخدم",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical عقد",
                "verbose_name_plural": "historical العقود",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="ContractSignature",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "signer_name",
                    models.CharField(max_length=200, verbose_name="اسم الموقع"),
                ),
                (
                    "signer_email",
                    models.EmailField(
                        max_length=254, verbose_name="بريد الموقع الإلكتروني"
                    ),
                ),
                (
                    "signature_type",
                    models.CharField(
                        choices=[
                            ("client", "العميل"),
                            ("company", "الشركة"),
                            ("witness", "شاهد"),
                        ],
                        max_length=10,
                        verbose_name="نوع التوقيع",
                    ),
                ),
                (
                    "signed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ التوقيع"
                    ),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="عنوان IP"
                    ),
                ),
                (
                    "signature_data",
                    models.TextField(
                        blank=True, null=True, verbose_name="بيانات التوقيع"
                    ),
                ),
                ("is_signed", models.BooleanField(default=False, verbose_name="موقع")),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "contract",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="signatures",
                        to="contracts.contract",
                        verbose_name="العقد",
                    ),
                ),
            ],
            options={
                "verbose_name": "توقيع عقد",
                "verbose_name_plural": "توقيعات العقود",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ContractRenewal",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("renewal_date", models.DateField(verbose_name="تاريخ التجديد")),
                ("new_end_date", models.DateField(verbose_name="تاريخ النهاية الجديد")),
                (
                    "new_value_currency",
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ("EGP", "جنيه مصري (EGP)"),
                            ("USD", "دولار أمريكي (USD)"),
                        ],
                        default="EGP",
                        editable=False,
                        max_length=3,
                        null=True,
                    ),
                ),
                (
                    "new_value",
                    djmoney.models.fields.MoneyField(
                        blank=True,
                        decimal_places=2,
                        max_digits=14,
                        null=True,
                        verbose_name="القيمة الجديدة",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "في الانتظار"),
                            ("approved", "موافق عليه"),
                            ("rejected", "مرفوض"),
                            ("completed", "مكتمل"),
                        ],
                        default="pending",
                        max_length=10,
                        verbose_name="حالة التجديد",
                    ),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="ملاحظات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_renewals",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="منشئ التجديد",
                    ),
                ),
                (
                    "new_contract",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="renewal_source",
                        to="contracts.contract",
                        verbose_name="العقد الجديد",
                    ),
                ),
                (
                    "original_contract",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="renewals",
                        to="contracts.contract",
                        verbose_name="العقد الأصلي",
                    ),
                ),
            ],
            options={
                "verbose_name": "تجديد عقد",
                "verbose_name_plural": "تجديدات العقود",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ContractClause",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="عنوان البند")),
                ("content", models.TextField(verbose_name="محتوى البند")),
                (
                    "clause_type",
                    models.CharField(
                        choices=[
                            ("payment", "الدفع"),
                            ("delivery", "التسليم"),
                            ("warranty", "الضمان"),
                            ("cancellation", "الإلغاء"),
                            ("ip", "الملكية الفكرية"),
                            ("confidentiality", "السرية"),
                            ("limitation", "تحديد المسؤولية"),
                            ("general", "عام"),
                        ],
                        default="general",
                        max_length=20,
                        verbose_name="نوع البند",
                    ),
                ),
                (
                    "is_mandatory",
                    models.BooleanField(default=False, verbose_name="إجباري"),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="نشط")),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_contract_clauses",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="منشئ البند",
                    ),
                ),
            ],
            options={
                "verbose_name": "بند عقد",
                "verbose_name_plural": "بنود العقود",
                "ordering": ["clause_type", "title"],
            },
        ),
        migrations.AddField(
            model_name="contract",
            name="template",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="contracts",
                to="contracts.contracttemplate",
                verbose_name="القالب المستخدم",
            ),
        ),
        migrations.AddIndex(
            model_name="contracttemplate",
            index=models.Index(
                fields=["template_type", "is_active"],
                name="contracts_c_templat_6aadaf_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="contracttemplate",
            index=models.Index(
                fields=["created_by"], name="contracts_c_created_849a46_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="contractsignature",
            unique_together={("contract", "signature_type")},
        ),
        migrations.AddIndex(
            model_name="contractclause",
            index=models.Index(
                fields=["clause_type", "is_active"],
                name="contracts_c_clause__9f7fdb_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="contract",
            index=models.Index(
                fields=["status", "priority"], name="contracts_c_status_4fc406_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="contract",
            index=models.Index(
                fields=["client", "status"], name="contracts_c_client__c0be66_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="contract",
            index=models.Index(
                fields=["assigned_to", "status"], name="contracts_c_assigne_e2f00b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="contract",
            index=models.Index(
                fields=["start_date"], name="contracts_c_start_d_4c01aa_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="contract",
            index=models.Index(
                fields=["end_date"], name="contracts_c_end_dat_dedd36_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="contract",
            index=models.Index(
                fields=["contract_number"], name="contracts_c_contrac_876f48_idx"
            ),
        ),
    ]
