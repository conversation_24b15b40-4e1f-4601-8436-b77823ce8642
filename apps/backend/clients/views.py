from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Count, Q, Sum
from django.utils import timezone
from datetime import datetime, timedelta
from mtbrmg_erp.cache import CacheService, ListCache, DashboardCache
from .models import Client, ClientCommunication
from .serializers import (
    ClientSerializer,
    ClientListSerializer,
    ClientCreateSerializer,
    ClientDetailSerializer,
    ClientCommunicationSerializer,
    ClientCommunicationCreateSerializer,
    ClientStatsSerializer
)


class ClientViewSet(viewsets.ModelViewSet):
    """Client management viewset"""
    queryset = Client.objects.all().select_related('sales_rep').order_by('-created_at')
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['mood', 'governorate', 'sales_rep']
    search_fields = ['name', 'email', 'company', 'phone']
    ordering_fields = ['name', 'created_at', 'total_revenue', 'total_projects', 'last_contact_date']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ClientListSerializer
        elif self.action == 'create':
            return ClientCreateSerializer
        elif self.action == 'retrieve':
            return ClientDetailSerializer
        return ClientSerializer

    def get_queryset(self):
        """Filter queryset based on user role"""
        queryset = super().get_queryset()
        user = self.request.user

        # Sales managers and admins can see all clients
        if user.role in ['admin', 'sales_manager']:
            return queryset

        # Other users can only see clients assigned to them
        return queryset.filter(sales_rep=user)

    def list(self, request, *args, **kwargs):
        """List clients with caching"""
        # Generate cache key based on user and query parameters
        cache_key = ListCache.get_list_cache_key(
            'clients',
            request.user.id,
            dict(request.GET.items())
        )

        # Try to get from cache
        cached_data = ListCache.get_cached_list(
            'clients',
            request.user.id,
            dict(request.GET.items())
        )

        if cached_data is not None:
            return Response(cached_data)

        # If not in cache, get from database
        response = super().list(request, *args, **kwargs)

        # Cache the response data
        ListCache.cache_list(
            'clients',
            request.user.id,
            response.data,
            dict(request.GET.items())
        )

        return response

    def perform_create(self, serializer):
        """Set sales rep to current user if not specified"""
        if not serializer.validated_data.get('sales_rep_id'):
            serializer.save(sales_rep=self.request.user)
        else:
            serializer.save()

    @action(detail=True, methods=['post'])
    def add_communication(self, request, pk=None):
        """Add communication record for client"""
        client = self.get_object()
        serializer = ClientCommunicationCreateSerializer(
            data=request.data,
            context={'client': client, 'request': request}
        )

        if serializer.is_valid():
            communication = serializer.save()

            # Update last contact date
            client.last_contact_date = timezone.now()
            client.save(update_fields=['last_contact_date'])

            return Response({
                'communication': ClientCommunicationSerializer(communication).data,
                'message': 'تم إضافة سجل التواصل بنجاح'
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def communications(self, request, pk=None):
        """Get all communications for client"""
        client = self.get_object()
        communications = client.communications.all().order_by('-created_at')
        serializer = ClientCommunicationSerializer(communications, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get client statistics with caching"""
        # Try to get from cache first
        cached_stats = DashboardCache.get_cached_metrics(request.user.id, {'type': 'client_stats'})
        if cached_stats is not None:
            return Response(cached_stats)

        queryset = self.get_queryset()

        # Basic counts
        total_clients = queryset.count()

        # New clients this month
        this_month = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        new_this_month = queryset.filter(created_at__gte=this_month).count()

        # Mood distribution
        mood_counts = queryset.values('mood').annotate(count=Count('id'))
        mood_distribution = {item['mood']: item['count'] for item in mood_counts}

        # Revenue stats
        total_revenue = queryset.aggregate(total=Sum('total_revenue'))['total'] or 0

        # Average projects per client
        avg_projects = queryset.aggregate(avg=Count('projects'))['avg'] or 0

        # Top governorates
        top_governorates = list(
            queryset.values('governorate')
            .annotate(count=Count('id'))
            .order_by('-count')[:5]
        )

        stats_data = {
            'total_clients': total_clients,
            'new_this_month': new_this_month,
            'happy_clients': mood_distribution.get('happy', 0),
            'concerned_clients': mood_distribution.get('concerned', 0),
            'angry_clients': mood_distribution.get('angry', 0),
            'total_revenue': total_revenue,
            'avg_projects_per_client': avg_projects,
            'top_governorates': top_governorates,
            'mood_distribution': mood_distribution
        }

        serializer = ClientStatsSerializer(stats_data)
        response_data = serializer.data

        # Cache the stats
        DashboardCache.cache_metrics(request.user.id, response_data, {'type': 'client_stats'})

        return Response(response_data)
