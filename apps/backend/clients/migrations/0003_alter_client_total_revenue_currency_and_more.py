# Generated by Django 4.2.9 on 2025-06-04 21:17

from django.db import migrations
import djmoney.models.fields


class Migration(migrations.Migration):
    dependencies = [
        ("clients", "0002_alter_client_created_at_alter_client_email_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="client",
            name="total_revenue_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="historicalclient",
            name="total_revenue_currency",
            field=djmoney.models.fields.CurrencyField(
                choices=[("EGP", "جنيه مصري (EGP)"), ("USD", "دولار أمريكي (USD)")],
                default="EGP",
                editable=False,
                max_length=3,
            ),
        ),
    ]
