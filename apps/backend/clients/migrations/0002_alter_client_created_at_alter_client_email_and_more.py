# Generated by Django 4.2.9 on 2025-06-03 15:09

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("clients", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="client",
            name="created_at",
            field=models.DateTimeField(
                auto_now_add=True, db_index=True, verbose_name="تاريخ الإنشاء"
            ),
        ),
        migrations.AlterField(
            model_name="client",
            name="email",
            field=models.EmailField(
                db_index=True, max_length=254, verbose_name="البريد الإلكتروني"
            ),
        ),
        migrations.AlterField(
            model_name="historicalclient",
            name="created_at",
            field=models.DateTimeField(
                blank=True, db_index=True, editable=False, verbose_name="تاريخ الإنشاء"
            ),
        ),
        migrations.AlterField(
            model_name="historicalclient",
            name="email",
            field=models.EmailField(
                db_index=True, max_length=254, verbose_name="البريد الإلكتروني"
            ),
        ),
        migrations.AddIndex(
            model_name="client",
            index=models.Index(
                fields=["mood", "governorate"], name="client_mood_gov_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="client",
            index=models.Index(
                fields=["sales_rep", "created_at"], name="client_sales_created_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="client",
            index=models.Index(fields=["mood"], name="client_mood_idx"),
        ),
    ]
