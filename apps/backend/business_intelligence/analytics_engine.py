"""
Business Intelligence Analytics Engine
Core calculation and data processing engine for BI module
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

from django.db.models import Count, Sum, Avg, Q, F, Case, When, Value, DecimalField
from django.utils import timezone
from datetime import datetime, timedelta, date
from decimal import Decimal
from typing import Dict, List, Any, Optional
import json

from clients.models import Client
from projects.models import Project
from tasks.models import Task
from finance.models import Invoice, Commission
from team.models import User
from .models import KPIMetric, DashboardWidget


class AnalyticsEngine:
    """Core analytics engine for business intelligence calculations"""

    @staticmethod
    def get_date_range(
        period_type: str, start_date: date = None, end_date: date = None
    ) -> tuple:
        """Get date range based on period type"""
        today = timezone.now().date()

        if period_type == "daily":
            start = today
            end = today
        elif period_type == "weekly":
            start = today - timedelta(days=today.weekday())
            end = start + timedelta(days=6)
        elif period_type == "monthly":
            start = today.replace(day=1)
            if today.month == 12:
                end = today.replace(year=today.year + 1, month=1, day=1) - timedelta(
                    days=1
                )
            else:
                end = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
        elif period_type == "quarterly":
            quarter = (today.month - 1) // 3 + 1
            start = today.replace(month=(quarter - 1) * 3 + 1, day=1)
            if quarter == 4:
                end = today.replace(year=today.year + 1, month=1, day=1) - timedelta(
                    days=1
                )
            else:
                end = today.replace(month=quarter * 3 + 1, day=1) - timedelta(days=1)
        elif period_type == "yearly":
            start = today.replace(month=1, day=1)
            end = today.replace(month=12, day=31)
        elif period_type == "custom" and start_date and end_date:
            start = start_date
            end = end_date
        else:
            # Default to current month
            start = today.replace(day=1)
            if today.month == 12:
                end = today.replace(year=today.year + 1, month=1, day=1) - timedelta(
                    days=1
                )
            else:
                end = today.replace(month=today.month + 1, day=1) - timedelta(days=1)

        return start, end

    @classmethod
    def calculate_kpi_value(cls, kpi: KPIMetric) -> Optional[Decimal]:
        """Calculate KPI value based on metric type"""
        try:
            if kpi.metric_type == KPIMetric.MetricType.REVENUE:
                return cls._calculate_revenue_kpi(kpi)
            elif kpi.metric_type == KPIMetric.MetricType.PROFIT:
                return cls._calculate_profit_kpi(kpi)
            elif kpi.metric_type == KPIMetric.MetricType.CLIENT_COUNT:
                return cls._calculate_client_count_kpi(kpi)
            elif kpi.metric_type == KPIMetric.MetricType.PROJECT_COUNT:
                return cls._calculate_project_count_kpi(kpi)
            elif kpi.metric_type == KPIMetric.MetricType.TASK_COMPLETION:
                return cls._calculate_task_completion_kpi(kpi)
            elif kpi.metric_type == KPIMetric.MetricType.TEAM_PRODUCTIVITY:
                return cls._calculate_team_productivity_kpi(kpi)
            elif kpi.metric_type == KPIMetric.MetricType.CLIENT_SATISFACTION:
                return cls._calculate_client_satisfaction_kpi(kpi)
            elif kpi.metric_type == KPIMetric.MetricType.CONVERSION_RATE:
                return cls._calculate_conversion_rate_kpi(kpi)
            elif kpi.metric_type == KPIMetric.MetricType.AVERAGE_PROJECT_VALUE:
                return cls._calculate_avg_project_value_kpi(kpi)
            else:
                return None
        except Exception as e:
            print(f"Error calculating KPI {kpi.name}: {str(e)}")
            return None

    @classmethod
    def _calculate_revenue_kpi(cls, kpi: KPIMetric) -> Decimal:
        """Calculate revenue KPI"""
        # Get date range based on frequency
        if kpi.frequency == KPIMetric.Frequency.DAILY:
            start_date, end_date = cls.get_date_range("daily")
        elif kpi.frequency == KPIMetric.Frequency.WEEKLY:
            start_date, end_date = cls.get_date_range("weekly")
        elif kpi.frequency == KPIMetric.Frequency.MONTHLY:
            start_date, end_date = cls.get_date_range("monthly")
        else:
            start_date, end_date = cls.get_date_range("monthly")

        # Calculate revenue from paid invoices
        revenue = Invoice.objects.filter(
            payment_status="paid", payment_date__range=[start_date, end_date]
        ).aggregate(total=Sum("total_amount_egp"))["total"] or Decimal("0")

        return revenue

    @classmethod
    def _calculate_profit_kpi(cls, kpi: KPIMetric) -> Decimal:
        """Calculate profit KPI (revenue - commissions - expenses)"""
        # Get revenue
        revenue = cls._calculate_revenue_kpi(kpi)

        # Get date range
        if kpi.frequency == KPIMetric.Frequency.DAILY:
            start_date, end_date = cls.get_date_range("daily")
        elif kpi.frequency == KPIMetric.Frequency.WEEKLY:
            start_date, end_date = cls.get_date_range("weekly")
        elif kpi.frequency == KPIMetric.Frequency.MONTHLY:
            start_date, end_date = cls.get_date_range("monthly")
        else:
            start_date, end_date = cls.get_date_range("monthly")

        # Calculate commissions
        commissions = Commission.objects.filter(
            payment_status="paid", payment_date__range=[start_date, end_date]
        ).aggregate(total=Sum("amount_egp"))["total"] or Decimal("0")

        # For now, profit = revenue - commissions
        # TODO: Add expense tracking when expense module is implemented
        profit = revenue - commissions

        return profit

    @classmethod
    def _calculate_client_count_kpi(cls, kpi: KPIMetric) -> Decimal:
        """Calculate client count KPI"""
        if kpi.frequency == KPIMetric.Frequency.DAILY:
            start_date, end_date = cls.get_date_range("daily")
            # New clients today
            count = Client.objects.filter(
                created_at__date__range=[start_date, end_date]
            ).count()
        elif kpi.frequency == KPIMetric.Frequency.MONTHLY:
            start_date, end_date = cls.get_date_range("monthly")
            # New clients this month
            count = Client.objects.filter(
                created_at__date__range=[start_date, end_date]
            ).count()
        else:
            # Total active clients
            count = Client.objects.filter(is_active=True).count()

        return Decimal(str(count))

    @classmethod
    def _calculate_project_count_kpi(cls, kpi: KPIMetric) -> Decimal:
        """Calculate project count KPI"""
        if kpi.frequency == KPIMetric.Frequency.DAILY:
            start_date, end_date = cls.get_date_range("daily")
            count = Project.objects.filter(
                created_at__date__range=[start_date, end_date]
            ).count()
        elif kpi.frequency == KPIMetric.Frequency.MONTHLY:
            start_date, end_date = cls.get_date_range("monthly")
            count = Project.objects.filter(
                created_at__date__range=[start_date, end_date]
            ).count()
        else:
            # Active projects
            count = Project.objects.filter(
                status__in=["planning", "in_progress", "review"]
            ).count()

        return Decimal(str(count))

    @classmethod
    def _calculate_task_completion_kpi(cls, kpi: KPIMetric) -> Decimal:
        """Calculate task completion rate KPI"""
        if kpi.frequency == KPIMetric.Frequency.DAILY:
            start_date, end_date = cls.get_date_range("daily")
        elif kpi.frequency == KPIMetric.Frequency.WEEKLY:
            start_date, end_date = cls.get_date_range("weekly")
        elif kpi.frequency == KPIMetric.Frequency.MONTHLY:
            start_date, end_date = cls.get_date_range("monthly")
        else:
            start_date, end_date = cls.get_date_range("weekly")

        # Get tasks in date range
        tasks = Task.objects.filter(created_at__date__range=[start_date, end_date])

        total_tasks = tasks.count()
        if total_tasks == 0:
            return Decimal("0")

        completed_tasks = tasks.filter(status="completed").count()
        completion_rate = (completed_tasks / total_tasks) * 100

        return Decimal(str(round(completion_rate, 2)))

    @classmethod
    def _calculate_team_productivity_kpi(cls, kpi: KPIMetric) -> Decimal:
        """Calculate team productivity KPI"""
        if kpi.frequency == KPIMetric.Frequency.DAILY:
            start_date, end_date = cls.get_date_range("daily")
        elif kpi.frequency == KPIMetric.Frequency.WEEKLY:
            start_date, end_date = cls.get_date_range("weekly")
        elif kpi.frequency == KPIMetric.Frequency.MONTHLY:
            start_date, end_date = cls.get_date_range("monthly")
        else:
            start_date, end_date = cls.get_date_range("weekly")

        # Calculate average tasks completed per team member
        team_members = User.objects.filter(
            role__in=["developer", "web_designer", "media_buyer", "sales_manager"]
        )

        total_members = team_members.count()
        if total_members == 0:
            return Decimal("0")

        total_completed_tasks = Task.objects.filter(
            assigned_to__in=team_members,
            status="completed",
            completed_at__date__range=[start_date, end_date],
        ).count()

        productivity = total_completed_tasks / total_members
        return Decimal(str(round(productivity, 2)))

    @classmethod
    def _calculate_client_satisfaction_kpi(cls, kpi: KPIMetric) -> Decimal:
        """Calculate client satisfaction KPI"""
        # Get average mood score from clients
        avg_mood = Client.objects.filter(is_active=True).aggregate(
            avg_mood=Avg("mood_score")
        )["avg_mood"]

        if avg_mood is None:
            return Decimal("0")

        # Convert mood score (1-5) to percentage
        satisfaction_percentage = (avg_mood / 5) * 100
        return Decimal(str(round(satisfaction_percentage, 2)))

    @classmethod
    def _calculate_conversion_rate_kpi(cls, kpi: KPIMetric) -> Decimal:
        """Calculate lead to client conversion rate KPI"""
        if kpi.frequency == KPIMetric.Frequency.MONTHLY:
            start_date, end_date = cls.get_date_range("monthly")
        else:
            start_date, end_date = cls.get_date_range("quarterly")

        # For now, calculate project to completion rate
        # TODO: Implement proper lead tracking when marketing module is added
        total_projects = Project.objects.filter(
            created_at__date__range=[start_date, end_date]
        ).count()

        if total_projects == 0:
            return Decimal("0")

        completed_projects = Project.objects.filter(
            created_at__date__range=[start_date, end_date], status="completed"
        ).count()

        conversion_rate = (completed_projects / total_projects) * 100
        return Decimal(str(round(conversion_rate, 2)))

    @classmethod
    def _calculate_avg_project_value_kpi(cls, kpi: KPIMetric) -> Decimal:
        """Calculate average project value KPI"""
        if kpi.frequency == KPIMetric.Frequency.MONTHLY:
            start_date, end_date = cls.get_date_range("monthly")
        else:
            start_date, end_date = cls.get_date_range("quarterly")

        avg_value = Project.objects.filter(
            created_at__date__range=[start_date, end_date]
        ).aggregate(avg_value=Avg("budget_egp"))["avg_value"]

        return Decimal(str(avg_value or 0))

    @classmethod
    def get_widget_data(cls, widget: DashboardWidget) -> Dict[str, Any]:
        """Get data for dashboard widget"""
        try:
            if widget.widget_type == DashboardWidget.WidgetType.KPI_CARD:
                return cls._get_kpi_card_data(widget)
            elif widget.widget_type == DashboardWidget.WidgetType.CHART:
                return cls._get_chart_data(widget)
            elif widget.widget_type == DashboardWidget.WidgetType.TABLE:
                return cls._get_table_data(widget)
            elif widget.widget_type == DashboardWidget.WidgetType.PROGRESS_BAR:
                return cls._get_progress_bar_data(widget)
            else:
                return {"error": "نوع ودجت غير مدعوم"}
        except Exception as e:
            return {"error": f"خطأ في تحميل البيانات: {str(e)}"}

    @classmethod
    def _get_kpi_card_data(cls, widget: DashboardWidget) -> Dict[str, Any]:
        """Get KPI card widget data"""
        data_source = widget.data_source

        if data_source == "revenue":
            value = cls._calculate_revenue_kpi(None)
            return {
                "title": "الإيرادات",
                "value": float(value),
                "unit": "currency",
                "currency": "EGP",
                "trend": "up",  # TODO: Calculate actual trend
                "change": 12.5,  # TODO: Calculate actual change
            }
        elif data_source == "clients":
            value = Client.objects.filter(is_active=True).count()
            return {
                "title": "العملاء النشطين",
                "value": value,
                "unit": "number",
                "trend": "up",
                "change": 5,
            }
        elif data_source == "projects":
            value = Project.objects.filter(
                status__in=["planning", "in_progress", "review"]
            ).count()
            return {
                "title": "المشاريع النشطة",
                "value": value,
                "unit": "number",
                "trend": "stable",
                "change": 0,
            }
        else:
            return {"error": "مصدر بيانات غير معروف"}

    @classmethod
    def _get_chart_data(cls, widget: DashboardWidget) -> Dict[str, Any]:
        """Get chart widget data"""
        # This is a simplified implementation
        # In a real scenario, you would implement specific chart data based on data_source
        return {
            "type": widget.chart_type,
            "data": {
                "labels": ["يناير", "فبراير", "مارس", "أبريل", "مايو"],
                "datasets": [
                    {
                        "label": "الإيرادات",
                        "data": [12000, 15000, 18000, 16000, 20000],
                        "backgroundColor": "#3B82F6",
                    }
                ],
            },
        }

    @classmethod
    def _get_table_data(cls, widget: DashboardWidget) -> Dict[str, Any]:
        """Get table widget data"""
        # This is a simplified implementation
        return {
            "headers": ["العميل", "المشروع", "القيمة", "الحالة"],
            "rows": [
                ["شركة ABC", "موقع إلكتروني", "15000 ج.م", "قيد التنفيذ"],
                ["شركة XYZ", "تطبيق موبايل", "25000 ج.م", "مكتمل"],
            ],
        }

    @classmethod
    def _get_progress_bar_data(cls, widget: DashboardWidget) -> Dict[str, Any]:
        """Get progress bar widget data"""
        # This is a simplified implementation
        return {
            "title": "تقدم المشاريع",
            "current": 75,
            "target": 100,
            "unit": "percentage",
        }

    @classmethod
    def get_revenue_trend_data(
        cls, period_type: str = "monthly", periods: int = 12
    ) -> List[Dict[str, Any]]:
        """Get revenue trend data for charts"""
        trend_data = []
        today = timezone.now().date()

        for i in range(periods):
            if period_type == "monthly":
                # Calculate month start/end
                if today.month - i <= 0:
                    year = today.year - 1
                    month = 12 + (today.month - i)
                else:
                    year = today.year
                    month = today.month - i

                start_date = date(year, month, 1)
                if month == 12:
                    end_date = date(year + 1, 1, 1) - timedelta(days=1)
                else:
                    end_date = date(year, month + 1, 1) - timedelta(days=1)

                # Calculate revenue for this month
                revenue = Invoice.objects.filter(
                    payment_status="paid", payment_date__range=[start_date, end_date]
                ).aggregate(total=Sum("total_amount_egp"))["total"] or Decimal("0")

                trend_data.append(
                    {
                        "period": f"{year}-{month:02d}",
                        "period_display": f"{cls._get_month_name(month)} {year}",
                        "revenue": float(revenue),
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat(),
                    }
                )

        return list(reversed(trend_data))

    @classmethod
    def get_revenue_by_source_data(
        cls, period_type: str = "monthly"
    ) -> List[Dict[str, Any]]:
        """Get revenue breakdown by source/service type"""
        start_date, end_date = cls.get_date_range(period_type)

        # Get revenue by project type (assuming projects have service_type field)
        revenue_by_source = []

        # Web Development
        web_revenue = Invoice.objects.filter(
            payment_status="paid",
            payment_date__range=[start_date, end_date],
            project__service_type="web_development",
        ).aggregate(total=Sum("total_amount_egp"))["total"] or Decimal("0")

        # Mobile App Development
        mobile_revenue = Invoice.objects.filter(
            payment_status="paid",
            payment_date__range=[start_date, end_date],
            project__service_type="mobile_app",
        ).aggregate(total=Sum("total_amount_egp"))["total"] or Decimal("0")

        # Digital Marketing
        marketing_revenue = Invoice.objects.filter(
            payment_status="paid",
            payment_date__range=[start_date, end_date],
            project__service_type="digital_marketing",
        ).aggregate(total=Sum("total_amount_egp"))["total"] or Decimal("0")

        # Branding & Design
        branding_revenue = Invoice.objects.filter(
            payment_status="paid",
            payment_date__range=[start_date, end_date],
            project__service_type="branding",
        ).aggregate(total=Sum("total_amount_egp"))["total"] or Decimal("0")

        revenue_by_source = [
            {
                "source": "تطوير المواقع",
                "revenue": float(web_revenue),
                "color": "#3B82F6",
            },
            {
                "source": "تطبيقات الموبايل",
                "revenue": float(mobile_revenue),
                "color": "#10B981",
            },
            {
                "source": "التسويق الرقمي",
                "revenue": float(marketing_revenue),
                "color": "#F59E0B",
            },
            {
                "source": "الهوية البصرية",
                "revenue": float(branding_revenue),
                "color": "#EF4444",
            },
        ]

        return revenue_by_source

    @classmethod
    def get_client_acquisition_trend(cls, periods: int = 12) -> List[Dict[str, Any]]:
        """Get client acquisition trend data"""
        trend_data = []
        today = timezone.now().date()

        for i in range(periods):
            # Calculate month start/end
            if today.month - i <= 0:
                year = today.year - 1
                month = 12 + (today.month - i)
            else:
                year = today.year
                month = today.month - i

            start_date = date(year, month, 1)
            if month == 12:
                end_date = date(year + 1, 1, 1) - timedelta(days=1)
            else:
                end_date = date(year, month + 1, 1) - timedelta(days=1)

            # Count new clients in this month
            new_clients = Client.objects.filter(
                created_at__date__range=[start_date, end_date]
            ).count()

            trend_data.append(
                {
                    "period": f"{year}-{month:02d}",
                    "period_display": f"{cls._get_month_name(month)} {year}",
                    "new_clients": new_clients,
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                }
            )

        return list(reversed(trend_data))

    @classmethod
    def get_top_clients_data(
        cls, period_type: str = "monthly", limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get top clients by revenue"""
        start_date, end_date = cls.get_date_range(period_type)

        # Get clients with their total revenue in the period
        top_clients = (
            Client.objects.filter(
                projects__invoices__payment_status="paid",
                projects__invoices__payment_date__range=[start_date, end_date],
            )
            .annotate(total_revenue=Sum("projects__invoices__total_amount_egp"))
            .order_by("-total_revenue")[:limit]
        )

        client_data = []
        for client in top_clients:
            client_data.append(
                {
                    "id": client.id,
                    "name": client.name,
                    "company": client.company_name or "",
                    "total_revenue": float(client.total_revenue or 0),
                    "projects_count": client.projects.count(),
                    "last_project_date": (
                        client.projects.order_by("-created_at")
                        .first()
                        .created_at.date()
                        .isoformat()
                        if client.projects.exists()
                        else None
                    ),
                }
            )

        return client_data

    @classmethod
    def get_project_status_distribution(cls) -> List[Dict[str, Any]]:
        """Get project status distribution data"""
        status_distribution = (
            Project.objects.values("status")
            .annotate(count=Count("id"))
            .order_by("-count")
        )

        status_labels = {
            "planning": "التخطيط",
            "in_progress": "قيد التنفيذ",
            "review": "المراجعة",
            "completed": "مكتمل",
            "on_hold": "متوقف",
            "cancelled": "ملغي",
        }

        colors = {
            "planning": "#3B82F6",
            "in_progress": "#F59E0B",
            "review": "#8B5CF6",
            "completed": "#10B981",
            "on_hold": "#6B7280",
            "cancelled": "#EF4444",
        }

        distribution_data = []
        for item in status_distribution:
            distribution_data.append(
                {
                    "status": item["status"],
                    "status_display": status_labels.get(item["status"], item["status"]),
                    "count": item["count"],
                    "color": colors.get(item["status"], "#6B7280"),
                }
            )

        return distribution_data

    @classmethod
    def _get_month_name(cls, month: int) -> str:
        """Get Arabic month name"""
        month_names = {
            1: "يناير",
            2: "فبراير",
            3: "مارس",
            4: "أبريل",
            5: "مايو",
            6: "يونيو",
            7: "يوليو",
            8: "أغسطس",
            9: "سبتمبر",
            10: "أكتوبر",
            11: "نوفمبر",
            12: "ديسمبر",
        }
        return month_names.get(month, str(month))
