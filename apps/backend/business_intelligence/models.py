"""
Business Intelligence & Analytics Models
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

from django.db import models
from django.conf import settings
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from simple_history.models import HistoricalRecords
from djmoney.models.fields import MoneyField
from datetime import datetime, timedelta
import json


class AnalyticsReport(models.Model):
    """Model for storing generated analytics reports"""

    class ReportType(models.TextChoices):
        EXECUTIVE_DASHBOARD = 'executive_dashboard', 'لوحة المدير التنفيذي'
        REVENUE_ANALYSIS = 'revenue_analysis', 'تحليل الإيرادات'
        CLIENT_ANALYSIS = 'client_analysis', 'تحليل العملاء'
        PROJECT_PERFORMANCE = 'project_performance', 'أداء المشاريع'
        TEAM_PERFORMANCE = 'team_performance', 'أداء الفريق'
        FINANCIAL_OVERVIEW = 'financial_overview', 'نظرة مالية شاملة'
        CUSTOM_REPORT = 'custom_report', 'تقرير مخصص'

    class Status(models.TextChoices):
        GENERATING = 'generating', 'قيد الإنشاء'
        COMPLETED = 'completed', 'مكتمل'
        FAILED = 'failed', 'فشل'
        SCHEDULED = 'scheduled', 'مجدول'

    class Period(models.TextChoices):
        DAILY = 'daily', 'يومي'
        WEEKLY = 'weekly', 'أسبوعي'
        MONTHLY = 'monthly', 'شهري'
        QUARTERLY = 'quarterly', 'ربع سنوي'
        YEARLY = 'yearly', 'سنوي'
        CUSTOM = 'custom', 'مخصص'

    # Basic Information
    title = models.CharField(max_length=200, verbose_name='عنوان التقرير')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    report_type = models.CharField(
        max_length=20,
        choices=ReportType.choices,
        default=ReportType.EXECUTIVE_DASHBOARD,
        verbose_name='نوع التقرير'
    )
    status = models.CharField(
        max_length=15,
        choices=Status.choices,
        default=Status.GENERATING,
        verbose_name='الحالة'
    )

    # User and Permissions
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_reports',
        verbose_name='منشئ التقرير'
    )
    shared_with = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        blank=True,
        related_name='shared_reports',
        verbose_name='مشارك مع'
    )

    # Time Period
    period_type = models.CharField(
        max_length=15,
        choices=Period.choices,
        default=Period.MONTHLY,
        verbose_name='نوع الفترة'
    )
    start_date = models.DateField(verbose_name='تاريخ البداية')
    end_date = models.DateField(verbose_name='تاريخ النهاية')

    # Report Data
    data = models.JSONField(default=dict, verbose_name='بيانات التقرير')
    filters = models.JSONField(default=dict, verbose_name='المرشحات')
    configuration = models.JSONField(default=dict, verbose_name='الإعدادات')

    # File Management
    pdf_file = models.FileField(
        upload_to='reports/pdfs/',
        blank=True,
        null=True,
        verbose_name='ملف PDF'
    )
    excel_file = models.FileField(
        upload_to='reports/excel/',
        blank=True,
        null=True,
        verbose_name='ملف Excel'
    )

    # Scheduling
    is_scheduled = models.BooleanField(default=False, verbose_name='مجدول')
    schedule_frequency = models.CharField(
        max_length=15,
        choices=Period.choices,
        blank=True,
        null=True,
        verbose_name='تكرار الجدولة'
    )
    next_generation = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='الإنشاء التالي'
    )

    # Tracking
    generation_time = models.DurationField(
        blank=True,
        null=True,
        verbose_name='وقت الإنشاء'
    )
    file_size = models.PositiveIntegerField(
        default=0,
        verbose_name='حجم الملف (بايت)'
    )
    view_count = models.PositiveIntegerField(default=0, verbose_name='عدد المشاهدات')
    download_count = models.PositiveIntegerField(default=0, verbose_name='عدد التحميلات')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    generated_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='تاريخ الإنشاء الفعلي'
    )

    # History tracking
    history = HistoricalRecords()

    class Meta:
        verbose_name = 'تقرير تحليلي'
        verbose_name_plural = 'التقارير التحليلية'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['report_type', 'status'], name='bi_report_type_status_idx'),
            models.Index(fields=['created_by', 'created_at'], name='bi_report_user_created_idx'),
            models.Index(fields=['start_date', 'end_date'], name='bi_report_period_idx'),
            models.Index(fields=['is_scheduled', 'next_generation'], name='bi_report_schedule_idx'),
        ]

    def __str__(self):
        return f"{self.title} - {self.get_report_type_display()}"

    def save(self, *args, **kwargs):
        """Override save to set generated_at when status changes to completed"""
        if self.status == self.Status.COMPLETED and not self.generated_at:
            self.generated_at = timezone.now()
        super().save(*args, **kwargs)

    @property
    def is_expired(self):
        """Check if report data is expired (older than 24 hours for real-time reports)"""
        if not self.generated_at:
            return True
        
        if self.report_type == self.ReportType.EXECUTIVE_DASHBOARD:
            # Executive dashboard expires after 1 hour
            return timezone.now() - self.generated_at > timedelta(hours=1)
        
        # Other reports expire after 24 hours
        return timezone.now() - self.generated_at > timedelta(hours=24)

    def increment_view_count(self):
        """Increment view count"""
        self.view_count += 1
        self.save(update_fields=['view_count'])

    def increment_download_count(self):
        """Increment download count"""
        self.download_count += 1
        self.save(update_fields=['download_count'])


class KPIMetric(models.Model):
    """Model for storing Key Performance Indicators"""

    class MetricType(models.TextChoices):
        REVENUE = 'revenue', 'الإيرادات'
        PROFIT = 'profit', 'الأرباح'
        CLIENT_COUNT = 'client_count', 'عدد العملاء'
        PROJECT_COUNT = 'project_count', 'عدد المشاريع'
        TASK_COMPLETION = 'task_completion', 'إنجاز المهام'
        TEAM_PRODUCTIVITY = 'team_productivity', 'إنتاجية الفريق'
        CLIENT_SATISFACTION = 'client_satisfaction', 'رضا العملاء'
        CONVERSION_RATE = 'conversion_rate', 'معدل التحويل'
        AVERAGE_PROJECT_VALUE = 'avg_project_value', 'متوسط قيمة المشروع'
        CUSTOM = 'custom', 'مخصص'

    class Frequency(models.TextChoices):
        REAL_TIME = 'real_time', 'فوري'
        HOURLY = 'hourly', 'كل ساعة'
        DAILY = 'daily', 'يومي'
        WEEKLY = 'weekly', 'أسبوعي'
        MONTHLY = 'monthly', 'شهري'

    # Basic Information
    name = models.CharField(max_length=100, verbose_name='اسم المؤشر')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    metric_type = models.CharField(
        max_length=20,
        choices=MetricType.choices,
        verbose_name='نوع المؤشر'
    )

    # Value and Target
    current_value = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name='القيمة الحالية'
    )
    target_value = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name='القيمة المستهدفة'
    )
    previous_value = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name='القيمة السابقة'
    )

    # Configuration
    unit = models.CharField(
        max_length=20,
        default='number',
        verbose_name='الوحدة'
    )  # 'number', 'currency', 'percentage', 'time'
    
    frequency = models.CharField(
        max_length=15,
        choices=Frequency.choices,
        default=Frequency.DAILY,
        verbose_name='تكرار التحديث'
    )

    # Calculation
    calculation_query = models.TextField(
        blank=True,
        null=True,
        verbose_name='استعلام الحساب'
    )
    is_automated = models.BooleanField(default=True, verbose_name='تلقائي')

    # Display
    display_order = models.PositiveIntegerField(default=0, verbose_name='ترتيب العرض')
    is_visible = models.BooleanField(default=True, verbose_name='مرئي')
    color_code = models.CharField(
        max_length=7,
        default='#3B82F6',
        verbose_name='رمز اللون'
    )

    # Timestamps
    last_calculated = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='آخر حساب'
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    # History tracking
    history = HistoricalRecords()

    class Meta:
        verbose_name = 'مؤشر أداء رئيسي'
        verbose_name_plural = 'مؤشرات الأداء الرئيسية'
        ordering = ['display_order', 'name']
        indexes = [
            models.Index(fields=['metric_type', 'is_visible'], name='bi_kpi_type_visible_idx'),
            models.Index(fields=['frequency', 'last_calculated'], name='bi_kpi_freq_calc_idx'),
            models.Index(fields=['display_order'], name='bi_kpi_order_idx'),
        ]

    def __str__(self):
        return f"{self.name} ({self.get_metric_type_display()})"

    @property
    def growth_percentage(self):
        """Calculate growth percentage from previous value"""
        if self.previous_value == 0:
            return 100 if self.current_value > 0 else 0
        
        return ((self.current_value - self.previous_value) / self.previous_value) * 100

    @property
    def target_achievement_percentage(self):
        """Calculate target achievement percentage"""
        if not self.target_value or self.target_value == 0:
            return None
        
        return (self.current_value / self.target_value) * 100

    @property
    def status(self):
        """Get status based on target achievement"""
        achievement = self.target_achievement_percentage
        if achievement is None:
            return 'no_target'
        elif achievement >= 100:
            return 'achieved'
        elif achievement >= 80:
            return 'on_track'
        elif achievement >= 60:
            return 'behind'
        else:
            return 'critical'

    def update_value(self, new_value):
        """Update current value and move current to previous"""
        self.previous_value = self.current_value
        self.current_value = new_value
        self.last_calculated = timezone.now()
        self.save(update_fields=['current_value', 'previous_value', 'last_calculated'])


class DashboardWidget(models.Model):
    """Model for customizable dashboard widgets"""

    class WidgetType(models.TextChoices):
        KPI_CARD = 'kpi_card', 'بطاقة مؤشر'
        CHART = 'chart', 'مخطط بياني'
        TABLE = 'table', 'جدول'
        PROGRESS_BAR = 'progress_bar', 'شريط تقدم'
        GAUGE = 'gauge', 'مقياس'
        MAP = 'map', 'خريطة'
        CALENDAR = 'calendar', 'تقويم'
        ACTIVITY_FEED = 'activity_feed', 'تغذية الأنشطة'

    class ChartType(models.TextChoices):
        LINE = 'line', 'خطي'
        BAR = 'bar', 'أعمدة'
        PIE = 'pie', 'دائري'
        DOUGHNUT = 'doughnut', 'كعكة'
        AREA = 'area', 'منطقة'
        SCATTER = 'scatter', 'نقطي'

    # Basic Information
    title = models.CharField(max_length=100, verbose_name='عنوان الودجت')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    widget_type = models.CharField(
        max_length=20,
        choices=WidgetType.choices,
        verbose_name='نوع الودجت'
    )

    # User and Dashboard
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='dashboard_widgets',
        verbose_name='المستخدم'
    )
    dashboard_name = models.CharField(
        max_length=50,
        default='main',
        verbose_name='اسم لوحة التحكم'
    )

    # Layout
    position_x = models.PositiveIntegerField(default=0, verbose_name='الموضع الأفقي')
    position_y = models.PositiveIntegerField(default=0, verbose_name='الموضع الرأسي')
    width = models.PositiveIntegerField(default=4, verbose_name='العرض')
    height = models.PositiveIntegerField(default=3, verbose_name='الارتفاع')

    # Configuration
    data_source = models.CharField(max_length=100, verbose_name='مصدر البيانات')
    chart_type = models.CharField(
        max_length=15,
        choices=ChartType.choices,
        blank=True,
        null=True,
        verbose_name='نوع المخطط'
    )
    configuration = models.JSONField(default=dict, verbose_name='الإعدادات')
    filters = models.JSONField(default=dict, verbose_name='المرشحات')

    # Display
    is_visible = models.BooleanField(default=True, verbose_name='مرئي')
    refresh_interval = models.PositiveIntegerField(
        default=300,  # 5 minutes
        verbose_name='فترة التحديث (ثانية)'
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'ودجت لوحة التحكم'
        verbose_name_plural = 'ودجتات لوحة التحكم'
        ordering = ['position_y', 'position_x']
        indexes = [
            models.Index(fields=['user', 'dashboard_name'], name='bi_widget_user_dash_idx'),
            models.Index(fields=['widget_type', 'is_visible'], name='bi_widget_type_visible_idx'),
        ]

    def __str__(self):
        return f"{self.title} ({self.get_widget_type_display()})"
