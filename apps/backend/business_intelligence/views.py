"""
Business Intelligence & Analytics Views
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.exceptions import ValidationError, NotFound, PermissionDenied
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Count, Sum, Avg, Q, F
from django.utils import timezone
from django.core.exceptions import ObjectDoesNotExist
from datetime import datetime, timedelta, date
from decimal import Decimal
import logging
import traceback

from .cache import AnalyticsCacheService, DashboardCacheManager
from clients.models import Client
from projects.models import Project
from tasks.models import Task
from invoices.models import Invoice
from commissions.models import Commission
from team.models import User
from .models import AnalyticsReport, KPIMetric, DashboardWidget
from .serializers import (
    AnalyticsReportSerializer,
    AnalyticsReportListSerializer,
    AnalyticsReportCreateSerializer,
    KPIMetricSerializer,
    KPIMetricListSerializer,
    KPIMetricCreateSerializer,
    DashboardWidgetSerializer,
    DashboardWidgetListSerializer,
    DashboardWidgetCreateSerializer,
    ExecutiveDashboardSerializer,
    RevenueAnalyticsSerializer,
    ClientAnalyticsSerializer,
    ProjectAnalyticsSerializer,
    TeamAnalyticsSerializer,
)
from .analytics_engine import AnalyticsEngine


class AnalyticsReportViewSet(viewsets.ModelViewSet):
    """Analytics report management viewset"""

    queryset = (
        AnalyticsReport.objects.all()
        .select_related("created_by")
        .prefetch_related("shared_with")
    )
    permission_classes = [IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["report_type", "status", "period_type", "is_scheduled"]
    search_fields = ["title", "description"]
    ordering_fields = ["title", "created_at", "generated_at", "view_count"]
    ordering = ["-created_at"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "list":
            return AnalyticsReportListSerializer
        elif self.action == "create":
            return AnalyticsReportCreateSerializer
        return AnalyticsReportSerializer

    def get_queryset(self):
        """Filter queryset based on user permissions"""
        queryset = super().get_queryset()
        user = self.request.user

        # Users can see reports they created or reports shared with them
        return queryset.filter(Q(created_by=user) | Q(shared_with=user)).distinct()

    def perform_create(self, serializer):
        """Create analytics report and trigger generation"""
        report = serializer.save(created_by=self.request.user)

        # Trigger report generation asynchronously
        from .tasks import generate_analytics_report

        generate_analytics_report.delay(report.id)

    @action(detail=True, methods=["post"])
    def regenerate(self, request, pk=None):
        """Regenerate analytics report"""
        report = self.get_object()

        # Check if user can regenerate this report
        if report.created_by != request.user:
            return Response(
                {"error": "ليس لديك صلاحية لإعادة إنشاء هذا التقرير"},
                status=status.HTTP_403_FORBIDDEN,
            )

        # Update status and trigger regeneration
        report.status = AnalyticsReport.Status.GENERATING
        report.save(update_fields=["status"])

        from .tasks import generate_analytics_report

        generate_analytics_report.delay(report.id)

        return Response({"message": "تم بدء إعادة إنشاء التقرير"})

    @action(detail=True, methods=["post"])
    def download(self, request, pk=None):
        """Download analytics report file"""
        report = self.get_object()

        # Increment download count
        report.increment_download_count()

        file_type = request.data.get("file_type", "pdf")

        if file_type == "pdf" and report.pdf_file:
            return Response(
                {
                    "download_url": report.pdf_file.url,
                    "file_name": f"{report.title}.pdf",
                }
            )
        elif file_type == "excel" and report.excel_file:
            return Response(
                {
                    "download_url": report.excel_file.url,
                    "file_name": f"{report.title}.xlsx",
                }
            )
        else:
            return Response(
                {"error": "الملف المطلوب غير متوفر"}, status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=["get"])
    def view(self, request, pk=None):
        """View analytics report data"""
        report = self.get_object()

        # Increment view count
        report.increment_view_count()

        # Return report data
        serializer = self.get_serializer(report)
        return Response(serializer.data)


class KPIMetricViewSet(viewsets.ModelViewSet):
    """KPI metric management viewset"""

    queryset = KPIMetric.objects.all().order_by("display_order", "name")
    permission_classes = [IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["metric_type", "frequency", "is_visible", "is_automated"]
    search_fields = ["name", "description"]
    ordering_fields = ["name", "display_order", "current_value", "last_calculated"]
    ordering = ["display_order", "name"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "list":
            return KPIMetricListSerializer
        elif self.action == "create":
            return KPIMetricCreateSerializer
        return KPIMetricSerializer

    @action(detail=False, methods=["get"])
    def dashboard_kpis(self, request):
        """Get KPIs for dashboard display"""
        # Try to get from cache first
        cache_key = f"dashboard_kpis_{request.user.id}"
        cached_data = AnalyticsCacheService.get(cache_key)

        if cached_data is not None:
            return Response(cached_data)

        # Get visible KPIs
        kpis = self.get_queryset().filter(is_visible=True)
        serializer = KPIMetricListSerializer(kpis, many=True)

        # Cache for 5 minutes
        AnalyticsCacheService.set(
            cache_key, serializer.data, AnalyticsCacheService.TIMEOUT_SHORT
        )

        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def update_value(self, request, pk=None):
        """Update KPI metric value"""
        metric = self.get_object()
        new_value = request.data.get("value")

        if new_value is None:
            return Response(
                {"error": "القيمة مطلوبة"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            new_value = Decimal(str(new_value))
            metric.update_value(new_value)

            # Invalidate cache
            cache_key = f"dashboard_kpis_{request.user.id}"
            CacheService.delete(cache_key)

            serializer = self.get_serializer(metric)
            return Response(serializer.data)

        except (ValueError, TypeError):
            return Response(
                {"error": "قيمة غير صحيحة"}, status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["post"])
    def calculate_all(self, request):
        """Calculate all automated KPI metrics"""
        automated_kpis = self.get_queryset().filter(is_automated=True)

        updated_count = 0
        for kpi in automated_kpis:
            try:
                # Calculate KPI value using analytics engine
                new_value = AnalyticsEngine.calculate_kpi_value(kpi)
                if new_value is not None:
                    kpi.update_value(new_value)
                    updated_count += 1
            except Exception as e:
                # Log error but continue with other KPIs
                print(f"Error calculating KPI {kpi.name}: {str(e)}")

        # Invalidate cache
        cache_key = f"dashboard_kpis_{request.user.id}"
        CacheService.delete(cache_key)

        return Response(
            {
                "message": f"تم تحديث {updated_count} مؤشر أداء",
                "updated_count": updated_count,
            }
        )


class DashboardWidgetViewSet(viewsets.ModelViewSet):
    """Dashboard widget management viewset"""

    queryset = DashboardWidget.objects.all().select_related("user")
    permission_classes = [IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["widget_type", "dashboard_name", "is_visible"]
    search_fields = ["title", "description"]
    ordering_fields = ["title", "position_x", "position_y", "created_at"]
    ordering = ["position_y", "position_x"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "list":
            return DashboardWidgetListSerializer
        elif self.action == "create":
            return DashboardWidgetCreateSerializer
        return DashboardWidgetSerializer

    def get_queryset(self):
        """Filter queryset to user's widgets"""
        return super().get_queryset().filter(user=self.request.user)

    def perform_create(self, serializer):
        """Create widget for current user"""
        serializer.save(user=self.request.user)

    @action(detail=False, methods=["get"])
    def dashboard_layout(self, request):
        """Get dashboard layout for specific dashboard"""
        dashboard_name = request.query_params.get("dashboard", "main")

        widgets = self.get_queryset().filter(
            dashboard_name=dashboard_name, is_visible=True
        )

        serializer = DashboardWidgetListSerializer(widgets, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["post"])
    def update_layout(self, request):
        """Update dashboard widget layout"""
        widgets_data = request.data.get("widgets", [])

        updated_count = 0
        for widget_data in widgets_data:
            widget_id = widget_data.get("id")
            if widget_id:
                try:
                    widget = self.get_queryset().get(id=widget_id)
                    widget.position_x = widget_data.get("position_x", widget.position_x)
                    widget.position_y = widget_data.get("position_y", widget.position_y)
                    widget.width = widget_data.get("width", widget.width)
                    widget.height = widget_data.get("height", widget.height)
                    widget.save(
                        update_fields=["position_x", "position_y", "width", "height"]
                    )
                    updated_count += 1
                except DashboardWidget.DoesNotExist:
                    continue

        return Response(
            {
                "message": f"تم تحديث {updated_count} ودجت",
                "updated_count": updated_count,
            }
        )

    @action(detail=True, methods=["get"])
    def data(self, request, pk=None):
        """Get widget data"""
        widget = self.get_object()

        try:
            # Get widget data using analytics engine
            widget_data = AnalyticsEngine.get_widget_data(widget)
            return Response(widget_data)
        except Exception as e:
            return Response(
                {"error": f"خطأ في تحميل بيانات الودجت: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ExecutiveDashboardView(APIView):
    """Executive dashboard analytics view"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get executive dashboard data"""
        # Try to get from cache first
        cache_key = f"executive_dashboard_{request.user.id}"
        cached_data = DashboardCache.get_dashboard_data(cache_key)

        if cached_data is not None:
            return Response(cached_data)

        try:
            # Generate dashboard data
            dashboard_data = self._generate_executive_dashboard_data(request)

            # Cache for 1 hour
            DashboardCache.set_dashboard_data(
                cache_key, dashboard_data, DashboardCache.TIMEOUT_MEDIUM
            )

            serializer = ExecutiveDashboardSerializer(dashboard_data)
            return Response(serializer.data)

        except Exception as e:
            return Response(
                {"error": f"خطأ في تحميل لوحة التحكم: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _generate_executive_dashboard_data(self, request):
        """Generate executive dashboard data"""
        from .analytics_engine import AnalyticsEngine

        # Get current month date range
        start_date, end_date = AnalyticsEngine.get_date_range("monthly")

        # KPI Summary
        kpi_summary = self._get_kpi_summary()

        # Revenue Overview
        revenue_overview = self._get_revenue_overview(start_date, end_date)

        # Client Overview
        client_overview = self._get_client_overview(start_date, end_date)

        # Project Overview
        project_overview = self._get_project_overview(start_date, end_date)

        # Team Overview
        team_overview = self._get_team_overview(start_date, end_date)

        # Financial Summary
        financial_summary = self._get_financial_summary(start_date, end_date)

        # Recent Activities
        recent_activities = self._get_recent_activities()

        # System Health
        system_health = self._get_system_health()

        # Get trend and analytics data
        revenue_trend = AnalyticsEngine.get_revenue_trend_data("monthly", 12)
        revenue_by_source = AnalyticsEngine.get_revenue_by_source_data("monthly")
        client_acquisition_trend = AnalyticsEngine.get_client_acquisition_trend(12)
        top_clients = AnalyticsEngine.get_top_clients_data("monthly", 10)
        project_status_distribution = AnalyticsEngine.get_project_status_distribution()

        return {
            "kpi_summary": kpi_summary,
            "revenue_overview": revenue_overview,
            "revenue_trend": revenue_trend,
            "revenue_by_source": revenue_by_source,
            "client_overview": client_overview,
            "client_satisfaction": self._get_client_satisfaction_data(),
            "client_acquisition_trend": client_acquisition_trend,
            "top_clients": top_clients,
            "project_overview": project_overview,
            "project_status_distribution": project_status_distribution,
            "project_performance_trend": self._get_project_performance_trend(),
            "team_overview": team_overview,
            "team_productivity": self._get_team_productivity_data(),
            "task_completion_trend": self._get_task_completion_trend(),
            "financial_summary": financial_summary,
            "cash_flow_trend": self._get_cash_flow_trend(),
            "expense_breakdown": [],  # TODO: Implement when expense module is added
            "recent_activities": recent_activities,
            "system_health": system_health,
            "generated_at": timezone.now(),
            "data_freshness": "real_time",
            "cache_status": "fresh",
        }

    def _get_kpi_summary(self):
        """Get KPI summary data"""
        kpis = KPIMetric.objects.filter(is_visible=True).order_by("display_order")[:6]

        kpi_data = []
        for kpi in kpis:
            kpi_data.append(
                {
                    "id": kpi.id,
                    "name": kpi.name,
                    "value": float(kpi.current_value),
                    "target": float(kpi.target_value) if kpi.target_value else None,
                    "unit": kpi.unit,
                    "growth": float(kpi.growth_percentage),
                    "status": kpi.status,
                    "color": kpi.color_code,
                }
            )

        return kpi_data

    def _get_revenue_overview(self, start_date, end_date):
        """Get revenue overview data"""
        from finance.models import Invoice

        # Current month revenue
        current_revenue = Invoice.objects.filter(
            payment_status="paid", payment_date__range=[start_date, end_date]
        ).aggregate(total=Sum("total_amount_egp"))["total"] or Decimal("0")

        # Previous month revenue for comparison
        prev_start = (
            start_date.replace(month=start_date.month - 1)
            if start_date.month > 1
            else start_date.replace(year=start_date.year - 1, month=12)
        )
        prev_end = start_date - timedelta(days=1)

        previous_revenue = Invoice.objects.filter(
            payment_status="paid", payment_date__range=[prev_start, prev_end]
        ).aggregate(total=Sum("total_amount_egp"))["total"] or Decimal("0")

        # Calculate growth
        growth = 0
        if previous_revenue > 0:
            growth = ((current_revenue - previous_revenue) / previous_revenue) * 100

        return {
            "current_revenue": float(current_revenue),
            "previous_revenue": float(previous_revenue),
            "growth_percentage": float(growth),
            "currency": "EGP",
        }

    def _get_client_overview(self, start_date, end_date):
        """Get client overview data"""
        total_clients = Client.objects.filter(is_active=True).count()
        new_clients = Client.objects.filter(
            created_at__date__range=[start_date, end_date]
        ).count()

        return {
            "total_clients": total_clients,
            "new_clients_this_month": new_clients,
            "active_clients": total_clients,  # Assuming all clients are active
            "client_retention_rate": 95.0,  # TODO: Calculate actual retention rate
        }

    def _get_project_overview(self, start_date, end_date):
        """Get project overview data"""
        total_projects = Project.objects.count()
        active_projects = Project.objects.filter(
            status__in=["planning", "in_progress", "review"]
        ).count()
        completed_projects = Project.objects.filter(status="completed").count()
        new_projects = Project.objects.filter(
            created_at__date__range=[start_date, end_date]
        ).count()

        return {
            "total_projects": total_projects,
            "active_projects": active_projects,
            "completed_projects": completed_projects,
            "new_projects_this_month": new_projects,
        }

    def _get_team_overview(self, start_date, end_date):
        """Get team overview data"""
        team_members = User.objects.filter(
            role__in=["developer", "web_designer", "media_buyer", "sales_manager"]
        ).count()

        completed_tasks = Task.objects.filter(
            status="completed", completed_at__date__range=[start_date, end_date]
        ).count()

        return {
            "total_team_members": team_members,
            "active_team_members": team_members,  # Assuming all are active
            "tasks_completed_this_month": completed_tasks,
            "average_productivity": 85.0,  # TODO: Calculate actual productivity
        }

    def _get_financial_summary(self, start_date, end_date):
        """Get financial summary data"""
        # Revenue
        revenue = Invoice.objects.filter(
            payment_status="paid", payment_date__range=[start_date, end_date]
        ).aggregate(total=Sum("total_amount_egp"))["total"] or Decimal("0")

        # Commissions
        commissions = Commission.objects.filter(
            payment_status="paid", payment_date__range=[start_date, end_date]
        ).aggregate(total=Sum("amount_egp"))["total"] or Decimal("0")

        # Profit (simplified calculation)
        profit = revenue - commissions

        return {
            "revenue": float(revenue),
            "commissions": float(commissions),
            "profit": float(profit),
            "currency": "EGP",
        }

    def _get_client_satisfaction_data(self):
        """Get client satisfaction data"""
        # Calculate average mood score from clients
        avg_mood = Client.objects.filter(is_active=True).aggregate(
            avg_mood=Avg("mood_score")
        )["avg_mood"]

        if avg_mood is None:
            avg_mood = 0

        # Convert mood score (1-5) to percentage
        satisfaction_percentage = (avg_mood / 5) * 100

        return {
            "average_satisfaction": round(satisfaction_percentage, 2),
            "total_responses": Client.objects.filter(
                is_active=True, mood_score__isnull=False
            ).count(),
            "satisfaction_distribution": {
                "excellent": Client.objects.filter(mood_score=5).count(),
                "good": Client.objects.filter(mood_score=4).count(),
                "average": Client.objects.filter(mood_score=3).count(),
                "poor": Client.objects.filter(mood_score=2).count(),
                "very_poor": Client.objects.filter(mood_score=1).count(),
            },
        }

    def _get_project_performance_trend(self):
        """Get project performance trend data"""
        from datetime import datetime, timedelta

        trend_data = []
        today = timezone.now().date()

        for i in range(6):  # Last 6 months
            if today.month - i <= 0:
                year = today.year - 1
                month = 12 + (today.month - i)
            else:
                year = today.year
                month = today.month - i

            start_date = datetime(year, month, 1).date()
            if month == 12:
                end_date = datetime(year + 1, 1, 1).date() - timedelta(days=1)
            else:
                end_date = datetime(year, month + 1, 1).date() - timedelta(days=1)

            # Calculate project metrics for this month
            projects_started = Project.objects.filter(
                created_at__date__range=[start_date, end_date]
            ).count()

            projects_completed = Project.objects.filter(
                status="completed", updated_at__date__range=[start_date, end_date]
            ).count()

            avg_completion_time = Project.objects.filter(
                status="completed", updated_at__date__range=[start_date, end_date]
            ).aggregate(avg_days=Avg(F("updated_at") - F("created_at")))["avg_days"]

            trend_data.append(
                {
                    "period": f"{year}-{month:02d}",
                    "period_display": f"{AnalyticsEngine._get_month_name(month)} {year}",
                    "projects_started": projects_started,
                    "projects_completed": projects_completed,
                    "avg_completion_days": (
                        avg_completion_time.days if avg_completion_time else 0
                    ),
                    "completion_rate": round(
                        (
                            (projects_completed / projects_started * 100)
                            if projects_started > 0
                            else 0
                        ),
                        2,
                    ),
                }
            )

        return list(reversed(trend_data))

    def _get_team_productivity_data(self):
        """Get team productivity data"""
        team_members = User.objects.filter(
            role__in=["developer", "web_designer", "media_buyer", "sales_manager"]
        )

        productivity_data = []
        for member in team_members:
            # Get tasks completed this month
            start_date, end_date = AnalyticsEngine.get_date_range("monthly")

            completed_tasks = Task.objects.filter(
                assigned_to=member,
                status="completed",
                completed_at__date__range=[start_date, end_date],
            ).count()

            total_tasks = Task.objects.filter(
                assigned_to=member, created_at__date__range=[start_date, end_date]
            ).count()

            productivity_data.append(
                {
                    "member_id": member.id,
                    "member_name": f"{member.first_name} {member.last_name}",
                    "role": member.get_role_display(),
                    "completed_tasks": completed_tasks,
                    "total_tasks": total_tasks,
                    "completion_rate": round(
                        (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0,
                        2,
                    ),
                    "productivity_score": round(
                        (completed_tasks / 30) * 100, 2
                    ),  # Assuming 30 days in month
                }
            )

        return productivity_data

    def _get_task_completion_trend(self):
        """Get task completion trend data"""
        trend_data = []
        today = timezone.now().date()

        for i in range(30):  # Last 30 days
            date_point = today - timedelta(days=i)

            tasks_created = Task.objects.filter(created_at__date=date_point).count()

            tasks_completed = Task.objects.filter(completed_at__date=date_point).count()

            trend_data.append(
                {
                    "date": date_point.isoformat(),
                    "date_display": date_point.strftime("%Y-%m-%d"),
                    "tasks_created": tasks_created,
                    "tasks_completed": tasks_completed,
                    "completion_rate": round(
                        (
                            (tasks_completed / tasks_created * 100)
                            if tasks_created > 0
                            else 0
                        ),
                        2,
                    ),
                }
            )

        return list(reversed(trend_data))

    def _get_cash_flow_trend(self):
        """Get cash flow trend data"""
        trend_data = []
        today = timezone.now().date()

        for i in range(12):  # Last 12 months
            if today.month - i <= 0:
                year = today.year - 1
                month = 12 + (today.month - i)
            else:
                year = today.year
                month = today.month - i

            start_date = datetime(year, month, 1).date()
            if month == 12:
                end_date = datetime(year + 1, 1, 1).date() - timedelta(days=1)
            else:
                end_date = datetime(year, month + 1, 1).date() - timedelta(days=1)

            # Calculate cash inflow (payments received)
            cash_inflow = Invoice.objects.filter(
                payment_status="paid", payment_date__range=[start_date, end_date]
            ).aggregate(total=Sum("total_amount_egp"))["total"] or Decimal("0")

            # Calculate cash outflow (commissions paid)
            cash_outflow = Commission.objects.filter(
                payment_status="paid", payment_date__range=[start_date, end_date]
            ).aggregate(total=Sum("amount_egp"))["total"] or Decimal("0")

            net_cash_flow = cash_inflow - cash_outflow

            trend_data.append(
                {
                    "period": f"{year}-{month:02d}",
                    "period_display": f"{AnalyticsEngine._get_month_name(month)} {year}",
                    "cash_inflow": float(cash_inflow),
                    "cash_outflow": float(cash_outflow),
                    "net_cash_flow": float(net_cash_flow),
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                }
            )

        return list(reversed(trend_data))

    def _get_recent_activities(self):
        """Get recent activities data"""
        # TODO: Implement activity tracking
        return [
            {
                "type": "project_created",
                "message": "تم إنشاء مشروع جديد",
                "timestamp": timezone.now(),
                "user": "محمد عبد الفتاح",
            }
        ]

    def _get_system_health(self):
        """Get system health data"""
        return {
            "status": "healthy",
            "uptime": "99.9%",
            "response_time": "120ms",
            "active_users": 5,
        }
