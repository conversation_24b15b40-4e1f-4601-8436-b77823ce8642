"""
Business Intelligence & Analytics Admin Configuration
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from simple_history.admin import SimpleHistoryAdmin

from .models import AnalyticsReport, KPIMetric, DashboardWidget


@admin.register(AnalyticsReport)
class AnalyticsReportAdmin(SimpleHistoryAdmin):
    """Analytics report admin configuration"""
    
    list_display = [
        'title',
        'report_type',
        'status_badge',
        'created_by',
        'period_display',
        'view_count',
        'download_count',
        'is_scheduled',
        'created_at',
        'generated_at'
    ]
    
    list_filter = [
        'report_type',
        'status',
        'period_type',
        'is_scheduled',
        'created_at',
        'generated_at'
    ]
    
    search_fields = [
        'title',
        'description',
        'created_by__username',
        'created_by__first_name',
        'created_by__last_name'
    ]
    
    readonly_fields = [
        'created_by',
        'status',
        'generation_time',
        'file_size',
        'view_count',
        'download_count',
        'created_at',
        'updated_at',
        'generated_at',
        'file_links'
    ]
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('title', 'description', 'report_type', 'created_by')
        }),
        ('الفترة الزمنية', {
            'fields': ('period_type', 'start_date', 'end_date')
        }),
        ('الإعدادات', {
            'fields': ('filters', 'configuration')
        }),
        ('المشاركة', {
            'fields': ('shared_with',)
        }),
        ('الجدولة', {
            'fields': ('is_scheduled', 'schedule_frequency', 'next_generation'),
            'classes': ('collapse',)
        }),
        ('حالة التقرير', {
            'fields': ('status', 'generation_time', 'file_size', 'file_links'),
            'classes': ('collapse',)
        }),
        ('الإحصائيات', {
            'fields': ('view_count', 'download_count'),
            'classes': ('collapse',)
        }),
        ('التواريخ', {
            'fields': ('created_at', 'updated_at', 'generated_at'),
            'classes': ('collapse',)
        })
    )
    
    filter_horizontal = ['shared_with']
    
    def status_badge(self, obj):
        """Display status as colored badge"""
        colors = {
            'generating': 'orange',
            'completed': 'green',
            'failed': 'red',
            'scheduled': 'blue'
        }
        color = colors.get(obj.status, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_badge.short_description = 'الحالة'
    
    def period_display(self, obj):
        """Display period information"""
        return f"{obj.start_date} - {obj.end_date} ({obj.get_period_type_display()})"
    period_display.short_description = 'الفترة'
    
    def file_links(self, obj):
        """Display download links for generated files"""
        links = []
        if obj.pdf_file:
            links.append(f'<a href="{obj.pdf_file.url}" target="_blank">PDF</a>')
        if obj.excel_file:
            links.append(f'<a href="{obj.excel_file.url}" target="_blank">Excel</a>')
        
        if links:
            return mark_safe(' | '.join(links))
        return 'لا توجد ملفات'
    file_links.short_description = 'الملفات'
    
    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related('created_by')


@admin.register(KPIMetric)
class KPIMetricAdmin(SimpleHistoryAdmin):
    """KPI metric admin configuration"""
    
    list_display = [
        'name',
        'metric_type',
        'current_value_display',
        'target_value_display',
        'growth_display',
        'status_badge',
        'frequency',
        'is_automated',
        'is_visible',
        'last_calculated'
    ]
    
    list_filter = [
        'metric_type',
        'frequency',
        'is_automated',
        'is_visible',
        'unit',
        'last_calculated'
    ]
    
    search_fields = [
        'name',
        'description'
    ]
    
    readonly_fields = [
        'growth_percentage',
        'target_achievement_percentage',
        'status',
        'last_calculated',
        'created_at',
        'updated_at'
    ]
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'description', 'metric_type')
        }),
        ('القيم', {
            'fields': ('current_value', 'target_value', 'previous_value', 'unit')
        }),
        ('الحساب', {
            'fields': ('frequency', 'calculation_query', 'is_automated')
        }),
        ('العرض', {
            'fields': ('display_order', 'is_visible', 'color_code')
        }),
        ('الإحصائيات', {
            'fields': ('growth_percentage', 'target_achievement_percentage', 'status'),
            'classes': ('collapse',)
        }),
        ('التواريخ', {
            'fields': ('last_calculated', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    ordering = ['display_order', 'name']
    
    def current_value_display(self, obj):
        """Display current value with unit"""
        if obj.unit == 'currency':
            return f"{obj.current_value:,.2f} ج.م"
        elif obj.unit == 'percentage':
            return f"{obj.current_value}%"
        else:
            return f"{obj.current_value:,.0f}"
    current_value_display.short_description = 'القيمة الحالية'
    
    def target_value_display(self, obj):
        """Display target value with unit"""
        if not obj.target_value:
            return '-'
        
        if obj.unit == 'currency':
            return f"{obj.target_value:,.2f} ج.م"
        elif obj.unit == 'percentage':
            return f"{obj.target_value}%"
        else:
            return f"{obj.target_value:,.0f}"
    target_value_display.short_description = 'القيمة المستهدفة'
    
    def growth_display(self, obj):
        """Display growth percentage with color"""
        growth = obj.growth_percentage
        if growth > 0:
            color = 'green'
            icon = '↗'
        elif growth < 0:
            color = 'red'
            icon = '↘'
        else:
            color = 'gray'
            icon = '→'
        
        return format_html(
            '<span style="color: {};">{} {:.1f}%</span>',
            color,
            icon,
            growth
        )
    growth_display.short_description = 'النمو'
    
    def status_badge(self, obj):
        """Display status as colored badge"""
        colors = {
            'achieved': 'green',
            'on_track': 'blue',
            'behind': 'orange',
            'critical': 'red',
            'no_target': 'gray'
        }
        
        status_labels = {
            'achieved': 'محقق',
            'on_track': 'على المسار',
            'behind': 'متأخر',
            'critical': 'حرج',
            'no_target': 'بدون هدف'
        }
        
        color = colors.get(obj.status, 'gray')
        label = status_labels.get(obj.status, obj.status)
        
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            label
        )
    status_badge.short_description = 'الحالة'
    
    actions = ['calculate_selected_kpis']
    
    def calculate_selected_kpis(self, request, queryset):
        """Calculate selected KPI metrics"""
        from .analytics_engine import AnalyticsEngine
        
        updated_count = 0
        for kpi in queryset.filter(is_automated=True):
            try:
                new_value = AnalyticsEngine.calculate_kpi_value(kpi)
                if new_value is not None:
                    kpi.update_value(new_value)
                    updated_count += 1
            except Exception:
                pass
        
        self.message_user(
            request,
            f'تم تحديث {updated_count} مؤشر أداء من أصل {queryset.count()}'
        )
    calculate_selected_kpis.short_description = 'حساب المؤشرات المحددة'


@admin.register(DashboardWidget)
class DashboardWidgetAdmin(admin.ModelAdmin):
    """Dashboard widget admin configuration"""
    
    list_display = [
        'title',
        'widget_type',
        'user',
        'dashboard_name',
        'position_display',
        'size_display',
        'is_visible',
        'refresh_interval',
        'created_at'
    ]
    
    list_filter = [
        'widget_type',
        'chart_type',
        'dashboard_name',
        'is_visible',
        'user',
        'created_at'
    ]
    
    search_fields = [
        'title',
        'description',
        'user__username',
        'user__first_name',
        'user__last_name'
    ]
    
    readonly_fields = [
        'user',
        'created_at',
        'updated_at'
    ]
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('title', 'description', 'widget_type', 'user')
        }),
        ('لوحة التحكم', {
            'fields': ('dashboard_name',)
        }),
        ('الموضع والحجم', {
            'fields': ('position_x', 'position_y', 'width', 'height')
        }),
        ('مصدر البيانات', {
            'fields': ('data_source', 'chart_type', 'configuration', 'filters')
        }),
        ('الإعدادات', {
            'fields': ('is_visible', 'refresh_interval')
        }),
        ('التواريخ', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def position_display(self, obj):
        """Display position coordinates"""
        return f"({obj.position_x}, {obj.position_y})"
    position_display.short_description = 'الموضع'
    
    def size_display(self, obj):
        """Display widget size"""
        return f"{obj.width} × {obj.height}"
    size_display.short_description = 'الحجم'
    
    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related('user')
