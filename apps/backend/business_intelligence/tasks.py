"""
Business Intelligence Celery Tasks
Automated report generation and scheduling tasks
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

import logging
from datetime import datetime, timedelta
from typing import Optional

from celery import shared_task
from django.core.files.base import ContentFile
from django.utils import timezone
from django.core.mail import EmailMessage
from django.conf import settings

from .models import AnalyticsReport, KPIMetric
from .analytics_engine import AnalyticsEngine

# Configure logging
logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def generate_analytics_report(self, report_id: int, file_type: str = "pdf"):
    """
    Generate analytics report asynchronously

    Args:
        report_id: ID of the AnalyticsReport to generate
        file_type: Type of file to generate ('pdf' or 'excel')
    """
    try:
        logger.info(
            f"Starting report generation for report_id: {report_id}, file_type: {file_type}"
        )

        # Get the report
        try:
            report = AnalyticsReport.objects.get(id=report_id)
        except AnalyticsReport.DoesNotExist:
            logger.error(f"Report with id {report_id} does not exist")
            return {
                "status": "error",
                "message": f"Report with id {report_id} not found",
            }

        # Update status to generating
        report.status = AnalyticsReport.Status.GENERATING
        report.save(update_fields=["status"])

        # Generate report content
        try:
            from .report_generators import ReportGeneratorFactory, ReportGeneratorError

            report_content = ReportGeneratorFactory.generate_report(report, file_type)

            # Save file to report
            file_extension = "pdf" if file_type.lower() == "pdf" else "xlsx"
            filename = f"{report.title}_{timezone.now().strftime('%Y%m%d_%H%M%S')}.{file_extension}"

            if file_type.lower() == "pdf":
                report.pdf_file.save(filename, ContentFile(report_content), save=False)
            else:
                report.excel_file.save(
                    filename, ContentFile(report_content), save=False
                )

            # Update report status and metadata
            report.status = AnalyticsReport.Status.COMPLETED
            report.generated_at = timezone.now()
            report.generation_time = (
                timezone.now() - report.updated_at
            ).total_seconds()
            report.file_size = len(report_content)
            report.save()

            logger.info(f"Successfully generated report {report_id}")

            # Send email notification if configured
            if (
                hasattr(settings, "ENABLE_REPORT_EMAIL_NOTIFICATIONS")
                and settings.ENABLE_REPORT_EMAIL_NOTIFICATIONS
            ):
                send_report_notification.delay(report_id, "completed")

            return {
                "status": "success",
                "report_id": report_id,
                "file_size": len(report_content),
                "generation_time": report.generation_time,
            }

        except Exception as e:
            logger.error(f"Report generation error for report {report_id}: {str(e)}")
            report.status = AnalyticsReport.Status.FAILED
            report.save(update_fields=["status"])

            # Send failure notification
            if (
                hasattr(settings, "ENABLE_REPORT_EMAIL_NOTIFICATIONS")
                and settings.ENABLE_REPORT_EMAIL_NOTIFICATIONS
            ):
                send_report_notification.delay(report_id, "failed", str(e))

            return {"status": "error", "message": str(e)}

    except Exception as e:
        logger.error(
            f"Unexpected error in report generation for report {report_id}: {str(e)}"
        )

        # Update report status to failed
        try:
            report = AnalyticsReport.objects.get(id=report_id)
            report.status = AnalyticsReport.Status.FAILED
            report.save(update_fields=["status"])
        except:
            pass

        # Retry the task
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying report generation for report {report_id}, attempt {self.request.retries + 1}"
            )
            raise self.retry(countdown=60 * (self.request.retries + 1))

        return {
            "status": "error",
            "message": f"Failed after {self.max_retries} retries: {str(e)}",
        }


@shared_task
def send_report_notification(
    report_id: int, status: str, error_message: Optional[str] = None
):
    """
    Send email notification about report generation status

    Args:
        report_id: ID of the AnalyticsReport
        status: Status of the report ('completed' or 'failed')
        error_message: Error message if status is 'failed'
    """
    try:
        from django.core.mail import EmailMessage

        report = AnalyticsReport.objects.get(id=report_id)

        if status == "completed":
            subject = f"تم إنشاء التقرير: {report.title}"
            message = f"""
            تم إنشاء التقرير بنجاح.

            تفاصيل التقرير:
            - العنوان: {report.title}
            - النوع: {report.get_report_type_display()}
            - الفترة: {report.start_date} إلى {report.end_date}
            - تاريخ الإنشاء: {report.generated_at.strftime('%Y-%m-%d %H:%M:%S')}
            - حجم الملف: {report.file_size_mb:.2f} ميجابايت

            يمكنك تحميل التقرير من لوحة التحكم.
            """
        else:
            subject = f"فشل في إنشاء التقرير: {report.title}"
            message = f"""
            فشل في إنشاء التقرير.

            تفاصيل التقرير:
            - العنوان: {report.title}
            - النوع: {report.get_report_type_display()}
            - سبب الفشل: {error_message or 'خطأ غير معروف'}

            يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.
            """

        # Send email to report creator
        email = EmailMessage(
            subject=subject,
            body=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[report.created_by.email],
        )

        # Attach report file if completed
        if status == "completed":
            if report.pdf_file:
                email.attach_file(report.pdf_file.path)
            elif report.excel_file:
                email.attach_file(report.excel_file.path)

        email.send()
        logger.info(
            f"Sent report notification for report {report_id} with status {status}"
        )

    except Exception as e:
        logger.error(
            f"Error sending report notification for report {report_id}: {str(e)}"
        )


@shared_task
def calculate_all_kpis():
    """
    Calculate all automated KPI metrics
    This task should be run periodically (e.g., every hour or daily)
    """
    try:
        logger.info("Starting automated KPI calculation")

        # Get all automated KPIs
        automated_kpis = KPIMetric.objects.filter(is_automated=True, is_visible=True)

        updated_count = 0
        failed_count = 0

        for kpi in automated_kpis:
            try:
                # Store previous value
                previous_value = kpi.current_value

                # Calculate new value
                new_value = AnalyticsEngine.calculate_kpi_value(kpi)

                if new_value is not None:
                    # Update KPI
                    kpi.previous_value = previous_value
                    kpi.current_value = new_value
                    kpi.last_calculated = timezone.now()

                    # Calculate growth percentage
                    if previous_value > 0:
                        kpi.growth_percentage = (
                            (new_value - previous_value) / previous_value
                        ) * 100
                    else:
                        kpi.growth_percentage = 0

                    # Update status based on target achievement
                    if kpi.target_value:
                        achievement = (new_value / kpi.target_value) * 100
                        if achievement >= 100:
                            kpi.status = "achieved"
                        elif achievement >= 80:
                            kpi.status = "on_track"
                        elif achievement >= 60:
                            kpi.status = "behind"
                        else:
                            kpi.status = "critical"
                    else:
                        kpi.status = "no_target"

                    kpi.save()
                    updated_count += 1

                    logger.debug(
                        f"Updated KPI {kpi.name}: {previous_value} -> {new_value}"
                    )

            except Exception as e:
                logger.error(f"Error calculating KPI {kpi.name}: {str(e)}")
                failed_count += 1

        logger.info(
            f"KPI calculation completed. Updated: {updated_count}, Failed: {failed_count}"
        )

        return {
            "status": "success",
            "updated_count": updated_count,
            "failed_count": failed_count,
            "total_kpis": automated_kpis.count(),
        }

    except Exception as e:
        logger.error(f"Error in automated KPI calculation: {str(e)}")
        return {"status": "error", "message": str(e)}


@shared_task
def generate_scheduled_reports():
    """Generate scheduled reports"""
    try:
        from .models import AnalyticsReport

        # Get reports that need to be generated
        now = timezone.now()
        scheduled_reports = AnalyticsReport.objects.filter(
            is_scheduled=True,
            next_generation__lte=now,
            status__in=[
                AnalyticsReport.Status.COMPLETED,
                AnalyticsReport.Status.SCHEDULED,
            ],
        )

        generated_count = 0
        for report in scheduled_reports:
            try:
                # Create new report instance
                new_report = AnalyticsReport.objects.create(
                    title=f"{report.title} - {now.strftime('%Y-%m-%d %H:%M')}",
                    description=report.description,
                    report_type=report.report_type,
                    created_by=report.created_by,
                    period_type=report.period_type,
                    start_date=report.start_date,
                    end_date=report.end_date,
                    filters=report.filters,
                    configuration=report.configuration,
                    is_scheduled=False,
                )

                # Copy shared users
                new_report.shared_with.set(report.shared_with.all())

                # Generate the report
                generate_analytics_report.delay(new_report.id)

                # Update next generation time
                if report.schedule_frequency == AnalyticsReport.Period.DAILY:
                    report.next_generation = now + timedelta(days=1)
                elif report.schedule_frequency == AnalyticsReport.Period.WEEKLY:
                    report.next_generation = now + timedelta(weeks=1)
                elif report.schedule_frequency == AnalyticsReport.Period.MONTHLY:
                    report.next_generation = now + timedelta(days=30)
                elif report.schedule_frequency == AnalyticsReport.Period.QUARTERLY:
                    report.next_generation = now + timedelta(days=90)
                elif report.schedule_frequency == AnalyticsReport.Period.YEARLY:
                    report.next_generation = now + timedelta(days=365)

                report.save(update_fields=["next_generation"])
                generated_count += 1

            except Exception as e:
                logger.error(f"Error generating scheduled report {report.id}: {str(e)}")

        logger.info(f"Generated {generated_count} scheduled reports")
        return generated_count

    except Exception as e:
        logger.error(f"Error in scheduled reports task: {str(e)}")
        return 0


def _generate_executive_dashboard_report(report):
    """Generate executive dashboard report data"""
    from .analytics_engine import AnalyticsEngine

    start_date, end_date = AnalyticsEngine.get_date_range(
        report.period_type, report.start_date, report.end_date
    )

    return {
        "report_type": "executive_dashboard",
        "period": {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "type": report.period_type,
        },
        "kpi_summary": _get_kpi_summary_data(),
        "revenue_overview": _get_revenue_overview_data(start_date, end_date),
        "client_overview": _get_client_overview_data(start_date, end_date),
        "project_overview": _get_project_overview_data(start_date, end_date),
        "team_overview": _get_team_overview_data(start_date, end_date),
        "financial_summary": _get_financial_summary_data(start_date, end_date),
        "generated_at": timezone.now().isoformat(),
    }


def _generate_revenue_analysis_report(report):
    """Generate revenue analysis report data"""
    # TODO: Implement detailed revenue analysis
    return {"report_type": "revenue_analysis", "data": {}}


def _generate_client_analysis_report(report):
    """Generate client analysis report data"""
    # TODO: Implement detailed client analysis
    return {"report_type": "client_analysis", "data": {}}


def _generate_project_performance_report(report):
    """Generate project performance report data"""
    # TODO: Implement detailed project performance analysis
    return {"report_type": "project_performance", "data": {}}


def _generate_team_performance_report(report):
    """Generate team performance report data"""
    # TODO: Implement detailed team performance analysis
    return {"report_type": "team_performance", "data": {}}


def _generate_financial_overview_report(report):
    """Generate financial overview report data"""
    # TODO: Implement detailed financial overview
    return {"report_type": "financial_overview", "data": {}}


def _generate_custom_report(report):
    """Generate custom report data"""
    # TODO: Implement custom report generation based on configuration
    return {"report_type": "custom", "data": {}}


def _get_kpi_summary_data():
    """Get KPI summary data for reports"""
    from .models import KPIMetric

    kpis = KPIMetric.objects.filter(is_visible=True).order_by("display_order")[:6]
    return [
        {
            "name": kpi.name,
            "value": float(kpi.current_value),
            "target": float(kpi.target_value) if kpi.target_value else None,
            "unit": kpi.unit,
            "growth": float(kpi.growth_percentage),
            "status": kpi.status,
        }
        for kpi in kpis
    ]


def _get_revenue_overview_data(start_date, end_date):
    """Get revenue overview data for reports"""
    # TODO: Implement revenue overview calculation
    return {"total_revenue": 0, "growth": 0}


def _get_client_overview_data(start_date, end_date):
    """Get client overview data for reports"""
    # TODO: Implement client overview calculation
    return {"total_clients": 0, "new_clients": 0}


def _get_project_overview_data(start_date, end_date):
    """Get project overview data for reports"""
    # TODO: Implement project overview calculation
    return {"total_projects": 0, "active_projects": 0}


def _get_team_overview_data(start_date, end_date):
    """Get team overview data for reports"""
    # TODO: Implement team overview calculation
    return {"total_members": 0, "productivity": 0}


def _get_financial_summary_data(start_date, end_date):
    """Get financial summary data for reports"""
    # TODO: Implement financial summary calculation
    return {"revenue": 0, "expenses": 0, "profit": 0}
