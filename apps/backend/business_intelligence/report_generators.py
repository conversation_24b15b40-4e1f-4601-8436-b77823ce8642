"""
Business Intelligence Report Generators
PDF and Excel report generation for analytics data
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

import os
import io
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Any, Optional

from django.conf import settings
from django.core.files.base import ContentFile
from django.utils import timezone
from django.template.loader import render_to_string

# PDF Generation
try:
    from reportlab.lib import colors
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.pdfgen import canvas
    from reportlab.lib.enums import TA_RIGHT, TA_CENTER
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

# Excel Generation
try:
    import openpyxl
    from openpyxl.styles import Font, Ali<PERSON>ment, <PERSON><PERSON><PERSON>ill, <PERSON>, Side
    from openpyxl.chart import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Reference
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

from .models import AnalyticsReport
from .analytics_engine import AnalyticsEngine


class ReportGeneratorError(Exception):
    """Custom exception for report generation errors"""
    pass


class PDFReportGenerator:
    """PDF report generator using ReportLab"""
    
    def __init__(self):
        if not REPORTLAB_AVAILABLE:
            raise ReportGeneratorError("ReportLab is not installed. Please install it with: pip install reportlab")
    
    def generate_executive_dashboard_pdf(self, report: AnalyticsReport, data: Dict[str, Any]) -> bytes:
        """Generate executive dashboard PDF report"""
        buffer = io.BytesIO()
        
        # Create PDF document
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        # Build story (content)
        story = []
        styles = getSampleStyleSheet()
        
        # Create RTL Arabic style
        arabic_style = ParagraphStyle(
            'Arabic',
            parent=styles['Normal'],
            fontName='Helvetica',
            fontSize=12,
            alignment=TA_RIGHT,
            rightIndent=0,
            leftIndent=0
        )
        
        title_style = ParagraphStyle(
            'ArabicTitle',
            parent=styles['Title'],
            fontName='Helvetica-Bold',
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=30
        )
        
        # Title
        story.append(Paragraph("تقرير لوحة المدير التنفيذي", title_style))
        story.append(Spacer(1, 12))
        
        # Report metadata
        metadata_data = [
            ['تاريخ التقرير:', data.get('generated_at', timezone.now().strftime('%Y-%m-%d %H:%M'))],
            ['الفترة:', f"{report.start_date} إلى {report.end_date}"],
            ['نوع التقرير:', report.get_report_type_display()],
            ['حالة البيانات:', data.get('data_freshness', 'real_time')]
        ]
        
        metadata_table = Table(metadata_data, colWidths=[2*inch, 3*inch])
        metadata_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ]))
        
        story.append(metadata_table)
        story.append(Spacer(1, 20))
        
        # KPI Summary
        if 'kpi_summary' in data and data['kpi_summary']:
            story.append(Paragraph("ملخص مؤشرات الأداء الرئيسية", arabic_style))
            story.append(Spacer(1, 12))
            
            kpi_data = [['المؤشر', 'القيمة الحالية', 'الهدف', 'النمو %', 'الحالة']]
            for kpi in data['kpi_summary']:
                kpi_data.append([
                    kpi.get('name', ''),
                    str(kpi.get('value', 0)),
                    str(kpi.get('target', 'غير محدد')),
                    f"{kpi.get('growth', 0):.1f}%",
                    kpi.get('status', '')
                ])
            
            kpi_table = Table(kpi_data, colWidths=[1.5*inch, 1*inch, 1*inch, 1*inch, 1*inch])
            kpi_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ]))
            
            story.append(kpi_table)
            story.append(Spacer(1, 20))
        
        # Revenue Overview
        if 'revenue_overview' in data:
            revenue = data['revenue_overview']
            story.append(Paragraph("نظرة عامة على الإيرادات", arabic_style))
            story.append(Spacer(1, 12))
            
            revenue_data = [
                ['الإيرادات الحالية', f"{revenue.get('current_revenue', 0):,.2f} ج.م"],
                ['الإيرادات السابقة', f"{revenue.get('previous_revenue', 0):,.2f} ج.م"],
                ['نسبة النمو', f"{revenue.get('growth_percentage', 0):.1f}%"],
                ['العملة', revenue.get('currency', 'EGP')]
            ]
            
            revenue_table = Table(revenue_data, colWidths=[2*inch, 2*inch])
            revenue_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 0), (0, -1), colors.lightblue),
            ]))
            
            story.append(revenue_table)
            story.append(Spacer(1, 20))
        
        # Build PDF
        doc.build(story)
        
        # Get PDF content
        pdf_content = buffer.getvalue()
        buffer.close()
        
        return pdf_content
    
    def generate_revenue_analysis_pdf(self, report: AnalyticsReport, data: Dict[str, Any]) -> bytes:
        """Generate revenue analysis PDF report"""
        buffer = io.BytesIO()
        
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        story = []
        styles = getSampleStyleSheet()
        
        # Title
        title_style = ParagraphStyle(
            'ArabicTitle',
            parent=styles['Title'],
            fontName='Helvetica-Bold',
            fontSize=18,
            alignment=TA_CENTER
        )
        
        story.append(Paragraph("تقرير تحليل الإيرادات", title_style))
        story.append(Spacer(1, 20))
        
        # Revenue trend data
        if 'revenue_trend' in data and data['revenue_trend']:
            story.append(Paragraph("اتجاه الإيرادات الشهرية", styles['Heading2']))
            story.append(Spacer(1, 12))
            
            trend_data = [['الشهر', 'الإيرادات (ج.م)', 'تاريخ البداية', 'تاريخ النهاية']]
            for item in data['revenue_trend']:
                trend_data.append([
                    item.get('period_display', ''),
                    f"{item.get('revenue', 0):,.2f}",
                    item.get('start_date', ''),
                    item.get('end_date', '')
                ])
            
            trend_table = Table(trend_data)
            trend_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ]))
            
            story.append(trend_table)
        
        doc.build(story)
        pdf_content = buffer.getvalue()
        buffer.close()
        
        return pdf_content


class ExcelReportGenerator:
    """Excel report generator using openpyxl"""
    
    def __init__(self):
        if not OPENPYXL_AVAILABLE:
            raise ReportGeneratorError("openpyxl is not installed. Please install it with: pip install openpyxl")
    
    def generate_executive_dashboard_excel(self, report: AnalyticsReport, data: Dict[str, Any]) -> bytes:
        """Generate executive dashboard Excel report"""
        workbook = openpyxl.Workbook()
        
        # Remove default sheet
        workbook.remove(workbook.active)
        
        # Create sheets
        summary_sheet = workbook.create_sheet("ملخص تنفيذي")
        kpi_sheet = workbook.create_sheet("مؤشرات الأداء")
        revenue_sheet = workbook.create_sheet("الإيرادات")
        
        # Style definitions
        header_font = Font(bold=True, size=14)
        data_font = Font(size=11)
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        
        # Summary Sheet
        self._create_summary_sheet(summary_sheet, report, data, header_font, data_font, header_fill)
        
        # KPI Sheet
        if 'kpi_summary' in data:
            self._create_kpi_sheet(kpi_sheet, data['kpi_summary'], header_font, data_font, header_fill)
        
        # Revenue Sheet
        if 'revenue_trend' in data:
            self._create_revenue_sheet(revenue_sheet, data['revenue_trend'], header_font, data_font, header_fill)
        
        # Save to bytes
        buffer = io.BytesIO()
        workbook.save(buffer)
        excel_content = buffer.getvalue()
        buffer.close()
        
        return excel_content
    
    def _create_summary_sheet(self, sheet, report, data, header_font, data_font, header_fill):
        """Create summary sheet"""
        sheet['A1'] = "تقرير لوحة المدير التنفيذي"
        sheet['A1'].font = header_font
        
        # Report metadata
        sheet['A3'] = "تاريخ التقرير:"
        sheet['B3'] = data.get('generated_at', timezone.now().strftime('%Y-%m-%d %H:%M'))
        sheet['A4'] = "الفترة:"
        sheet['B4'] = f"{report.start_date} إلى {report.end_date}"
        sheet['A5'] = "نوع التقرير:"
        sheet['B5'] = report.get_report_type_display()
        
        # Apply styles
        for row in range(3, 6):
            sheet[f'A{row}'].font = data_font
            sheet[f'B{row}'].font = data_font
    
    def _create_kpi_sheet(self, sheet, kpi_data, header_font, data_font, header_fill):
        """Create KPI sheet"""
        headers = ['المؤشر', 'القيمة الحالية', 'الهدف', 'النمو %', 'الحالة']
        
        # Add headers
        for col, header in enumerate(headers, 1):
            cell = sheet.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
        
        # Add data
        for row, kpi in enumerate(kpi_data, 2):
            sheet.cell(row=row, column=1, value=kpi.get('name', ''))
            sheet.cell(row=row, column=2, value=kpi.get('value', 0))
            sheet.cell(row=row, column=3, value=kpi.get('target', 'غير محدد'))
            sheet.cell(row=row, column=4, value=f"{kpi.get('growth', 0):.1f}%")
            sheet.cell(row=row, column=5, value=kpi.get('status', ''))
        
        # Auto-adjust column widths
        for column in sheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            sheet.column_dimensions[column_letter].width = adjusted_width
    
    def _create_revenue_sheet(self, sheet, revenue_data, header_font, data_font, header_fill):
        """Create revenue sheet"""
        headers = ['الشهر', 'الإيرادات (ج.م)', 'تاريخ البداية', 'تاريخ النهاية']
        
        # Add headers
        for col, header in enumerate(headers, 1):
            cell = sheet.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
        
        # Add data
        for row, item in enumerate(revenue_data, 2):
            sheet.cell(row=row, column=1, value=item.get('period_display', ''))
            sheet.cell(row=row, column=2, value=item.get('revenue', 0))
            sheet.cell(row=row, column=3, value=item.get('start_date', ''))
            sheet.cell(row=row, column=4, value=item.get('end_date', ''))
        
        # Auto-adjust column widths
        for column in sheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            sheet.column_dimensions[column_letter].width = adjusted_width


class ReportGeneratorFactory:
    """Factory class for creating report generators"""
    
    @staticmethod
    def create_pdf_generator() -> PDFReportGenerator:
        """Create PDF report generator"""
        return PDFReportGenerator()
    
    @staticmethod
    def create_excel_generator() -> ExcelReportGenerator:
        """Create Excel report generator"""
        return ExcelReportGenerator()
    
    @staticmethod
    def generate_report(report: AnalyticsReport, file_type: str = 'pdf') -> bytes:
        """Generate report based on type and format"""
        try:
            # Get report data
            from .views import ExecutiveDashboardView
            dashboard_view = ExecutiveDashboardView()
            data = dashboard_view._generate_executive_dashboard_data(None)
            
            if file_type.lower() == 'pdf':
                generator = ReportGeneratorFactory.create_pdf_generator()
                if report.report_type == AnalyticsReport.ReportType.EXECUTIVE_DASHBOARD:
                    return generator.generate_executive_dashboard_pdf(report, data)
                elif report.report_type == AnalyticsReport.ReportType.REVENUE_ANALYSIS:
                    return generator.generate_revenue_analysis_pdf(report, data)
                else:
                    return generator.generate_executive_dashboard_pdf(report, data)
            
            elif file_type.lower() == 'excel':
                generator = ReportGeneratorFactory.create_excel_generator()
                return generator.generate_executive_dashboard_excel(report, data)
            
            else:
                raise ReportGeneratorError(f"Unsupported file type: {file_type}")
                
        except Exception as e:
            raise ReportGeneratorError(f"Error generating report: {str(e)}")
