# Generated by Django 4.2.9 on 2025-06-05 20:36

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import simple_history.models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="KPIMetric",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم المؤشر")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                (
                    "metric_type",
                    models.CharField(
                        choices=[
                            ("revenue", "الإيرادات"),
                            ("profit", "الأرباح"),
                            ("client_count", "عدد العملاء"),
                            ("project_count", "عدد المشاريع"),
                            ("task_completion", "إنجاز المهام"),
                            ("team_productivity", "إنتاجية الفريق"),
                            ("client_satisfaction", "رضا العملاء"),
                            ("conversion_rate", "معدل التحويل"),
                            ("avg_project_value", "متوسط قيمة المشروع"),
                            ("custom", "مخصص"),
                        ],
                        max_length=20,
                        verbose_name="نوع المؤشر",
                    ),
                ),
                (
                    "current_value",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="القيمة الحالية",
                    ),
                ),
                (
                    "target_value",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=15,
                        null=True,
                        verbose_name="القيمة المستهدفة",
                    ),
                ),
                (
                    "previous_value",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="القيمة السابقة",
                    ),
                ),
                (
                    "unit",
                    models.CharField(
                        default="number", max_length=20, verbose_name="الوحدة"
                    ),
                ),
                (
                    "frequency",
                    models.CharField(
                        choices=[
                            ("real_time", "فوري"),
                            ("hourly", "كل ساعة"),
                            ("daily", "يومي"),
                            ("weekly", "أسبوعي"),
                            ("monthly", "شهري"),
                        ],
                        default="daily",
                        max_length=15,
                        verbose_name="تكرار التحديث",
                    ),
                ),
                (
                    "calculation_query",
                    models.TextField(
                        blank=True, null=True, verbose_name="استعلام الحساب"
                    ),
                ),
                (
                    "is_automated",
                    models.BooleanField(default=True, verbose_name="تلقائي"),
                ),
                (
                    "display_order",
                    models.PositiveIntegerField(default=0, verbose_name="ترتيب العرض"),
                ),
                ("is_visible", models.BooleanField(default=True, verbose_name="مرئي")),
                (
                    "color_code",
                    models.CharField(
                        default="#3B82F6", max_length=7, verbose_name="رمز اللون"
                    ),
                ),
                (
                    "last_calculated",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="آخر حساب"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "مؤشر أداء رئيسي",
                "verbose_name_plural": "مؤشرات الأداء الرئيسية",
                "ordering": ["display_order", "name"],
                "indexes": [
                    models.Index(
                        fields=["metric_type", "is_visible"],
                        name="bi_kpi_type_visible_idx",
                    ),
                    models.Index(
                        fields=["frequency", "last_calculated"],
                        name="bi_kpi_freq_calc_idx",
                    ),
                    models.Index(fields=["display_order"], name="bi_kpi_order_idx"),
                ],
            },
        ),
        migrations.CreateModel(
            name="HistoricalKPIMetric",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم المؤشر")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                (
                    "metric_type",
                    models.CharField(
                        choices=[
                            ("revenue", "الإيرادات"),
                            ("profit", "الأرباح"),
                            ("client_count", "عدد العملاء"),
                            ("project_count", "عدد المشاريع"),
                            ("task_completion", "إنجاز المهام"),
                            ("team_productivity", "إنتاجية الفريق"),
                            ("client_satisfaction", "رضا العملاء"),
                            ("conversion_rate", "معدل التحويل"),
                            ("avg_project_value", "متوسط قيمة المشروع"),
                            ("custom", "مخصص"),
                        ],
                        max_length=20,
                        verbose_name="نوع المؤشر",
                    ),
                ),
                (
                    "current_value",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="القيمة الحالية",
                    ),
                ),
                (
                    "target_value",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=15,
                        null=True,
                        verbose_name="القيمة المستهدفة",
                    ),
                ),
                (
                    "previous_value",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=15,
                        verbose_name="القيمة السابقة",
                    ),
                ),
                (
                    "unit",
                    models.CharField(
                        default="number", max_length=20, verbose_name="الوحدة"
                    ),
                ),
                (
                    "frequency",
                    models.CharField(
                        choices=[
                            ("real_time", "فوري"),
                            ("hourly", "كل ساعة"),
                            ("daily", "يومي"),
                            ("weekly", "أسبوعي"),
                            ("monthly", "شهري"),
                        ],
                        default="daily",
                        max_length=15,
                        verbose_name="تكرار التحديث",
                    ),
                ),
                (
                    "calculation_query",
                    models.TextField(
                        blank=True, null=True, verbose_name="استعلام الحساب"
                    ),
                ),
                (
                    "is_automated",
                    models.BooleanField(default=True, verbose_name="تلقائي"),
                ),
                (
                    "display_order",
                    models.PositiveIntegerField(default=0, verbose_name="ترتيب العرض"),
                ),
                ("is_visible", models.BooleanField(default=True, verbose_name="مرئي")),
                (
                    "color_code",
                    models.CharField(
                        default="#3B82F6", max_length=7, verbose_name="رمز اللون"
                    ),
                ),
                (
                    "last_calculated",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="آخر حساب"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ التحديث"
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "historical مؤشر أداء رئيسي",
                "verbose_name_plural": "historical مؤشرات الأداء الرئيسية",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="HistoricalAnalyticsReport",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "title",
                    models.CharField(max_length=200, verbose_name="عنوان التقرير"),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                (
                    "report_type",
                    models.CharField(
                        choices=[
                            ("executive_dashboard", "لوحة المدير التنفيذي"),
                            ("revenue_analysis", "تحليل الإيرادات"),
                            ("client_analysis", "تحليل العملاء"),
                            ("project_performance", "أداء المشاريع"),
                            ("team_performance", "أداء الفريق"),
                            ("financial_overview", "نظرة مالية شاملة"),
                            ("custom_report", "تقرير مخصص"),
                        ],
                        default="executive_dashboard",
                        max_length=20,
                        verbose_name="نوع التقرير",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("generating", "قيد الإنشاء"),
                            ("completed", "مكتمل"),
                            ("failed", "فشل"),
                            ("scheduled", "مجدول"),
                        ],
                        default="generating",
                        max_length=15,
                        verbose_name="الحالة",
                    ),
                ),
                (
                    "period_type",
                    models.CharField(
                        choices=[
                            ("daily", "يومي"),
                            ("weekly", "أسبوعي"),
                            ("monthly", "شهري"),
                            ("quarterly", "ربع سنوي"),
                            ("yearly", "سنوي"),
                            ("custom", "مخصص"),
                        ],
                        default="monthly",
                        max_length=15,
                        verbose_name="نوع الفترة",
                    ),
                ),
                ("start_date", models.DateField(verbose_name="تاريخ البداية")),
                ("end_date", models.DateField(verbose_name="تاريخ النهاية")),
                ("data", models.JSONField(default=dict, verbose_name="بيانات التقرير")),
                ("filters", models.JSONField(default=dict, verbose_name="المرشحات")),
                (
                    "configuration",
                    models.JSONField(default=dict, verbose_name="الإعدادات"),
                ),
                (
                    "pdf_file",
                    models.TextField(
                        blank=True, max_length=100, null=True, verbose_name="ملف PDF"
                    ),
                ),
                (
                    "excel_file",
                    models.TextField(
                        blank=True, max_length=100, null=True, verbose_name="ملف Excel"
                    ),
                ),
                (
                    "is_scheduled",
                    models.BooleanField(default=False, verbose_name="مجدول"),
                ),
                (
                    "schedule_frequency",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("daily", "يومي"),
                            ("weekly", "أسبوعي"),
                            ("monthly", "شهري"),
                            ("quarterly", "ربع سنوي"),
                            ("yearly", "سنوي"),
                            ("custom", "مخصص"),
                        ],
                        max_length=15,
                        null=True,
                        verbose_name="تكرار الجدولة",
                    ),
                ),
                (
                    "next_generation",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="الإنشاء التالي"
                    ),
                ),
                (
                    "generation_time",
                    models.DurationField(
                        blank=True, null=True, verbose_name="وقت الإنشاء"
                    ),
                ),
                (
                    "file_size",
                    models.PositiveIntegerField(
                        default=0, verbose_name="حجم الملف (بايت)"
                    ),
                ),
                (
                    "view_count",
                    models.PositiveIntegerField(
                        default=0, verbose_name="عدد المشاهدات"
                    ),
                ),
                (
                    "download_count",
                    models.PositiveIntegerField(
                        default=0, verbose_name="عدد التحميلات"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ التحديث"
                    ),
                ),
                (
                    "generated_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ الإنشاء الفعلي"
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="منشئ التقرير",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "historical تقرير تحليلي",
                "verbose_name_plural": "historical التقارير التحليلية",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="DashboardWidget",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title",
                    models.CharField(max_length=100, verbose_name="عنوان الودجت"),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                (
                    "widget_type",
                    models.CharField(
                        choices=[
                            ("kpi_card", "بطاقة مؤشر"),
                            ("chart", "مخطط بياني"),
                            ("table", "جدول"),
                            ("progress_bar", "شريط تقدم"),
                            ("gauge", "مقياس"),
                            ("map", "خريطة"),
                            ("calendar", "تقويم"),
                            ("activity_feed", "تغذية الأنشطة"),
                        ],
                        max_length=20,
                        verbose_name="نوع الودجت",
                    ),
                ),
                (
                    "dashboard_name",
                    models.CharField(
                        default="main", max_length=50, verbose_name="اسم لوحة التحكم"
                    ),
                ),
                (
                    "position_x",
                    models.PositiveIntegerField(
                        default=0, verbose_name="الموضع الأفقي"
                    ),
                ),
                (
                    "position_y",
                    models.PositiveIntegerField(
                        default=0, verbose_name="الموضع الرأسي"
                    ),
                ),
                ("width", models.PositiveIntegerField(default=4, verbose_name="العرض")),
                (
                    "height",
                    models.PositiveIntegerField(default=3, verbose_name="الارتفاع"),
                ),
                (
                    "data_source",
                    models.CharField(max_length=100, verbose_name="مصدر البيانات"),
                ),
                (
                    "chart_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("line", "خطي"),
                            ("bar", "أعمدة"),
                            ("pie", "دائري"),
                            ("doughnut", "كعكة"),
                            ("area", "منطقة"),
                            ("scatter", "نقطي"),
                        ],
                        max_length=15,
                        null=True,
                        verbose_name="نوع المخطط",
                    ),
                ),
                (
                    "configuration",
                    models.JSONField(default=dict, verbose_name="الإعدادات"),
                ),
                ("filters", models.JSONField(default=dict, verbose_name="المرشحات")),
                ("is_visible", models.BooleanField(default=True, verbose_name="مرئي")),
                (
                    "refresh_interval",
                    models.PositiveIntegerField(
                        default=300, verbose_name="فترة التحديث (ثانية)"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="dashboard_widgets",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="المستخدم",
                    ),
                ),
            ],
            options={
                "verbose_name": "ودجت لوحة التحكم",
                "verbose_name_plural": "ودجتات لوحة التحكم",
                "ordering": ["position_y", "position_x"],
                "indexes": [
                    models.Index(
                        fields=["user", "dashboard_name"],
                        name="bi_widget_user_dash_idx",
                    ),
                    models.Index(
                        fields=["widget_type", "is_visible"],
                        name="bi_widget_type_visible_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="AnalyticsReport",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title",
                    models.CharField(max_length=200, verbose_name="عنوان التقرير"),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                (
                    "report_type",
                    models.CharField(
                        choices=[
                            ("executive_dashboard", "لوحة المدير التنفيذي"),
                            ("revenue_analysis", "تحليل الإيرادات"),
                            ("client_analysis", "تحليل العملاء"),
                            ("project_performance", "أداء المشاريع"),
                            ("team_performance", "أداء الفريق"),
                            ("financial_overview", "نظرة مالية شاملة"),
                            ("custom_report", "تقرير مخصص"),
                        ],
                        default="executive_dashboard",
                        max_length=20,
                        verbose_name="نوع التقرير",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("generating", "قيد الإنشاء"),
                            ("completed", "مكتمل"),
                            ("failed", "فشل"),
                            ("scheduled", "مجدول"),
                        ],
                        default="generating",
                        max_length=15,
                        verbose_name="الحالة",
                    ),
                ),
                (
                    "period_type",
                    models.CharField(
                        choices=[
                            ("daily", "يومي"),
                            ("weekly", "أسبوعي"),
                            ("monthly", "شهري"),
                            ("quarterly", "ربع سنوي"),
                            ("yearly", "سنوي"),
                            ("custom", "مخصص"),
                        ],
                        default="monthly",
                        max_length=15,
                        verbose_name="نوع الفترة",
                    ),
                ),
                ("start_date", models.DateField(verbose_name="تاريخ البداية")),
                ("end_date", models.DateField(verbose_name="تاريخ النهاية")),
                ("data", models.JSONField(default=dict, verbose_name="بيانات التقرير")),
                ("filters", models.JSONField(default=dict, verbose_name="المرشحات")),
                (
                    "configuration",
                    models.JSONField(default=dict, verbose_name="الإعدادات"),
                ),
                (
                    "pdf_file",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="reports/pdfs/",
                        verbose_name="ملف PDF",
                    ),
                ),
                (
                    "excel_file",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="reports/excel/",
                        verbose_name="ملف Excel",
                    ),
                ),
                (
                    "is_scheduled",
                    models.BooleanField(default=False, verbose_name="مجدول"),
                ),
                (
                    "schedule_frequency",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("daily", "يومي"),
                            ("weekly", "أسبوعي"),
                            ("monthly", "شهري"),
                            ("quarterly", "ربع سنوي"),
                            ("yearly", "سنوي"),
                            ("custom", "مخصص"),
                        ],
                        max_length=15,
                        null=True,
                        verbose_name="تكرار الجدولة",
                    ),
                ),
                (
                    "next_generation",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="الإنشاء التالي"
                    ),
                ),
                (
                    "generation_time",
                    models.DurationField(
                        blank=True, null=True, verbose_name="وقت الإنشاء"
                    ),
                ),
                (
                    "file_size",
                    models.PositiveIntegerField(
                        default=0, verbose_name="حجم الملف (بايت)"
                    ),
                ),
                (
                    "view_count",
                    models.PositiveIntegerField(
                        default=0, verbose_name="عدد المشاهدات"
                    ),
                ),
                (
                    "download_count",
                    models.PositiveIntegerField(
                        default=0, verbose_name="عدد التحميلات"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "generated_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ الإنشاء الفعلي"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_reports",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="منشئ التقرير",
                    ),
                ),
                (
                    "shared_with",
                    models.ManyToManyField(
                        blank=True,
                        related_name="shared_reports",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="مشارك مع",
                    ),
                ),
            ],
            options={
                "verbose_name": "تقرير تحليلي",
                "verbose_name_plural": "التقارير التحليلية",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["report_type", "status"],
                        name="bi_report_type_status_idx",
                    ),
                    models.Index(
                        fields=["created_by", "created_at"],
                        name="bi_report_user_created_idx",
                    ),
                    models.Index(
                        fields=["start_date", "end_date"], name="bi_report_period_idx"
                    ),
                    models.Index(
                        fields=["is_scheduled", "next_generation"],
                        name="bi_report_schedule_idx",
                    ),
                ],
            },
        ),
    ]
