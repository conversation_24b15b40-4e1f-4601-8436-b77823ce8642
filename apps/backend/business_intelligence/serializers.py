"""
Business Intelligence & Analytics Serializers
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import AnalyticsReport, KPIMetric, DashboardWidget

User = get_user_model()


class UserListSerializer(serializers.ModelSerializer):
    """Lightweight user serializer for lists"""

    full_name = serializers.CharField(read_only=True)

    class Meta:
        model = User
        fields = ["id", "username", "first_name", "last_name", "full_name", "role"]


class AnalyticsReportSerializer(serializers.ModelSerializer):
    """Analytics report serializer with all fields"""

    created_by = UserListSerializer(read_only=True)
    shared_with = UserListSerializer(many=True, read_only=True)
    shared_with_ids = serializers.ListField(
        child=serializers.IntegerField(), write_only=True, required=False
    )

    report_type_display = serializers.CharField(
        source="get_report_type_display", read_only=True
    )
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    period_type_display = serializers.CharField(
        source="get_period_type_display", read_only=True
    )

    is_expired = serializers.BooleanField(read_only=True)
    generation_time_seconds = serializers.SerializerMethodField()
    file_size_mb = serializers.SerializerMethodField()

    class Meta:
        model = AnalyticsReport
        fields = [
            "id",
            "title",
            "description",
            "report_type",
            "report_type_display",
            "status",
            "status_display",
            "created_by",
            "shared_with",
            "shared_with_ids",
            "period_type",
            "period_type_display",
            "start_date",
            "end_date",
            "data",
            "filters",
            "configuration",
            "pdf_file",
            "excel_file",
            "is_scheduled",
            "schedule_frequency",
            "next_generation",
            "generation_time",
            "generation_time_seconds",
            "file_size",
            "file_size_mb",
            "view_count",
            "download_count",
            "is_expired",
            "created_at",
            "updated_at",
            "generated_at",
        ]
        read_only_fields = [
            "id",
            "created_by",
            "status",
            "generation_time",
            "file_size",
            "view_count",
            "download_count",
            "created_at",
            "updated_at",
            "generated_at",
        ]

    def get_generation_time_seconds(self, obj):
        """Get generation time in seconds"""
        if obj.generation_time:
            return obj.generation_time.total_seconds()
        return None

    def get_file_size_mb(self, obj):
        """Get file size in MB"""
        if obj.file_size:
            return round(obj.file_size / (1024 * 1024), 2)
        return 0

    def create(self, validated_data):
        """Create analytics report with shared users"""
        shared_with_ids = validated_data.pop("shared_with_ids", [])
        validated_data["created_by"] = self.context["request"].user

        report = super().create(validated_data)

        if shared_with_ids:
            shared_users = User.objects.filter(id__in=shared_with_ids)
            report.shared_with.set(shared_users)

        return report

    def update(self, instance, validated_data):
        """Update analytics report with shared users"""
        shared_with_ids = validated_data.pop("shared_with_ids", None)

        instance = super().update(instance, validated_data)

        if shared_with_ids is not None:
            shared_users = User.objects.filter(id__in=shared_with_ids)
            instance.shared_with.set(shared_users)

        return instance


class AnalyticsReportListSerializer(serializers.ModelSerializer):
    """Lightweight analytics report serializer for lists"""

    created_by = UserListSerializer(read_only=True)
    report_type_display = serializers.CharField(
        source="get_report_type_display", read_only=True
    )
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    is_expired = serializers.BooleanField(read_only=True)
    file_size_mb = serializers.SerializerMethodField()

    class Meta:
        model = AnalyticsReport
        fields = [
            "id",
            "title",
            "report_type",
            "report_type_display",
            "status",
            "status_display",
            "created_by",
            "start_date",
            "end_date",
            "is_scheduled",
            "file_size_mb",
            "view_count",
            "download_count",
            "is_expired",
            "created_at",
            "generated_at",
        ]

    def get_file_size_mb(self, obj):
        """Get file size in MB"""
        if obj.file_size:
            return round(obj.file_size / (1024 * 1024), 2)
        return 0


class AnalyticsReportCreateSerializer(serializers.ModelSerializer):
    """Analytics report creation serializer"""

    shared_with_ids = serializers.ListField(
        child=serializers.IntegerField(), required=False, allow_empty=True
    )

    class Meta:
        model = AnalyticsReport
        fields = [
            "title",
            "description",
            "report_type",
            "period_type",
            "start_date",
            "end_date",
            "filters",
            "configuration",
            "shared_with_ids",
            "is_scheduled",
            "schedule_frequency",
        ]

    def validate(self, data):
        """Validate report data"""
        if data["start_date"] > data["end_date"]:
            raise serializers.ValidationError(
                "تاريخ البداية يجب أن يكون قبل تاريخ النهاية"
            )

        if data.get("is_scheduled") and not data.get("schedule_frequency"):
            raise serializers.ValidationError(
                "يجب تحديد تكرار الجدولة للتقارير المجدولة"
            )

        return data


class KPIMetricSerializer(serializers.ModelSerializer):
    """KPI metric serializer with all fields"""

    metric_type_display = serializers.CharField(
        source="get_metric_type_display", read_only=True
    )
    frequency_display = serializers.CharField(
        source="get_frequency_display", read_only=True
    )

    growth_percentage = serializers.DecimalField(
        max_digits=10, decimal_places=2, read_only=True
    )
    target_achievement_percentage = serializers.DecimalField(
        max_digits=10, decimal_places=2, read_only=True
    )
    status = serializers.CharField(read_only=True)

    class Meta:
        model = KPIMetric
        fields = [
            "id",
            "name",
            "description",
            "metric_type",
            "metric_type_display",
            "current_value",
            "target_value",
            "previous_value",
            "unit",
            "frequency",
            "frequency_display",
            "calculation_query",
            "is_automated",
            "display_order",
            "is_visible",
            "color_code",
            "growth_percentage",
            "target_achievement_percentage",
            "status",
            "last_calculated",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "last_calculated", "created_at", "updated_at"]


class KPIMetricListSerializer(serializers.ModelSerializer):
    """Lightweight KPI metric serializer for lists"""

    metric_type_display = serializers.CharField(
        source="get_metric_type_display", read_only=True
    )
    growth_percentage = serializers.DecimalField(
        max_digits=10, decimal_places=2, read_only=True
    )
    target_achievement_percentage = serializers.DecimalField(
        max_digits=10, decimal_places=2, read_only=True
    )
    status = serializers.CharField(read_only=True)

    class Meta:
        model = KPIMetric
        fields = [
            "id",
            "name",
            "metric_type",
            "metric_type_display",
            "current_value",
            "target_value",
            "unit",
            "growth_percentage",
            "target_achievement_percentage",
            "status",
            "color_code",
            "is_visible",
            "last_calculated",
        ]


class KPIMetricCreateSerializer(serializers.ModelSerializer):
    """KPI metric creation serializer"""

    class Meta:
        model = KPIMetric
        fields = [
            "name",
            "description",
            "metric_type",
            "target_value",
            "unit",
            "frequency",
            "calculation_query",
            "is_automated",
            "display_order",
            "is_visible",
            "color_code",
        ]


class DashboardWidgetSerializer(serializers.ModelSerializer):
    """Dashboard widget serializer with all fields"""

    user = UserListSerializer(read_only=True)
    widget_type_display = serializers.CharField(
        source="get_widget_type_display", read_only=True
    )
    chart_type_display = serializers.CharField(
        source="get_chart_type_display", read_only=True
    )

    class Meta:
        model = DashboardWidget
        fields = [
            "id",
            "title",
            "description",
            "widget_type",
            "widget_type_display",
            "user",
            "dashboard_name",
            "position_x",
            "position_y",
            "width",
            "height",
            "data_source",
            "chart_type",
            "chart_type_display",
            "configuration",
            "filters",
            "is_visible",
            "refresh_interval",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "user", "created_at", "updated_at"]


class DashboardWidgetListSerializer(serializers.ModelSerializer):
    """Lightweight dashboard widget serializer for lists"""

    widget_type_display = serializers.CharField(
        source="get_widget_type_display", read_only=True
    )

    class Meta:
        model = DashboardWidget
        fields = [
            "id",
            "title",
            "widget_type",
            "widget_type_display",
            "dashboard_name",
            "position_x",
            "position_y",
            "width",
            "height",
            "is_visible",
            "refresh_interval",
        ]


class DashboardWidgetCreateSerializer(serializers.ModelSerializer):
    """Dashboard widget creation serializer"""

    class Meta:
        model = DashboardWidget
        fields = [
            "title",
            "description",
            "widget_type",
            "dashboard_name",
            "position_x",
            "position_y",
            "width",
            "height",
            "data_source",
            "chart_type",
            "configuration",
            "filters",
            "is_visible",
            "refresh_interval",
        ]


# Executive Dashboard Data Serializers
class ExecutiveDashboardSerializer(serializers.Serializer):
    """Executive dashboard data serializer"""

    # KPI Summary
    kpi_summary = serializers.DictField()

    # Revenue Analytics
    revenue_overview = serializers.DictField()
    revenue_trend = serializers.ListField()
    revenue_by_source = serializers.ListField()

    # Client Analytics
    client_overview = serializers.DictField()
    client_satisfaction = serializers.DictField()
    client_acquisition_trend = serializers.ListField()
    top_clients = serializers.ListField()

    # Project Analytics
    project_overview = serializers.DictField()
    project_status_distribution = serializers.ListField()
    project_performance_trend = serializers.ListField()

    # Team Analytics
    team_overview = serializers.DictField()
    team_productivity = serializers.ListField()
    task_completion_trend = serializers.ListField()

    # Financial Overview
    financial_summary = serializers.DictField()
    cash_flow_trend = serializers.ListField()
    expense_breakdown = serializers.ListField()

    # Recent Activities
    recent_activities = serializers.ListField()

    # System Health
    system_health = serializers.DictField()

    # Metadata
    generated_at = serializers.DateTimeField()
    data_freshness = serializers.CharField()
    cache_status = serializers.CharField()


class RevenueAnalyticsSerializer(serializers.Serializer):
    """Revenue analytics data serializer"""

    # Overview
    total_revenue = serializers.DecimalField(max_digits=15, decimal_places=2)
    monthly_revenue = serializers.DecimalField(max_digits=15, decimal_places=2)
    revenue_growth = serializers.DecimalField(max_digits=10, decimal_places=2)
    revenue_target = serializers.DecimalField(max_digits=15, decimal_places=2)
    target_achievement = serializers.DecimalField(max_digits=10, decimal_places=2)

    # Trends
    revenue_trend_monthly = serializers.ListField()
    revenue_trend_weekly = serializers.ListField()
    revenue_forecast = serializers.ListField()

    # Breakdown
    revenue_by_service = serializers.ListField()
    revenue_by_client = serializers.ListField()
    revenue_by_team_member = serializers.ListField()

    # Comparisons
    year_over_year = serializers.DictField()
    quarter_over_quarter = serializers.DictField()

    # Metadata
    period_start = serializers.DateField()
    period_end = serializers.DateField()
    generated_at = serializers.DateTimeField()


class ClientAnalyticsSerializer(serializers.Serializer):
    """Client analytics data serializer"""

    # Overview
    total_clients = serializers.IntegerField()
    new_clients_this_month = serializers.IntegerField()
    active_clients = serializers.IntegerField()
    client_retention_rate = serializers.DecimalField(max_digits=10, decimal_places=2)

    # Satisfaction
    satisfaction_overview = serializers.DictField()
    satisfaction_trend = serializers.ListField()

    # Acquisition
    acquisition_channels = serializers.ListField()
    acquisition_trend = serializers.ListField()
    conversion_rates = serializers.DictField()

    # Value Analysis
    client_lifetime_value = serializers.DecimalField(max_digits=15, decimal_places=2)
    average_project_value = serializers.DecimalField(max_digits=15, decimal_places=2)
    top_clients_by_revenue = serializers.ListField()

    # Geographic Distribution
    clients_by_governorate = serializers.ListField()

    # Metadata
    period_start = serializers.DateField()
    period_end = serializers.DateField()
    generated_at = serializers.DateTimeField()


class ProjectAnalyticsSerializer(serializers.Serializer):
    """Project analytics data serializer"""

    # Overview
    total_projects = serializers.IntegerField()
    active_projects = serializers.IntegerField()
    completed_projects = serializers.IntegerField()
    overdue_projects = serializers.IntegerField()

    # Performance
    average_completion_time = serializers.DecimalField(max_digits=10, decimal_places=2)
    on_time_delivery_rate = serializers.DecimalField(max_digits=10, decimal_places=2)
    budget_variance = serializers.DecimalField(max_digits=10, decimal_places=2)

    # Status Distribution
    project_status_breakdown = serializers.ListField()
    project_type_breakdown = serializers.ListField()

    # Trends
    project_completion_trend = serializers.ListField()
    project_value_trend = serializers.ListField()

    # Team Performance
    projects_by_team_member = serializers.ListField()
    team_efficiency = serializers.ListField()

    # Metadata
    period_start = serializers.DateField()
    period_end = serializers.DateField()
    generated_at = serializers.DateTimeField()


class TeamAnalyticsSerializer(serializers.Serializer):
    """Team analytics data serializer"""

    # Overview
    total_team_members = serializers.IntegerField()
    active_team_members = serializers.IntegerField()
    average_productivity = serializers.DecimalField(max_digits=10, decimal_places=2)

    # Task Performance
    total_tasks_completed = serializers.IntegerField()
    average_task_completion_time = serializers.DecimalField(
        max_digits=10, decimal_places=2
    )
    task_completion_rate = serializers.DecimalField(max_digits=10, decimal_places=2)

    # Individual Performance
    team_member_performance = serializers.ListField()
    top_performers = serializers.ListField()

    # Workload Analysis
    workload_distribution = serializers.ListField()
    capacity_utilization = serializers.DecimalField(max_digits=10, decimal_places=2)

    # Trends
    productivity_trend = serializers.ListField()
    task_completion_trend = serializers.ListField()

    # Metadata
    period_start = serializers.DateField()
    period_end = serializers.DateField()
    generated_at = serializers.DateTimeField()
