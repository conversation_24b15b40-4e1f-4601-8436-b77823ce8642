"""
Business Intelligence Caching System
High-performance caching for dashboard data and analytics
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

import json
import hashlib
from typing import Any, Optional, Dict, List
from datetime import datetime, timedelta

from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
from django.core.serializers.json import DjangoJSONEncoder

import logging

logger = logging.getLogger(__name__)


class AnalyticsCacheService:
    """Centralized caching service for business intelligence data"""
    
    # Cache timeout constants (in seconds)
    TIMEOUT_SHORT = 300      # 5 minutes
    TIMEOUT_MEDIUM = 1800    # 30 minutes
    TIMEOUT_LONG = 3600      # 1 hour
    TIMEOUT_EXTENDED = 7200  # 2 hours
    TIMEOUT_DAILY = 86400    # 24 hours
    
    # Cache key prefixes
    PREFIX_DASHBOARD = "bi_dashboard"
    PREFIX_KPI = "bi_kpi"
    PREFIX_REPORT = "bi_report"
    PREFIX_WIDGET = "bi_widget"
    PREFIX_ANALYTICS = "bi_analytics"
    
    @classmethod
    def _generate_cache_key(cls, prefix: str, identifier: str, params: Optional[Dict] = None) -> str:
        """Generate a unique cache key"""
        key_parts = [prefix, str(identifier)]
        
        if params:
            # Sort params for consistent key generation
            sorted_params = sorted(params.items())
            params_str = json.dumps(sorted_params, sort_keys=True)
            params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
            key_parts.append(params_hash)
        
        return ":".join(key_parts)
    
    @classmethod
    def get(cls, key: str) -> Optional[Any]:
        """Get data from cache"""
        try:
            cached_data = cache.get(key)
            if cached_data is not None:
                logger.debug(f"Cache hit for key: {key}")
                return cached_data
            else:
                logger.debug(f"Cache miss for key: {key}")
                return None
        except Exception as e:
            logger.error(f"Error getting cache key {key}: {str(e)}")
            return None
    
    @classmethod
    def set(cls, key: str, value: Any, timeout: int = TIMEOUT_MEDIUM) -> bool:
        """Set data in cache"""
        try:
            # Serialize complex objects
            if hasattr(value, '__dict__') or isinstance(value, (list, dict)):
                serialized_value = json.dumps(value, cls=DjangoJSONEncoder)
                cache.set(key, serialized_value, timeout)
            else:
                cache.set(key, value, timeout)
            
            logger.debug(f"Cache set for key: {key}, timeout: {timeout}")
            return True
        except Exception as e:
            logger.error(f"Error setting cache key {key}: {str(e)}")
            return False
    
    @classmethod
    def delete(cls, key: str) -> bool:
        """Delete data from cache"""
        try:
            cache.delete(key)
            logger.debug(f"Cache deleted for key: {key}")
            return True
        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {str(e)}")
            return False
    
    @classmethod
    def delete_pattern(cls, pattern: str) -> int:
        """Delete all cache keys matching pattern"""
        try:
            if hasattr(cache, 'delete_pattern'):
                deleted_count = cache.delete_pattern(pattern)
                logger.debug(f"Deleted {deleted_count} cache keys matching pattern: {pattern}")
                return deleted_count
            else:
                # Fallback for cache backends that don't support pattern deletion
                logger.warning("Cache backend doesn't support pattern deletion")
                return 0
        except Exception as e:
            logger.error(f"Error deleting cache pattern {pattern}: {str(e)}")
            return 0
    
    @classmethod
    def clear_user_cache(cls, user_id: int) -> int:
        """Clear all cache entries for a specific user"""
        patterns = [
            f"{cls.PREFIX_DASHBOARD}:*:{user_id}:*",
            f"{cls.PREFIX_KPI}:*:{user_id}:*",
            f"{cls.PREFIX_WIDGET}:*:{user_id}:*",
        ]
        
        total_deleted = 0
        for pattern in patterns:
            total_deleted += cls.delete_pattern(pattern)
        
        logger.info(f"Cleared {total_deleted} cache entries for user {user_id}")
        return total_deleted


class DashboardCacheManager:
    """Specialized cache manager for dashboard data"""
    
    @classmethod
    def get_executive_dashboard(cls, user_id: int, params: Optional[Dict] = None) -> Optional[Dict]:
        """Get executive dashboard data from cache"""
        cache_key = AnalyticsCacheService._generate_cache_key(
            AnalyticsCacheService.PREFIX_DASHBOARD,
            f"executive:{user_id}",
            params
        )
        return AnalyticsCacheService.get(cache_key)
    
    @classmethod
    def set_executive_dashboard(cls, user_id: int, data: Dict, params: Optional[Dict] = None, timeout: int = AnalyticsCacheService.TIMEOUT_MEDIUM) -> bool:
        """Set executive dashboard data in cache"""
        cache_key = AnalyticsCacheService._generate_cache_key(
            AnalyticsCacheService.PREFIX_DASHBOARD,
            f"executive:{user_id}",
            params
        )
        
        # Add metadata
        cached_data = {
            'data': data,
            'cached_at': timezone.now().isoformat(),
            'expires_at': (timezone.now() + timedelta(seconds=timeout)).isoformat(),
            'cache_key': cache_key
        }
        
        return AnalyticsCacheService.set(cache_key, cached_data, timeout)
    
    @classmethod
    def invalidate_dashboard(cls, user_id: int) -> int:
        """Invalidate all dashboard cache for a user"""
        pattern = f"{AnalyticsCacheService.PREFIX_DASHBOARD}:*:{user_id}:*"
        return AnalyticsCacheService.delete_pattern(pattern)


class KPICacheManager:
    """Specialized cache manager for KPI data"""
    
    @classmethod
    def get_kpi_metrics(cls, user_id: int, filters: Optional[Dict] = None) -> Optional[List]:
        """Get KPI metrics from cache"""
        cache_key = AnalyticsCacheService._generate_cache_key(
            AnalyticsCacheService.PREFIX_KPI,
            f"metrics:{user_id}",
            filters
        )
        return AnalyticsCacheService.get(cache_key)
    
    @classmethod
    def set_kpi_metrics(cls, user_id: int, data: List, filters: Optional[Dict] = None, timeout: int = AnalyticsCacheService.TIMEOUT_SHORT) -> bool:
        """Set KPI metrics in cache"""
        cache_key = AnalyticsCacheService._generate_cache_key(
            AnalyticsCacheService.PREFIX_KPI,
            f"metrics:{user_id}",
            filters
        )
        return AnalyticsCacheService.set(cache_key, data, timeout)
    
    @classmethod
    def invalidate_kpi_metrics(cls, user_id: Optional[int] = None) -> int:
        """Invalidate KPI metrics cache"""
        if user_id:
            pattern = f"{AnalyticsCacheService.PREFIX_KPI}:metrics:{user_id}:*"
        else:
            pattern = f"{AnalyticsCacheService.PREFIX_KPI}:metrics:*"
        return AnalyticsCacheService.delete_pattern(pattern)


class WidgetCacheManager:
    """Specialized cache manager for widget data"""
    
    @classmethod
    def get_widget_data(cls, widget_id: int, user_id: int) -> Optional[Dict]:
        """Get widget data from cache"""
        cache_key = AnalyticsCacheService._generate_cache_key(
            AnalyticsCacheService.PREFIX_WIDGET,
            f"{widget_id}:{user_id}"
        )
        return AnalyticsCacheService.get(cache_key)
    
    @classmethod
    def set_widget_data(cls, widget_id: int, user_id: int, data: Dict, timeout: int = AnalyticsCacheService.TIMEOUT_SHORT) -> bool:
        """Set widget data in cache"""
        cache_key = AnalyticsCacheService._generate_cache_key(
            AnalyticsCacheService.PREFIX_WIDGET,
            f"{widget_id}:{user_id}"
        )
        return AnalyticsCacheService.set(cache_key, data, timeout)
    
    @classmethod
    def invalidate_widget(cls, widget_id: int, user_id: Optional[int] = None) -> int:
        """Invalidate widget cache"""
        if user_id:
            pattern = f"{AnalyticsCacheService.PREFIX_WIDGET}:{widget_id}:{user_id}"
            return AnalyticsCacheService.delete(pattern)
        else:
            pattern = f"{AnalyticsCacheService.PREFIX_WIDGET}:{widget_id}:*"
            return AnalyticsCacheService.delete_pattern(pattern)


class AnalyticsCacheManager:
    """Specialized cache manager for analytics data"""
    
    @classmethod
    def get_revenue_trend(cls, period_type: str, periods: int) -> Optional[List]:
        """Get revenue trend data from cache"""
        cache_key = AnalyticsCacheService._generate_cache_key(
            AnalyticsCacheService.PREFIX_ANALYTICS,
            "revenue_trend",
            {'period_type': period_type, 'periods': periods}
        )
        return AnalyticsCacheService.get(cache_key)
    
    @classmethod
    def set_revenue_trend(cls, period_type: str, periods: int, data: List, timeout: int = AnalyticsCacheService.TIMEOUT_LONG) -> bool:
        """Set revenue trend data in cache"""
        cache_key = AnalyticsCacheService._generate_cache_key(
            AnalyticsCacheService.PREFIX_ANALYTICS,
            "revenue_trend",
            {'period_type': period_type, 'periods': periods}
        )
        return AnalyticsCacheService.set(cache_key, data, timeout)
    
    @classmethod
    def get_client_analytics(cls, period_type: str) -> Optional[Dict]:
        """Get client analytics from cache"""
        cache_key = AnalyticsCacheService._generate_cache_key(
            AnalyticsCacheService.PREFIX_ANALYTICS,
            "client_analytics",
            {'period_type': period_type}
        )
        return AnalyticsCacheService.get(cache_key)
    
    @classmethod
    def set_client_analytics(cls, period_type: str, data: Dict, timeout: int = AnalyticsCacheService.TIMEOUT_LONG) -> bool:
        """Set client analytics in cache"""
        cache_key = AnalyticsCacheService._generate_cache_key(
            AnalyticsCacheService.PREFIX_ANALYTICS,
            "client_analytics",
            {'period_type': period_type}
        )
        return AnalyticsCacheService.set(cache_key, data, timeout)
    
    @classmethod
    def invalidate_analytics(cls, analytics_type: Optional[str] = None) -> int:
        """Invalidate analytics cache"""
        if analytics_type:
            pattern = f"{AnalyticsCacheService.PREFIX_ANALYTICS}:{analytics_type}:*"
        else:
            pattern = f"{AnalyticsCacheService.PREFIX_ANALYTICS}:*"
        return AnalyticsCacheService.delete_pattern(pattern)


class CacheWarmer:
    """Cache warming service for preloading frequently accessed data"""
    
    @classmethod
    def warm_dashboard_cache(cls, user_id: int) -> bool:
        """Warm up dashboard cache for a user"""
        try:
            from .views import ExecutiveDashboardView
            
            # Create a mock request object
            class MockRequest:
                def __init__(self, user_id):
                    self.user = type('User', (), {'id': user_id})()
            
            request = MockRequest(user_id)
            dashboard_view = ExecutiveDashboardView()
            
            # Generate and cache dashboard data
            dashboard_data = dashboard_view._generate_executive_dashboard_data(request)
            DashboardCacheManager.set_executive_dashboard(user_id, dashboard_data)
            
            logger.info(f"Warmed dashboard cache for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error warming dashboard cache for user {user_id}: {str(e)}")
            return False
    
    @classmethod
    def warm_kpi_cache(cls) -> bool:
        """Warm up KPI cache"""
        try:
            from .models import KPIMetric
            from .serializers import KPIMetricListSerializer
            
            # Get visible KPIs
            kpis = KPIMetric.objects.filter(is_visible=True).order_by('display_order')
            serializer = KPIMetricListSerializer(kpis, many=True)
            
            # Cache for all users (generic cache)
            KPICacheManager.set_kpi_metrics(0, serializer.data, timeout=AnalyticsCacheService.TIMEOUT_LONG)
            
            logger.info("Warmed KPI cache")
            return True
            
        except Exception as e:
            logger.error(f"Error warming KPI cache: {str(e)}")
            return False


# Convenience aliases for backward compatibility
CacheService = AnalyticsCacheService
DashboardCache = DashboardCacheManager
