"""
Business Intelligence & Analytics URLs
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    AnalyticsReportViewSet,
    KPIMetricViewSet,
    DashboardWidgetViewSet,
    ExecutiveDashboardView,
)

# Create router for viewsets
router = DefaultRouter()
router.register(r"reports", AnalyticsReportViewSet, basename="analytics-reports")
router.register(r"kpis", KPIMetricViewSet, basename="kpi-metrics")
router.register(r"widgets", DashboardWidgetViewSet, basename="dashboard-widgets")

# URL patterns
urlpatterns = [
    # ViewSet URLs
    path("", include(router.urls)),
    # Dashboard Analytics URLs
    path(
        "dashboard/executive/",
        ExecutiveDashboardView.as_view(),
        name="executive-dashboard",
    ),
]

app_name = "business_intelligence"
