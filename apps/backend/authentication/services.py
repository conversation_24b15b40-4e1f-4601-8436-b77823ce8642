"""
Authentication services for MTBRMG ERP system
"""
import qrcode
import io
import base64
from django.conf import settings
from django.contrib.auth import get_user_model
from django_otp.models import Device
from django_otp.plugins.otp_totp.models import TOTPDevice
from django_otp.plugins.otp_static.models import StaticDevice, StaticToken
from django.core.cache import cache
from django.utils import timezone
from datetime import timedelta
import secrets
import string

User = get_user_model()


class MFAService:
    """Service for managing Multi-Factor Authentication"""
    
    @staticmethod
    def setup_totp_for_user(user):
        """Setup TOTP device for user and return QR code"""
        # Check if user already has a TOTP device
        existing_device = TOTPDevice.objects.filter(user=user, confirmed=True).first()
        if existing_device:
            return None, "المستخدم لديه بالفعل جهاز TOTP مفعل"
        
        # Create new TOTP device
        device = TOTPDevice.objects.create(
            user=user,
            name=f"{user.username}-totp",
            confirmed=False
        )
        
        # Generate QR code
        qr_code_url = device.config_url
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(qr_code_url)
        qr.make(fit=True)
        
        # Create QR code image
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        qr_code_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        return {
            'device_id': device.id,
            'qr_code': f"data:image/png;base64,{qr_code_base64}",
            'secret_key': device.bin_key.hex(),
            'config_url': qr_code_url
        }, None
    
    @staticmethod
    def confirm_totp_setup(user, device_id, token):
        """Confirm TOTP setup with verification token"""
        try:
            device = TOTPDevice.objects.get(id=device_id, user=user, confirmed=False)
            if device.verify_token(token):
                device.confirmed = True
                device.save()
                
                # Generate backup codes
                backup_codes = MFAService.generate_backup_codes(user)
                
                return {
                    'success': True,
                    'backup_codes': backup_codes,
                    'message': 'تم تفعيل المصادقة الثنائية بنجاح'
                }
            else:
                return {
                    'success': False,
                    'message': 'رمز التحقق غير صحيح'
                }
        except TOTPDevice.DoesNotExist:
            return {
                'success': False,
                'message': 'جهاز TOTP غير موجود'
            }
    
    @staticmethod
    def verify_totp_token(user, token):
        """Verify TOTP token for user"""
        devices = TOTPDevice.objects.filter(user=user, confirmed=True)
        for device in devices:
            if device.verify_token(token):
                return True
        return False
    
    @staticmethod
    def generate_backup_codes(user, count=10):
        """Generate backup codes for user"""
        # Remove existing backup codes
        StaticDevice.objects.filter(user=user, name='backup').delete()
        
        # Create new static device for backup codes
        device = StaticDevice.objects.create(user=user, name='backup')
        
        backup_codes = []
        for _ in range(count):
            code = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(8))
            StaticToken.objects.create(device=device, token=code)
            backup_codes.append(code)
        
        return backup_codes
    
    @staticmethod
    def verify_backup_code(user, code):
        """Verify backup code and consume it"""
        try:
            device = StaticDevice.objects.get(user=user, name='backup')
            token = StaticToken.objects.get(device=device, token=code.upper())
            token.delete()  # Consume the backup code
            return True
        except (StaticDevice.DoesNotExist, StaticToken.DoesNotExist):
            return False
    
    @staticmethod
    def disable_mfa_for_user(user):
        """Disable MFA for user"""
        # Remove all TOTP devices
        TOTPDevice.objects.filter(user=user).delete()
        
        # Remove backup codes
        StaticDevice.objects.filter(user=user, name='backup').delete()
        
        return True
    
    @staticmethod
    def is_mfa_enabled(user):
        """Check if MFA is enabled for user"""
        return TOTPDevice.objects.filter(user=user, confirmed=True).exists()
    
    @staticmethod
    def get_backup_codes_count(user):
        """Get remaining backup codes count"""
        try:
            device = StaticDevice.objects.get(user=user, name='backup')
            return StaticToken.objects.filter(device=device).count()
        except StaticDevice.DoesNotExist:
            return 0


class SecurityService:
    """Service for security-related operations"""
    
    @staticmethod
    def log_security_event(user, event_type, details=None, ip_address=None):
        """Log security events"""
        from .models import SecurityLog
        
        SecurityLog.objects.create(
            user=user,
            event_type=event_type,
            details=details or {},
            ip_address=ip_address,
            timestamp=timezone.now()
        )
    
    @staticmethod
    def check_login_attempts(identifier, max_attempts=5, window_minutes=15):
        """Check failed login attempts for rate limiting"""
        cache_key = f"login_attempts_{identifier}"
        attempts = cache.get(cache_key, 0)
        
        if attempts >= max_attempts:
            return False, f"تم تجاوز عدد محاولات تسجيل الدخول المسموح. حاول مرة أخرى بعد {window_minutes} دقيقة"
        
        return True, None
    
    @staticmethod
    def record_failed_login(identifier, window_minutes=15):
        """Record a failed login attempt"""
        cache_key = f"login_attempts_{identifier}"
        attempts = cache.get(cache_key, 0)
        cache.set(cache_key, attempts + 1, timeout=window_minutes * 60)
    
    @staticmethod
    def clear_login_attempts(identifier):
        """Clear failed login attempts after successful login"""
        cache_key = f"login_attempts_{identifier}"
        cache.delete(cache_key)
    
    @staticmethod
    def generate_secure_token(length=32):
        """Generate a secure random token"""
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def validate_password_strength(password):
        """Validate password strength"""
        errors = []
        
        if len(password) < 8:
            errors.append("كلمة المرور يجب أن تكون 8 أحرف على الأقل")
        
        if not any(c.isupper() for c in password):
            errors.append("كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل")
        
        if not any(c.islower() for c in password):
            errors.append("كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل")
        
        if not any(c.isdigit() for c in password):
            errors.append("كلمة المرور يجب أن تحتوي على رقم واحد على الأقل")
        
        special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        if not any(c in special_chars for c in password):
            errors.append("كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل")
        
        return len(errors) == 0, errors


class AuditService:
    """Service for audit logging"""
    
    @staticmethod
    def log_user_action(user, action, model_name=None, object_id=None, changes=None, ip_address=None):
        """Log user actions for audit trail"""
        from .models import AuditLog
        
        AuditLog.objects.create(
            user=user,
            action=action,
            model_name=model_name,
            object_id=str(object_id) if object_id else None,
            changes=changes or {},
            ip_address=ip_address,
            timestamp=timezone.now()
        )
    
    @staticmethod
    def get_user_activity(user, days=30):
        """Get user activity for the last N days"""
        from .models import AuditLog
        
        since = timezone.now() - timedelta(days=days)
        return AuditLog.objects.filter(
            user=user,
            timestamp__gte=since
        ).order_by('-timestamp')
    
    @staticmethod
    def get_model_changes(model_name, object_id, days=30):
        """Get changes for a specific model instance"""
        from .models import AuditLog
        
        since = timezone.now() - timedelta(days=days)
        return AuditLog.objects.filter(
            model_name=model_name,
            object_id=str(object_id),
            timestamp__gte=since
        ).order_by('-timestamp')
