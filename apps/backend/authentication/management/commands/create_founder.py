from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from clients.models import Client
from projects.models import Project
from tasks.models import Task

User = get_user_model()


class Command(BaseCommand):
    help = 'Create founder user and sample data for MTBRMG ERP system'

    def handle(self, *args, **options):
        # Create founder user
        if not User.objects.filter(username='founder').exists():
            founder = User.objects.create_user(
                username='founder',
                email='<EMAIL>',
                password='demo123',
                first_name='محمد',
                last_name='يوسف',
                role='admin',
                status='active',
                phone='+201234567890',
                is_staff=True,
                is_superuser=True
            )
            self.stdout.write(
                self.style.SUCCESS(f'Successfully created founder user: {founder.username}')
            )
        else:
            self.stdout.write(
                self.style.WARNING('Founder user already exists')
            )

        # Create sample clients if they don't exist
        if not Client.objects.exists():
            clients_data = [
                {
                    'name': 'شركة التقنية المتقدمة',
                    'email': '<EMAIL>',
                    'phone': '+201111111111',
                    'company': 'شركة التقنية المتقدمة',
                    'website': 'https://techadvanced.com',
                    'address': 'القاهرة الجديدة، القاهرة',
                    'governorate': 'cairo',
                    'mood': 'happy',
                    'notes': 'عميل مميز، يطلب دائماً أحدث التقنيات',
                },
                {
                    'name': 'متجر الأزياء العصرية',
                    'email': '<EMAIL>',
                    'phone': '+201222222222',
                    'company': 'متجر الأزياء العصرية',
                    'website': 'https://fashionstore.com',
                    'address': 'الإسكندرية',
                    'governorate': 'alexandria',
                    'mood': 'neutral',
                    'notes': 'يركز على التجارة الإلكترونية والتسويق الرقمي',
                },
                {
                    'name': 'مطعم الأصالة',
                    'email': '<EMAIL>',
                    'phone': '+201333333333',
                    'company': 'مطعم الأصالة',
                    'website': 'https://asala-restaurant.com',
                    'address': 'الجيزة',
                    'governorate': 'giza',
                    'mood': 'concerned',
                    'notes': 'يحتاج إلى نظام حجوزات متقدم',
                }
            ]

            for client_data in clients_data:
                client = Client.objects.create(**client_data)
                self.stdout.write(
                    self.style.SUCCESS(f'Created client: {client.name}')
                )

        # Create sample projects if they don't exist
        if not Project.objects.exists():
            clients = Client.objects.all()
            if clients.exists():
                projects_data = [
                    {
                        'name': 'موقع شركة التقنية المتقدمة',
                        'description': 'تطوير موقع إلكتروني متقدم مع نظام إدارة المحتوى',
                        'type': 'website',
                        'status': 'development',
                        'priority': 'high',
                        'client': clients[0],
                        'start_date': '2024-05-01',
                        'budget': 75000,
                        'progress': 65,
                    },
                    {
                        'name': 'متجر الأزياء الإلكتروني',
                        'description': 'تطوير متجر إلكتروني متكامل مع نظام الدفع',
                        'type': 'ecommerce',
                        'status': 'testing',
                        'priority': 'medium',
                        'client': clients[1] if len(clients) > 1 else clients[0],
                        'start_date': '2024-04-15',
                        'budget': 60000,
                        'progress': 85,
                    },
                    {
                        'name': 'نظام حجوزات مطعم الأصالة',
                        'description': 'تطوير نظام حجوزات متقدم مع تطبيق جوال',
                        'type': 'web_app',
                        'status': 'planning',
                        'priority': 'low',
                        'client': clients[2] if len(clients) > 2 else clients[0],
                        'start_date': '2024-06-10',
                        'budget': 45000,
                        'progress': 15,
                    }
                ]

                for project_data in projects_data:
                    project = Project.objects.create(**project_data)
                    self.stdout.write(
                        self.style.SUCCESS(f'Created project: {project.name}')
                    )

        # Create sample tasks if they don't exist
        if not Task.objects.exists():
            projects = Project.objects.all()
            founder = User.objects.get(username='founder')
            
            if projects.exists():
                tasks_data = [
                    {
                        'title': 'تصميم واجهة المستخدم الرئيسية',
                        'description': 'تصميم الصفحة الرئيسية وصفحات الخدمات',
                        'category': 'medium',
                        'priority': 'high',
                        'status': 'in_progress',
                        'project': projects[0],
                        'created_by': founder,
                        'estimated_hours': 16,
                    },
                    {
                        'title': 'تطوير نظام إدارة المحتوى',
                        'description': 'برمجة لوحة تحكم إدارة المحتوى',
                        'category': 'extreme',
                        'priority': 'high',
                        'status': 'in_progress',
                        'project': projects[0],
                        'created_by': founder,
                        'estimated_hours': 24,
                    },
                    {
                        'title': 'اختبار نظام الدفع',
                        'description': 'اختبار تكامل بوابات الدفع المختلفة',
                        'category': 'light',
                        'priority': 'urgent',
                        'status': 'review',
                        'project': projects[1] if len(projects) > 1 else projects[0],
                        'created_by': founder,
                        'estimated_hours': 4,
                    }
                ]

                for task_data in tasks_data:
                    task = Task.objects.create(**task_data)
                    self.stdout.write(
                        self.style.SUCCESS(f'Created task: {task.title}')
                    )

        self.stdout.write(
            self.style.SUCCESS('Successfully created founder user and sample data!')
        )
