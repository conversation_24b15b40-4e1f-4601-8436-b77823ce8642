# Generated by Django 4.2.9 on 2025-06-03 15:09

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("authentication", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="SecurityLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "event_type",
                    models.CharField(
                        choices=[
                            ("login_success", "تسجيل دخول ناجح"),
                            ("login_failed", "فشل تسجيل الدخول"),
                            ("logout", "تسجيل خروج"),
                            ("password_change", "تغيير كلمة المرور"),
                            ("mfa_enabled", "تفعيل المصادقة الثنائية"),
                            ("mfa_disabled", "إلغاء المصادقة الثنائية"),
                            ("account_locked", "قفل الحساب"),
                            ("account_unlocked", "إلغاء قفل الحساب"),
                        ],
                        max_length=20,
                        verbose_name="نوع الحدث",
                    ),
                ),
                (
                    "details",
                    models.JSONField(blank=True, default=dict, verbose_name="التفاصيل"),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="عنوان IP"
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(auto_now_add=True, verbose_name="وقت الحدث"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="المستخدم",
                    ),
                ),
            ],
            options={
                "verbose_name": "سجل الأمان",
                "verbose_name_plural": "سجلات الأمان",
                "ordering": ["-timestamp"],
            },
        ),
        migrations.CreateModel(
            name="AuditLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("create", "إنشاء"),
                            ("update", "تحديث"),
                            ("delete", "حذف"),
                            ("view", "عرض"),
                            ("export", "تصدير"),
                        ],
                        max_length=10,
                        verbose_name="الإجراء",
                    ),
                ),
                (
                    "model_name",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="اسم النموذج"
                    ),
                ),
                (
                    "object_id",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="معرف الكائن"
                    ),
                ),
                (
                    "changes",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="التغييرات"
                    ),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="عنوان IP"
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(auto_now_add=True, verbose_name="وقت الإجراء"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="المستخدم",
                    ),
                ),
            ],
            options={
                "verbose_name": "سجل المراجعة",
                "verbose_name_plural": "سجلات المراجعة",
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["user", "timestamp"],
                        name="authenticat_user_id_3b88ca_idx",
                    ),
                    models.Index(
                        fields=["model_name", "object_id"],
                        name="authenticat_model_n_99e131_idx",
                    ),
                    models.Index(
                        fields=["action", "timestamp"],
                        name="authenticat_action_4a725a_idx",
                    ),
                ],
            },
        ),
    ]
