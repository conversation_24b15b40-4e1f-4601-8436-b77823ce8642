#!/usr/bin/env python
"""
Test script to verify the invoices module API endpoints are working correctly.
"""

import os
import sys
import django
from datetime import date, timed<PERSON><PERSON>

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "mtbrmg_erp.settings")
django.setup()

from invoices.models import InvoiceTemplate, Invoice, InvoiceItem, InvoicePayment
from clients.models import Client
from authentication.models import User


def test_invoice_models():
    """Test invoice models creation and functionality"""
    print("🧪 Testing Invoice Models...")

    # Create a test user
    user, created = User.objects.get_or_create(
        email="<EMAIL>",
        defaults={"first_name": "Test", "last_name": "User", "role": "founder"},
    )
    if created:
        user.set_password("testpass123")
        user.save()
        print("✅ Test user created")

    # Create a test client
    client, created = Client.objects.get_or_create(
        name="Test Client",
        defaults={
            "email": "<EMAIL>",
            "phone": "+201234567890",
            "governorate": "Cairo",
            "mood": "happy",
        },
    )
    if created:
        print("✅ Test client created")

    # Create an invoice template
    template, created = InvoiceTemplate.objects.get_or_create(
        name="Standard Template",
        defaults={
            "description": "Standard invoice template",
            "template_type": "standard",
            "is_default": True,
            "header_text": "شركة MTBRMG للحلول الرقمية",
            "footer_text": "شكراً لثقتكم بنا",
            "terms_conditions": "الدفع خلال 30 يوم من تاريخ الفاتورة",
        },
    )
    if created:
        print("✅ Invoice template created")

    # Create an invoice
    invoice, created = Invoice.objects.get_or_create(
        title="Test Invoice",
        client=client,
        defaults={
            "description": "Test invoice for API verification",
            "sales_rep": user,
            "template": template,
            "invoice_date": date.today(),
            "priority": "medium",
            "discount_percentage": 10,
            "tax_percentage": 14,
        },
    )
    if created:
        print("✅ Invoice created")
        print(f"   Invoice Number: {invoice.invoice_number}")
        print(f"   Due Date: {invoice.due_date}")

    # Create invoice items
    item1, created = InvoiceItem.objects.get_or_create(
        invoice=invoice,
        name="Website Development",
        defaults={
            "description": "Complete website development with responsive design",
            "quantity": 1,
            "unit": "project",
            "unit_price": 15000,
            "item_type": "service",
        },
    )
    if created:
        print("✅ Invoice item 1 created")

    item2, created = InvoiceItem.objects.get_or_create(
        invoice=invoice,
        name="SEO Optimization",
        defaults={
            "description": "Search engine optimization for 6 months",
            "quantity": 6,
            "unit": "month",
            "unit_price": 2000,
            "item_type": "service",
        },
    )
    if created:
        print("✅ Invoice item 2 created")

    # Refresh invoice to get calculated amounts
    invoice.refresh_from_db()
    print(f"   Subtotal: {invoice.subtotal}")
    print(f"   Discount: {invoice.discount_amount}")
    print(f"   Tax: {invoice.tax_amount}")
    print(f"   Total: {invoice.total_amount}")

    # Create a payment
    payment, created = InvoicePayment.objects.get_or_create(
        invoice=invoice,
        payment_method="bank_transfer",
        defaults={
            "amount": 10000,
            "payment_date": date.today(),
            "transaction_id": "TXN123456",
            "status": "completed",
            "processed_by": user,
        },
    )
    if created:
        print("✅ Payment created")

    # Refresh invoice to see updated payment status
    invoice.refresh_from_db()
    print(f"   Paid Amount: {invoice.paid_amount}")
    print(f"   Remaining: {invoice.remaining_amount}")
    print(f"   Status: {invoice.status}")
    print(f"   Payment Percentage: {invoice.payment_percentage:.1f}%")

    return invoice


def test_invoice_api_endpoints():
    """Test invoice API endpoints"""
    print("\n🌐 Testing Invoice API Endpoints...")

    from django.test import Client as TestClient
    from django.urls import reverse
    from rest_framework.test import APIClient
    from rest_framework_simplejwt.tokens import RefreshToken

    # Create API client
    api_client = APIClient()

    # Get test user and create JWT token
    user = User.objects.get(email="<EMAIL>")
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)

    # Set authentication header
    api_client.credentials(HTTP_AUTHORIZATION=f"Bearer {access_token}")

    # Test invoice templates endpoint
    try:
        response = api_client.get("/api/v1/invoice-templates/")
        print(f"✅ Invoice Templates API: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Found {len(data.get('results', []))} templates")
    except Exception as e:
        print(f"❌ Invoice Templates API Error: {e}")

    # Test invoices endpoint
    try:
        response = api_client.get("/api/v1/invoices/")
        print(f"✅ Invoices API: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Found {len(data.get('results', []))} invoices")
    except Exception as e:
        print(f"❌ Invoices API Error: {e}")

    # Test invoice stats endpoint
    try:
        response = api_client.get("/api/v1/invoices/stats/")
        print(f"✅ Invoice Stats API: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Total Invoices: {data.get('total_invoices', 0)}")
            print(f"   Total Amount: {data.get('total_amount', 0)}")
    except Exception as e:
        print(f"❌ Invoice Stats API Error: {e}")

    # Test invoice items endpoint
    try:
        response = api_client.get("/api/v1/invoice-items/")
        print(f"✅ Invoice Items API: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Found {len(data.get('results', []))} items")
    except Exception as e:
        print(f"❌ Invoice Items API Error: {e}")

    # Test invoice payments endpoint
    try:
        response = api_client.get("/api/v1/invoice-payments/")
        print(f"✅ Invoice Payments API: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Found {len(data.get('results', []))} payments")
    except Exception as e:
        print(f"❌ Invoice Payments API Error: {e}")


def main():
    """Main test function"""
    print("🚀 Starting Invoice Module Tests...\n")

    try:
        # Test models
        invoice = test_invoice_models()

        # Test API endpoints
        test_invoice_api_endpoints()

        print("\n✅ All tests completed successfully!")
        print(f"📊 Test Invoice ID: {invoice.id}")
        print(f"📊 Test Invoice Number: {invoice.invoice_number}")

    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
