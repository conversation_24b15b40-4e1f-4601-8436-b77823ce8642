from django.db import models
from django.conf import settings
from simple_history.models import HistoricalRecords
from django.core.validators import MinValueValidator


class Task(models.Model):
    """Task model for project management"""

    class Priority(models.TextChoices):
        LOW = 'low', 'منخفض'
        MEDIUM = 'medium', 'متوسط'
        HIGH = 'high', 'عالي'
        URGENT = 'urgent', 'عاجل'

    class Status(models.TextChoices):
        PENDING = 'pending', 'في الانتظار'
        IN_PROGRESS = 'in_progress', 'قيد التنفيذ'
        REVIEW = 'review', 'مراجعة'
        TESTING = 'testing', 'اختبار'
        COMPLETED = 'completed', 'مكتمل'
        CANCELLED = 'cancelled', 'ملغي'

    class Category(models.TextChoices):
        LIGHT = 'light', 'خفيف (1-4 ساعات)'
        MEDIUM = 'medium', 'متوسط (4-8 ساعات)'
        EXTREME = 'extreme', 'شاق (8+ ساعات)'

    # Basic Information
    title = models.CharField(max_length=200, verbose_name='عنوان المهمة')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    category = models.CharField(
        max_length=10,
        choices=Category.choices,
        default=Category.MEDIUM,
        verbose_name='فئة المهمة'
    )
    priority = models.CharField(
        max_length=10,
        choices=Priority.choices,
        default=Priority.MEDIUM,
        verbose_name='الأولوية'
    )
    status = models.CharField(
        max_length=15,
        choices=Status.choices,
        default=Status.PENDING,
        verbose_name='الحالة'
    )

    # Relationships
    project = models.ForeignKey(
        'projects.Project',
        on_delete=models.CASCADE,
        related_name='tasks',
        blank=True,
        null=True,
        verbose_name='المشروع'
    )
    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name='assigned_tasks',
        blank=True,
        null=True,
        verbose_name='مكلف إلى'
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_tasks',
        verbose_name='منشئ المهمة'
    )

    # Time Management
    estimated_hours = models.PositiveIntegerField(
        validators=[MinValueValidator(1)],
        verbose_name='الساعات المقدرة'
    )
    actual_hours = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name='الساعات الفعلية'
    )
    start_date = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ البداية')
    due_date = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاستحقاق')

    # Dependencies and Organization
    dependencies = models.ManyToManyField(
        'self',
        symmetrical=False,
        blank=True,
        verbose_name='المهام المعتمدة عليها'
    )
    tags = models.JSONField(
        default=list,
        blank=True,
        verbose_name='العلامات'
    )

    # File Attachments
    attachments = models.JSONField(
        default=list,
        blank=True,
        verbose_name='المرفقات'
    )

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    completed_at = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإنجاز')

    # History tracking
    history = HistoricalRecords()

    class Meta:
        verbose_name = 'مهمة'
        verbose_name_plural = 'المهام'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['assigned_to', 'status'], name='task_assigned_status_idx'),
            models.Index(fields=['project', 'status'], name='task_project_status_idx'),
            models.Index(fields=['priority', 'status'], name='task_priority_status_idx'),
            models.Index(fields=['due_date'], name='task_due_date_idx'),
            models.Index(fields=['created_by', 'status'], name='task_creator_status_idx'),
            # Partial index for active tasks
            models.Index(
                fields=['assigned_to'],
                name='task_active_assigned_idx',
                condition=models.Q(status__in=['pending', 'in_progress', 'review', 'testing'])
            ),
        ]

    def __str__(self):
        return f"{self.title} - {self.get_status_display()}"

    @property
    def is_overdue(self):
        """Check if task is overdue"""
        if self.due_date and self.status != self.Status.COMPLETED:
            from django.utils import timezone
            return timezone.now() > self.due_date
        return False

    @property
    def category_hours_range(self):
        """Get hour range for task category"""
        ranges = {
            self.Category.LIGHT: (1, 4),
            self.Category.MEDIUM: (4, 8),
            self.Category.EXTREME: (8, 24),
        }
        return ranges.get(self.category, (1, 8))

    def mark_completed(self):
        """Mark task as completed"""
        from django.utils import timezone
        self.status = self.Status.COMPLETED
        self.completed_at = timezone.now()
        self.save(update_fields=['status', 'completed_at'])

        # Update project progress if task belongs to a project
        if self.project:
            self.project.update_progress()


class TaskTimeLog(models.Model):
    """Track time spent on tasks"""

    task = models.ForeignKey(
        Task,
        on_delete=models.CASCADE,
        related_name='time_logs',
        verbose_name='المهمة'
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name='المستخدم'
    )
    hours = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0.1)],
        verbose_name='الساعات'
    )
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    date = models.DateField(verbose_name='التاريخ')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    class Meta:
        verbose_name = 'سجل وقت المهمة'
        verbose_name_plural = 'سجلات أوقات المهام'
        ordering = ['-date', '-created_at']

    def __str__(self):
        return f"{self.task.title} - {self.hours} ساعة - {self.user.get_full_name()}"


class TaskComment(models.Model):
    """Comments on tasks"""

    task = models.ForeignKey(
        Task,
        on_delete=models.CASCADE,
        related_name='comments',
        verbose_name='المهمة'
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name='المستخدم'
    )
    content = models.TextField(verbose_name='المحتوى')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'تعليق على المهمة'
        verbose_name_plural = 'تعليقات المهام'
        ordering = ['-created_at']

    def __str__(self):
        return f"تعليق على {self.task.title} بواسطة {self.user.get_full_name()}"
