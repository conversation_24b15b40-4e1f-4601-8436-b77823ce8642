# Generated by Django 4.2.9 on 2025-06-03 15:09

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("tasks", "0002_historicaltask_assigned_to_historicaltask_start_date_and_more"),
    ]

    operations = [
        migrations.AddIndex(
            model_name="task",
            index=models.Index(
                fields=["assigned_to", "status"], name="task_assigned_status_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="task",
            index=models.Index(
                fields=["project", "status"], name="task_project_status_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="task",
            index=models.Index(
                fields=["priority", "status"], name="task_priority_status_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="task",
            index=models.Index(fields=["due_date"], name="task_due_date_idx"),
        ),
        migrations.AddIndex(
            model_name="task",
            index=models.Index(
                fields=["created_by", "status"], name="task_creator_status_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="task",
            index=models.Index(
                condition=models.Q(
                    ("status__in", ["pending", "in_progress", "review", "testing"])
                ),
                fields=["assigned_to"],
                name="task_active_assigned_idx",
            ),
        ),
    ]
