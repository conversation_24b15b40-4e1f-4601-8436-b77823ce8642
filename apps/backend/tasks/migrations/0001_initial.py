# Generated by Django 4.2.9 on 2025-06-01 22:52

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import simple_history.models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("projects", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Task",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title",
                    models.CharField(max_length=200, verbose_name="عنوان المهمة"),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("light", "خفيف (1-4 ساعات)"),
                            ("medium", "متوسط (4-8 ساعات)"),
                            ("extreme", "شاق (8+ ساعات)"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="فئة المهمة",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "منخفض"),
                            ("medium", "متوسط"),
                            ("high", "عالي"),
                            ("urgent", "عاجل"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="الأولوية",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("todo", "قائمة المهام"),
                            ("in_progress", "قيد التنفيذ"),
                            ("review", "مراجعة"),
                            ("testing", "اختبار"),
                            ("completed", "مكتمل"),
                            ("cancelled", "ملغي"),
                        ],
                        default="todo",
                        max_length=15,
                        verbose_name="الحالة",
                    ),
                ),
                (
                    "estimated_hours",
                    models.PositiveIntegerField(
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="الساعات المقدرة",
                    ),
                ),
                (
                    "actual_hours",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=5,
                        null=True,
                        verbose_name="الساعات الفعلية",
                    ),
                ),
                (
                    "due_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ الاستحقاق"
                    ),
                ),
                (
                    "tags",
                    models.JSONField(blank=True, default=list, verbose_name="العلامات"),
                ),
                (
                    "attachments",
                    models.JSONField(blank=True, default=list, verbose_name="المرفقات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ الإنجاز"
                    ),
                ),
                (
                    "assigned_to",
                    models.ManyToManyField(
                        blank=True,
                        related_name="assigned_tasks",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="مكلف إلى",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_tasks",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="منشئ المهمة",
                    ),
                ),
                (
                    "dependencies",
                    models.ManyToManyField(
                        blank=True,
                        to="tasks.task",
                        verbose_name="المهام المعتمدة عليها",
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tasks",
                        to="projects.project",
                        verbose_name="المشروع",
                    ),
                ),
            ],
            options={
                "verbose_name": "مهمة",
                "verbose_name_plural": "المهام",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="TaskTimeLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "hours",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=5,
                        validators=[django.core.validators.MinValueValidator(0.1)],
                        verbose_name="الساعات",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                ("date", models.DateField(verbose_name="التاريخ")),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "task",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="time_logs",
                        to="tasks.task",
                        verbose_name="المهمة",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="المستخدم",
                    ),
                ),
            ],
            options={
                "verbose_name": "سجل وقت المهمة",
                "verbose_name_plural": "سجلات أوقات المهام",
                "ordering": ["-date", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="TaskComment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("content", models.TextField(verbose_name="المحتوى")),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "task",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="comments",
                        to="tasks.task",
                        verbose_name="المهمة",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="المستخدم",
                    ),
                ),
            ],
            options={
                "verbose_name": "تعليق على المهمة",
                "verbose_name_plural": "تعليقات المهام",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="HistoricalTask",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "title",
                    models.CharField(max_length=200, verbose_name="عنوان المهمة"),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("light", "خفيف (1-4 ساعات)"),
                            ("medium", "متوسط (4-8 ساعات)"),
                            ("extreme", "شاق (8+ ساعات)"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="فئة المهمة",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "منخفض"),
                            ("medium", "متوسط"),
                            ("high", "عالي"),
                            ("urgent", "عاجل"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="الأولوية",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("todo", "قائمة المهام"),
                            ("in_progress", "قيد التنفيذ"),
                            ("review", "مراجعة"),
                            ("testing", "اختبار"),
                            ("completed", "مكتمل"),
                            ("cancelled", "ملغي"),
                        ],
                        default="todo",
                        max_length=15,
                        verbose_name="الحالة",
                    ),
                ),
                (
                    "estimated_hours",
                    models.PositiveIntegerField(
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="الساعات المقدرة",
                    ),
                ),
                (
                    "actual_hours",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=5,
                        null=True,
                        verbose_name="الساعات الفعلية",
                    ),
                ),
                (
                    "due_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ الاستحقاق"
                    ),
                ),
                (
                    "tags",
                    models.JSONField(blank=True, default=list, verbose_name="العلامات"),
                ),
                (
                    "attachments",
                    models.JSONField(blank=True, default=list, verbose_name="المرفقات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ التحديث"
                    ),
                ),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ الإنجاز"
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="منشئ المهمة",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="projects.project",
                        verbose_name="المشروع",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical مهمة",
                "verbose_name_plural": "historical المهام",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
