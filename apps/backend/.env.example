# Django Settings
SECRET_KEY=django-insecure-mtbrmg-erp-development-key-change-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Settings
USE_SQLITE=True
DB_NAME=mtbrmg_erp
DB_USER=postgres
DB_PASSWORD=postgres
DB_HOST=localhost
DB_PORT=5432

# Redis Settings
REDIS_URL=redis://localhost:6379/0

# Email Settings
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# AWS S3 Settings (for production)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_STORAGE_BUCKET_NAME=
AWS_S3_REGION_NAME=us-east-1

# Session Settings - Set to unlimited time (very long duration)
SESSION_COOKIE_AGE=***********
SESSION_EXPIRE_AT_BROWSER_CLOSE=False
SESSION_SAVE_EVERY_REQUEST=False
SESSION_COOKIE_SECURE=False

# JWT Settings - Extended timeouts
JWT_ACCESS_TOKEN_HOURS=24
JWT_REFRESH_TOKEN_DAYS=30

# WhatsApp Business API (optional)
WHATSAPP_API_TOKEN=
WHATSAPP_PHONE_NUMBER_ID=
