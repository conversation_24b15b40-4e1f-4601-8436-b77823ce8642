# MTBRMG ERP Production Environment Variables
# Copy this file to .env.prod and update the values for your production environment

# Database Configuration
POSTGRES_DB=mtbrmg_erp_prod
POSTGRES_USER=mtbrmg_user
POSTGRES_PASSWORD=secure_password_2024_change_me

# Redis Configuration
REDIS_PASSWORD=redis_secure_2024_change_me

# Django Configuration
DJANGO_SECRET_KEY=your-super-secret-key-change-in-production-make-it-very-long-and-random
ALLOWED_HOSTS=your-domain.com,www.your-domain.com,api.your-domain.com
CORS_ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# Frontend Configuration
NEXT_PUBLIC_API_URL=https://api.your-domain.com
NEXT_PUBLIC_APP_URL=https://your-domain.com

# Email Configuration (Gmail example)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-specific-password

# SSL Configuration
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=mtbrmg-backups
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1

# Monitoring Configuration
SENTRY_DSN=https://<EMAIL>/project-id
MONITORING_ENABLED=true

# Admin Configuration
ADMIN_URL=secure-admin-url/

# Performance Configuration
GUNICORN_WORKERS=4
GUNICORN_MAX_REQUESTS=1000

# Security Configuration
SECURE_SSL_REDIRECT=true
SECURE_HSTS_SECONDS=31536000

# Domain Configuration
DOMAIN_NAME=your-domain.com
SUBDOMAIN_API=api
SUBDOMAIN_WWW=www
