{"name": "mtbrmg-erp", "version": "1.0.0", "description": "Comprehensive RTL ERP system for Egyptian digital agency", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo build", "dev": "./scripts/dev-simple.sh", "dev:stable": "./scripts/dev-stable.sh", "dev:turbo": "turbo dev", "lint": "turbo lint", "type-check": "turbo type-check", "test": "turbo test", "clean": "turbo clean", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "changeset": "changeset", "version-packages": "changeset version", "release": "turbo build --filter=docs^... && changeset publish", "setup:local": "./scripts/setup-local.sh", "start:local": "./scripts/start-local.sh", "dev:backend": "cd apps/backend && source venv/bin/activate && python manage.py runserver 8000", "dev:frontend": "cd apps/frontend && pnpm dev", "migrate": "cd apps/backend && source venv/bin/activate && python manage.py migrate", "create-founder": "cd apps/backend && source venv/bin/activate && python manage.py shell -c \"from django.contrib.auth import get_user_model; User = get_user_model(); User.objects.filter(username='founder').exists() or User.objects.create_superuser('founder', '<EMAIL>', 'demo123')\""}, "devDependencies": {"@changesets/cli": "^2.27.1", "@turbo/gen": "^1.12.4", "prettier": "^3.2.5", "turbo": "^1.12.4", "typescript": "^5.3.3"}, "packageManager": "pnpm@8.15.6", "engines": {"node": ">=18"}}