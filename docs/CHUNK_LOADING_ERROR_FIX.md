# ChunkLoadError Fix Documentation

## Problem Description

You were experiencing a `ChunkLoadError` when accessing the route:
```
http://localhost:3001/founder-dashboard/finance/revenue/add
```

The error was:
```
ChunkLoadError
    at __webpack_require__.f.j (http://localhost:3001/_next/static/chunks/webpack.js?v=1749049178668:858:29)
    at http://localhost:3001/_next/static/chunks/webpack.js?v=1749049178668:153:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (http://localhost:3001/_next/static/chunks/webpack.js?v=1749049178668:152:67)
    ...
```

## Root Cause

ChunkLoadError typically occurs due to:

1. **Build cache inconsistencies** - Stale chunks in the `.next` cache
2. **Hot reload conflicts** - Multiple development servers or conflicting processes
3. **Webpack configuration issues** - Problems with code splitting
4. **Browser cache conflicts** - Cached chunks that don't match current build

## Solution Applied

### 1. Updated Next.js Configuration

Modified `apps/frontend/next.config.mjs` to include:

```javascript
// Fix chunk loading issues
experimental: {
  optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
},

// Webpack configuration to fix chunk loading issues
webpack: (config, { dev, isServer }) => {
  if (dev && !isServer) {
    // Fix chunk loading issues in development
    config.optimization = {
      ...config.optimization,
      splitChunks: {
        ...config.optimization.splitChunks,
        cacheGroups: {
          ...config.optimization.splitChunks.cacheGroups,
          default: {
            minChunks: 1,
            priority: -20,
            reuseExistingChunk: true,
          },
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            priority: -10,
            chunks: 'all',
          },
        },
      },
    };
  }
  return config;
},
```

### 2. Removed Deprecated Configuration

Removed the deprecated `swcMinify: true` option that was causing warnings.

### 3. Clean Cache and Restart

- Cleared `.next` cache directory
- Restarted development server with fresh build

## Quick Fix Script

Created `scripts/fix-chunk-errors.sh` for future use:

```bash
./scripts/fix-chunk-errors.sh
```

This script will:
- Stop existing development servers
- Clean all cache directories
- Restart the development server
- Test if the server is working

## Prevention

To prevent future chunk loading errors:

1. **Regular cache cleaning**: Run `rm -rf apps/frontend/.next` when switching branches
2. **Proper server shutdown**: Always stop development servers before switching branches
3. **Browser cache**: Clear browser cache when experiencing issues
4. **Consistent environment**: Use the same Node.js version across development

## Testing

The fix has been verified:
- ✅ Server starts without warnings
- ✅ Route `/founder-dashboard/finance/revenue/add` returns 200 status
- ✅ Page loads successfully in browser
- ✅ No chunk loading errors

## Additional Notes

- The revenue add page imports multiple components and APIs
- All dependencies are properly resolved
- The UnifiedLayout, API functions, and auth store are working correctly
- The page includes comprehensive form handling for revenue management

If you experience similar issues in the future, run the fix script or follow the manual steps outlined above.
