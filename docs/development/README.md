# MTBRMG ERP - Development Setup

## Quick Start

1. **Install Extensions**: Run `./scripts/setup-extensions.sh`
2. **Setup Configuration**: Run `./scripts/setup-config.sh`
3. **Start Development**: Press `Ctrl+Shift+P` → "Tasks: Run Task" → "Start Both Servers"

## VS Code Features

### Debugging
- **F5**: Start debugging (Next.js or Django)
- **Ctrl+Shift+D**: Open debug panel

### Tasks
- **Ctrl+Shift+P** → "Tasks: Run Task":
  - Start Frontend Dev Server
  - Start Backend Dev Server
  - Start Both Servers
  - Run Tests
  - Format Code

### Extensions Installed
- Tailwind CSS IntelliSense
- Prettier Code Formatter
- ESLint
- Python Support
- Django Support
- GitLens
- REST Client
- And more...

## Development Workflow

1. **Code Formatting**: Automatic on save
2. **Linting**: Real-time error detection
3. **Type Checking**: TypeScript support
4. **Git Integration**: Enhanced with GitLens
5. **API Testing**: Use REST Client extension

## Keyboard Shortcuts

- **Ctrl+`**: Toggle terminal
- **Ctrl+Shift+`**: New terminal
- **Ctrl+P**: Quick file open
- **Ctrl+Shift+P**: Command palette
- **F12**: Go to definition
- **Shift+F12**: Find all references
