// API Endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    REFRESH: '/auth/refresh',
    LOGOUT: '/auth/logout',
    PROFILE: '/auth/profile',
    CHANGE_PASSWORD: '/auth/change-password',
  },
  USERS: {
    LIST: '/users',
    CREATE: '/users',
    UPDATE: (id: string) => `/users/${id}`,
    DELETE: (id: string) => `/users/${id}`,
    PROFILE: (id: string) => `/users/${id}/profile`,
  },
  PROJECTS: {
    LIST: '/projects',
    CREATE: '/projects',
    UPDATE: (id: string) => `/projects/${id}`,
    DELETE: (id: string) => `/projects/${id}`,
    DETAILS: (id: string) => `/projects/${id}`,
  },
  CLIENTS: {
    LIST: '/clients',
    CREATE: '/clients',
    UPDATE: (id: string) => `/clients/${id}`,
    DELETE: (id: string) => `/clients/${id}`,
    DETAILS: (id: string) => `/clients/${id}`,
    COMMUNICATIONS: (id: string) => `/clients/${id}/communications`,
  },
  TASKS: {
    LIST: '/tasks',
    CREATE: '/tasks',
    UPDATE: (id: string) => `/tasks/${id}`,
    DELETE: (id: string) => `/tasks/${id}`,
    DETAILS: (id: string) => `/tasks/${id}`,
    TIME_LOGS: (id: string) => `/tasks/${id}/time-logs`,
    COMMENTS: (id: string) => `/tasks/${id}/comments`,
  },
} as const;

// Role Permissions
export const ROLE_PERMISSIONS = {
  admin: ['*'], // All permissions
  sales_manager: [
    'clients:read',
    'clients:write',
    'projects:read',
    'projects:write',
    'tasks:read',
    'analytics:read',
  ],
  media_buyer: [
    'clients:read',
    'projects:read',
    'tasks:read',
    'tasks:write',
    'analytics:read',
  ],
  developer: [
    'projects:read',
    'projects:write',
    'tasks:read',
    'tasks:write',
  ],
  designer: [
    'projects:read',
    'projects:write',
    'tasks:read',
    'tasks:write',
  ],
  wordpress_developer: [
    'projects:read',
    'projects:write',
    'tasks:read',
    'tasks:write',
  ],
} as const;

// Egyptian Governorate Labels (Arabic)
export const GOVERNORATE_LABELS = {
  cairo: 'القاهرة',
  alexandria: 'الإسكندرية',
  giza: 'الجيزة',
  qalyubia: 'القليوبية',
  port_said: 'بورسعيد',
  suez: 'السويس',
  luxor: 'الأقصر',
  aswan: 'أسوان',
  asyut: 'أسيوط',
  beheira: 'البحيرة',
  beni_suef: 'بني سويف',
  dakahlia: 'الدقهلية',
  damietta: 'دمياط',
  fayyum: 'الفيوم',
  gharbia: 'الغربية',
  ismailia: 'الإسماعيلية',
  kafr_el_sheikh: 'كفر الشيخ',
  matrouh: 'مطروح',
  minya: 'المنيا',
  monufia: 'المنوفية',
  new_valley: 'الوادي الجديد',
  north_sinai: 'شمال سيناء',
  qena: 'قنا',
  red_sea: 'البحر الأحمر',
  sharqia: 'الشرقية',
  sohag: 'سوهاج',
  south_sinai: 'جنوب سيناء',
} as const;

// Task Category Time Estimates
export const TASK_CATEGORY_HOURS = {
  light: { min: 1, max: 4, default: 2 },
  medium: { min: 4, max: 8, default: 6 },
  extreme: { min: 8, max: 24, default: 12 },
} as const;

// Default Pagination
export const DEFAULT_PAGINATION = {
  page: 1,
  limit: 20,
} as const;

// File Upload Limits
export const FILE_UPLOAD_LIMITS = {
  maxSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ],
} as const;

// Currency Configuration
export const CURRENCY_CONFIG = {
  PRIMARY: 'EGP',
  SECONDARY: 'USD',
  SUPPORTED: ['EGP', 'USD', 'SAR', 'AED'],
  LABELS: {
    EGP: 'جنيه مصري',
    USD: 'دولار أمريكي',
    SAR: 'ريال سعودي',
    AED: 'درهم إماراتي',
  },
  SYMBOLS: {
    EGP: 'ج.م',
    USD: '$',
    SAR: 'ر.س',
    AED: 'د.إ',
  },
} as const;
