import { z } from 'zod';

// Client Mood
export enum ClientMood {
  HAPPY = 'happy',
  NEUTRAL = 'neutral',
  CONCERNED = 'concerned',
  ANGRY = 'angry',
}

// Egyptian Governorates
export enum EgyptianGovernorate {
  CAIRO = 'cairo',
  ALEXANDRIA = 'alexandria',
  GIZA = 'giza',
  QALYUBIA = 'qalyubia',
  PORT_SAID = 'port_said',
  SUEZ = 'suez',
  LUXOR = 'luxor',
  ASWAN = 'aswan',
  ASYUT = 'asyut',
  BEHEIRA = 'beheira',
  BENI_SUEF = 'beni_suef',
  DAKAHLIA = 'dakahlia',
  DAMIETTA = 'damietta',
  FAYYUM = 'fayyum',
  GHARBIA = 'gharbia',
  ISMAILIA = 'ismailia',
  KAFR_EL_SHEIKH = 'kafr_el_sheikh',
  MATROUH = 'matrouh',
  MINYA = 'minya',
  MONUFIA = 'monufia',
  NEW_VALLEY = 'new_valley',
  NORTH_SINAI = 'north_sinai',
  QENA = 'qena',
  RED_SEA = 'red_sea',
  SHARQIA = 'sharqia',
  SOHAG = 'sohag',
  SOUTH_SINAI = 'south_sinai',
}

// Client Schema
export const ClientSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
  phone: z.string(),
  company: z.string().optional(),
  website: z.string().url().optional(),
  address: z.string().optional(),
  governorate: z.nativeEnum(EgyptianGovernorate).optional(),
  mood: z.nativeEnum(ClientMood),
  salesRepId: z.string(),
  notes: z.string().optional(),
  totalProjects: z.number().default(0),
  totalRevenue: z.number().default(0),
  lastContactDate: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type Client = z.infer<typeof ClientSchema>;

// Create Client Schema
export const CreateClientSchema = ClientSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  totalProjects: true,
  totalRevenue: true,
});

export type CreateClientData = z.infer<typeof CreateClientSchema>;

// Update Client Schema
export const UpdateClientSchema = CreateClientSchema.partial();

export type UpdateClientData = z.infer<typeof UpdateClientSchema>;

// Client Communication Schema
export const ClientCommunicationSchema = z.object({
  id: z.string(),
  clientId: z.string(),
  type: z.enum(['call', 'email', 'whatsapp', 'meeting', 'other']),
  subject: z.string(),
  content: z.string(),
  userId: z.string(),
  createdAt: z.string(),
});

export type ClientCommunication = z.infer<typeof ClientCommunicationSchema>;
