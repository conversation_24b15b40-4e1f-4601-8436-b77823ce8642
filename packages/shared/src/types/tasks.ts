import { z } from 'zod';

// Task Priority
export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

// Task Status
export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  REVIEW = 'review',
  TESTING = 'testing',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

// Task Category (with time estimates in hours)
export enum TaskCategory {
  LIGHT = 'light',    // 1-4 hours
  MEDIUM = 'medium',  // 4-8 hours
  EXTREME = 'extreme', // 8+ hours
}

// Task Schema
export const TaskSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().optional(),
  category: z.nativeEnum(TaskCategory),
  priority: z.nativeEnum(TaskPriority),
  status: z.nativeEnum(TaskStatus),
  projectId: z.string().optional(),
  assignedTo: z.array(z.string()),
  createdBy: z.string(),
  estimatedHours: z.number().positive(),
  actualHours: z.number().optional(),
  dueDate: z.string().optional(),
  dependencies: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  attachments: z.array(z.string()).optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
  completedAt: z.string().optional(),
});

export type Task = z.infer<typeof TaskSchema>;

// Create Task Schema
export const CreateTaskSchema = TaskSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  completedAt: true,
  actualHours: true,
});

export type CreateTaskData = z.infer<typeof CreateTaskSchema>;

// Update Task Schema
export const UpdateTaskSchema = CreateTaskSchema.partial();

export type UpdateTaskData = z.infer<typeof UpdateTaskSchema>;

// Task Time Log Schema
export const TaskTimeLogSchema = z.object({
  id: z.string(),
  taskId: z.string(),
  userId: z.string(),
  hours: z.number().positive(),
  description: z.string().optional(),
  date: z.string(),
  createdAt: z.string(),
});

export type TaskTimeLog = z.infer<typeof TaskTimeLogSchema>;

// Task Comment Schema
export const TaskCommentSchema = z.object({
  id: z.string(),
  taskId: z.string(),
  userId: z.string(),
  content: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type TaskComment = z.infer<typeof TaskCommentSchema>;
