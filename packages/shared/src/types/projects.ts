import { z } from 'zod';

// Project Status
export enum ProjectStatus {
  PLANNING = 'planning',
  DEVELOPMENT = 'development',
  TESTING = 'testing',
  DEPLOYMENT = 'deployment',
  MAINTENANCE = 'maintenance',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

// Project Priority
export enum ProjectPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

// Project Type
export enum ProjectType {
  WEBSITE = 'website',
  MOBILE_APP = 'mobile_app',
  WEB_APP = 'web_app',
  ECOMMERCE = 'ecommerce',
  WORDPRESS = 'wordpress',
  MAINTENANCE = 'maintenance',
  MARKETING = 'marketing',
}

// Project Schema
export const ProjectSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  type: z.nativeEnum(ProjectType),
  status: z.nativeEnum(ProjectStatus),
  priority: z.nativeEnum(ProjectPriority),
  clientId: z.string(),
  assignedTeam: z.array(z.string()),
  startDate: z.string(),
  endDate: z.string().optional(),
  budget: z.number().optional(),
  actualCost: z.number().optional(),
  progress: z.number().min(0).max(100),
  domains: z.array(z.string()).optional(),
  serverCredentials: z.object({
    host: z.string(),
    username: z.string(),
    password: z.string(),
  }).optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type Project = z.infer<typeof ProjectSchema>;

// Create Project Schema
export const CreateProjectSchema = ProjectSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  progress: true,
  actualCost: true,
});

export type CreateProjectData = z.infer<typeof CreateProjectSchema>;

// Update Project Schema
export const UpdateProjectSchema = CreateProjectSchema.partial();

export type UpdateProjectData = z.infer<typeof UpdateProjectSchema>;
