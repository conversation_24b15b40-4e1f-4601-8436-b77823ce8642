#!/bin/bash

# MTBRMG ERP - Optimized Development Manager
# Ultra-fast development environment with all optimizations

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/apps/backend"
FRONTEND_DIR="$PROJECT_ROOT/apps/frontend"
LOGS_DIR="$PROJECT_ROOT/logs"
PIDS_DIR="$PROJECT_ROOT/tmp"

# Create directories
mkdir -p "$LOGS_DIR" "$PIDS_DIR"

# Performance optimizations
export NODE_OPTIONS="--max-old-space-size=8192 --max-semi-space-size=512"
export UV_THREADPOOL_SIZE=128
export PYTHONUNBUFFERED=1
export NEXT_TELEMETRY_DISABLED=1
export TURBO_TELEMETRY_DISABLED=1
export DISABLE_ESLINT_PLUGIN=true
export FAST_REFRESH=true

# Logging function
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if process is running
is_running() {
    local pid_file="$1"
    [[ -f "$pid_file" ]] && kill -0 "$(cat "$pid_file")" 2>/dev/null
}

# Start Redis with optimization
start_redis() {
    log_info "Starting Redis with optimizations..."
    
    if brew services list | grep redis | grep started > /dev/null; then
        log_success "Redis is already running"
        return
    fi
    
    # Start Redis with optimized configuration
    brew services start redis
    
    # Wait for Redis to start
    sleep 2
    if redis-cli ping > /dev/null 2>&1; then
        log_success "Redis started successfully"
    else
        log_error "Failed to start Redis"
        return 1
    fi
}

# Start PostgreSQL with optimization
start_postgres() {
    log_info "Starting PostgreSQL with optimizations..."
    
    if brew services list | grep postgresql | grep started > /dev/null; then
        log_success "PostgreSQL is already running"
        return
    fi
    
    brew services start postgresql@15
    
    # Wait for PostgreSQL to start
    sleep 3
    if psql -h localhost -U muhammadyoussef -d mtbrmg_erp -c "SELECT 1;" > /dev/null 2>&1; then
        log_success "PostgreSQL started successfully"
    else
        log_error "Failed to start PostgreSQL"
        return 1
    fi
}

# Start optimized backend
start_backend() {
    log_info "Starting optimized Django backend..."
    
    if is_running "$PIDS_DIR/backend.pid"; then
        log_warning "Backend is already running"
        return
    fi
    
    cd "$BACKEND_DIR"
    source venv/bin/activate
    
    # Quick migration check
    python manage.py migrate --check > /dev/null 2>&1 || {
        log_info "Running database migrations..."
        python manage.py migrate
    }
    
    # Start optimized server
    nohup python manage.py runserver 8000 --noreload > "$LOGS_DIR/backend-optimized.log" 2>&1 &
    echo $! > "$PIDS_DIR/backend.pid"
    
    sleep 3
    if is_running "$PIDS_DIR/backend.pid"; then
        log_success "Backend started on http://localhost:8000"
    else
        log_error "Failed to start backend"
        return 1
    fi
}

# Start ultra-fast frontend (Vite or optimized Next.js)
start_frontend() {
    log_info "Starting ultra-fast frontend..."
    
    if is_running "$PIDS_DIR/frontend.pid"; then
        log_warning "Frontend is already running"
        return
    fi
    
    cd "$FRONTEND_DIR"
    
    # Install dependencies if needed
    if [[ ! -d "node_modules" ]]; then
        log_info "Installing frontend dependencies..."
        pnpm install
    fi
    
    # Check if Vite is available
    if [[ -f "vite.config.ts" ]]; then
        log_info "Using Vite for ultra-fast development..."
        nohup pnpm dev > "$LOGS_DIR/frontend-vite.log" 2>&1 &
    else
        log_info "Using optimized Next.js..."
        nohup pnpm dev > "$LOGS_DIR/frontend-next.log" 2>&1 &
    fi
    
    echo $! > "$PIDS_DIR/frontend.pid"
    
    sleep 5
    if is_running "$PIDS_DIR/frontend.pid"; then
        log_success "Frontend started on http://localhost:3001"
    else
        log_error "Failed to start frontend"
        return 1
    fi
}

# Stop service
stop_service() {
    local service="$1"
    local pid_file="$PIDS_DIR/${service}.pid"
    
    if is_running "$pid_file"; then
        local pid=$(cat "$pid_file")
        kill "$pid" 2>/dev/null || true
        rm "$pid_file"
        log_success "$service stopped"
    else
        log_warning "$service is not running"
    fi
}

# Show status
status() {
    echo -e "\n${CYAN}📊 Development Environment Status${NC}"
    echo "=================================="
    
    # Check services
    if brew services list | grep postgresql | grep started > /dev/null; then
        echo -e "PostgreSQL: ${GREEN}✅ Running${NC}"
    else
        echo -e "PostgreSQL: ${RED}❌ Stopped${NC}"
    fi
    
    if brew services list | grep redis | grep started > /dev/null; then
        echo -e "Redis: ${GREEN}✅ Running${NC}"
    else
        echo -e "Redis: ${RED}❌ Stopped${NC}"
    fi
    
    if is_running "$PIDS_DIR/backend.pid"; then
        echo -e "Backend: ${GREEN}✅ Running (PID: $(cat "$PIDS_DIR/backend.pid"))${NC}"
    else
        echo -e "Backend: ${RED}❌ Stopped${NC}"
    fi
    
    if is_running "$PIDS_DIR/frontend.pid"; then
        echo -e "Frontend: ${GREEN}✅ Running (PID: $(cat "$PIDS_DIR/frontend.pid"))${NC}"
    else
        echo -e "Frontend: ${RED}❌ Stopped${NC}"
    fi
    
    echo -e "\n${BLUE}🌐 Access URLs:${NC}"
    echo "Frontend: http://localhost:3001/founder-dashboard"
    echo "Backend API: http://localhost:8000/api/"
    echo "Admin Panel: http://localhost:8000/admin/"
}

# Show logs
logs() {
    local service="$1"
    case "$service" in
        backend)
            tail -f "$LOGS_DIR/backend-optimized.log"
            ;;
        frontend)
            if [[ -f "$LOGS_DIR/frontend-vite.log" ]]; then
                tail -f "$LOGS_DIR/frontend-vite.log"
            else
                tail -f "$LOGS_DIR/frontend-next.log"
            fi
            ;;
        *)
            echo "Available logs: backend, frontend"
            ;;
    esac
}

# Performance monitoring
monitor() {
    echo -e "${CYAN}🔍 Real-time Performance Monitor${NC}"
    echo "================================="
    
    while true; do
        clear
        echo -e "${CYAN}🔍 MTBRMG ERP Performance Monitor${NC}"
        echo "================================="
        echo "Time: $(date)"
        echo ""
        
        # System load
        echo -e "${YELLOW}System Load:${NC}"
        uptime
        echo ""
        
        # Memory usage
        echo -e "${YELLOW}Memory Usage:${NC}"
        vm_stat | head -5
        echo ""
        
        # Process status
        status
        
        sleep 5
    done
}

# Main command handler
case "$1" in
    start)
        echo -e "${BLUE}🚀 Starting Optimized Development Environment${NC}"
        echo "=============================================="
        
        start_postgres
        start_redis
        start_backend
        start_frontend
        
        echo ""
        status
        echo ""
        log_success "Optimized development environment started!"
        echo -e "${PURPLE}💡 Use 'dev-optimized.sh monitor' for real-time monitoring${NC}"
        ;;
    stop)
        echo -e "${BLUE}🛑 Stopping Development Environment${NC}"
        echo "=================================="
        
        stop_service backend
        stop_service frontend
        brew services stop postgresql@15
        brew services stop redis
        
        log_success "Development environment stopped"
        ;;
    restart)
        $0 stop
        sleep 2
        $0 start
        ;;
    status)
        status
        ;;
    logs)
        logs "$2"
        ;;
    monitor)
        monitor
        ;;
    *)
        echo -e "${CYAN}MTBRMG ERP - Optimized Development Manager${NC}"
        echo ""
        echo "Usage: $0 {start|stop|restart|status|logs|monitor}"
        echo ""
        echo "Commands:"
        echo "  start    - Start all optimized services"
        echo "  stop     - Stop all services"
        echo "  restart  - Restart all services"
        echo "  status   - Show service status"
        echo "  logs     - Show logs (backend|frontend)"
        echo "  monitor  - Real-time performance monitoring"
        echo ""
        echo "Examples:"
        echo "  $0 start           # Start optimized environment"
        echo "  $0 logs backend    # Show backend logs"
        echo "  $0 monitor         # Monitor performance"
        ;;
esac
