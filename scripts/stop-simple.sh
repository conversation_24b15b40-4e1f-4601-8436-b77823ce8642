#!/bin/bash

# Stop simple development environment

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
PIDS_DIR="$PROJECT_ROOT/tmp"

echo "🛑 Stopping simple development environment..."

# Kill processes
if [[ -f "$PIDS_DIR/backend-simple.pid" ]]; then
    kill $(cat "$PIDS_DIR/backend-simple.pid") 2>/dev/null || true
    rm "$PIDS_DIR/backend-simple.pid"
    echo "✅ Backend stopped"
fi

if [[ -f "$PIDS_DIR/frontend-simple.pid" ]]; then
    kill $(cat "$PIDS_DIR/frontend-simple.pid") 2>/dev/null || true
    rm "$PIDS_DIR/frontend-simple.pid"
    echo "✅ Frontend stopped"
fi

echo "✅ Simple development environment stopped"
