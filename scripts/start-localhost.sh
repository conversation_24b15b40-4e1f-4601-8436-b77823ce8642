#!/bin/bash

# MTBRMG ERP - Simple Localhost Development Server
# Start both backend and frontend on localhost

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 Starting MTBRMG ERP on Localhost${NC}"
echo "=========================================="

# Navigate to project root
cd "$(dirname "$0")/.."

# Function to check if port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Function to kill processes on port
kill_port() {
    local port=$1
    if port_in_use $port; then
        echo -e "${YELLOW}🔄 Killing processes on port $port...${NC}"
        lsof -ti :$port | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
}

# Clean up any existing processes
echo -e "${YELLOW}🧹 Cleaning up existing processes...${NC}"
kill_port 8000
kill_port 3001

# Start backend
echo -e "${BLUE}🐍 Starting Django Backend...${NC}"
cd apps/backend

# Check if virtual environment exists
if [[ ! -d "venv" ]]; then
    echo -e "${RED}❌ Virtual environment not found. Please run setup first.${NC}"
    exit 1
fi

# Start backend in background
source venv/bin/activate
nohup python manage.py runserver 8000 > ../../logs/backend.log 2>&1 &
BACKEND_PID=$!

# Wait for backend to start
echo -e "${YELLOW}⏳ Waiting for backend to start...${NC}"
sleep 5

# Check if backend is running
if port_in_use 8000; then
    echo -e "${GREEN}✅ Backend started successfully on http://localhost:8000${NC}"
else
    echo -e "${RED}❌ Backend failed to start. Check logs/backend.log${NC}"
    exit 1
fi

# Start frontend
echo -e "${BLUE}⚛️ Starting Next.js Frontend...${NC}"
cd ../frontend

# Clean cache
rm -rf .next

# Start frontend in background
nohup pnpm dev > ../../logs/frontend.log 2>&1 &
FRONTEND_PID=$!

# Wait for frontend to start
echo -e "${YELLOW}⏳ Waiting for frontend to start...${NC}"
sleep 10

# Check if frontend is running
if port_in_use 3001; then
    echo -e "${GREEN}✅ Frontend started successfully on http://localhost:3001${NC}"
else
    echo -e "${RED}❌ Frontend failed to start. Check logs/frontend.log${NC}"
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

# Create PID files for easy cleanup
mkdir -p ../../pids
echo $BACKEND_PID > ../../pids/backend.pid
echo $FRONTEND_PID > ../../pids/frontend.pid

echo ""
echo -e "${GREEN}🎉 MTBRMG ERP is now running!${NC}"
echo "=========================================="
echo -e "${BLUE}🌐 Frontend:${NC} http://localhost:3001"
echo -e "${BLUE}🔧 Backend:${NC}  http://localhost:8000"
echo -e "${BLUE}📊 Admin:${NC}    http://localhost:8000/admin"
echo ""
echo -e "${YELLOW}📝 Logs:${NC}"
echo "  Backend:  tail -f logs/backend.log"
echo "  Frontend: tail -f logs/frontend.log"
echo ""
echo -e "${YELLOW}🛑 To stop:${NC} ./scripts/stop-localhost.sh"
echo ""
echo -e "${GREEN}✨ Ready for development!${NC}"
