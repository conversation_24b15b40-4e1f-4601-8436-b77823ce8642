#!/bin/bash

# Stop fast development environment

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
PIDS_DIR="$PROJECT_ROOT/tmp"

echo "🛑 Stopping fast development environment..."

# Kill processes
if [[ -f "$PIDS_DIR/backend-fast.pid" ]]; then
    kill $(cat "$PIDS_DIR/backend-fast.pid") 2>/dev/null || true
    rm "$PIDS_DIR/backend-fast.pid"
    echo "✅ Backend stopped"
fi

if [[ -f "$PIDS_DIR/frontend-fast.pid" ]]; then
    kill $(cat "$PIDS_DIR/frontend-fast.pid") 2>/dev/null || true
    rm "$PIDS_DIR/frontend-fast.pid"
    echo "✅ Frontend stopped"
fi

echo "✅ Fast development environment stopped"
