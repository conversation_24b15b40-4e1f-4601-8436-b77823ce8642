#!/bin/bash

# Monitor fast development environment performance

echo "🔍 MTBRMG ERP Performance Monitor"
echo "================================="

while true; do
    clear
    echo "🔍 MTBRMG ERP Performance Monitor - $(date)"
    echo "============================================="
    
    # System load
    echo -e "\n📊 System Load:"
    uptime
    
    # Memory usage
    echo -e "\n🧠 Memory Usage:"
    vm_stat | head -5
    
    # Process status
    echo -e "\n🔧 Process Status:"
    if pgrep -f "python manage.py runserver" > /dev/null; then
        echo "✅ Backend: Running"
    else
        echo "❌ Backend: Stopped"
    fi
    
    if pgrep -f "pnpm dev" > /dev/null; then
        echo "✅ Frontend: Running"
    else
        echo "❌ Frontend: Stopped"
    fi
    
    # Port status
    echo -e "\n🌐 Port Status:"
    if lsof -i :8000 > /dev/null 2>&1; then
        echo "✅ Port 8000: In use (Backend)"
    else
        echo "❌ Port 8000: Free"
    fi
    
    if lsof -i :3001 > /dev/null 2>&1; then
        echo "✅ Port 3001: In use (Frontend)"
    else
        echo "❌ Port 3001: Free"
    fi
    
    echo -e "\n💡 Press Ctrl+C to exit monitoring"
    sleep 5
done
