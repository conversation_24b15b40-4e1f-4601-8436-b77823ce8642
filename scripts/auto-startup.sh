#!/bin/bash

# MTBRMG ERP - Auto Startup Configuration
# Configure PM2 to start automatically on macOS boot

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project paths
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo -e "${BLUE}🔧 MTBRMG ERP - Auto Startup Configuration${NC}"
echo -e "${BLUE}===========================================${NC}"

# Function to print colored output
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if PM2 is installed
check_pm2() {
    if ! command -v pm2 &> /dev/null; then
        log_error "PM2 is not installed. Please run ./scripts/setup-pm2.sh first."
        exit 1
    fi
    log_success "PM2 found: $(pm2 --version)"
}

# Setup PM2 startup
setup_pm2_startup() {
    log_info "Configuring PM2 for automatic startup..."
    
    # Generate startup script
    pm2 startup
    
    log_warning "Please run the command shown above with sudo to complete the setup."
    log_info "After running the sudo command, press Enter to continue..."
    read -r
}

# Save PM2 configuration
save_pm2_config() {
    log_info "Starting MTBRMG ERP to save configuration..."
    
    cd "$PROJECT_ROOT"
    
    # Start the applications
    pm2 start ecosystem.config.js
    
    # Save the current PM2 configuration
    pm2 save
    
    log_success "PM2 configuration saved"
}

# Create LaunchAgent for additional services
create_launch_agent() {
    log_info "Creating LaunchAgent for PostgreSQL and Redis..."
    
    # Create LaunchAgents directory if it doesn't exist
    mkdir -p ~/Library/LaunchAgents
    
    # PostgreSQL LaunchAgent
    cat > ~/Library/LaunchAgents/com.mtbrmg.postgresql.plist << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.mtbrmg.postgresql</string>
    <key>ProgramArguments</key>
    <array>
        <string>/opt/homebrew/bin/brew</string>
        <string>services</string>
        <string>start</string>
        <string>postgresql@15</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <false/>
</dict>
</plist>
EOF
    
    # Redis LaunchAgent
    cat > ~/Library/LaunchAgents/com.mtbrmg.redis.plist << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.mtbrmg.redis</string>
    <key>ProgramArguments</key>
    <array>
        <string>/opt/homebrew/bin/brew</string>
        <string>services</string>
        <string>start</string>
        <string>redis</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <false/>
</dict>
</plist>
EOF
    
    # Load the LaunchAgents
    launchctl load ~/Library/LaunchAgents/com.mtbrmg.postgresql.plist
    launchctl load ~/Library/LaunchAgents/com.mtbrmg.redis.plist
    
    log_success "LaunchAgents created and loaded"
}

# Create status check script
create_status_script() {
    log_info "Creating status check script..."
    
    cat > "$PROJECT_ROOT/scripts/status.sh" << 'EOF'
#!/bin/bash

# MTBRMG ERP - Status Check Script

echo "🔍 MTBRMG ERP Status Check"
echo "=========================="

# Check PM2 processes
echo ""
echo "📊 PM2 Processes:"
pm2 status

# Check database connection
echo ""
echo "🗄️  Database Status:"
cd "$(dirname "${BASH_SOURCE[0]}")/../apps/backend"
source venv/bin/activate
if python manage.py check --database default > /dev/null 2>&1; then
    echo "✅ Database: Connected"
else
    echo "❌ Database: Connection failed"
fi

# Check services
echo ""
echo "🔧 System Services:"
if brew services list | grep postgresql | grep started > /dev/null; then
    echo "✅ PostgreSQL: Running"
else
    echo "❌ PostgreSQL: Not running"
fi

if brew services list | grep redis | grep started > /dev/null; then
    echo "✅ Redis: Running"
else
    echo "❌ Redis: Not running"
fi

# Check URLs
echo ""
echo "🌐 Application URLs:"
echo "• Frontend: http://localhost:3001"
echo "• Backend: http://localhost:8000"

# Check if ports are accessible
echo ""
echo "🔌 Port Status:"
if nc -z localhost 3001 2>/dev/null; then
    echo "✅ Frontend (3001): Accessible"
else
    echo "❌ Frontend (3001): Not accessible"
fi

if nc -z localhost 8000 2>/dev/null; then
    echo "✅ Backend (8000): Accessible"
else
    echo "❌ Backend (8000): Not accessible"
fi
EOF
    
    chmod +x "$PROJECT_ROOT/scripts/status.sh"
    log_success "Status check script created"
}

# Create restart script
create_restart_script() {
    log_info "Creating restart script..."
    
    cat > "$PROJECT_ROOT/scripts/restart-pm2.sh" << 'EOF'
#!/bin/bash

# MTBRMG ERP - PM2 Restart Script

echo "🔄 Restarting MTBRMG ERP..."

# Restart PM2 processes
pm2 restart all

# Show status
pm2 status

echo ""
echo "✅ MTBRMG ERP restarted successfully!"
echo "🌐 Frontend: http://localhost:3001"
echo "🌐 Backend: http://localhost:8000"
EOF
    
    chmod +x "$PROJECT_ROOT/scripts/restart-pm2.sh"
    log_success "Restart script created"
}

# Main setup function
main() {
    log_info "Starting auto-startup configuration for MTBRMG ERP..."
    
    check_pm2
    setup_pm2_startup
    save_pm2_config
    create_launch_agent
    create_status_script
    create_restart_script
    
    echo ""
    echo -e "${GREEN}🎉 Auto-startup configuration completed!${NC}"
    echo ""
    echo -e "${YELLOW}Available commands:${NC}"
    echo "• Start: ./scripts/start-pm2.sh"
    echo "• Stop: ./scripts/stop-pm2.sh"
    echo "• Restart: ./scripts/restart-pm2.sh"
    echo "• Status: ./scripts/status.sh"
    echo "• Monitor: pm2 monit"
    echo "• Logs: pm2 logs"
    echo ""
    echo -e "${BLUE}Your MTBRMG ERP will now start automatically on system boot!${NC}"
}

# Run main function
main "$@"
