#!/bin/bash

# MTBRMG ERP - Simple Development Server for macOS
# Ultra-lightweight solution - no complex process management

set -e

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Project paths
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/apps/backend"
FRONTEND_DIR="$PROJECT_ROOT/apps/frontend"

echo -e "${BLUE}🚀 MTBRMG ERP - Simple Development Server${NC}"
echo "=============================================="

# Kill existing processes
echo -e "${YELLOW}🧹 Cleaning up existing processes...${NC}"
lsof -ti:8000 2>/dev/null | xargs kill -9 2>/dev/null || true
lsof -ti:3001 2>/dev/null | xargs kill -9 2>/dev/null || true
sleep 1

# Start services
echo -e "${YELLOW}🔄 Starting PostgreSQL and Redis...${NC}"
brew services start postgresql@15 2>/dev/null || true
brew services start redis 2>/dev/null || true

# Start backend
echo -e "${YELLOW}🐍 Starting Django backend...${NC}"
cd "$BACKEND_DIR"
source venv/bin/activate

# Quick migration check
python manage.py migrate --check >/dev/null 2>&1 || {
    echo -e "${YELLOW}📊 Running migrations...${NC}"
    python manage.py migrate
}

# Start backend in background
python manage.py runserver 8000 --noreload &
BACKEND_PID=$!
echo "Backend PID: $BACKEND_PID"

# Wait for backend
sleep 3
if curl -f http://localhost:8000/api/ >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Backend started on http://localhost:8000${NC}"
else
    echo -e "${YELLOW}⏳ Backend starting... (may take a moment)${NC}"
fi

# Start frontend
echo -e "${YELLOW}⚡ Starting Next.js frontend...${NC}"
cd "$FRONTEND_DIR"

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}📦 Installing dependencies...${NC}"
    pnpm install
fi

# Set optimizations
export NODE_OPTIONS="--max-old-space-size=4096"
export NEXT_TELEMETRY_DISABLED=1

# Start frontend in background
pnpm dev &
FRONTEND_PID=$!
echo "Frontend PID: $FRONTEND_PID"

# Wait for frontend
sleep 5
if curl -f http://localhost:3001 >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Frontend started on http://localhost:3001${NC}"
else
    echo -e "${YELLOW}⏳ Frontend starting... (may take a moment)${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Development servers started!${NC}"
echo "=================================="
echo "🌐 Frontend: http://localhost:3001"
echo "🔧 Backend:  http://localhost:8000"
echo "👤 Login:    founder / demo123"
echo ""
echo "📋 Process IDs:"
echo "   Backend:  $BACKEND_PID"
echo "   Frontend: $FRONTEND_PID"
echo ""
echo "🛑 To stop servers:"
echo "   kill $BACKEND_PID $FRONTEND_PID"
echo "   or use: lsof -ti:8000,3001 | xargs kill"
echo ""
echo "📝 View logs:"
echo "   Backend:  tail -f apps/backend/logs/django.log"
echo "   Frontend: Check terminal output"
echo ""

# Keep script running to show output
echo -e "${BLUE}Press Ctrl+C to stop all servers${NC}"
trap 'echo ""; echo "Stopping servers..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true; exit' INT

# Wait for processes
wait
