#!/bin/bash

# MTBRMG ERP - Automated Development Environment Setup
# This script installs VS Code extensions and sets up development tools

echo "🚀 MTBRMG ERP - Setting up development environment..."
echo "=================================================="

# Check if VS Code is installed
if ! command -v code &> /dev/null; then
    echo "❌ VS Code is not installed or not in PATH"
    echo "Please install VS Code first: https://code.visualstudio.com/"
    exit 1
fi

echo "✅ VS Code found, proceeding with extension installation..."

# Frontend Development Extensions
echo "📦 Installing Frontend Development Extensions..."
code --install-extension bradlc.vscode-tailwindcss
code --install-extension ms-vscode.vscode-typescript-next
code --install-extension esbenp.prettier-vscode
code --install-extension dbaeumer.vscode-eslint
code --install-extension formulahendry.auto-rename-tag
code --install-extension PulkitGangwar.nextjs-snippets
code --install-extension ms-vscode.vscode-json

# Backend Development Extensions
echo "🐍 Installing Backend Development Extensions..."
code --install-extension ms-python.python
code --install-extension ms-python.pylint
code --install-extension ms-python.black-formatter
code --install-extension batisteo.vscode-django
code --install-extension ms-python.flake8
code --install-extension ms-python.isort

# Database Extensions
echo "🗄️ Installing Database Extensions..."
code --install-extension mtxr.sqltools
code --install-extension mtxr.sqltools-driver-pg

# Development Workflow Extensions
echo "🔧 Installing Development Workflow Extensions..."
code --install-extension eamodio.gitlens
code --install-extension mhutchie.git-graph
code --install-extension alefragnani.project-manager
code --install-extension yzhang.markdown-all-in-one
code --install-extension shd101wyy.markdown-preview-enhanced
code --install-extension humao.rest-client
code --install-extension rangav.vscode-thunder-client

# Additional Useful Extensions
echo "⚡ Installing Additional Useful Extensions..."
code --install-extension ms-vscode.vscode-file-watcher
code --install-extension christian-kohler.path-intellisense
code --install-extension ms-vscode.vscode-icons
code --install-extension PKief.material-icon-theme

echo "✅ All VS Code extensions installed successfully!"

# Check if Node.js is available for frontend setup
if command -v node &> /dev/null; then
    echo "📦 Setting up Frontend Development Tools..."
    cd apps/frontend
    
    # Install additional development dependencies
    npm install --save-dev @next/bundle-analyzer
    npm install --save-dev eslint-config-prettier
    npm install --save-dev prettier
    npm install --save-dev prettier-plugin-tailwindcss
    npm install --save-dev @tailwindcss/forms
    npm install --save-dev @tailwindcss/typography
    
    echo "✅ Frontend development tools installed!"
    cd ../..
else
    echo "⚠️ Node.js not found. Please install Node.js to set up frontend tools."
fi

# Check if Python is available for backend setup
if command -v python3 &> /dev/null; then
    echo "🐍 Setting up Backend Development Tools..."
    cd apps/backend
    
    # Create requirements-dev.txt if it doesn't exist
    if [ ! -f "requirements-dev.txt" ]; then
        cat > requirements-dev.txt << EOF
black==23.9.1
flake8==6.1.0
isort==5.12.0
pylint==3.0.1
django-debug-toolbar==4.2.0
django-extensions==3.2.3
pytest-django==4.5.2
coverage==7.3.2
django-silk==5.0.4
EOF
    fi
    
    # Install development dependencies
    if [ -d "venv" ]; then
        source venv/bin/activate
        pip install -r requirements-dev.txt
        echo "✅ Backend development tools installed!"
    else
        echo "⚠️ Virtual environment not found. Please activate your venv and run:"
        echo "pip install -r requirements-dev.txt"
    fi
    
    cd ../..
else
    echo "⚠️ Python not found. Please install Python to set up backend tools."
fi

echo ""
echo "🎉 Development environment setup complete!"
echo "=================================================="
echo "Next steps:"
echo "1. Restart VS Code to activate all extensions"
echo "2. Open the project in VS Code"
echo "3. Check that all extensions are working properly"
echo ""
echo "📋 Installed Extensions:"
echo "- Tailwind CSS IntelliSense"
echo "- TypeScript Support"
echo "- Prettier Code Formatter"
echo "- ESLint"
echo "- Python Support"
echo "- Django Support"
echo "- GitLens"
echo "- SQL Tools"
echo "- REST Client"
echo "- And many more..."
echo ""
echo "🔧 To complete setup, run: ./scripts/setup-config.sh"
