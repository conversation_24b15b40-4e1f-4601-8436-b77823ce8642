#!/bin/bash

# MTBRMG ERP - Stop Localhost Development Servers

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🛑 Stopping MTBRMG ERP Localhost Servers${NC}"
echo "============================================="

# Navigate to project root
cd "$(dirname "$0")/.."

# Function to check if port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Function to kill processes on port
kill_port() {
    local port=$1
    local service=$2
    if port_in_use $port; then
        echo -e "${YELLOW}🔄 Stopping $service on port $port...${NC}"
        lsof -ti :$port | xargs kill -9 2>/dev/null || true
        sleep 2
        if port_in_use $port; then
            echo -e "${RED}❌ Failed to stop $service${NC}"
        else
            echo -e "${GREEN}✅ $service stopped${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ $service is not running${NC}"
    fi
}

# Stop using PID files if they exist
if [[ -f "pids/backend.pid" ]]; then
    BACKEND_PID=$(cat pids/backend.pid)
    if kill -0 $BACKEND_PID 2>/dev/null; then
        echo -e "${YELLOW}🔄 Stopping backend (PID: $BACKEND_PID)...${NC}"
        kill $BACKEND_PID 2>/dev/null || true
        sleep 2
    fi
    rm -f pids/backend.pid
fi

if [[ -f "pids/frontend.pid" ]]; then
    FRONTEND_PID=$(cat pids/frontend.pid)
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        echo -e "${YELLOW}🔄 Stopping frontend (PID: $FRONTEND_PID)...${NC}"
        kill $FRONTEND_PID 2>/dev/null || true
        sleep 2
    fi
    rm -f pids/frontend.pid
fi

# Kill any remaining processes on the ports
kill_port 8000 "Backend"
kill_port 3001 "Frontend"

# Kill any remaining Next.js or Django processes
echo -e "${YELLOW}🧹 Cleaning up remaining processes...${NC}"
pkill -f "python manage.py runserver" 2>/dev/null || true
pkill -f "next dev" 2>/dev/null || true
pkill -f "pnpm dev" 2>/dev/null || true

# Clean up PID directory
rm -rf pids

echo ""
echo -e "${GREEN}✅ All servers stopped successfully!${NC}"
echo -e "${BLUE}💡 To start again: ./scripts/start-localhost.sh${NC}"
