#!/bin/bash

# MTBRMG ERP - Simple Fast Development Environment
# Optimized Next.js setup without complex Vite configuration

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/apps/backend"
FRONTEND_DIR="$PROJECT_ROOT/apps/frontend"
LOGS_DIR="$PROJECT_ROOT/logs"
PIDS_DIR="$PROJECT_ROOT/tmp"

# Create directories
mkdir -p "$LOGS_DIR" "$PIDS_DIR"

echo -e "${BLUE}🚀 Starting Simple Fast Development Environment${NC}"
echo "=============================================="

# Function to check if port is in use
port_in_use() {
    lsof -i :$1 > /dev/null 2>&1
}

# Function to kill processes on port
kill_port() {
    local port=$1
    if port_in_use $port; then
        echo -e "${YELLOW}🔄 Killing processes on port $port...${NC}"
        lsof -ti :$port | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
}

# Clean up any existing processes
echo -e "${YELLOW}🧹 Cleaning up existing processes...${NC}"
kill_port 8000
kill_port 3001
kill_port 3002

# Remove Vite files that are causing issues
echo -e "${YELLOW}🧹 Cleaning up Vite configuration...${NC}"
cd "$FRONTEND_DIR"
rm -f vite.config.ts index.html 2>/dev/null || true
rm -rf src/ 2>/dev/null || true

# Restore original package.json if backup exists
if [[ -f "package.json.backup" ]]; then
    echo -e "${YELLOW}📦 Restoring original package.json...${NC}"
    mv package.json.backup package.json
fi

# Start Redis if not running
if ! brew services list | grep redis | grep started > /dev/null; then
    echo -e "${YELLOW}🔄 Starting Redis...${NC}"
    brew services start redis
    sleep 2
fi

# Start PostgreSQL if not running
if ! brew services list | grep postgresql | grep started > /dev/null; then
    echo -e "${YELLOW}🔄 Starting PostgreSQL...${NC}"
    brew services start postgresql@15
    sleep 3
fi

# Start optimized backend
echo -e "${YELLOW}🐍 Starting optimized Django backend...${NC}"
cd "$BACKEND_DIR"
source venv/bin/activate

# Apply performance optimizations
export PYTHONUNBUFFERED=1

# Quick migration check
python manage.py migrate --check > /dev/null 2>&1 || {
    echo -e "${YELLOW}📊 Running database migrations...${NC}"
    python manage.py migrate
}

# Start backend with optimizations (no reload for better performance)
nohup python manage.py runserver 8000 --noreload > "$LOGS_DIR/backend-simple.log" 2>&1 &
BACKEND_PID=$!

# Start optimized Next.js frontend
echo -e "${YELLOW}⚡ Starting optimized Next.js frontend...${NC}"
cd "$FRONTEND_DIR"

# Apply performance optimizations
export NODE_OPTIONS="--max-old-space-size=8192"
export NEXT_TELEMETRY_DISABLED=1
export DISABLE_ESLINT_PLUGIN=true

# Install dependencies if needed
if [[ ! -d "node_modules" ]]; then
    echo -e "${YELLOW}📦 Installing frontend dependencies...${NC}"
    pnpm install
fi

# Start Next.js with optimizations
nohup pnpm dev > "$LOGS_DIR/frontend-simple.log" 2>&1 &
FRONTEND_PID=$!

# Wait for services to start
echo -e "${YELLOW}⏳ Waiting for services to start...${NC}"
sleep 8

# Check if services are running
backend_running=false
frontend_running=false

if kill -0 $BACKEND_PID 2>/dev/null; then
    backend_running=true
fi

if kill -0 $FRONTEND_PID 2>/dev/null; then
    frontend_running=true
fi

# Save PIDs for cleanup
echo $BACKEND_PID > "$PIDS_DIR/backend-simple.pid"
echo $FRONTEND_PID > "$PIDS_DIR/frontend-simple.pid"

echo -e "\n${GREEN}✅ Simple Fast Development Environment Status${NC}"
echo "=============================================="

if $backend_running; then
    echo -e "${GREEN}✅ Backend: Running (PID: $BACKEND_PID)${NC}"
    echo -e "${BLUE}🌐 Backend API: http://localhost:8000/api/${NC}"
    echo -e "${BLUE}🌐 Admin Panel: http://localhost:8000/admin/${NC}"
else
    echo -e "${RED}❌ Backend: Failed to start${NC}"
    echo -e "${YELLOW}💡 Check logs: tail -f logs/backend-simple.log${NC}"
fi

if $frontend_running; then
    echo -e "${GREEN}✅ Frontend: Running (PID: $FRONTEND_PID)${NC}"
    echo -e "${BLUE}🌐 Frontend: http://localhost:3001/founder-dashboard${NC}"
    echo -e "${BLUE}🌐 Login: founder / demo123${NC}"
else
    echo -e "${RED}❌ Frontend: Failed to start${NC}"
    echo -e "${YELLOW}💡 Check logs: tail -f logs/frontend-simple.log${NC}"
fi

echo -e "\n${PURPLE}💡 Performance Optimizations Applied:${NC}"
echo "  • Node.js memory increased to 8GB"
echo "  • Django auto-reload disabled for better performance"
echo "  • Next.js telemetry disabled"
echo "  • ESLint disabled for faster builds"
echo "  • Redis and PostgreSQL optimized"

echo -e "\n${PURPLE}📊 Useful Commands:${NC}"
echo "  • View backend logs: tail -f logs/backend-simple.log"
echo "  • View frontend logs: tail -f logs/frontend-simple.log"
echo "  • Stop services: ./scripts/stop-simple.sh"

# Create stop script
cat > "$PROJECT_ROOT/scripts/stop-simple.sh" <<'EOF'
#!/bin/bash

# Stop simple development environment

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
PIDS_DIR="$PROJECT_ROOT/tmp"

echo "🛑 Stopping simple development environment..."

# Kill processes
if [[ -f "$PIDS_DIR/backend-simple.pid" ]]; then
    kill $(cat "$PIDS_DIR/backend-simple.pid") 2>/dev/null || true
    rm "$PIDS_DIR/backend-simple.pid"
    echo "✅ Backend stopped"
fi

if [[ -f "$PIDS_DIR/frontend-simple.pid" ]]; then
    kill $(cat "$PIDS_DIR/frontend-simple.pid") 2>/dev/null || true
    rm "$PIDS_DIR/frontend-simple.pid"
    echo "✅ Frontend stopped"
fi

echo "✅ Simple development environment stopped"
EOF

chmod +x "$PROJECT_ROOT/scripts/stop-simple.sh"

# Test the application
echo -e "\n${YELLOW}🧪 Testing application...${NC}"
sleep 5

# Test backend
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/api/ | grep -q "401\|200"; then
    echo -e "${GREEN}✅ Backend API is responding${NC}"
else
    echo -e "${RED}❌ Backend API is not responding${NC}"
fi

# Test frontend
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3001/ | grep -q "200"; then
    echo -e "${GREEN}✅ Frontend is responding${NC}"
else
    echo -e "${RED}❌ Frontend is not responding${NC}"
fi

echo -e "\n${GREEN}🎉 Simple fast development environment is ready!${NC}"
echo -e "${YELLOW}💡 This setup should be much faster than the default configuration.${NC}"

# Wait for user input to keep script running
echo -e "\n${CYAN}Press Ctrl+C to stop all services, or run './scripts/stop-simple.sh' from another terminal${NC}"
trap "echo -e '\n${YELLOW}🛑 Stopping services...${NC}'; ./scripts/stop-simple.sh; exit" INT
wait
