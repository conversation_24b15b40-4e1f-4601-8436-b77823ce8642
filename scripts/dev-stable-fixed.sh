#!/bin/bash

# MTBRMG ERP - Stable Development Server (Fixed)
# Optimized for stability and performance

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project paths
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/apps/backend"
FRONTEND_DIR="$PROJECT_ROOT/apps/frontend"
LOGS_DIR="$PROJECT_ROOT/logs"
PIDS_DIR="$PROJECT_ROOT/pids"

# Create necessary directories
mkdir -p "$LOGS_DIR" "$PIDS_DIR"

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✅${NC} $1"
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌${NC} $1"
}

# Function to check if port is in use
port_in_use() {
    lsof -ti:$1 > /dev/null 2>&1
}

# Function to kill process on port
kill_port() {
    if port_in_use $1; then
        log "Killing process on port $1..."
        lsof -ti:$1 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
}

# Function to check if service is running
check_service() {
    local port=$1
    local name=$2
    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if [ "$port" = "8000" ]; then
            # For backend, check API endpoint
            if curl -f http://localhost:$port/api/ >/dev/null 2>&1; then
                success "$name is running on port $port"
                return 0
            fi
        else
            # For frontend, check root
            if curl -f http://localhost:$port/ >/dev/null 2>&1; then
                success "$name is running on port $port"
                return 0
            fi
        fi
        sleep 1
        ((attempt++))
    done

    error "$name failed to start on port $port"
    return 1
}

# Cleanup function
cleanup() {
    log "Cleaning up..."
    kill_port 8000
    kill_port 3001
    
    # Kill any remaining processes
    pkill -f "python manage.py runserver" 2>/dev/null || true
    pkill -f "next dev" 2>/dev/null || true
    
    # Clean up PID files
    rm -f "$PIDS_DIR"/*.pid
    
    success "Cleanup completed"
}

# Trap cleanup on exit
trap cleanup EXIT INT TERM

# Main execution
main() {
    log "Starting MTBRMG ERP Development Server (Stable Fixed Version)"
    
    # Initial cleanup
    cleanup
    
    # Check and start required services
    log "Checking required services..."
    
    # Start PostgreSQL if not running
    if ! brew services list | grep -q "postgresql@14.*started"; then
        log "Starting PostgreSQL@14..."
        brew services start postgresql@14
        sleep 3
    fi
    
    # Start Redis if not running
    if ! brew services list | grep -q "redis.*started"; then
        log "Starting Redis..."
        brew services start redis
        sleep 2
    fi
    
    # Verify database connection
    log "Verifying database connection..."
    cd "$BACKEND_DIR"
    source venv/bin/activate
    
    if ! python manage.py check --database default >/dev/null 2>&1; then
        error "Database connection failed. Please check your PostgreSQL configuration."
        exit 1
    fi
    
    success "Database connection verified"
    
    # Run migrations if needed
    log "Checking for pending migrations..."
    if ! python manage.py migrate --check >/dev/null 2>&1; then
        log "Running database migrations..."
        python manage.py migrate
    fi
    
    # Start backend with optimizations
    log "Starting Django backend (optimized)..."
    export PYTHONUNBUFFERED=1
    export DJANGO_SETTINGS_MODULE=mtbrmg_erp.settings
    
    # Start backend without auto-reload for stability
    nohup python manage.py runserver 8000 --noreload --insecure >"$LOGS_DIR/backend-stable.log" 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > "$PIDS_DIR/backend.pid"
    
    # Wait for backend to start
    if check_service 8000 "Backend"; then
        success "Backend started successfully"
    else
        error "Backend failed to start. Check logs/backend-stable.log"
        exit 1
    fi
    
    # Start frontend
    log "Starting Next.js frontend (optimized)..."
    cd "$FRONTEND_DIR"
    
    # Set Node.js optimizations
    export NODE_OPTIONS="--max-old-space-size=4096"
    export NEXT_TELEMETRY_DISABLED=1
    export DISABLE_ESLINT_PLUGIN=true
    
    # Install dependencies if needed
    if [[ ! -d "node_modules" ]]; then
        log "Installing frontend dependencies..."
        npm install
    fi
    
    # Start frontend
    nohup npm run dev >"$LOGS_DIR/frontend-stable.log" 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > "$PIDS_DIR/frontend.pid"
    
    # Wait for frontend to start
    if check_service 3001 "Frontend"; then
        success "Frontend started successfully"
    else
        error "Frontend failed to start. Check logs/frontend-stable.log"
        exit 1
    fi
    
    # Display status
    echo ""
    success "🚀 MTBRMG ERP Development Server is running!"
    echo ""
    echo -e "${GREEN}📊 Services Status:${NC}"
    echo -e "  ${BLUE}Backend:${NC}  http://localhost:8000"
    echo -e "  ${BLUE}Frontend:${NC} http://localhost:3001"
    echo -e "  ${BLUE}Admin:${NC}    http://localhost:8000/admin/"
    echo ""
    echo -e "${YELLOW}📝 Logs:${NC}"
    echo -e "  Backend:  tail -f $LOGS_DIR/backend-stable.log"
    echo -e "  Frontend: tail -f $LOGS_DIR/frontend-stable.log"
    echo ""
    echo -e "${GREEN}🔑 Default Login:${NC}"
    echo -e "  Email:    <EMAIL>"
    echo -e "  Password: demo123"
    echo ""
    echo -e "${YELLOW}Press Ctrl+C to stop all services${NC}"
    
    # Keep script running
    wait
}

# Run main function
main "$@"
