#!/bin/bash

# MTBRMG ERP - Status Check Script

echo "🔍 MTBRMG ERP Status Check"
echo "=========================="

# Check PM2 processes
echo ""
echo "📊 PM2 Processes:"
pm2 status

# Check database connection
echo ""
echo "🗄️  Database Status:"
cd "$(dirname "${BASH_SOURCE[0]}")/../apps/backend"
source venv/bin/activate
if python manage.py check --database default > /dev/null 2>&1; then
    echo "✅ Database: Connected"
else
    echo "❌ Database: Connection failed"
fi

# Check services
echo ""
echo "🔧 System Services:"
if brew services list | grep postgresql | grep started > /dev/null; then
    echo "✅ PostgreSQL: Running"
else
    echo "❌ PostgreSQL: Not running"
fi

if brew services list | grep redis | grep started > /dev/null; then
    echo "✅ Redis: Running"
else
    echo "❌ Redis: Not running"
fi

# Check URLs
echo ""
echo "🌐 Application URLs:"
echo "• Frontend: http://localhost:3001"
echo "• Backend: http://localhost:8000"

# Check if ports are accessible
echo ""
echo "🔌 Port Status:"
if nc -z localhost 3001 2>/dev/null; then
    echo "✅ Frontend (3001): Accessible"
else
    echo "❌ Frontend (3001): Not accessible"
fi

if nc -z localhost 8000 2>/dev/null; then
    echo "✅ Backend (8000): Accessible"
else
    echo "❌ Backend (8000): Not accessible"
fi
