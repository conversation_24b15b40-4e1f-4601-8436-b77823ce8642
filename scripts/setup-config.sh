#!/bin/bash

# MTBRMG ERP - Automated Configuration Setup
# This script creates VS Code configuration files and project settings

echo "⚙️ MTBRMG ERP - Setting up project configurations..."
echo "=================================================="

# Create .vscode directory if it doesn't exist
mkdir -p .vscode

echo "📁 Creating VS Code configuration files..."

# Create settings.json
cat > .vscode/settings.json << 'EOF'
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "tailwindCSS.includeLanguages": {
    "typescript": "typescript",
    "typescriptreact": "typescriptreact"
  },
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],
  "files.associations": {
    "*.css": "tailwindcss"
  },
  "python.defaultInterpreterPath": "./apps/backend/venv/bin/python",
  "python.formatting.provider": "black",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": true,
  "python.linting.flake8Enabled": true,
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },
  "editor.rulers": [80, 120],
  "editor.wordWrap": "on",
  "editor.minimap.enabled": true,
  "workbench.iconTheme": "material-icon-theme",
  "git.enableSmartCommit": true,
  "git.confirmSync": false,
  "explorer.confirmDelete": false,
  "explorer.confirmDragAndDrop": false,
  "terminal.integrated.defaultProfile.osx": "bash",
  "files.exclude": {
    "**/node_modules": true,
    "**/.git": true,
    "**/.DS_Store": true,
    "**/venv": true,
    "**/__pycache__": true,
    "**/*.pyc": true
  }
}
EOF

# Create extensions.json
cat > .vscode/extensions.json << 'EOF'
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-python.python",
    "ms-python.black-formatter",
    "batisteo.vscode-django",
    "eamodio.gitlens",
    "yzhang.markdown-all-in-one",
    "humao.rest-client",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "PulkitGangwar.nextjs-snippets",
    "mtxr.sqltools",
    "mtxr.sqltools-driver-pg",
    "mhutchie.git-graph",
    "PKief.material-icon-theme",
    "christian-kohler.path-intellisense"
  ]
}
EOF

# Create launch.json for debugging
cat > .vscode/launch.json << 'EOF'
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Next.js: debug server-side",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/apps/frontend/node_modules/.bin/next",
      "args": ["dev", "--port", "3001"],
      "cwd": "${workspaceFolder}/apps/frontend",
      "console": "integratedTerminal",
      "skipFiles": ["<node_internals>/**"],
      "env": {
        "NODE_ENV": "development"
      }
    },
    {
      "name": "Python: Django",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/apps/backend/manage.py",
      "args": ["runserver", "0.0.0.0:8000"],
      "django": true,
      "cwd": "${workspaceFolder}/apps/backend",
      "console": "integratedTerminal",
      "env": {
        "DJANGO_SETTINGS_MODULE": "mtbrmg_erp.settings"
      }
    },
    {
      "name": "Python: Current File",
      "type": "python",
      "request": "launch",
      "program": "${file}",
      "console": "integratedTerminal",
      "cwd": "${workspaceFolder}/apps/backend"
    }
  ]
}
EOF

# Create tasks.json for build tasks
cat > .vscode/tasks.json << 'EOF'
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Start Frontend Dev Server",
      "type": "shell",
      "command": "npm run dev",
      "group": "build",
      "options": {
        "cwd": "${workspaceFolder}/apps/frontend"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      },
      "problemMatcher": []
    },
    {
      "label": "Start Backend Dev Server",
      "type": "shell",
      "command": "source venv/bin/activate && python manage.py runserver 0.0.0.0:8000",
      "group": "build",
      "options": {
        "cwd": "${workspaceFolder}/apps/backend"
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      },
      "problemMatcher": []
    },
    {
      "label": "Start Both Servers",
      "dependsOrder": "parallel",
      "dependsOn": ["Start Frontend Dev Server", "Start Backend Dev Server"],
      "group": {
        "kind": "build",
        "isDefault": true
      }
    },
    {
      "label": "Run Frontend Tests",
      "type": "shell",
      "command": "npm test",
      "group": "test",
      "options": {
        "cwd": "${workspaceFolder}/apps/frontend"
      }
    },
    {
      "label": "Run Backend Tests",
      "type": "shell",
      "command": "source venv/bin/activate && python manage.py test",
      "group": "test",
      "options": {
        "cwd": "${workspaceFolder}/apps/backend"
      }
    },
    {
      "label": "Format Frontend Code",
      "type": "shell",
      "command": "npm run format",
      "group": "build",
      "options": {
        "cwd": "${workspaceFolder}/apps/frontend"
      }
    },
    {
      "label": "Format Backend Code",
      "type": "shell",
      "command": "source venv/bin/activate && black . && isort .",
      "group": "build",
      "options": {
        "cwd": "${workspaceFolder}/apps/backend"
      }
    }
  ]
}
EOF

echo "✅ VS Code configuration files created!"

# Setup Frontend configurations
echo "📦 Setting up Frontend configurations..."

# Create .prettierrc for frontend
cat > apps/frontend/.prettierrc << 'EOF'
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "plugins": ["prettier-plugin-tailwindcss"]
}
EOF

# Create .eslintrc.json for frontend
cat > apps/frontend/.eslintrc.json << 'EOF'
{
  "extends": [
    "next/core-web-vitals",
    "prettier"
  ],
  "rules": {
    "prefer-const": "error",
    "no-unused-vars": "warn",
    "@typescript-eslint/no-unused-vars": "warn",
    "no-console": "warn"
  }
}
EOF

echo "✅ Frontend configuration files created!"

# Setup Backend configurations
echo "🐍 Setting up Backend configurations..."

# Create pyproject.toml for backend
cat > apps/backend/pyproject.toml << 'EOF'
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  migrations
  | venv
  | __pycache__
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
skip = ["migrations", "venv"]
known_django = "django"
known_first_party = ["mtbrmg_erp"]
sections = ["FUTURE", "STDLIB", "DJANGO", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

[tool.pylint.messages_control]
disable = [
  "C0114",  # missing-module-docstring
  "C0115",  # missing-class-docstring
  "C0116",  # missing-function-docstring
  "R0903",  # too-few-public-methods
  "R0801"   # duplicate-code
]

[tool.pylint.format]
max-line-length = 88
EOF

echo "✅ Backend configuration files created!"

# Create development documentation
echo "📚 Creating development documentation..."

mkdir -p docs/development

cat > docs/development/README.md << 'EOF'
# MTBRMG ERP - Development Setup

## Quick Start

1. **Install Extensions**: Run `./scripts/setup-extensions.sh`
2. **Setup Configuration**: Run `./scripts/setup-config.sh`
3. **Start Development**: Press `Ctrl+Shift+P` → "Tasks: Run Task" → "Start Both Servers"

## VS Code Features

### Debugging
- **F5**: Start debugging (Next.js or Django)
- **Ctrl+Shift+D**: Open debug panel

### Tasks
- **Ctrl+Shift+P** → "Tasks: Run Task":
  - Start Frontend Dev Server
  - Start Backend Dev Server
  - Start Both Servers
  - Run Tests
  - Format Code

### Extensions Installed
- Tailwind CSS IntelliSense
- Prettier Code Formatter
- ESLint
- Python Support
- Django Support
- GitLens
- REST Client
- And more...

## Development Workflow

1. **Code Formatting**: Automatic on save
2. **Linting**: Real-time error detection
3. **Type Checking**: TypeScript support
4. **Git Integration**: Enhanced with GitLens
5. **API Testing**: Use REST Client extension

## Keyboard Shortcuts

- **Ctrl+`**: Toggle terminal
- **Ctrl+Shift+`**: New terminal
- **Ctrl+P**: Quick file open
- **Ctrl+Shift+P**: Command palette
- **F12**: Go to definition
- **Shift+F12**: Find all references
EOF

echo "✅ Development documentation created!"

echo ""
echo "🎉 Project configuration setup complete!"
echo "=================================================="
echo "✅ Created VS Code configuration files:"
echo "   - .vscode/settings.json"
echo "   - .vscode/extensions.json"
echo "   - .vscode/launch.json"
echo "   - .vscode/tasks.json"
echo ""
echo "✅ Created Frontend configuration files:"
echo "   - apps/frontend/.prettierrc"
echo "   - apps/frontend/.eslintrc.json"
echo ""
echo "✅ Created Backend configuration files:"
echo "   - apps/backend/pyproject.toml"
echo ""
echo "✅ Created development documentation:"
echo "   - docs/development/README.md"
echo ""
echo "🚀 Next steps:"
echo "1. Restart VS Code"
echo "2. Press Ctrl+Shift+P → 'Tasks: Run Task' → 'Start Both Servers'"
echo "3. Start coding with enhanced development experience!"
