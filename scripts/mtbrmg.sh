#!/bin/bash

# MTBRMG ERP - Main Control Script
# Simple interface for managing the ERP system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Project paths
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# Function to print colored output
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Show banner
show_banner() {
    echo -e "${CYAN}"
    echo "███╗   ███╗████████╗██████╗ ██████╗ ███╗   ███╗ ██████╗ "
    echo "████╗ ████║╚══██╔══╝██╔══██╗██╔══██╗████╗ ████║██╔════╝ "
    echo "██╔████╔██║   ██║   ██████╔╝██████╔╝██╔████╔██║██║  ███╗"
    echo "██║╚██╔╝██║   ██║   ██╔══██╗██╔══██╗██║╚██╔╝██║██║   ██║"
    echo "██║ ╚═╝ ██║   ██║   ██████╔╝██║  ██║██║ ╚═╝ ██║╚██████╔╝"
    echo "╚═╝     ╚═╝   ╚═╝   ╚═════╝ ╚═╝  ╚═╝╚═╝     ╚═╝ ╚═════╝ "
    echo -e "${NC}"
    echo -e "${PURPLE}MTBRMG ERP System - Digital Agency Management${NC}"
    echo -e "${BLUE}=============================================${NC}"
    echo ""
}

# Show help
show_help() {
    echo -e "${YELLOW}Usage: ./scripts/mtbrmg.sh [COMMAND]${NC}"
    echo ""
    echo -e "${CYAN}Available Commands:${NC}"
    echo "  setup       - Initial setup with PM2"
    echo "  start       - Start the ERP system"
    echo "  stop        - Stop the ERP system"
    echo "  restart     - Restart the ERP system"
    echo "  status      - Show system status"
    echo "  logs        - Show application logs"
    echo "  monitor     - Open PM2 monitoring dashboard"
    echo "  auto        - Configure auto-startup on boot"
    echo "  update      - Update dependencies"
    echo "  backup      - Create database backup"
    echo "  help        - Show this help message"
    echo ""
    echo -e "${CYAN}Quick URLs:${NC}"
    echo "  Frontend: http://localhost:3001"
    echo "  Backend:  http://localhost:8000"
    echo ""
}

# Setup command
cmd_setup() {
    log_info "Setting up MTBRMG ERP with PM2..."
    cd "$PROJECT_ROOT"
    ./scripts/setup-pm2.sh
}

# Start command
cmd_start() {
    log_info "Starting MTBRMG ERP..."
    cd "$PROJECT_ROOT"
    
    # Check if PM2 is installed
    if ! command -v pm2 &> /dev/null; then
        log_error "PM2 not found. Please run: ./scripts/mtbrmg.sh setup"
        exit 1
    fi
    
    # Start services
    pm2 start ecosystem.config.js
    
    echo ""
    log_success "MTBRMG ERP started successfully!"
    echo -e "${CYAN}🌐 Frontend: http://localhost:3001${NC}"
    echo -e "${CYAN}🌐 Backend:  http://localhost:8000${NC}"
    echo ""
    echo -e "${YELLOW}Use './scripts/mtbrmg.sh status' to check system status${NC}"
}

# Stop command
cmd_stop() {
    log_info "Stopping MTBRMG ERP..."
    pm2 stop all
    log_success "MTBRMG ERP stopped successfully!"
}

# Restart command
cmd_restart() {
    log_info "Restarting MTBRMG ERP..."
    pm2 restart all
    log_success "MTBRMG ERP restarted successfully!"
}

# Status command
cmd_status() {
    cd "$PROJECT_ROOT"
    ./scripts/status.sh
}

# Logs command
cmd_logs() {
    echo -e "${CYAN}📋 MTBRMG ERP Logs${NC}"
    echo "=================="
    echo ""
    echo -e "${YELLOW}Choose log type:${NC}"
    echo "1. All logs"
    echo "2. Frontend only"
    echo "3. Backend only"
    echo "4. Error logs only"
    echo ""
    read -p "Enter choice (1-4): " choice
    
    case $choice in
        1) pm2 logs ;;
        2) pm2 logs mtbrmg-frontend ;;
        3) pm2 logs mtbrmg-backend ;;
        4) pm2 logs --err ;;
        *) log_error "Invalid choice" ;;
    esac
}

# Monitor command
cmd_monitor() {
    log_info "Opening PM2 monitoring dashboard..."
    pm2 monit
}

# Auto-startup command
cmd_auto() {
    log_info "Configuring auto-startup..."
    cd "$PROJECT_ROOT"
    ./scripts/auto-startup.sh
}

# Update command
cmd_update() {
    log_info "Updating MTBRMG ERP dependencies..."
    cd "$PROJECT_ROOT"
    
    # Update frontend dependencies
    log_info "Updating frontend dependencies..."
    cd apps/frontend
    pnpm update
    
    # Update backend dependencies
    log_info "Updating backend dependencies..."
    cd ../backend
    source venv/bin/activate
    pip install --upgrade -r requirements.txt
    
    # Run migrations
    log_info "Running database migrations..."
    python manage.py migrate
    
    log_success "Dependencies updated successfully!"
}

# Backup command
cmd_backup() {
    log_info "Creating database backup..."
    
    BACKUP_DIR="$PROJECT_ROOT/backups"
    mkdir -p "$BACKUP_DIR"
    
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    BACKUP_FILE="$BACKUP_DIR/mtbrmg_erp_backup_$TIMESTAMP.sql"
    
    pg_dump mtbrmg_erp > "$BACKUP_FILE"
    
    log_success "Database backup created: $BACKUP_FILE"
}

# Main function
main() {
    show_banner
    
    # Check if command is provided
    if [[ $# -eq 0 ]]; then
        show_help
        exit 0
    fi
    
    # Execute command
    case "$1" in
        setup)
            cmd_setup
            ;;
        start)
            cmd_start
            ;;
        stop)
            cmd_stop
            ;;
        restart)
            cmd_restart
            ;;
        status)
            cmd_status
            ;;
        logs)
            cmd_logs
            ;;
        monitor)
            cmd_monitor
            ;;
        auto)
            cmd_auto
            ;;
        update)
            cmd_update
            ;;
        backup)
            cmd_backup
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
