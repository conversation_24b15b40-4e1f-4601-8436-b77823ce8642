#!/bin/bash

# MTBRMG ERP - Working Fast Development Environment
# Simple, reliable, and optimized setup

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/apps/backend"
FRONTEND_DIR="$PROJECT_ROOT/apps/frontend"

echo -e "${BLUE}🚀 Starting Working Fast Development Environment${NC}"
echo "=============================================="

# Function to kill processes on port
kill_port() {
    local port=$1
    echo -e "${YELLOW}🔄 Cleaning port $port...${NC}"
    lsof -ti :$port | xargs kill -9 2>/dev/null || true
    sleep 1
}

# Clean up existing processes
kill_port 8000
kill_port 3001

# Start services
echo -e "${YELLOW}🔄 Starting Redis...${NC}"
brew services start redis 2>/dev/null || true

echo -e "${YELLOW}🔄 Starting PostgreSQL...${NC}"
brew services start postgresql@15 2>/dev/null || true

# Apply performance optimizations
export NODE_OPTIONS="--max-old-space-size=8192"
export NEXT_TELEMETRY_DISABLED=1
export PYTHONUNBUFFERED=1

# Start backend
echo -e "${YELLOW}🐍 Starting Django backend...${NC}"
cd "$BACKEND_DIR"
source venv/bin/activate

# Quick migration check
python manage.py migrate --check > /dev/null 2>&1 || {
    echo -e "${YELLOW}📊 Running migrations...${NC}"
    python manage.py migrate
}

# Start backend in background
python manage.py runserver 8000 --noreload &
BACKEND_PID=$!

# Start frontend
echo -e "${YELLOW}⚡ Starting Next.js frontend...${NC}"
cd "$FRONTEND_DIR"

# Install dependencies if needed
if [[ ! -d "node_modules" ]]; then
    echo -e "${YELLOW}📦 Installing dependencies...${NC}"
    pnpm install
fi

# Start frontend in background
pnpm dev &
FRONTEND_PID=$!

# Wait for services
echo -e "${YELLOW}⏳ Waiting for services to start...${NC}"
sleep 10

# Check services
echo -e "\n${GREEN}✅ Development Environment Status${NC}"
echo "=================================="

if kill -0 $BACKEND_PID 2>/dev/null; then
    echo -e "${GREEN}✅ Backend: Running (PID: $BACKEND_PID)${NC}"
    echo -e "${BLUE}🌐 Backend API: http://localhost:8000/api/${NC}"
    echo -e "${BLUE}🌐 Admin Panel: http://localhost:8000/admin/${NC}"
else
    echo -e "${RED}❌ Backend: Failed${NC}"
fi

if kill -0 $FRONTEND_PID 2>/dev/null; then
    echo -e "${GREEN}✅ Frontend: Running (PID: $FRONTEND_PID)${NC}"
    echo -e "${BLUE}🌐 Frontend: http://localhost:3001/founder-dashboard${NC}"
    echo -e "${BLUE}🌐 Login: founder / demo123${NC}"
else
    echo -e "${RED}❌ Frontend: Failed${NC}"
fi

# Test connectivity
echo -e "\n${YELLOW}🧪 Testing connectivity...${NC}"
sleep 5

if curl -s http://localhost:8000/api/ > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Backend API responding${NC}"
else
    echo -e "${YELLOW}⚠️  Backend API not ready yet${NC}"
fi

if curl -s http://localhost:3001/ > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Frontend responding${NC}"
else
    echo -e "${YELLOW}⚠️  Frontend not ready yet (may take a moment)${NC}"
fi

echo -e "\n${PURPLE}💡 Performance Optimizations:${NC}"
echo "  • Node.js memory: 8GB"
echo "  • Django auto-reload: disabled"
echo "  • Telemetry: disabled"
echo "  • Services: optimized"

echo -e "\n${PURPLE}📊 Management:${NC}"
echo "  • Stop: Ctrl+C or kill $BACKEND_PID $FRONTEND_PID"
echo "  • Backend logs: Check terminal output"
echo "  • Frontend logs: Check terminal output"

echo -e "\n${GREEN}🎉 Development environment ready!${NC}"
echo -e "${CYAN}Press Ctrl+C to stop all services${NC}"

# Handle cleanup on exit
cleanup() {
    echo -e "\n${YELLOW}🛑 Stopping services...${NC}"
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true
    echo -e "${GREEN}✅ Services stopped${NC}"
    exit 0
}

trap cleanup INT TERM

# Wait for processes
wait
