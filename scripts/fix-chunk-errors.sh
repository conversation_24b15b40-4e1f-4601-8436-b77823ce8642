#!/bin/bash

# MTBRMG ERP - Fix Chunk Loading Errors Script
# This script fixes common Next.js chunk loading errors

echo "🔧 Fixing Next.js Chunk Loading Errors"
echo "======================================"

# Navigate to project root
cd "$(dirname "$0")/.."

echo "📍 Current directory: $(pwd)"

# Stop any running development servers
echo "🛑 Stopping existing development servers..."
pkill -f "next dev" 2>/dev/null || true
pkill -f "pnpm dev" 2>/dev/null || true
sleep 2

# Clean Next.js cache and build artifacts
echo "🧹 Cleaning Next.js cache..."
rm -rf apps/frontend/.next
rm -rf apps/frontend/node_modules/.cache
rm -rf node_modules/.cache
rm -rf .turbo

# Clear browser cache instruction
echo "🌐 Please clear your browser cache:"
echo "   - Chrome/Edge: Ctrl+Shift+R (Cmd+Shift+R on Mac)"
echo "   - Firefox: Ctrl+F5 (Cmd+Shift+R on Mac)"
echo "   - Safari: Cmd+Option+R"

# Restart development server
echo "🚀 Starting fresh development server..."
cd apps/frontend

# Start the development server
echo "⚡ Starting Next.js development server on port 3001..."
npx next dev --port 3001 &

# Wait for server to start
echo "⏳ Waiting for server to start..."
sleep 5

# Test if server is running
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3001/ | grep -q "200"; then
    echo "✅ Development server is running successfully!"
    echo "🌐 Open: http://localhost:3001"
    echo "📊 Finance Revenue Add: http://localhost:3001/founder-dashboard/finance/revenue/add"
else
    echo "❌ Server failed to start. Check the logs above."
    exit 1
fi

echo ""
echo "🎉 Chunk loading errors should now be fixed!"
echo "💡 If you still experience issues:"
echo "   1. Clear browser cache completely"
echo "   2. Try incognito/private browsing mode"
echo "   3. Restart your browser"
echo "   4. Run this script again"
