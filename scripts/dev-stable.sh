#!/bin/bash

# MTBRMG ERP - Stable Development Server for macOS
# Lightweight solution without Vite - optimized for Mac performance

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project paths
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/apps/backend"
FRONTEND_DIR="$PROJECT_ROOT/apps/frontend"
LOGS_DIR="$PROJECT_ROOT/logs"

# Create logs directory
mkdir -p "$LOGS_DIR"

# Function to print colored output
log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] ✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}[$(date +'%H:%M:%S')] ⚠️  $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%H:%M:%S')] ❌ $1${NC}"
}

# Function to kill processes on specific ports
kill_port() {
    local port=$1
    local pids=$(lsof -ti:$port 2>/dev/null || true)
    if [ ! -z "$pids" ]; then
        warning "Killing processes on port $port"
        for pid in $pids; do
            kill -9 $pid 2>/dev/null || true
        done
        sleep 1
    fi
}

# Function to check if a service is running
check_service() {
    local port=$1
    local service_name=$2
    if curl -f http://localhost:$port >/dev/null 2>&1; then
        success "$service_name is running on port $port"
        return 0
    else
        return 1
    fi
}

# Cleanup function
cleanup() {
    log "Cleaning up processes..."
    kill_port 8000
    kill_port 3001

    # Kill any remaining Python/Node processes related to our project
    pkill -f "python manage.py runserver" 2>/dev/null || true
    pkill -f "next dev" 2>/dev/null || true
    pkill -f "pnpm dev" 2>/dev/null || true

    # Kill turbo daemon to prevent conflicts
    pkill -f "turbo.*daemon" 2>/dev/null || true

    sleep 1
    success "Cleanup completed"
}

# Start PostgreSQL and Redis
start_services() {
    log "Starting required services..."
    
    # Start PostgreSQL
    if ! brew services list | grep postgresql@15 | grep started >/dev/null; then
        log "Starting PostgreSQL..."
        brew services start postgresql@15 >/dev/null 2>&1 || true
    fi
    
    # Start Redis
    if ! brew services list | grep redis | grep started >/dev/null; then
        log "Starting Redis..."
        brew services start redis >/dev/null 2>&1 || true
    fi
    
    sleep 2
    success "Services started"
}

# Start Django backend
start_backend() {
    log "Starting Django backend..."
    
    cd "$BACKEND_DIR"
    
    # Activate virtual environment
    if [ ! -d "venv" ]; then
        error "Virtual environment not found. Please run setup first."
        exit 1
    fi
    
    source venv/bin/activate
    
    # Quick migration check
    python manage.py migrate --check >/dev/null 2>&1 || {
        log "Running database migrations..."
        python manage.py migrate
    }
    
    # Start backend with optimizations
    log "Starting Django server on port 8000..."
    nohup python manage.py runserver 8000 --noreload >"$LOGS_DIR/backend.log" 2>&1 &
    BACKEND_PID=$!
    
    # Wait for backend to start
    for i in {1..30}; do
        if check_service 8000 "Backend"; then
            break
        fi
        sleep 1
    done
    
    if ! check_service 8000 "Backend"; then
        error "Backend failed to start"
        exit 1
    fi
}

# Start Next.js frontend
start_frontend() {
    log "Starting Next.js frontend..."
    
    cd "$FRONTEND_DIR"
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        log "Installing frontend dependencies..."
        pnpm install
    fi
    
    # Set performance optimizations
    export NODE_OPTIONS="--max-old-space-size=4096"
    export NEXT_TELEMETRY_DISABLED=1
    export DISABLE_ESLINT_PLUGIN=true
    
    # Start frontend
    log "Starting Next.js server on port 3001..."
    nohup pnpm dev >"$LOGS_DIR/frontend.log" 2>&1 &
    FRONTEND_PID=$!
    
    # Wait for frontend to start
    for i in {1..60}; do
        if check_service 3001 "Frontend"; then
            break
        fi
        sleep 1
    done
    
    if ! check_service 3001 "Frontend"; then
        error "Frontend failed to start"
        exit 1
    fi
}

# Show status
show_status() {
    echo ""
    echo "🚀 MTBRMG ERP Development Server Status"
    echo "======================================"
    
    if check_service 8000 "Backend API"; then
        echo "✅ Backend:  http://localhost:8000"
        echo "   API:      http://localhost:8000/api/"
        echo "   Admin:    http://localhost:8000/admin/"
    else
        echo "❌ Backend:  Not running"
    fi
    
    if check_service 3001 "Frontend"; then
        echo "✅ Frontend: http://localhost:3001"
        echo "   Login:    founder / demo123"
        echo "   Dashboard: http://localhost:3001/founder-dashboard"
    else
        echo "❌ Frontend: Not running"
    fi
    
    echo ""
    echo "📋 Commands:"
    echo "   View logs: tail -f $LOGS_DIR/backend.log"
    echo "   View logs: tail -f $LOGS_DIR/frontend.log"
    echo "   Stop all:  $0 stop"
    echo ""
}

# Main execution
case "${1:-start}" in
    "start")
        log "Starting MTBRMG ERP Development Environment..."
        cleanup
        start_services
        start_backend
        start_frontend
        show_status
        ;;
    "stop")
        log "Stopping MTBRMG ERP Development Environment..."
        cleanup
        success "All services stopped"
        ;;
    "restart")
        log "Restarting MTBRMG ERP Development Environment..."
        cleanup
        sleep 2
        start_services
        start_backend
        start_frontend
        show_status
        ;;
    "status")
        show_status
        ;;
    "logs")
        case "$2" in
            "backend")
                tail -f "$LOGS_DIR/backend.log"
                ;;
            "frontend")
                tail -f "$LOGS_DIR/frontend.log"
                ;;
            *)
                echo "Usage: $0 logs [backend|frontend]"
                ;;
        esac
        ;;
    *)
        echo "MTBRMG ERP - Stable Development Server"
        echo ""
        echo "Usage: $0 [start|stop|restart|status|logs]"
        echo ""
        echo "Commands:"
        echo "  start    - Start all services (default)"
        echo "  stop     - Stop all services"
        echo "  restart  - Restart all services"
        echo "  status   - Show service status"
        echo "  logs     - View logs (backend|frontend)"
        echo ""
        ;;
esac
