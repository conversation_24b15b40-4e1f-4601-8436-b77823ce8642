#!/bin/bash

# MTBRMG ERP - Complete Development Environment Setup
# This script runs all setup scripts in the correct order

echo "🚀 MTBRMG ERP - Complete Development Environment Setup"
echo "====================================================="
echo ""
echo "This script will:"
echo "1. Install VS Code extensions"
echo "2. Setup project configurations"
echo "3. Install development dependencies"
echo "4. Create documentation"
echo ""

read -p "Do you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Setup cancelled."
    exit 1
fi

echo ""
echo "🔧 Step 1: Installing VS Code extensions..."
echo "==========================================="
chmod +x scripts/setup-extensions.sh
./scripts/setup-extensions.sh

if [ $? -ne 0 ]; then
    echo "❌ Extension installation failed. Please check the errors above."
    exit 1
fi

echo ""
echo "⚙️ Step 2: Setting up project configurations..."
echo "=============================================="
chmod +x scripts/setup-config.sh
./scripts/setup-config.sh

if [ $? -ne 0 ]; then
    echo "❌ Configuration setup failed. Please check the errors above."
    exit 1
fi

echo ""
echo "📦 Step 3: Installing additional dependencies..."
echo "=============================================="

# Frontend dependencies
if [ -d "apps/frontend" ] && command -v npm &> /dev/null; then
    echo "📦 Installing frontend development dependencies..."
    cd apps/frontend
    
    # Update package.json scripts
    npm pkg set scripts.lint:fix="next lint --fix"
    npm pkg set scripts.type-check="tsc --noEmit"
    npm pkg set scripts.format="prettier --write ."
    npm pkg set scripts.analyze="ANALYZE=true npm run build"
    
    # Install if not already installed
    npm install --save-dev @next/bundle-analyzer 2>/dev/null || true
    npm install --save-dev eslint-config-prettier 2>/dev/null || true
    npm install --save-dev prettier 2>/dev/null || true
    npm install --save-dev prettier-plugin-tailwindcss 2>/dev/null || true
    
    cd ../..
    echo "✅ Frontend dependencies updated!"
else
    echo "⚠️ Skipping frontend dependencies (npm not found or directory missing)"
fi

# Backend dependencies
if [ -d "apps/backend" ] && command -v python3 &> /dev/null; then
    echo "🐍 Setting up backend development dependencies..."
    cd apps/backend
    
    # Create requirements-dev.txt if it doesn't exist
    if [ ! -f "requirements-dev.txt" ]; then
        cat > requirements-dev.txt << 'EOF'
# Development dependencies
black==23.9.1
flake8==6.1.0
isort==5.12.0
pylint==3.0.1
django-debug-toolbar==4.2.0
django-extensions==3.2.3
pytest-django==4.5.2
coverage==7.3.2
django-silk==5.0.4
pre-commit==3.5.0
EOF
        echo "✅ Created requirements-dev.txt"
    fi
    
    cd ../..
    echo "✅ Backend dependencies configured!"
else
    echo "⚠️ Skipping backend dependencies (python not found or directory missing)"
fi

echo ""
echo "📚 Step 4: Creating additional documentation..."
echo "============================================="

# Create comprehensive documentation structure
mkdir -p docs/{api,components,deployment,development,responsive-design}

# Create API documentation template
cat > docs/api/README.md << 'EOF'
# MTBRMG ERP - API Documentation

## Authentication Endpoints
- `POST /api/auth/login/` - User login
- `POST /api/auth/logout/` - User logout
- `GET /api/auth/profile/` - Get user profile

## Client Management
- `GET /api/clients/` - List clients
- `POST /api/clients/` - Create client
- `GET /api/clients/{id}/` - Get client details
- `PUT /api/clients/{id}/` - Update client
- `DELETE /api/clients/{id}/` - Delete client

## Project Management
- `GET /api/projects/` - List projects
- `POST /api/projects/` - Create project
- `GET /api/projects/{id}/` - Get project details

## Finance Module
- `GET /api/revenue/` - Revenue data
- `GET /api/expenses/` - Expense data
- `GET /api/cash-flow/` - Cash flow data

## Testing APIs
Use the REST Client extension in VS Code:
1. Create `.http` files in `docs/api/`
2. Use the format shown in the examples
EOF

# Create component documentation
cat > docs/components/README.md << 'EOF'
# MTBRMG ERP - Component Documentation

## Responsive Design Components

### UnifiedLayout
Main layout component with responsive sidebar and header.

### ResponsiveBreadcrumb
Breadcrumb navigation with mobile-optimized spacing.

### Cards and Grids
All cards use responsive grid patterns:
- Mobile: `grid-cols-1`
- Tablet: `grid-cols-2`
- Desktop: `grid-cols-4`

## Design System

### Breakpoints
- Mobile: < 640px
- Tablet: 640px - 1024px
- Desktop: 1024px+

### Touch Targets
All interactive elements use `min-h-[44px] touch-target`

### Typography
Responsive text sizing: `text-2xl sm:text-3xl lg:text-4xl`
EOF

# Create deployment documentation
cat > docs/deployment/README.md << 'EOF'
# MTBRMG ERP - Deployment Guide

## Development Deployment
1. Run `./scripts/dev-stable.sh start`
2. Access frontend: http://localhost:3001
3. Access backend: http://localhost:8000

## Production Deployment
(To be documented based on deployment strategy)

## Environment Variables
- `DATABASE_URL` - PostgreSQL connection string
- `REDIS_URL` - Redis connection string
- `SECRET_KEY` - Django secret key
- `DEBUG` - Debug mode (False in production)
EOF

echo "✅ Documentation structure created!"

echo ""
echo "🎉 COMPLETE SETUP FINISHED!"
echo "=========================="
echo ""
echo "✅ What was installed/configured:"
echo "   📦 VS Code extensions (20+ extensions)"
echo "   ⚙️ Project configuration files"
echo "   🎨 Code formatting and linting setup"
echo "   🐛 Debugging configuration"
echo "   📚 Development documentation"
echo "   🔧 Build tasks and shortcuts"
echo ""
echo "🚀 Next steps:"
echo "   1. Restart VS Code completely"
echo "   2. Open this project in VS Code"
echo "   3. Press Ctrl+Shift+P → 'Tasks: Run Task' → 'Start Both Servers'"
echo "   4. Start developing with enhanced productivity!"
echo ""
echo "💡 Pro Tips:"
echo "   - Use F5 to start debugging"
echo "   - Code formatting happens automatically on save"
echo "   - Use Ctrl+` to toggle terminal"
echo "   - Use REST Client extension to test APIs"
echo "   - Check docs/development/README.md for more info"
echo ""
echo "🎯 Your development environment is now optimized for:"
echo "   ✅ Responsive design development"
echo "   ✅ TypeScript/React development"
echo "   ✅ Django/Python development"
echo "   ✅ API testing and debugging"
echo "   ✅ Git workflow optimization"
echo "   ✅ Code quality and formatting"
echo ""
echo "Happy coding! 🚀"
