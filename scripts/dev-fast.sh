#!/bin/bash

# MTBRMG ERP - Fast Development Environment
# Optimized Next.js + Django setup with performance enhancements

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/apps/backend"
FRONTEND_DIR="$PROJECT_ROOT/apps/frontend"
LOGS_DIR="$PROJECT_ROOT/logs"
PIDS_DIR="$PROJECT_ROOT/tmp"

# Create directories
mkdir -p "$LOGS_DIR" "$PIDS_DIR"

# Apply performance optimizations
export NODE_OPTIONS="--max-old-space-size=8192 --max-semi-space-size=512"
export UV_THREADPOOL_SIZE=128
export PYTHONUNBUFFERED=1
export NEXT_TELEMETRY_DISABLED=1
export TURBO_TELEMETRY_DISABLED=1
export DISABLE_ESLINT_PLUGIN=true
export FAST_REFRESH=true

echo -e "${BLUE}🚀 Starting Fast Development Environment${NC}"
echo "=============================================="

# Function to check if port is in use
port_in_use() {
    lsof -i :$1 > /dev/null 2>&1
}

# Function to kill processes on port
kill_port() {
    local port=$1
    if port_in_use $port; then
        echo -e "${YELLOW}🔄 Killing processes on port $port...${NC}"
        lsof -ti :$port | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
}

# Clean up any existing processes
echo -e "${YELLOW}🧹 Cleaning up existing processes...${NC}"
kill_port 8000
kill_port 3001
kill_port 3002

# Start Redis if not running
if ! brew services list | grep redis | grep started > /dev/null; then
    echo -e "${YELLOW}🔄 Starting Redis...${NC}"
    brew services start redis
    sleep 2
fi

# Start PostgreSQL if not running
if ! brew services list | grep postgresql | grep started > /dev/null; then
    echo -e "${YELLOW}🔄 Starting PostgreSQL...${NC}"
    brew services start postgresql@15
    sleep 3
fi

# Start optimized backend
echo -e "${YELLOW}🐍 Starting optimized Django backend...${NC}"
cd "$BACKEND_DIR"
source venv/bin/activate

# Quick migration check
python manage.py migrate --check > /dev/null 2>&1 || {
    echo -e "${YELLOW}📊 Running database migrations...${NC}"
    python manage.py migrate
}

# Start backend with optimizations (no reload for better performance)
nohup python manage.py runserver 8000 --noreload > "$LOGS_DIR/backend-fast.log" 2>&1 &
BACKEND_PID=$!

# Start optimized frontend
echo -e "${YELLOW}⚡ Starting optimized Next.js frontend...${NC}"
cd "$FRONTEND_DIR"

# Install dependencies if needed
if [[ ! -d "node_modules" ]]; then
    echo -e "${YELLOW}📦 Installing frontend dependencies...${NC}"
    pnpm install
fi

# Start Next.js with optimizations
nohup pnpm dev > "$LOGS_DIR/frontend-fast.log" 2>&1 &
FRONTEND_PID=$!

# Wait for services to start
echo -e "${YELLOW}⏳ Waiting for services to start...${NC}"
sleep 8

# Check if services are running
backend_running=false
frontend_running=false

if kill -0 $BACKEND_PID 2>/dev/null; then
    backend_running=true
fi

if kill -0 $FRONTEND_PID 2>/dev/null; then
    frontend_running=true
fi

# Save PIDs for cleanup
echo $BACKEND_PID > "$PIDS_DIR/backend-fast.pid"
echo $FRONTEND_PID > "$PIDS_DIR/frontend-fast.pid"

echo -e "\n${GREEN}✅ Fast Development Environment Status${NC}"
echo "=============================================="

if $backend_running; then
    echo -e "${GREEN}✅ Backend: Running (PID: $BACKEND_PID)${NC}"
    echo -e "${BLUE}🌐 Backend API: http://localhost:8000/api/${NC}"
    echo -e "${BLUE}🌐 Admin Panel: http://localhost:8000/admin/${NC}"
else
    echo -e "${RED}❌ Backend: Failed to start${NC}"
fi

if $frontend_running; then
    echo -e "${GREEN}✅ Frontend: Running (PID: $FRONTEND_PID)${NC}"
    echo -e "${BLUE}🌐 Frontend: http://localhost:3001/founder-dashboard${NC}"
else
    echo -e "${RED}❌ Frontend: Failed to start${NC}"
fi

echo -e "\n${PURPLE}💡 Performance Optimizations Applied:${NC}"
echo "  • Node.js memory increased to 8GB"
echo "  • Django auto-reload disabled"
echo "  • Telemetry disabled"
echo "  • ESLint disabled for faster builds"
echo "  • Fast refresh enabled"

echo -e "\n${PURPLE}📊 Useful Commands:${NC}"
echo "  • View backend logs: tail -f logs/backend-fast.log"
echo "  • View frontend logs: tail -f logs/frontend-fast.log"
echo "  • Stop services: ./scripts/stop-fast.sh"
echo "  • Monitor performance: ./scripts/monitor-fast.sh"

# Create stop script
cat > "$PROJECT_ROOT/scripts/stop-fast.sh" <<'EOF'
#!/bin/bash

# Stop fast development environment

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
PIDS_DIR="$PROJECT_ROOT/tmp"

echo "🛑 Stopping fast development environment..."

# Kill processes
if [[ -f "$PIDS_DIR/backend-fast.pid" ]]; then
    kill $(cat "$PIDS_DIR/backend-fast.pid") 2>/dev/null || true
    rm "$PIDS_DIR/backend-fast.pid"
    echo "✅ Backend stopped"
fi

if [[ -f "$PIDS_DIR/frontend-fast.pid" ]]; then
    kill $(cat "$PIDS_DIR/frontend-fast.pid") 2>/dev/null || true
    rm "$PIDS_DIR/frontend-fast.pid"
    echo "✅ Frontend stopped"
fi

echo "✅ Fast development environment stopped"
EOF

chmod +x "$PROJECT_ROOT/scripts/stop-fast.sh"

# Create monitoring script
cat > "$PROJECT_ROOT/scripts/monitor-fast.sh" <<'EOF'
#!/bin/bash

# Monitor fast development environment performance

echo "🔍 MTBRMG ERP Performance Monitor"
echo "================================="

while true; do
    clear
    echo "🔍 MTBRMG ERP Performance Monitor - $(date)"
    echo "============================================="
    
    # System load
    echo -e "\n📊 System Load:"
    uptime
    
    # Memory usage
    echo -e "\n🧠 Memory Usage:"
    vm_stat | head -5
    
    # Process status
    echo -e "\n🔧 Process Status:"
    if pgrep -f "python manage.py runserver" > /dev/null; then
        echo "✅ Backend: Running"
    else
        echo "❌ Backend: Stopped"
    fi
    
    if pgrep -f "pnpm dev" > /dev/null; then
        echo "✅ Frontend: Running"
    else
        echo "❌ Frontend: Stopped"
    fi
    
    # Port status
    echo -e "\n🌐 Port Status:"
    if lsof -i :8000 > /dev/null 2>&1; then
        echo "✅ Port 8000: In use (Backend)"
    else
        echo "❌ Port 8000: Free"
    fi
    
    if lsof -i :3001 > /dev/null 2>&1; then
        echo "✅ Port 3001: In use (Frontend)"
    else
        echo "❌ Port 3001: Free"
    fi
    
    echo -e "\n💡 Press Ctrl+C to exit monitoring"
    sleep 5
done
EOF

chmod +x "$PROJECT_ROOT/scripts/monitor-fast.sh"

echo -e "\n${GREEN}🎉 Fast development environment is ready!${NC}"
echo -e "${YELLOW}💡 This setup should be significantly faster than the default configuration.${NC}"

# Wait for user input to keep script running
echo -e "\n${CYAN}Press Ctrl+C to stop all services, or run './scripts/stop-fast.sh' from another terminal${NC}"
trap "echo -e '\n${YELLOW}🛑 Stopping services...${NC}'; ./scripts/stop-fast.sh; exit" INT
wait
