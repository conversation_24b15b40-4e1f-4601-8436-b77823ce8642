#!/bin/bash

# MTBRMG ERP - PM2 Start Script
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo "🚀 Starting MTBRMG ERP with PM2..."

# Navigate to project root
cd "$PROJECT_ROOT"

# Start services with PM2
pm2 start ecosystem.config.js

# Show status
pm2 status

echo ""
echo "✅ MTBRMG ERP is running!"
echo "🌐 Frontend: http://localhost:3001"
echo "🌐 Backend: http://localhost:8000"
echo ""
echo "📊 Monitor with: pm2 monit"
echo "📋 View logs: pm2 logs"
echo "🛑 Stop all: pm2 stop all"
