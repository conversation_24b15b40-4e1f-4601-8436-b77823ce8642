#!/bin/bash

# MTBRMG ERP - PM2 Setup Script
# Automated setup for stable local development using PM2

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project paths
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/apps/backend"
FRONTEND_DIR="$PROJECT_ROOT/apps/frontend"

echo -e "${BLUE}🚀 MTBRMG ERP - PM2 Setup${NC}"
echo -e "${BLUE}================================${NC}"

# Function to print colored output
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_node() {
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi
    log_success "Node.js found: $(node --version)"
}

# Install PM2 globally
install_pm2() {
    log_info "Installing PM2 globally..."
    
    if command -v pm2 &> /dev/null; then
        log_success "PM2 is already installed: $(pm2 --version)"
    else
        npm install -g pm2
        log_success "PM2 installed successfully"
    fi
}

# Create PM2 ecosystem file
create_ecosystem() {
    log_info "Creating PM2 ecosystem configuration..."
    
    cat > "$PROJECT_ROOT/ecosystem.config.js" << 'EOF'
module.exports = {
  apps: [
    {
      name: 'mtbrmg-backend',
      cwd: './apps/backend',
      script: 'python',
      args: 'manage.py runserver 8000',
      interpreter: './venv/bin/python',
      env: {
        DJANGO_SETTINGS_MODULE: 'mtbrmg_erp.settings',
        PYTHONUNBUFFERED: '1',
        DEBUG: 'True'
      },
      watch: false,
      autorestart: true,
      max_restarts: 10,
      min_uptime: '10s',
      max_memory_restart: '500M',
      error_file: './logs/backend-error.log',
      out_file: './logs/backend-out.log',
      log_file: './logs/backend-combined.log',
      time: true
    },
    {
      name: 'mtbrmg-frontend',
      cwd: './apps/frontend',
      script: 'pnpm',
      args: 'dev',
      env: {
        NODE_ENV: 'development',
        PORT: '3001',
        NEXT_TELEMETRY_DISABLED: '1'
      },
      watch: false,
      autorestart: true,
      max_restarts: 10,
      min_uptime: '10s',
      max_memory_restart: '1G',
      error_file: './logs/frontend-error.log',
      out_file: './logs/frontend-out.log',
      log_file: './logs/frontend-combined.log',
      time: true
    }
  ]
};
EOF
    
    log_success "PM2 ecosystem configuration created"
}

# Create logs directory
create_logs_dir() {
    log_info "Creating logs directory..."
    mkdir -p "$PROJECT_ROOT/logs"
    log_success "Logs directory created"
}

# Setup Python virtual environment
setup_backend() {
    log_info "Setting up backend environment..."
    
    cd "$BACKEND_DIR"
    
    # Check if virtual environment exists
    if [[ ! -d "venv" ]]; then
        log_info "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment and install dependencies
    source venv/bin/activate
    
    if [[ -f "requirements.txt" ]]; then
        log_info "Installing Python dependencies..."
        pip install -r requirements.txt
    fi
    
    log_success "Backend environment ready"
}

# Setup frontend dependencies
setup_frontend() {
    log_info "Setting up frontend environment..."
    
    cd "$FRONTEND_DIR"
    
    # Install dependencies if needed
    if [[ ! -d "node_modules" ]]; then
        log_info "Installing frontend dependencies..."
        pnpm install
    fi
    
    log_success "Frontend environment ready"
}

# Check database connection
check_database() {
    log_info "Checking database connection..."
    
    cd "$BACKEND_DIR"
    source venv/bin/activate
    
    # Check if database is accessible
    if python manage.py check --database default > /dev/null 2>&1; then
        log_success "Database connection OK"
    else
        log_warning "Database connection failed. Make sure PostgreSQL is running."
        log_info "Starting PostgreSQL..."
        brew services start postgresql@15 || brew services start postgresql
    fi
}

# Create startup script
create_startup_script() {
    log_info "Creating startup script..."
    
    cat > "$PROJECT_ROOT/scripts/start-pm2.sh" << 'EOF'
#!/bin/bash

# MTBRMG ERP - PM2 Start Script
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo "🚀 Starting MTBRMG ERP with PM2..."

# Navigate to project root
cd "$PROJECT_ROOT"

# Start services with PM2
pm2 start ecosystem.config.js

# Show status
pm2 status

echo ""
echo "✅ MTBRMG ERP is running!"
echo "🌐 Frontend: http://localhost:3001"
echo "🌐 Backend: http://localhost:8000"
echo ""
echo "📊 Monitor with: pm2 monit"
echo "📋 View logs: pm2 logs"
echo "🛑 Stop all: pm2 stop all"
EOF
    
    chmod +x "$PROJECT_ROOT/scripts/start-pm2.sh"
    log_success "Startup script created"
}

# Create stop script
create_stop_script() {
    log_info "Creating stop script..."
    
    cat > "$PROJECT_ROOT/scripts/stop-pm2.sh" << 'EOF'
#!/bin/bash

echo "🛑 Stopping MTBRMG ERP..."

# Stop all PM2 processes
pm2 stop all

# Delete processes from PM2
pm2 delete all

echo "✅ MTBRMG ERP stopped successfully"
EOF
    
    chmod +x "$PROJECT_ROOT/scripts/stop-pm2.sh"
    log_success "Stop script created"
}

# Main setup function
main() {
    log_info "Starting PM2 setup for MTBRMG ERP..."
    
    check_node
    install_pm2
    create_logs_dir
    setup_backend
    setup_frontend
    check_database
    create_ecosystem
    create_startup_script
    create_stop_script
    
    echo ""
    echo -e "${GREEN}🎉 PM2 setup completed successfully!${NC}"
    echo ""
    echo -e "${YELLOW}Next steps:${NC}"
    echo "1. Start the application: ./scripts/start-pm2.sh"
    echo "2. Monitor processes: pm2 monit"
    echo "3. View logs: pm2 logs"
    echo "4. Stop application: ./scripts/stop-pm2.sh"
    echo ""
    echo -e "${BLUE}URLs:${NC}"
    echo "• Frontend: http://localhost:3001"
    echo "• Backend: http://localhost:8000"
}

# Run main function
main "$@"
